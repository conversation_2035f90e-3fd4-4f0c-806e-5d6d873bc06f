export const dynamic = 'force-dynamic';

import { Suspense } from 'react';
import Spinner from '@/app/components/ui/Spinner';
import RolePermissionsContent from './components/RolePermissionsContent';

export default function RolePermissionsPage() {
    return (
        <Suspense fallback={<div className="flex min-h-[300px] items-center justify-center">
            <Spinner size="lg" />
        </div>}>
            <RolePermissionsContent />
        </Suspense>
    );
}
