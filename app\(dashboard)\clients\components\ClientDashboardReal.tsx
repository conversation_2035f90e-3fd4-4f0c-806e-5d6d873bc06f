'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Users, UserPlus, TrendingUp, Search, Filter, ChevronDown, ArrowUpRight, ArrowDownRight, Briefcase, Building, ShoppingCart, User, Wrench, Store, Plus } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import Link from 'next/link';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import DashboardHeader from './DashboardHeader';
import ClientFormModal from './modals/ClientFormModal';

// Types
interface Client {
  id: string;
  name: string;
  category_id: string;
  category_name?: string;
  client_type: 'credit' | 'cash';
  phone_number: string;
  email: string | null;
  can_receive_credit: boolean;
  is_active: boolean;
  created_at: string;
}

interface ClientCategory {
  id: string;
  name: string;
  description: string | null;
}

interface ClientStats {
  totalClients: number;
  activeClients: number;
  creditClients: number;
  cashClients: number;
  categoryBreakdown: {
    [key: string]: number;
  };
  newClientsThisMonth: number;
}

// Stats Card Component
interface StatsCardProps {
  title: string;
  value: string | number;
  change?: number;
  icon: React.ReactNode;
  color: 'teal' | 'orange' | 'gray' | 'blue';
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, change, icon, color }) => {
  const colorClasses = {
    teal: 'bg-teal-50 border-teal-200',
    orange: 'bg-orange-50 border-orange-200',
    gray: 'bg-gray-50 border-gray-200',
    blue: 'bg-blue-50 border-blue-200',
  };

  const iconColorClasses = {
    teal: 'text-teal-600',
    orange: 'text-orange-600',
    gray: 'text-gray-600',
    blue: 'text-blue-600',
  };

  const changeColor = change && change > 0 ? 'text-green-600' : 'text-red-600';
  const ChangeIcon = change && change > 0 ? ArrowUpRight : ArrowDownRight;

  return (
    <div className={`p-4 md:p-6 rounded-lg shadow-sm border ${colorClasses[color]} flex flex-col justify-between`}>
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-medium text-gray-500">{title}</h3>
        <div className={`p-2 rounded-full bg-white ${iconColorClasses[color]}`}>
          {icon}
        </div>
      </div>
      <p className="text-2xl md:text-3xl font-semibold text-gray-800 mb-1">{value}</p>
      {change !== undefined && (
        <div className={`flex items-center text-xs ${changeColor}`}>
          <ChangeIcon className="w-3 h-3 mr-1" />
          <span>{Math.abs(change)}% vs last period</span>
        </div>
      )}
    </div>
  );
};

// Client List Card Component
interface ClientListCardProps {
  title: string;
  clients: Client[];
  categories: ClientCategory[];
  isLoading: boolean;
  sortBy?: 'created_at' | 'client_type';
  filterBy?: {
    category?: string;
    type?: 'credit' | 'cash';
  };
}

const ClientListCard: React.FC<ClientListCardProps> = ({
  title,
  clients,
  categories,
  isLoading,
  sortBy,
  filterBy
}) => {
  // Filter clients if needed
  let filteredClients = [...clients];

  if (filterBy?.category) {
    filteredClients = filteredClients.filter(client =>
      client.category_name === filterBy.category
    );
  }

  if (filterBy?.type) {
    filteredClients = filteredClients.filter(client =>
      client.client_type === filterBy.type
    );
  }

  // Sort clients if needed
  if (sortBy === 'created_at') {
    filteredClients.sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  }

  // Limit to top 5 for display
  const displayClients = filteredClients.slice(0, 5);

  // Get category icon
  const getCategoryIcon = (categoryName: string) => {
    switch(categoryName) {
      case 'Individual':
        return <User className="w-3 h-3" />;
      case 'Garage':
        return <Wrench className="w-3 h-3" />;
      case 'Shop':
        return <Store className="w-3 h-3" />;
      case 'Broker':
        return <Briefcase className="w-3 h-3" />;
      default:
        return <Building className="w-3 h-3" />;
    }
  };

  return (
    <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">{title}</h3>

      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <LoadingSpinner size={16} />
        </div>
      ) : displayClients.length > 0 ? (
        <div className="space-y-3 overflow-y-auto flex-grow">
          {displayClients.map((client) => (
            <div
              key={client.id}
              className="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors duration-150"
            >
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600">
                  {client.name?.charAt(0) || '?'}
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">{client.name || 'Unnamed Client'}</p>
                  <p className="text-xs text-gray-500 flex items-center">
                    {client.category_name && getCategoryIcon(client.category_name)}
                    <span className="ml-1">{client.category_name || 'Unknown Category'}</span>
                  </p>
                </div>
              </div>
              <span className={`text-xs px-2 py-1 rounded-full ${
                client.client_type === 'credit'
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-green-100 text-green-800'
              }`}>
                {client.client_type === 'credit' ? 'Credit' : 'Cash'}
              </span>
            </div>
          ))}
        </div>
      ) : (
        <div className="flex justify-center items-center py-8 text-gray-500">
          No clients found
        </div>
      )}

      <Link href="/clients/list" className="mt-4 text-sm text-teal-600 hover:text-teal-800 font-medium self-start">
        View All
      </Link>
    </div>
  );
};

// Category Distribution Card
interface CategoryDistributionCardProps {
  categories: ClientCategory[];
  categoryStats: {[key: string]: number};
  isLoading: boolean;
}

const CategoryDistributionCard: React.FC<CategoryDistributionCardProps> = ({
  categories,
  categoryStats,
  isLoading
}) => {
  // Get category icon
  const getCategoryIcon = (categoryName: string) => {
    switch(categoryName) {
      case 'Individual':
        return <User className="w-4 h-4" />;
      case 'Garage':
        return <Wrench className="w-4 h-4" />;
      case 'Shop':
        return <Store className="w-4 h-4" />;
      case 'Broker':
        return <Briefcase className="w-4 h-4" />;
      default:
        return <Building className="w-4 h-4" />;
    }
  };

  // Calculate total for percentages
  const total = Object.values(categoryStats).reduce((sum, count) => sum + count, 0);

  return (
    <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm border border-gray-200 h-full">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Client Categories</h3>

      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <LoadingSpinner size={16} />
        </div>
      ) : categories.length > 0 ? (
        <div className="space-y-4">
          {categories.map((category) => {
            const count = categoryStats[category.name] || 0;
            const percentage = total > 0 ? Math.round((count / total) * 100) : 0;

            return (
              <div key={category.id} className="space-y-1">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <div className="mr-2 text-gray-600">
                      {getCategoryIcon(category.name)}
                    </div>
                    <span className="text-sm font-medium">{category.name}</span>
                  </div>
                  <span className="text-sm text-gray-500">{count} ({percentage}%)</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-teal-600 h-2 rounded-full"
                    style={{ width: `${percentage}%` }}
                  ></div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="flex justify-center items-center py-8 text-gray-500">
          No categories found
        </div>
      )}
    </div>
  );
};

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5
    }
  },
  hover: {
    y: -5,
    boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
    transition: {
      duration: 0.2
    }
  }
};

// Main Dashboard Component
const ClientDashboardReal: React.FC = () => {
  const [clients, setClients] = useState<Client[]>([]);
  const [categories, setCategories] = useState<ClientCategory[]>([]);
  const [stats, setStats] = useState<ClientStats>({
    totalClients: 0,
    activeClients: 0,
    creditClients: 0,
    cashClients: 0,
    categoryBreakdown: {},
    newClientsThisMonth: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // Handle refresh
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const supabase = createClient();

        // Fetch categories
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('client_categories')
          .select('*');

        if (categoriesError) throw new Error(categoriesError.message);

        // Fetch clients with their category names
        const { data: clientsData, error: clientsError } = await supabase
          .from('clients')
          .select(`
            *,
            client_categories(name)
          `);

        if (clientsError) throw new Error(clientsError.message);

        // Process the data
        const processedClients = clientsData.map(client => ({
          ...client,
          category_name: client.client_categories?.name
        }));

        // Calculate stats
        const now = new Date();
        const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        const statsData: ClientStats = {
          totalClients: processedClients.length,
          activeClients: processedClients.filter(c => c.is_active).length,
          creditClients: processedClients.filter(c => c.client_type === 'credit').length,
          cashClients: processedClients.filter(c => c.client_type === 'cash').length,
          categoryBreakdown: {},
          newClientsThisMonth: processedClients.filter(c =>
            new Date(c.created_at) >= firstDayOfMonth
          ).length
        };

        // Calculate category breakdown
        categoriesData.forEach(category => {
          statsData.categoryBreakdown[category.name] = processedClients.filter(
            client => client.category_id === category.id
          ).length;
        });

        setCategories(categoriesData);
        setClients(processedClients);
        setStats(statsData);
      } catch (err) {
        console.error('Error fetching client data:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [refreshTrigger]);

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader title="Client Management" />

      <div className="container mx-auto px-4 py-8">
        {/* Action Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="flex flex-col md:flex-row justify-between items-center mb-6"
        >
          <div className="mb-4 md:mb-0">
            <h2 className="text-xl font-semibold text-gray-800">Client Overview</h2>
            <p className="text-gray-600">Manage and monitor your client relationships</p>
          </div>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 transition-colors flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" /> Add New Client
          </button>
        </motion.div>

      {error && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6"
        >
          <p>{error}</p>
        </motion.div>
      )}

      {/* Stats Cards */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6"
      >
        <motion.div variants={itemVariants}>
          <StatsCard
            title="Total Clients"
            value={isLoading ? '-' : stats.totalClients}
            icon={<Users className="w-5 h-5" />}
            color="teal"
          />
        </motion.div>
        <motion.div variants={itemVariants}>
          <StatsCard
            title="Credit Clients"
            value={isLoading ? '-' : stats.creditClients}
            icon={<Briefcase className="w-5 h-5" />}
            color="blue"
          />
        </motion.div>
        <motion.div variants={itemVariants}>
          <StatsCard
            title="Cash Clients"
            value={isLoading ? '-' : stats.cashClients}
            icon={<ShoppingCart className="w-5 h-5" />}
            color="orange"
          />
        </motion.div>
        <motion.div variants={itemVariants}>
          <StatsCard
            title="New This Month"
            value={isLoading ? '-' : stats.newClientsThisMonth}
            change={10} // Example change percentage
            icon={<UserPlus className="w-5 h-5" />}
            color="gray"
          />
        </motion.div>
      </motion.div>

      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="bg-white rounded-lg shadow-md p-6 mb-8"
      >
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Client Information</h2>
        <p className="text-gray-600 mb-4">
          View and manage your clients. Use the dashboard to monitor client statistics and relationships.
        </p>

        <div className="mt-6">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner size={16} />
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
              <motion.div
                variants={itemVariants}
                initial="hidden"
                animate="visible"
                whileHover="hover"
              >
                <ClientListCard
                  title="Recent Clients"
                  clients={clients}
                  categories={categories}
                  isLoading={isLoading}
                  sortBy="created_at"
                />
              </motion.div>
              <motion.div
                variants={itemVariants}
                initial="hidden"
                animate="visible"
                whileHover="hover"
                transition={{ delay: 0.1 }}
              >
                <ClientListCard
                  title="Credit Clients"
                  clients={clients}
                  categories={categories}
                  isLoading={isLoading}
                  filterBy={{ type: 'credit' }}
                />
              </motion.div>
              <motion.div
                variants={itemVariants}
                initial="hidden"
                animate="visible"
                whileHover="hover"
                transition={{ delay: 0.2 }}
              >
                <CategoryDistributionCard
                  categories={categories}
                  categoryStats={stats.categoryBreakdown}
                  isLoading={isLoading}
                />
              </motion.div>
            </div>
          )}
        </div>
      </motion.div>

      {/* Quick Links */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6"
      >
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate="visible"
          whileHover="hover"
          className="bg-white p-6 rounded-lg shadow-md border border-gray-200"
        >
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Manage Clients</h3>
          <p className="text-gray-600 mb-4">View and manage all your clients in one place.</p>
          <Link
            href="/clients/list"
            className="inline-flex items-center text-teal-600 hover:text-teal-800"
          >
            View All Clients <ArrowUpRight className="w-4 h-4 ml-1" />
          </Link>
        </motion.div>

        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate="visible"
          whileHover="hover"
          transition={{ delay: 0.1 }}
          className="bg-white p-6 rounded-lg shadow-md border border-gray-200"
        >
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Client Reports</h3>
          <p className="text-gray-600 mb-4">Generate detailed reports and analytics about your clients.</p>
          <Link
            href="/clients/reports"
            className="inline-flex items-center text-teal-600 hover:text-teal-800"
          >
            View Reports <ArrowUpRight className="w-4 h-4 ml-1" />
          </Link>
        </motion.div>

        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate="visible"
          whileHover="hover"
          transition={{ delay: 0.2 }}
          className="bg-white p-6 rounded-lg shadow-md border border-gray-200"
        >
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Add New Client</h3>
          <p className="text-gray-600 mb-4">Create a new client record in the system.</p>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="inline-flex items-center text-teal-600 hover:text-teal-800"
          >
            Add Client <ArrowUpRight className="w-4 h-4 ml-1" />
          </button>
        </motion.div>
      </motion.div>
      </div>

      {/* Client Form Modal */}
      <ClientFormModal
        isOpen={isAddModalOpen}
        onClose={() => {
          setIsAddModalOpen(false);
          handleRefresh();
        }}
        title="Add New Client"
      />
    </div>
  );
};

export default ClientDashboardReal;
