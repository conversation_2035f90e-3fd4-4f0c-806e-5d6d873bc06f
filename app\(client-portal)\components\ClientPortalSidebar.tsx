'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  Home, 
  Car, 
  ShoppingCart, 
  User, 
  Settings, 
  HelpCircle 
} from 'lucide-react';
import { useClientAuth } from './ClientAuthContext';

const ClientPortalSidebar: React.FC = () => {
  const pathname = usePathname();
  const { isLoading, isClient } = useClientAuth();
  
  // Don't render sidebar if loading or not a client
  if (isLoading || !isClient) {
    return null;
  }
  
  const menuItems = [
    { href: '/client', label: 'Dashboard', icon: <Home className="w-5 h-5" /> },
    { href: '/client/cars', label: 'My Cars', icon: <Car className="w-5 h-5" /> },
    { href: '/client/orders', label: 'Orders', icon: <ShoppingCart className="w-5 h-5" /> },
    { href: '/client/profile', label: 'Profile', icon: <User className="w-5 h-5" /> },
    { href: '/client/settings', label: 'Settings', icon: <Settings className="w-5 h-5" /> },
    { href: '/client/help', label: 'Help', icon: <HelpCircle className="w-5 h-5" /> },
  ];
  
  return (
    <aside className="hidden md:block w-64 bg-white border-r border-gray-200">
      <div className="h-full px-3 py-4 overflow-y-auto">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const isActive = pathname === item.href;
            
            return (
              <li key={item.href}>
                <Link
                  href={item.href}
                  className={`flex items-center p-2 rounded-lg ${
                    isActive 
                      ? 'bg-teal-100 text-teal-700' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <div className={isActive ? 'text-teal-600' : 'text-gray-500'}>
                    {item.icon}
                  </div>
                  <span className="ml-3">{item.label}</span>
                </Link>
              </li>
            );
          })}
        </ul>
      </div>
    </aside>
  );
};

export default ClientPortalSidebar;
