import React, { useState, useCallback } from 'react';
import { Control, Controller } from 'react-hook-form';

interface CurrencyInputProps {
  name: string;
  control?: Control<any>;
  currencySymbol?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  label?: string;
  errorMessage?: string | undefined;
}

const CurrencyInput: React.FC<CurrencyInputProps> = ({
  name,
  control,
  currencySymbol = 'KES',
  placeholder = 'Amount',
  value: initialValue = '',
  onChange,
  label = placeholder,
  errorMessage = undefined,
}) => {
  const [inputValue, setInputValue] = useState(initialValue);
  const [isFocused, setIsFocused] = useState(false);

  const formatNumber = useCallback((number: string): string => {
    // Remove all non-numeric characters except decimal point
    const cleanValue = number.replace(/[^0-9.]/g, '');
    
    // Handle empty input
    if (!cleanValue) return '';
    
    // Split into whole and decimal parts
    const [whole, decimal] = cleanValue.split('.');
    
    // Format whole number part with commas
    const formattedWhole = whole.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    
    // If there's a decimal part, add it back
    if (decimal) {
      // Limit decimal places to 2
      const limitedDecimal = decimal.slice(0, 2);
      return `${formattedWhole}.${limitedDecimal}`;
    }
    
    return formattedWhole;
  }, []);

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const rawValue = e.target.value;
      const formattedValue = formatNumber(rawValue);
      setInputValue(formattedValue);
      onChange?.(formattedValue);
    },
    [formatNumber, onChange]
  );

  const inputClassName = `peer h-12 pl-11 pr-4 font-medium text-gray-900 placeholder-transparent border rounded-md focus:ring-2 focus:outline-none w-full ${
    errorMessage
      ? 'border-red-500 focus:border-red-500 focus:ring-red-200'
      : 'border-gray-300 focus:border-blue-500 focus:ring-blue-200'
  } ${isFocused || control ? 'pt-6 pb-2' : 'py-3'}`;

  const labelClassName = `absolute left-2 top-1 pt-1 -translate-y-2 transform origin-[0] scale-75 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:-translate-y-2 peer-focus:scale-75 motion-reduce:transition-none transition-all duration-200 ease-out ${
    errorMessage ? 'text-red-500' : 'text-gray-500'
  }`;

  if (control) {
    return (
      <Controller
        control={control}
        name={name}
        render={({ field }) => (
          <div className="relative w-full">
            <div className="absolute inset-y-0 pt-5 pb-2 left-0 flex items-center pointer-events-none pl-2 text-gray-500">
              {currencySymbol}
            </div>
            <input
              {...field}
              type="text"
              className={inputClassName}
              placeholder={placeholder}
              value={field.value ? formatNumber(field.value.toString()) : ''}
              onChange={(e) => {
                const formattedValue = formatNumber(e.target.value);
                // Convert the formatted string to a number before passing to the form
                const numericValue = formattedValue ? parseFloat(formattedValue.replace(/,/g, '')) : 0;
                field.onChange(numericValue);
              }}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              id={`currency-input-${name}`}
            />
            <label htmlFor={`currency-input-${name}`} className={labelClassName}>
              {label}
            </label>
            {errorMessage && <p className="mt-1 text-sm text-red-500">{errorMessage}</p>}
          </div>
        )}
      />
    );
  }

  return (
    <div className="relative w-full">
      <div className="absolute inset-y-0 pt-5 pb-2 left-0 flex items-center pointer-events-none pl-2 text-gray-500">
        {currencySymbol}
      </div>
      <input
        type="text"
        name={name}
        className={inputClassName}
        placeholder={placeholder}
        value={inputValue}
        onChange={handleInputChange}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        id={`currency-input-${name}`}
      />
      <label htmlFor={`currency-input-${name}`} className={labelClassName}>
        {label}
      </label>
      {errorMessage && <p className="mt-1 text-sm text-red-500">{errorMessage}</p>}
    </div>
  );
};

export default CurrencyInput;