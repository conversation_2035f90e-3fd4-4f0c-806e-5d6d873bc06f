// import { gql } from '@apollo/client';
const gql = (strings: TemplateStringsArray, ...values: any[]) => {
  return strings.reduce((result, string, i) => {
    return result + string + (values[i] || '');
  }, '');
};

export const typeDefs = gql`
  type Part {
    id: ID!
    title: String!
    partnumber: String!
    price: Float!
    discountedPrice: Float
    stock: Int!
    description: String
    category: String!
    images: [String!]!
    attributes: [PartAttribute!]!
    compatibleCars: [CompatibleCar!]!
    similarParts: [SimilarPart!]!
  }

  type PartAttribute {
    name: String!
    icon: String!
    value: String!
  }

  type CompatibleCar {
    id: ID!
    brand: String!
    model: String!
    year: String!
    trim: String
  }

  type SimilarPart {
    id: ID!
    name: String!
    partNumber: String!
    price: Float!
    imageSrc: String!
    attributes: [PartAttribute!]!
  }

  type Query {
    part(id: ID!): Part
    parts(
      category: String
      search: String
      minPrice: Float
      maxPrice: Float
      inStock: Boolean
      page: Int
      limit: Int
      sortBy: String
      sortOrder: String
    ): [Part!]!
    categories: [String!]!
  }
`;

export const resolvers = {
  Query: {
    part: async (_: any, { id }: { id: string }, { dataSources }: any) => {
      return dataSources.partsAPI.getPart(id);
    },
    parts: async (_: any, args: any, { dataSources }: any) => {
      return dataSources.partsAPI.getParts(args);
    },
    categories: async (_: any, __: any, { dataSources }: any) => {
      return dataSources.partsAPI.getCategories();
    },
  },
};