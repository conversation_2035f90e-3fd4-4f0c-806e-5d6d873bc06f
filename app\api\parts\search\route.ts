import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/app/libs/supabase/server';
import { getAdjustedPrice } from '@/app/utils/priceAdjustment';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '12', 10);
    const sort = searchParams.get('sort') || '';
    const filter = searchParams.get('filter') || '';
    const offset = (page - 1) * limit;

    console.log('Searching parts with params:', { query, page, limit, offset, sort, filter });

    // Create a Supabase client for API route
    const response = NextResponse.next();
    const supabase = createRouteHandlerClient({
      request,
      response
    });

    // If query is empty, return all parts (or filtered by category)
    if (!query.trim()) {
      console.log('Fetching all parts...');

      // Parse the filter parameter for category
      let categoryId: number | undefined;
      if (filter && filter.startsWith('category:')) {
        categoryId = parseInt(filter.split(':')[1], 10);
        console.log('Filtering by category ID:', categoryId);
      }

      // Build the query
      let partsQuery = supabase
        .from('parts')
        .select('*', { count: 'exact' })
        .order(sort === 'newest' ? 'createdAt' : 'id', { ascending: false });

      // Add category filter if provided
      if (categoryId) {
        // Use our new function to get parts from the category and all its children
        const { data: categoryParts, error: categoryError } = await supabase
          .rpc('get_category_parts_with_children', {
            p_category_id: categoryId,
            p_sort_by: sort === 'price-low' ? 'price-low' :
                      sort === 'price-high' ? 'price-high' :
                      sort === 'newest' ? 'newest' : 'featured',
            p_limit: limit,
            p_offset: offset
          });

        if (categoryError) {
          console.error('Error using get_category_parts_with_children:', categoryError);
          return NextResponse.json(
            {
              error: `Failed to fetch category parts: ${categoryError.message}`,
              details: categoryError
            },
            { status: 500 }
          );
        }

        // If we have results from the category function, return them directly
        if (categoryParts && categoryParts.length > 0) {
          console.log(`Found ${categoryParts.length} parts in category ${categoryId} and its children`);

          // Transform the data to match our frontend model
          const transformedParts = categoryParts.map((part: any) => ({
            id: part.part_id.toString(),
            title: part.title || 'Unnamed Part',
            partNumber: part.part_number || 'N/A',
            actualPartNumber: part.part_number || 'N/A',
            price: part.price || 0,
            discountedPrice: part.discounted_price || null,
            stock: part.stock || 0,
            thumbnailUrl: part.image_url || '',
            imageUrl: part.image_url || '',
            category_id: part.category_id,
            category_name: part.category_name
          }));

          // Get the total count from the first result
          const totalParts = categoryParts.length > 0 ? categoryParts[0].total_count : 0;
          const totalPages = Math.ceil(totalParts / limit);

          return NextResponse.json({
            parts: transformedParts,
            totalParts,
            currentPage: page,
            totalPages
          });
        }

        // If no results from the function, fall back to the original query
        partsQuery = partsQuery.eq('category_id', categoryId);
      }

      // Add pagination
      partsQuery = partsQuery.range(offset, offset + limit - 1);

      // Execute the query
      const { data: parts, error, count } = await partsQuery;

      if (error) {
        console.error('Error fetching parts:', error);
        return NextResponse.json(
          {
            error: `Failed to fetch parts: ${error.message}`,
            details: error
          },
          { status: 500 }
        );
      }

      // Process the parts to get images, conditions, etc.
      const transformedParts = await processPartsData(supabase, parts || [], filter);

      const totalParts = count || 0;
      const totalPages = Math.ceil(totalParts / limit);

      console.log(`Calculated totalPages: ${totalPages} (totalParts: ${totalParts}, limit: ${limit})`);

      // Ensure totalPages is at least 1 if we have parts
      const finalTotalPages = transformedParts.length > 0 ? Math.max(1, totalPages) : 0;

      return NextResponse.json({
        parts: transformedParts,
        totalParts,
        currentPage: page,
        totalPages: finalTotalPages
      });
    }

    // Use the advanced_part_search function for search queries
    console.log('Using advanced_part_search function with query:', query);

    // Call the advanced_part_search function
    const { data: searchResults, error: searchError } = await supabase
      .rpc('advanced_part_search', {
        p_search_query: query,
        p_limit: limit,
        p_offset: offset
      });

    if (searchError) {
      console.error('Error using advanced_part_search:', searchError);
      return NextResponse.json(
        {
          error: `Failed to search parts: ${searchError.message}`,
          details: searchError
        },
        { status: 500 }
      );
    }

    console.log(`Found ${searchResults?.length || 0} parts with advanced search`);

    // Transform the search results to match our frontend model
    const transformedParts = await processSearchResults(supabase, searchResults || [], filter);

    // For search results, we don't have an exact count, so we estimate
    const totalParts = searchResults?.length || 0;
    const hasMorePages = searchResults?.length === limit;
    const totalPages = hasMorePages ? page + 1 : page;

    console.log(`Calculated totalPages for search: ${totalPages} (totalParts: ${totalParts}, hasMorePages: ${hasMorePages})`);

    // Ensure totalPages is at least 1 if we have parts, but 0 if no parts
    const finalTotalPages = transformedParts.length > 0 ? Math.max(1, totalPages) : 0;

    return NextResponse.json({
      parts: transformedParts,
      totalParts,
      currentPage: page,
      totalPages: finalTotalPages,
      isSearchResult: true
    });
  } catch (error) {
    console.error('Unexpected error in parts search API:', error);
    return NextResponse.json(
      {
        error: 'An unexpected error occurred while searching parts',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper function to process parts data
async function processPartsData(supabase: any, parts: any[], filter: string) {
  if (!parts || parts.length === 0) {
    return [];
  }

  // Parse the filter parameter
  let categoryId: number | undefined;
  let reorderFilter = false;

  if (filter) {
    // Check if it's a category filter
    if (filter.startsWith('category:')) {
      categoryId = parseInt(filter.split(':')[1], 10);
      console.log('Filtering by category ID:', categoryId);
    } else if (filter === 'reorder') {
      reorderFilter = true;
    }
  }

  // If we have a category filter, filter the parts
  if (categoryId) {
    parts = parts.filter(part => part.category_id === categoryId);
    console.log(`Filtered to ${parts.length} parts in category ${categoryId}`);

    // If no parts match the category, return empty array
    if (parts.length === 0) {
      return [];
    }
  }

  const partIds = parts.map(part => part.id);

  // Fetch part numbers
  const { data: partNumbers } = await supabase
    .from('part_compatibility_groups')
    .select('id, part_number')
    .in('id', parts.filter(p => p.partnumber_group).map(p => p.partnumber_group));

  // Create a map of part number IDs to part numbers
  const partNumberMap = (partNumbers || []).reduce((map: Record<number, string>, pn: any) => {
    map[pn.id] = pn.part_number;
    return map;
  }, {});

  // Fetch images
  const { data: images } = await supabase
    .from('part_images')
    .select('*')
    .in('part_id', partIds);

  // Group images by part_id
  const partImages = (images || []).reduce((acc: Record<number, any[]>, img: any) => {
    const partId = img.part_id;
    if (!acc[partId]) {
      acc[partId] = [];
    }
    acc[partId].push(img);
    return acc;
  }, {});

  // Fetch conditions and stock
  const { data: conditions } = await supabase
    .from('parts_condition')
    .select('*')
    .in('part_id', partIds);

  // Group conditions by part_id
  const partConditions = (conditions || []).reduce((acc: Record<number, any[]>, condition: any) => {
    const partId = condition.part_id;
    if (!acc[partId]) {
      acc[partId] = [];
    }
    acc[partId].push(condition);
    return acc;
  }, {});

  // Fetch prices for all conditions
  const conditionIds = conditions?.map((condition: any) => condition.id) || [];
  const { data: prices } = await supabase
    .from('part_price')
    .select('*')
    .in('condition_id', conditionIds);

  // Group prices by condition_id for easier lookup
  const conditionPrices = (prices || []).reduce((acc: Record<number, any>, price: any) => {
    acc[price.condition_id] = price;
    return acc;
  }, {});

  // Transform the data
  let transformedParts = parts.map((part: any) => {
    // Find images for this part
    const images = partImages[part.id] || [];
    // Find the main image or use the first one
    const mainImage = images.find((img: any) => img.is_main_image === true) || images[0];
    // Get conditions for this part
    const conditions = partConditions[part.id] || [];
    // Calculate total stock
    const totalStock = conditions.reduce((total: number, condition: any) => total + (condition.stock || 0), 0);

    // Check if any condition is at or below reorder level
    const needsReorder = conditions.some((condition: any) => {
      const reorderLevel = condition.reorder_level || 5;
      return condition.stock > 0 && condition.stock <= reorderLevel;
    });

    // Find price for this part (use the first condition with a price)
    let actualPrice = 0;
    let discountedPrice = null;

    for (const condition of conditions) {
      const price = conditionPrices[condition.id];
      if (price && price.price > 0) {
        actualPrice = price.price;
        discountedPrice = price.discounted_price;
        break;
      }
    }

    // Calculate the adjusted price for frontend display
    const adjustedPrice = getAdjustedPrice(actualPrice);
    const adjustedDiscountedPrice = discountedPrice ? getAdjustedPrice(discountedPrice) : null;

    return {
      id: part.id.toString(),
      title: part.title || 'Unnamed Part',
      partNumber: part.partnumber_group?.toString() || 'N/A',
      actualPartNumber: partNumberMap[part.partnumber_group] || 'N/A',
      price: adjustedPrice,
      discountedPrice: adjustedDiscountedPrice,
      actualPrice: actualPrice,
      stock: totalStock,
      needsReorder: needsReorder,
      conditions: conditions,
      thumbnailUrl: mainImage?.image_url || '',
      imageUrl: mainImage?.image_url || '',
      images: images.length > 0 ? images : undefined,
      userId: part.createdBy?.toString() || ''
    };
  });

  // Apply the reorder filter if needed
  if (reorderFilter) {
    transformedParts = transformedParts.filter(part => part.needsReorder);
  }

  return transformedParts;
}

// Helper function to process search results from advanced_part_search
async function processSearchResults(supabase: any, searchResults: any[], filter: string) {
  if (!searchResults || searchResults.length === 0) {
    return [];
  }

  // Parse the filter parameter
  let categoryId: number | undefined;
  let reorderFilter = false;

  if (filter) {
    // Check if it's a category filter
    if (filter.startsWith('category:')) {
      categoryId = parseInt(filter.split(':')[1], 10);
      console.log('Filtering search results by category ID:', categoryId);
    } else if (filter === 'reorder') {
      reorderFilter = true;
    }
  }

  // We need to fetch the parts to get their category_id
  const partIds = searchResults.map(result => result.part_id);

  // If we have a category filter, use our new function to get all child categories
  if (categoryId) {
    try {
      // First, get all child categories of the specified category
      const { data: childCategories } = await supabase
        .rpc('get_all_child_categories', {
          p_parent_id: categoryId
        });

      if (childCategories && childCategories.length > 0) {
        // Extract all category IDs (parent + children)
        const categoryIds = childCategories.map((cat: any) => cat.id);
        console.log(`Found ${categoryIds.length} categories in the tree for category ${categoryId}`);

        // Fetch the parts to get their category_id
        const { data: partsData } = await supabase
          .from('parts')
          .select('id, category_id')
          .in('id', partIds);

        // Create a map of part IDs to category IDs
        const partCategoryMap = (partsData || []).reduce((map: Record<number, number>, part: any) => {
          map[part.id] = part.category_id;
          return map;
        }, {});

        // Filter search results by category tree (include all child categories)
        searchResults = searchResults.filter(result =>
          categoryIds.includes(partCategoryMap[result.part_id])
        );

        console.log(`Filtered search results to ${searchResults.length} parts in category ${categoryId} and its children`);

        // If no parts match the category tree, return empty array
        if (searchResults.length === 0) {
          return [];
        }
      }
    } catch (error) {
      console.error('Error filtering search results by category tree:', error);
      // Fall back to exact category match if there's an error
      const { data: partsData } = await supabase
        .from('parts')
        .select('id, category_id')
        .in('id', partIds);

      // Create a map of part IDs to category IDs
      const partCategoryMap = (partsData || []).reduce((map: Record<number, number>, part: any) => {
        map[part.id] = part.category_id;
        return map;
      }, {});

      // Filter search results by exact category match
      searchResults = searchResults.filter(result => partCategoryMap[result.part_id] === categoryId);
      console.log(`Filtered search results to ${searchResults.length} parts in category ${categoryId}`);

      // If no parts match the category, return empty array
      if (searchResults.length === 0) {
        return [];
      }
    }
  }

  // Fetch conditions and stock
  const { data: conditions } = await supabase
    .from('parts_condition')
    .select('*')
    .in('part_id', partIds);

  // Group conditions by part_id
  const partConditions = (conditions || []).reduce((acc: Record<number, any[]>, condition: any) => {
    const partId = condition.part_id;
    if (!acc[partId]) {
      acc[partId] = [];
    }
    acc[partId].push(condition);
    return acc;
  }, {});

  // Transform the search results
  let transformedParts = searchResults.map((result: any) => {
    // Get conditions for this part
    const conditions = partConditions[result.part_id] || [];

    // Calculate total stock (use the stock from search result if conditions are not available)
    const totalStock = conditions.length > 0
      ? conditions.reduce((total: number, condition: any) => total + (condition.stock || 0), 0)
      : result.stock || 0;

    // Check if any condition is at or below reorder level
    const needsReorder = conditions.some((condition: any) => {
      const reorderLevel = condition.reorder_level || 5;
      return condition.stock > 0 && condition.stock <= reorderLevel;
    });

    // Get the actual price from the search result
    const actualPrice = result.price || 0;

    // Calculate the adjusted price for frontend display
    const adjustedPrice = getAdjustedPrice(actualPrice);

    return {
      id: result.part_id.toString(),
      title: result.title || 'Unnamed Part',
      partNumber: 'N/A', // We don't have this in the search results
      actualPartNumber: 'N/A', // We don't have this in the search results
      price: adjustedPrice, // Use the adjusted price for frontend display
      actualPrice: actualPrice, // Keep the actual price for reference
      stock: totalStock,
      needsReorder: needsReorder,
      conditions: conditions,
      thumbnailUrl: result.thumbnail_url || '',
      imageUrl: result.thumbnail_url || '',
      relevanceScore: result.relevance_score || 0,
      debugInfo: result.debug_info || ''
    };
  });

  // Apply the reorder filter if needed
  if (reorderFilter) {
    transformedParts = transformedParts.filter(part => part.needsReorder);
  }

  return transformedParts;
}
