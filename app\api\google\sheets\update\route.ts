import { NextRequest, NextResponse } from 'next/server';
import { googleSheetsService } from '@/app/services/googleSheets';

export async function POST(request: NextRequest) {
  try {
    console.log('=== Starting Spreadsheet Update Request ===');

    // Parse request body
    const body = await request.json();
    const { spreadsheetId, range, values } = body;

    if (!spreadsheetId) {
      console.error('No spreadsheet ID provided');
      return NextResponse.json(
        { error: 'Spreadsheet ID is required' },
        { status: 400 }
      );
    }

    if (!range) {
      console.error('No range provided');
      return NextResponse.json(
        { error: 'Range is required' },
        { status: 400 }
      );
    }

    if (!values || !Array.isArray(values)) {
      console.error('Invalid values provided');
      return NextResponse.json(
        { error: 'Values must be a 2D array' },
        { status: 400 }
      );
    }

    // Set spreadsheet ID
    googleSheetsService.setSpreadsheetId(spreadsheetId);

    // Update the range
    const result = await googleSheetsService.updateRange(range, values);

    return NextResponse.json({
      success: true,
      message: 'Spreadsheet updated successfully',
      result
    });
  } catch (error) {
    console.error('=== Spreadsheet Update Error ===');
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      status: error.status,
      response: error.response?.data
    });

    return NextResponse.json(
      {
        error: 'Failed to update spreadsheet',
        details: {
          message: error.message,
          code: error.code,
          status: error.status,
          response: error.response?.data
        }
      },
      { status: 500 }
    );
  }
} 