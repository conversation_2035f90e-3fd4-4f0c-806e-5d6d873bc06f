'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Save, FolderTree, Plus } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { CategoryWithChildren, CategoryFormData } from '../types';
import { useForm, SubmitHandler } from 'react-hook-form';
import BatchRenameModal from './BatchRenameModal';

interface AddSubcategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (newSubcategory: CategoryWithChildren) => void;
  parentCategory: CategoryWithChildren;
}

const AddSubcategoryModal: React.FC<AddSubcategoryModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  parentCategory
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showBatchRename, setShowBatchRename] = useState(false);
  const [newCategoryId, setNewCategoryId] = useState<number | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<CategoryFormData>({
    defaultValues: {
      label: '',
      href: '',
      icon: '',
      library: '',
      parent_category_id: parentCategory.id,
      isActive: true,
      requirePartNumber: true,
      isEnginePart: false,
      title_template: ''
    }
  });

  const supabase = createClient();

  const onSubmit: SubmitHandler<CategoryFormData> = async (data) => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Format the href if not provided
      if (!data.href) {
        data.href = `/parts/category/${data.label.toLowerCase().replace(/\s+/g, '-')}`;
      }

      const { data: insertedData, error: insertError } = await supabase
        .from('car_part_categories')
        .insert({
          label: data.label,
          href: data.href,
          icon: data.icon || null,
          library: data.library || null,
          parent_category_id: parentCategory.id,
          isActive: data.isActive,
          requirePartNumber: data.requirePartNumber,
          isEnginePart: data.isEnginePart,
          title_template: data.title_template || null
        })
        .select()
        .single();

      if (insertError) throw insertError;

      // Create the new subcategory object with children array
      const newSubcategory: CategoryWithChildren = {
        ...insertedData,
        children: []
      };

      // Check if title template was provided and show batch rename modal
      if (data.title_template && insertedData) {
        setNewCategoryId(insertedData.id);
        // Small delay to prevent flickering
        setTimeout(() => {
          setShowBatchRename(true);
        }, 100);
        return; // Don't close the modal yet
      }

      // Success
      reset();
      onSuccess(newSubcategory);
      onClose();
    } catch (err: any) {
      console.error('Error adding subcategory:', err);
      setError(err.message || 'Failed to add subcategory');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    setError(null);
    onClose();
  };

  return (
    <>
      <AnimatePresence>
      {isOpen && !showBatchRename && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2 }}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center">
                <div className="p-2 bg-teal-100 text-teal-600 rounded-md mr-3">
                  <Plus size={20} />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">Add Subcategory</h2>
                  <p className="text-sm text-gray-500">Add a new subcategory to "{parentCategory.label}"</p>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-4">
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
                  {error}
                </div>
              )}

              {/* Label */}
              <div>
                <label htmlFor="label" className="block text-sm font-medium text-gray-700 mb-1">
                  Category Name *
                </label>
                <input
                  type="text"
                  id="label"
                  {...register('label', { required: 'Category name is required' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  placeholder="Enter category name"
                />
                {errors.label && (
                  <p className="mt-1 text-sm text-red-600">{errors.label.message}</p>
                )}
              </div>

              {/* Href */}
              <div>
                <label htmlFor="href" className="block text-sm font-medium text-gray-700 mb-1">
                  URL Path
                </label>
                <input
                  type="text"
                  id="href"
                  {...register('href')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  placeholder="Auto-generated if empty"
                />
              </div>

              {/* Icon */}
              <div>
                <label htmlFor="icon" className="block text-sm font-medium text-gray-700 mb-1">
                  Icon Name
                </label>
                <input
                  type="text"
                  id="icon"
                  {...register('icon')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  placeholder="e.g., FolderTree"
                />
              </div>

              {/* Library */}
              <div>
                <label htmlFor="library" className="block text-sm font-medium text-gray-700 mb-1">
                  Icon Library
                </label>
                <input
                  type="text"
                  id="library"
                  {...register('library')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  placeholder="e.g., lucide-react"
                />
              </div>

              {/* Title Template */}
              <div>
                <label htmlFor="title_template" className="block text-sm font-medium text-gray-700 mb-1">
                  Title Template (optional)
                </label>
                <textarea
                  id="title_template"
                  {...register('title_template')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  placeholder="e.g., Volkswagen (VW) Audi {category_name} {part_number} {compatible_part_numbers} {compatible_vehicles}"
                  rows={3}
                />
                <p className="mt-1 text-xs text-gray-500">
                  Available placeholders: {'{brand_name}'}, {'{model_name}'}, {'{generation_name}'}, {'{variation}'}, {'{trim}'}, {'{category_name}'}, {'{part_number}'}, {'{compatible_part_numbers}'}, {'{compatible_vehicles}'}, {'{condition}'}, {'{attributes}'}
                </p>
              </div>

              {/* Checkboxes */}
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    {...register('isActive')}
                    className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                    Active
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="requirePartNumber"
                    {...register('requirePartNumber')}
                    className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                  />
                  <label htmlFor="requirePartNumber" className="ml-2 block text-sm text-gray-700">
                    Require Part Number
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isEnginePart"
                    {...register('isEnginePart')}
                    className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isEnginePart" className="ml-2 block text-sm text-gray-700">
                    Engine Part
                  </label>
                </div>
              </div>

              {/* Buttons */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={handleClose}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 text-sm font-medium text-white bg-teal-600 border border-transparent rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Adding...
                    </>
                  ) : (
                    <>
                      <Save size={16} className="mr-2" />
                      Add Subcategory
                    </>
                  )}
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AnimatePresence>

    {/* Batch Rename Modal - Outside AnimatePresence to avoid key conflicts */}
    {newCategoryId && (
      <BatchRenameModal
        isOpen={showBatchRename}
        onClose={() => setShowBatchRename(false)}
        categoryId={newCategoryId}
        categoryName={`${parentCategory.label} Subcategory`}
        onComplete={() => {
          setShowBatchRename(false);
          const newSubcategory: CategoryWithChildren = {
            id: newCategoryId,
            label: '',
            href: '',
            children: []
          };
          onSuccess(newSubcategory);
          handleClose();
        }}
      />
    )}
    </>
  );
};

export default AddSubcategoryModal;
