'use client'

import { useEffect } from 'react';
import { createClient } from '../libs/supabase/client';
import { useRouter } from 'next/navigation';
import { usePathname } from 'next/navigation';

type AuthCheckProps = {
    children: React.ReactNode;
};

export default function AuthCheck({ children }: AuthCheckProps) {
    const router = useRouter();
    const supabase = createClient();
    const pathname = usePathname();

    useEffect(() => {
        const checkAuth = async () => {
            const { data: { user } } = await supabase.auth.getUser();

            // Define public paths that don't require authentication
            const publicPaths = [
                '/',
                '/shop',
                // '/parts' is now protected and requires authentication
                '/register'
            ];

            // Check if current path is public or starts with a public path
            const isPublicPath = publicPaths.some(path =>
                pathname === path || pathname?.startsWith(path + '/')
            );

            // Special case for shop item details pages
            const isShopItemPage = pathname?.match(/^\/shop\/\d+$/);

            // Only redirect to login if not on a public path and not authenticated
            if (!user && !isPublicPath && !isShopItemPage) {
                router.push('/login');
            }
        };

        checkAuth();
    }, [router, supabase, pathname]);

    return <>{children}</>;
}