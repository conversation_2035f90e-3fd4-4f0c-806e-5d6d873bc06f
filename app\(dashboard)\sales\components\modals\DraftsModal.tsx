'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, FileText, Clock, Trash2, Edit3, Calendar } from 'lucide-react';
import { useSalesDrafts, SalesDraft } from '../../hooks/useSalesDrafts';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

interface DraftsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLoadDraft: (draft: SalesDraft) => void;
}

const DraftsModal: React.FC<DraftsModalProps> = ({ isOpen, onClose, onLoadDraft }) => {
  const { drafts, isLoading, deleteDraft } = useSalesDrafts();
  const [deletingId, setDeletingId] = useState<string | null>(null);

  // Simple relative time function
  const getRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days`;
    return `${Math.floor(diffInSeconds / 2592000)} months`;
  };

  const getTimeUntilExpiry = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((date.getTime() - now.getTime()) / 1000);

    if (diffInSeconds < 0) return 'expired';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours`;
    return `${Math.floor(diffInSeconds / 86400)} days`;
  };

  const handleLoadDraft = (draft: SalesDraft) => {
    onLoadDraft(draft);
    onClose();
  };

  const handleDeleteDraft = async (draftId: string, draftName: string) => {
    if (!confirm(`Are you sure you want to delete "${draftName}"?`)) {
      return;
    }

    setDeletingId(draftId);
    try {
      await deleteDraft(draftId);
    } catch (error) {
      console.error('Error deleting draft:', error);
    } finally {
      setDeletingId(null);
    }
  };

  const getStepName = (step: number) => {
    switch (step) {
      case 1: return 'Sale Type';
      case 2: return 'Client Selection';
      case 3: return 'Product Selection';
      case 4: return 'Summary';
      default: return `Step ${step}`;
    }
  };

  const getProductCount = (products: any[]) => {
    if (!products || !Array.isArray(products)) return 0;
    return products.length;
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[80vh] overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center">
              <FileText className="h-6 w-6 text-teal-600 mr-3" />
              <h2 className="text-xl font-semibold text-gray-900">Sales Drafts</h2>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(80vh-120px)]">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <LoadingSpinner size={32} />
                <span className="ml-3 text-gray-600">Loading drafts...</span>
              </div>
            ) : drafts.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No drafts found</h3>
                <p className="text-gray-600">
                  Start creating a sale and your progress will be automatically saved as a draft.
                </p>
              </div>
            ) : (
              <div className="grid gap-4">
                {drafts.map((draft) => (
                  <motion.div
                    key={draft.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-gray-200 rounded-lg p-4 hover:border-teal-300 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center mb-2">
                          <h3 className="text-lg font-medium text-gray-900 mr-3">
                            {draft.draft_name}
                          </h3>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800">
                            {getStepName(draft.current_step)}
                          </span>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                          <div className="flex items-center">
                            <FileText className="h-4 w-4 mr-2" />
                            <span>{getProductCount(draft.selected_products)} products</span>
                          </div>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-2" />
                            <span>Updated {getRelativeTime(draft.updated_at)} ago</span>
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2" />
                            <span>Expires in {getTimeUntilExpiry(draft.expires_at)}</span>
                          </div>
                        </div>

                        {/* Form data preview */}
                        {draft.form_data && Object.keys(draft.form_data).length > 0 && (
                          <div className="bg-gray-50 rounded-md p-3 mb-3">
                            <h4 className="text-sm font-medium text-gray-700 mb-2">Form Data:</h4>
                            <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
                              {draft.form_data.saleType && (
                                <div>Sale Type: <span className="font-medium">{draft.form_data.saleType}</span></div>
                              )}
                              {draft.form_data.cashPaymentMethod && (
                                <div>Payment: <span className="font-medium">{draft.form_data.cashPaymentMethod}</span></div>
                              )}
                              {draft.form_data.clientId && (
                                <div>Client: <span className="font-medium">Selected</span></div>
                              )}
                              {draft.form_data.oneOffClientName && (
                                <div>One-off Client: <span className="font-medium">{draft.form_data.oneOffClientName}</span></div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => handleLoadDraft(draft)}
                          className="flex items-center px-3 py-2 text-sm bg-teal-600 text-white rounded-md hover:bg-teal-700 transition-colors"
                        >
                          <Edit3 className="h-4 w-4 mr-1" />
                          Continue
                        </button>
                        <button
                          onClick={() => handleDeleteDraft(draft.id, draft.draft_name)}
                          disabled={deletingId === draft.id}
                          className="flex items-center px-3 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
                        >
                          {deletingId === draft.id ? (
                            <LoadingSpinner size={16} />
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex justify-end p-6 border-t border-gray-200 bg-gray-50">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Close
            </button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default DraftsModal;
