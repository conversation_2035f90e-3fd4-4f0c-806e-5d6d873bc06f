#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 AutoFlow Mobile App Setup');
console.log('============================\n');

// Check if we're in the right directory
const currentDir = process.cwd();
const mobileDir = path.join(currentDir, 'mobile');

if (!fs.existsSync(mobileDir)) {
  console.error('❌ Mobile directory not found!');
  console.log('Please run this script from the root of the AutoFlow project.');
  process.exit(1);
}

// Function to run commands
function runCommand(command, description) {
  console.log(`📦 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit', cwd: mobileDir });
    console.log(`✅ ${description} completed\n`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    return false;
  }
  return true;
}

// Function to check if command exists
function commandExists(command) {
  try {
    execSync(`which ${command}`, { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}

// Check prerequisites
console.log('🔍 Checking prerequisites...\n');

const prerequisites = [
  { command: 'node', name: 'Node.js', required: true },
  { command: 'npm', name: 'npm', required: true },
  { command: 'react-native', name: 'React Native CLI', required: false },
  { command: 'pod', name: 'CocoaPods (for iOS)', required: false },
];

let allPrerequisitesMet = true;

prerequisites.forEach(({ command, name, required }) => {
  if (commandExists(command)) {
    console.log(`✅ ${name} is installed`);
  } else {
    if (required) {
      console.log(`❌ ${name} is required but not installed`);
      allPrerequisitesMet = false;
    } else {
      console.log(`⚠️  ${name} is not installed (optional)`);
    }
  }
});

if (!allPrerequisitesMet) {
  console.log('\n❌ Please install the required prerequisites before continuing.');
  console.log('See SETUP.md for detailed installation instructions.');
  process.exit(1);
}

console.log('\n✅ Prerequisites check completed\n');

// Install React Native CLI if not present
if (!commandExists('react-native')) {
  console.log('📦 Installing React Native CLI globally...');
  try {
    execSync('npm install -g react-native-cli', { stdio: 'inherit' });
    console.log('✅ React Native CLI installed\n');
  } catch (error) {
    console.log('⚠️  Could not install React Native CLI globally. You may need to run with sudo or use a Node version manager.\n');
  }
}

// Install dependencies
if (!runCommand('npm install', 'Installing Node.js dependencies')) {
  process.exit(1);
}

// Install iOS dependencies if on macOS
if (process.platform === 'darwin' && commandExists('pod')) {
  console.log('📦 Installing iOS dependencies...');
  try {
    execSync('cd ios && pod install', { stdio: 'inherit', cwd: mobileDir });
    console.log('✅ iOS dependencies installed\n');
  } catch (error) {
    console.log('⚠️  iOS dependencies installation failed. You may need to run this manually:\n');
    console.log('   cd mobile/ios && pod install\n');
  }
} else if (process.platform === 'darwin') {
  console.log('⚠️  CocoaPods not found. iOS dependencies not installed.\n');
} else {
  console.log('ℹ️  Skipping iOS dependencies (not on macOS)\n');
}

// Create .env file if it doesn't exist
const envPath = path.join(mobileDir, '.env');
const envExamplePath = path.join(mobileDir, '.env.example');

if (!fs.existsSync(envPath)) {
  console.log('📝 Creating environment configuration...');
  
  // Read values from main .env.local if it exists
  const mainEnvPath = path.join(currentDir, '.env.local');
  let envContent = `# Supabase Configuration
SUPABASE_URL=https://excgraelqcvcdsnlvrtv.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV4Y2dyYWVscWN2Y2Rzbmx2cnR2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NjcyNjAsImV4cCI6MjA1NDU0MzI2MH0.vPmu8B_MO6Nfl5LSum9WBYbOjlG8HO5L7AormN48RAQ

# API Configuration
API_BASE_URL=https://autoflow.parts
API_BASE_URL_DEV=http://localhost:3000

# M-PESA Configuration
MPESA_CONSUMER_KEY=E99Tt2NWJD6lANVsiLVbzbwWCvveexzP8SxBjbjIinGaA8bI
MPESA_CONSUMER_SECRET=MmWemHeLMRIAguxoL9W2rAueAbbA16uu2OaxRpFakWJ3tHR9LV0A7FZ9cWfflWbdRQztNs6SyJF1PK0ActxzomAt
MPESA_SHORT_CODE=174379
MPESA_ENVIRONMENT=sandbox

# App Configuration
APP_NAME=AutoFlow
APP_VERSION=1.0.0
`;

  if (fs.existsSync(mainEnvPath)) {
    console.log('📖 Reading configuration from main .env.local...');
    // You could parse the main .env.local file here if needed
  }

  fs.writeFileSync(envPath, envContent);
  console.log('✅ Environment configuration created\n');
  console.log('⚠️  Please review and update mobile/.env with your specific configuration\n');
} else {
  console.log('✅ Environment configuration already exists\n');
}

// Final instructions
console.log('🎉 Setup completed successfully!\n');
console.log('Next steps:');
console.log('1. Review and update mobile/.env with your configuration');
console.log('2. Start the development server:');
console.log('   cd mobile && npm start');
console.log('3. In another terminal, run the app:');
console.log('   cd mobile && npm run android  # for Android');
console.log('   cd mobile && npm run ios      # for iOS');
console.log('\nFor detailed instructions, see mobile/SETUP.md');
console.log('For project overview, see mobile/README.md\n');

console.log('Happy coding! 🚀');
