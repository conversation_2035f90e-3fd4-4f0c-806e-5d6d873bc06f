'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Save, Edit } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { StorageArea, StorageAreaFormData } from '../../types';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

interface EditAreaModalProps {
  isOpen: boolean;
  onClose: () => void;
  area: StorageArea;
  onSuccess: (updatedItem?: any, itemType?: 'area' | 'unit', action?: 'add' | 'update' | 'delete') => void;
}

// Form validation schema
const areaSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  location_type: z.enum(['indoor', 'outdoor']),
  level: z.enum(['upstairs', 'downstairs', 'ground']),
  description: z.string().optional(),
});

const EditAreaModal: React.FC<EditAreaModalProps> = ({
  isOpen,
  onClose,
  area,
  onSuccess
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    setValue
  } = useForm<StorageAreaFormData>({
    resolver: zodResolver(areaSchema),
    defaultValues: {
      name: '',
      location_type: 'indoor',
      level: 'ground',
      description: ''
    }
  });

  // Initialize form with area data
  useEffect(() => {
    if (area) {
      setValue('name', area.name);
      setValue('location_type', area.location_type);
      setValue('level', area.level);
      setValue('description', area.description || '');
    }
  }, [area, setValue]);

  const supabase = createClient();

  const onSubmit: SubmitHandler<StorageAreaFormData> = async (data) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const { data: updatedData, error: updateError } = await supabase
        .from('storage_areas')
        .update({
          name: data.name,
          location_type: data.location_type,
          level: data.level,
          description: data.description || null,
          updated_at: new Date().toISOString()
        })
        .eq('area_id', area.area_id)
        .select('*')
        .single();

      if (updateError) throw updateError;

      // Success
      onSuccess(updatedData, 'area', 'update');
      onClose();
    } catch (err: any) {
      console.error('Error updating storage area:', err);
      setError(err.message || 'Failed to update storage area');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Animation variants
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3 } }
  };

  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    exit: {
      opacity: 0,
      y: 50,
      scale: 0.95,
      transition: { duration: 0.2 }
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          onClick={onClose}
        >
          <motion.div
            className="bg-white rounded-lg shadow-xl w-full max-w-md overflow-hidden"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <div className="flex items-center">
                <Edit className="w-6 h-6 text-teal-600 mr-3" />
                <h2 className="text-xl font-semibold text-gray-800">Edit Storage Area</h2>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {error && (
              <div className="px-6 pt-4 pb-0">
                <div className="p-3 bg-red-50 text-red-700 rounded-md text-sm">
                  {error}
                </div>
              </div>
            )}

            <div className="p-6">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Area Name *
                  </label>
                  <input
                    id="name"
                    type="text"
                    {...register('name')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    disabled={isSubmitting}
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="location_type" className="block text-sm font-medium text-gray-700 mb-1">
                    Location Type *
                  </label>
                  <select
                    id="location_type"
                    {...register('location_type')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    disabled={isSubmitting}
                  >
                    <option value="indoor">Indoor</option>
                    <option value="outdoor">Outdoor</option>
                  </select>
                  {errors.location_type && (
                    <p className="mt-1 text-sm text-red-600">{errors.location_type.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="level" className="block text-sm font-medium text-gray-700 mb-1">
                    Level *
                  </label>
                  <select
                    id="level"
                    {...register('level')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    disabled={isSubmitting}
                  >
                    <option value="ground">Ground Floor</option>
                    <option value="upstairs">Upstairs</option>
                    <option value="downstairs">Downstairs</option>
                  </select>
                  {errors.level && (
                    <p className="mt-1 text-sm text-red-600">{errors.level.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description (optional)
                  </label>
                  <textarea
                    id="description"
                    {...register('description')}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    disabled={isSubmitting}
                  />
                </div>

                <div className="mt-6 flex justify-end">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 mr-2"
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 flex items-center"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Save Changes
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default EditAreaModal;
