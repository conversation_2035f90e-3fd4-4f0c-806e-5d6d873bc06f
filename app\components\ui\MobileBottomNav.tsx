'use client';

import React, { useMemo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/app/utils/cn';
import { menuItems } from '@/app/config/menu';
import { useUserCookie } from '@/app/hooks/useUserCookie';
import { Loader2 } from 'lucide-react';

const MobileBottomNav: React.FC = () => {
  const pathname = usePathname();
  const { roleName, isLoading } = useUserCookie();

  // Filter menu items based on user role and exclude items with children
  const filteredMenuItems = useMemo(() => {
    if (!roleName) return [];

    return menuItems.filter(item => {
      // Skip items with children
      if (item.children && item.children.length > 0) return false;

      // If no roles specified, show to everyone
      if (!item.roles) return true;

      // Check if user's role is in the allowed roles for this item
      return item.roles.includes(roleName);
    });
  }, [roleName]);

  // Handle loading state
  if (isLoading) {
    return (
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 md:hidden h-16 flex justify-center items-center">
        <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
      </div>
    );
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 md:hidden">
      <nav className="flex justify-around">
        {filteredMenuItems.map((item) => (
          <Link
            key={item.id}
            href={item.href}
            className={`flex flex-col items-center justify-center p-2 text-gray-700 ${
              pathname === item.href ? 'text-cyan-500' : ''
            }`}
          >
            <item.icon className="h-6 w-6" />
            <span className="text-xs mt-1">{item.label}</span>
          </Link>
        ))}
      </nav>
    </div>
  );
};

export default MobileBottomNav;