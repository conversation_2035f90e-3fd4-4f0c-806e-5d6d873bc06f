import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  Snackbar,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { NativeStackScreenProps } from '@react-navigation/native-stack';

import { AuthStackParamList } from '@/navigation/AuthNavigator';
import { useAuth } from '@/store/auth';
import { theme } from '@/theme';
import { CONFIG } from '@/constants/config';

type Props = NativeStackScreenProps<AuthStackParamList, 'OTP'>;

export const OTPScreen: React.FC<Props> = ({ navigation, route }) => {
  const { email } = route.params;
  const [otp, setOtp] = useState('');
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const { completeLogin, isLoading, error, clearError } = useAuth();

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleVerifyOTP = async () => {
    if (!otp.trim() || otp.length !== 6) {
      showSnackbar('Please enter a valid 6-digit OTP');
      return;
    }

    try {
      clearError();
      await completeLogin(email, otp.trim());
      showSnackbar('Login successful!');
    } catch (err) {
      showSnackbar(error || 'OTP verification failed');
    }
  };

  const handleResendOTP = async () => {
    // Reset countdown
    setCountdown(60);
    setCanResend(false);
    
    // In a real app, you would call the resend OTP API here
    showSnackbar('New OTP sent to your email');
  };

  const showSnackbar = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <Text variant="headlineLarge" style={styles.title}>
              Verify Your Email
            </Text>
            <Text variant="bodyLarge" style={styles.subtitle}>
              We've sent a 6-digit code to
            </Text>
            <Text variant="bodyLarge" style={styles.email}>
              {email}
            </Text>
          </View>

          <Card style={styles.card}>
            <Card.Content style={styles.cardContent}>
              <TextInput
                label="Enter OTP"
                value={otp}
                onChangeText={(text) => setOtp(text.replace(/[^0-9]/g, '').slice(0, 6))}
                mode="outlined"
                keyboardType="numeric"
                maxLength={6}
                style={styles.input}
                disabled={isLoading}
                textAlign="center"
              />

              <Button
                mode="contained"
                onPress={handleVerifyOTP}
                loading={isLoading}
                disabled={isLoading || otp.length !== 6}
                style={styles.verifyButton}
                contentStyle={styles.buttonContent}
              >
                Verify OTP
              </Button>

              <View style={styles.resendContainer}>
                {!canResend ? (
                  <Text variant="bodyMedium" style={styles.countdownText}>
                    Resend OTP in {formatTime(countdown)}
                  </Text>
                ) : (
                  <Button
                    mode="text"
                    onPress={handleResendOTP}
                    disabled={isLoading}
                    textColor={theme.colors.primary}
                  >
                    Resend OTP
                  </Button>
                )}
              </View>
            </Card.Content>
          </Card>

          <View style={styles.footer}>
            <Button
              mode="text"
              onPress={() => navigation.goBack()}
              disabled={isLoading}
              textColor={theme.colors.primary}
            >
              Back to Login
            </Button>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={4000}
        action={{
          label: 'Dismiss',
          onPress: () => setSnackbarVisible(false),
        }}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    color: theme.colors.primary,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    color: theme.colors.onBackground,
    textAlign: 'center',
    marginBottom: 4,
  },
  email: {
    color: theme.colors.primary,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  card: {
    elevation: 4,
    marginBottom: 20,
  },
  cardContent: {
    padding: 20,
  },
  input: {
    marginBottom: 16,
    fontSize: 18,
    letterSpacing: 4,
  },
  verifyButton: {
    marginTop: 10,
    marginBottom: 16,
  },
  buttonContent: {
    paddingVertical: 8,
  },
  resendContainer: {
    alignItems: 'center',
    marginTop: 10,
  },
  countdownText: {
    color: theme.colors.onBackground,
  },
  footer: {
    alignItems: 'center',
    marginTop: 20,
  },
});
