'use client';

import React, { useState, useCallback, useEffect } from 'react';
import Modal from '@/app/components/ui/Modal';
import { createClient } from '@/app/libs/supabase/client';
import { Crop, PixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import dynamic from 'next/dynamic';
import { Trash2, AlertTriangle, Star, CheckCircle2, RotateCcw, RotateCw, FlipHorizontal, FlipVertical, MoreVertical } from 'lucide-react';
import Button from '@/app/components/ui/inputs/Button';
import Upload from '@/app/components/ui/inputs/Upload';
import AnimatedEllipsisLoader from '@/app/components/ui/AnimatedEllipsisLoader';
import { toast } from 'react-hot-toast';
import { deleteSupabaseImage } from '@/app/utils/deleteSupabaseImage';

const ReactCrop = dynamic(() => import('react-image-crop'), { ssr: false });

// Constants for image compression
const MAX_IMAGE_SIZE = 800; // max dimension (width or height) in pixels
const JPEG_QUALITY = 0.85; // quality setting for JPEG compression (0-1)

interface ManageImagesModalProps {
  isOpen: boolean;
  onClose: () => void;
  partId: string;
  partImages: Array<{ image_url: string; is_main_image?: boolean; id?: string }>;
  onImagesUpdated: () => void;
}

const ManageImagesModal: React.FC<ManageImagesModalProps> = ({
  isOpen,
  onClose,
  partId,
  partImages,
  onImagesUpdated,
}) => {
  const [images, setImages] = useState<Array<{ url: string; isMain: boolean; id?: string }>>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  // State for image replacement
  const [isReplacing, setIsReplacing] = useState(false);
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [croppedImageUrl, setCroppedImageUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState<number | null>(null);
  const [showRotateMenu, setShowRotateMenu] = useState<number | null>(null);
  const [crop, setCrop] = useState<Crop>({
    x: 0,
    y: 0,
    width: 100,
    height: 100,
    unit: '%',
  });
  const supabase = createClient();

  // Initialize images from partImages prop
  useEffect(() => {
    if (partImages && partImages.length > 0) {
      setImages(
        partImages.map((img) => ({
          url: img.image_url,
          isMain: img.is_main_image || false,
          id: img.id,
        }))
      );
    } else {
      setImages([]);
    }
  }, [partImages]);

  // Close rotate menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check if the click was on a rotate button (which has a rotate-btn class)
      const target = event.target as HTMLElement;
      const isRotateButton = target.closest('.rotate-btn');
      const isRotateMenu = target.closest('.rotate-menu');

      if (!isRotateButton && !isRotateMenu) {
        setShowRotateMenu(null);
      }
    };

    if (showRotateMenu !== null) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showRotateMenu]);

  // Helper function to compress image
  const compressImage = useCallback((sourceCanvas: HTMLCanvasElement, quality: number = JPEG_QUALITY): string => {
    try {
      // Return compressed data URL
      return sourceCanvas.toDataURL('image/jpeg', quality);
    } catch (err) {
      console.error('Error compressing image:', err);
      // Fallback to uncompressed if compression fails
      return sourceCanvas.toDataURL('image/jpeg');
    }
  }, []);

  // Helper function to resize image while maintaining aspect ratio
  const resizeImage = useCallback((img: HTMLImageElement, maxSize: number = MAX_IMAGE_SIZE): HTMLCanvasElement => {
    const canvas = document.createElement('canvas');
    let width = img.width;
    let height = img.height;

    // Calculate new dimensions while maintaining aspect ratio
    if (width > height) {
      if (width > maxSize) {
        height = Math.round(height * (maxSize / width));
        width = maxSize;
      }
    } else {
      if (height > maxSize) {
        width = Math.round(width * (maxSize / height));
        height = maxSize;
      }
    }

    canvas.width = width;
    canvas.height = height;

    const ctx = canvas.getContext('2d');
    ctx?.drawImage(img, 0, 0, width, height);

    return canvas;
  }, []);

  const onImageLoaded = useCallback((image: HTMLImageElement) => {
    setCrop({
      x: 0,
      y: 0,
      width: 100,
      height: 100,
      unit: '%',
    });
  }, []);

  const onChange = useCallback(
    (newCrop: Crop) => {
      setCrop((currentCrop) => ({
        ...currentCrop,
        ...newCrop,
      }));
    },
    [setCrop]
  );

  const onCropComplete = useCallback(
    (crop: PixelCrop) => {
      if (imageSrc && crop.width && crop.height) {
        makeClientCrop(imageSrc, crop);
      }
    },
    [imageSrc]
  );

  const makeClientCrop = useCallback(async (mediaUrl: string, crop: any) => {
    const image = new Image();
    image.src = mediaUrl;

    image.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = crop.width;
      canvas.height = crop.height;

      ctx?.drawImage(image, crop.x, crop.y, crop.width, crop.height, 0, 0, crop.width, crop.height);

      // Compress the cropped image
      setCroppedImageUrl(compressImage(canvas));
    };
  }, [compressImage]);

  const handleFileAdded = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = () => {
      setImageSrc(reader.result as string);
    };
    reader.readAsDataURL(file);
  }, []);

  const handleSetMainImage = useCallback(async (imageUrl: string) => {
    setIsLoading(true);
    try {
      // Find the image in our local state
      const imageToUpdate = images.find(img => img.url === imageUrl);
      if (!imageToUpdate) {
        throw new Error('Image not found');
      }

      // Update in Supabase
      const supabase = createClient();

      // First, set all images to not main
      await supabase
        .from('part_images')
        .update({ is_main_image: false })
        .eq('part_id', partId);

      // Then set the selected image as main
      await supabase
        .from('part_images')
        .update({ is_main_image: true })
        .eq('part_id', partId)
        .eq('image_url', imageUrl);

      // Update local state
      setImages(prevImages =>
        prevImages.map(img => ({
          ...img,
          isMain: img.url === imageUrl
        }))
      );

      toast.success('Main image updated successfully');
    } catch (error) {
      console.error('Error setting main image:', error);
      toast.error('Failed to update main image');
    } finally {
      setIsLoading(false);
    }
  }, [images, partId]);

  const handleDeleteImage = useCallback(async (imageUrl: string) => {
    setIsLoading(true);
    try {
      // Find the image in our local state
      const imageToDelete = images.find(img => img.url === imageUrl);
      if (!imageToDelete) {
        throw new Error('Image not found');
      }

      // Delete from Supabase storage
      const deleteResult = await deleteSupabaseImage(imageUrl);
      if (!deleteResult.success) {
        console.warn('Warning: Image may not have been deleted from storage:', deleteResult.error);
      }

      // Delete from database
      const { error } = await supabase
        .from('part_images')
        .delete()
        .eq('part_id', partId)
        .eq('image_url', imageUrl);

      if (error) {
        throw error;
      }

      // Update local state
      setImages(prevImages => prevImages.filter(img => img.url !== imageUrl));

      // If we deleted the main image and there are other images, set the first one as main
      const wasMainImage = imageToDelete.isMain;
      const remainingImages = images.filter(img => img.url !== imageUrl);

      if (wasMainImage && remainingImages.length > 0) {
        await handleSetMainImage(remainingImages[0].url);
      }

      toast.success('Image deleted successfully');
      setShowDeleteConfirm(null);
      setShowRotateMenu(null);
      setCurrentImageIndex(null);

      // Notify parent component that images have been updated
      onImagesUpdated();
    } catch (error) {
      console.error('Error deleting image:', error);
      toast.error('Failed to delete image');
    } finally {
      setIsLoading(false);
    }
  }, [images, partId, handleSetMainImage, onImagesUpdated]);

  const uploadImage = useCallback(async (imageDataUrl: string, isMain: boolean = false) => {
    setIsUploading(true);
    try {
      if (!imageDataUrl) {
        throw new Error('No image data provided');
      }

      // Generate a unique filename
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 10);
      const filePath = `${timestamp}-${randomString}.jpg`;

      // Extract the base64 data
      const commaIndex = imageDataUrl.indexOf(',');
      if (commaIndex === -1) {
        throw new Error('Invalid image data format');
      }

      const base64Data = imageDataUrl.substring(commaIndex + 1);
      const buffer = Buffer.from(base64Data, 'base64');

      // Upload to Supabase Storage
      const { data, error } = await supabase
        .storage
        .from('car-part-images')
        .upload(filePath, buffer, {
          contentType: 'image/jpeg',
          upsert: true,
        });

      if (error) {
        throw error;
      }

      // Get the public URL
      const { data: publicUrlData } = supabase
        .storage
        .from('car-part-images')
        .getPublicUrl(filePath);

      const imageUrl = publicUrlData.publicUrl;

      // If this is a replacement and we're replacing the only image
      if (isReplacing && images.length === 1) {
        // Delete the old image first
        await handleDeleteImage(images[0].url);
      }

      // Create record in part_images table
      const { data: imageData, error: imageError } = await supabase
        .from('part_images')
        .insert({
          part_id: partId,
          image_url: imageUrl,
          is_main_image: isMain || images.length === 0, // Set as main if it's the first image
        })
        .select()
        .single();

      if (imageError) {
        throw imageError;
      }

      // Update local state
      if (isMain || images.length === 0) {
        // If this is the main image, update all other images to not be main
        setImages(prevImages => [
          ...prevImages.map(img => ({ ...img, isMain: false })),
          { url: imageUrl, isMain: true, id: imageData.id }
        ]);
      } else {
        setImages(prevImages => [
          ...prevImages,
          { url: imageUrl, isMain: false, id: imageData.id }
        ]);
      }

      toast.success('Image uploaded successfully');

      // Reset state
      setImageSrc(null);
      setCroppedImageUrl(null);
      setIsReplacing(false);

      // Notify parent component that images have been updated
      onImagesUpdated();

      return imageUrl;
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image');
      return null;
    } finally {
      setIsUploading(false);
    }
  }, [partId, images, isReplacing, handleDeleteImage, onImagesUpdated]);

  const handleUploadClick = useCallback(async () => {
    if (!croppedImageUrl && !imageSrc) return;

    let imageDataUrl: string | null = null;

    if (croppedImageUrl) {
      imageDataUrl = croppedImageUrl;
    } else if (imageSrc) {
      // Process the full image if not cropped
      const image = new Image();
      image.src = imageSrc;

      imageDataUrl = await new Promise<string>((resolve, reject) => {
        image.onload = () => {
          try {
            // First resize the image to manageable dimensions
            const resizedCanvas = resizeImage(image);
            // Then compress it with quality setting
            const compressedDataUrl = compressImage(resizedCanvas);
            resolve(compressedDataUrl);
          } catch (err) {
            console.error('Error processing image:', err);
            reject(new Error('Failed to process image'));
          }
        };
        image.onerror = () => reject(new Error('Failed to load image'));
      });
    }

    if (imageDataUrl) {
      // If this is a replacement and we have only one image
      if (isReplacing && images.length === 1) {
        // Show confirmation dialog before replacing
        if (confirm('Are you sure you want to replace the existing image? The current image will be permanently deleted.')) {
          await uploadImage(imageDataUrl, true);
        }
      } else {
        // Normal upload (add as new image)
        await uploadImage(imageDataUrl, images.length === 0);
      }
    }
  }, [croppedImageUrl, imageSrc, uploadImage, images.length, resizeImage, compressImage, isReplacing]);

  const handleCancelCrop = useCallback(() => {
    setImageSrc(null);
    setCroppedImageUrl(null);
  }, []);

  // Image manipulation functions
  const rotateImage = useCallback(async (imageUrl: string, degrees: number, flipType: 'none' | 'horizontal' | 'vertical' = 'none', index: number) => {
    setIsLoading(true);
    setCurrentImageIndex(index);
    try {
      // Load the image
      const image = new Image();
      image.crossOrigin = 'anonymous'; // Enable CORS for the image

      // Create a promise to wait for the image to load
      await new Promise<void>((resolve, reject) => {
        image.onload = () => resolve();
        image.onerror = () => reject(new Error('Failed to load image for rotation'));
        image.src = imageUrl;
      });

      // Create a canvas to draw the rotated image
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('Failed to get canvas context');
      }

      // Set canvas dimensions based on rotation
      if (degrees === 90 || degrees === 270) {
        canvas.width = image.height;
        canvas.height = image.width;
      } else {
        canvas.width = image.width;
        canvas.height = image.height;
      }

      // Translate and rotate the canvas context
      ctx.save();
      ctx.translate(canvas.width / 2, canvas.height / 2);
      ctx.rotate((degrees * Math.PI) / 180);

      // Apply flip if needed
      if (flipType === 'horizontal') {
        ctx.scale(-1, 1); // Horizontal flip
      } else if (flipType === 'vertical') {
        ctx.scale(1, -1); // Vertical flip
      }

      // Draw the image
      ctx.drawImage(
        image,
        -image.width / 2,
        -image.height / 2,
        image.width,
        image.height
      );
      ctx.restore();

      // Convert canvas to data URL
      const rotatedImageDataUrl = canvas.toDataURL('image/jpeg', JPEG_QUALITY);

      // Upload the rotated image
      const imageToUpdate = images.find(img => img.url === imageUrl);
      if (!imageToUpdate) {
        throw new Error('Image not found');
      }

      // Generate a unique filename
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 10);
      const filePath = `${timestamp}-${randomString}.jpg`;

      // Extract the base64 data
      const commaIndex = rotatedImageDataUrl.indexOf(',');
      if (commaIndex === -1) {
        throw new Error('Invalid image data format');
      }

      const base64Data = rotatedImageDataUrl.substring(commaIndex + 1);
      const buffer = Buffer.from(base64Data, 'base64');

      // Upload to Supabase Storage
      const { data, error } = await supabase
        .storage
        .from('car-part-images')
        .upload(filePath, buffer, {
          contentType: 'image/jpeg',
          upsert: true,
        });

      if (error) {
        throw error;
      }

      // Get the public URL
      const { data: publicUrlData } = supabase
        .storage
        .from('car-part-images')
        .getPublicUrl(filePath);

      const newImageUrl = publicUrlData.publicUrl;

      // Delete the old image from storage
      await deleteSupabaseImage(imageUrl);

      // Update the image record in the database
      const { error: updateError } = await supabase
        .from('part_images')
        .update({ image_url: newImageUrl })
        .eq('part_id', partId)
        .eq('image_url', imageUrl);

      if (updateError) {
        throw updateError;
      }

      // Update local state
      setImages(prevImages =>
        prevImages.map(img =>
          img.url === imageUrl ? { ...img, url: newImageUrl } : img
        )
      );

      toast.success('Image rotated successfully');
      setShowRotateMenu(null);
      setCurrentImageIndex(null);

      // Notify parent component that images have been updated
      onImagesUpdated();

      return newImageUrl;
    } catch (error) {
      console.error('Error rotating image:', error);
      toast.error('Failed to rotate image');
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [images, partId, deleteSupabaseImage, onImagesUpdated]);

  // Specific rotation functions
  const rotateLeft = useCallback((imageUrl: string, index: number) => rotateImage(imageUrl, 270, 'none', index), [rotateImage]);
  const rotateRight = useCallback((imageUrl: string, index: number) => rotateImage(imageUrl, 90, 'none', index), [rotateImage]);
  const rotate180 = useCallback((imageUrl: string, index: number) => rotateImage(imageUrl, 180, 'none', index), [rotateImage]);
  const flipHorizontal = useCallback((imageUrl: string, index: number) => rotateImage(imageUrl, 0, 'horizontal', index), [rotateImage]);
  const flipVertical = useCallback((imageUrl: string, index: number) => rotateImage(imageUrl, 0, 'vertical', index), [rotateImage]);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      width="w-full md:max-w-2xl"
      animationType="slide-in-bottom"
      header={
        <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold">Manage Part Images</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      }
    >
      <div className="p-6">
        {/* Current Images */}
        {images.length > 0 && (
          <div className="mb-6">
            <h3 className="text-sm font-medium mb-2">Current Images</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {images.map((image, index) => (
                <div key={index} className="relative group">
                  {isLoading && currentImageIndex === index && (
                    <div className="absolute inset-0 flex items-center justify-center z-10 bg-black bg-opacity-50 rounded-md">
                      <div className="text-white text-sm font-medium">
                        <AnimatedEllipsisLoader text="Processing" textColor="white" />
                      </div>
                    </div>
                  )}
                  <div className="aspect-square relative overflow-hidden rounded-md border border-gray-200">
                    <img src={image.url} alt={`Part image ${index + 1}`} className="object-cover w-full h-full" />
                    {image.isMain && (
                      <div className="absolute top-1 left-1 bg-yellow-400 text-white p-1 rounded-full">
                        <Star size={16} />
                      </div>
                    )}
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black bg-opacity-50 rounded-md">
                    {!image.isMain && (
                      <button
                        type="button"
                        className="p-1 bg-white rounded-full mr-2"
                        onClick={() => handleSetMainImage(image.url)}
                        title="Set as main image"
                      >
                        <Star size={18} className="text-yellow-500" />
                      </button>
                    )}
                    <button
                      type="button"
                      className="p-1 bg-white rounded-full mr-2 rotate-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowRotateMenu(index);
                      }}
                      title="Rotate image"
                      disabled={isLoading}
                    >
                      <RotateCw size={18} className="text-blue-500" />
                    </button>
                    <button
                      type="button"
                      className="p-1 bg-white rounded-full"
                      onClick={() => setShowDeleteConfirm(image.url)}
                      title="Delete image"
                      disabled={isLoading}
                    >
                      <Trash2 size={18} className="text-red-500" />
                    </button>

                    {/* Rotate submenu */}
                    {showRotateMenu === index && (
                      <div
                        className="absolute top-0 right-0 mt-10 bg-white rounded-md shadow-lg z-20 py-1 w-32 rotate-menu"
                        onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside menu
                      >
                        <button
                          className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center"
                          onClick={() => rotateLeft(image.url, index)}
                          disabled={isLoading}
                        >
                          <RotateCcw size={16} className="mr-2 text-blue-500" />
                          Rotate Left
                        </button>
                        <button
                          className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center"
                          onClick={() => rotateRight(image.url, index)}
                          disabled={isLoading}
                        >
                          <RotateCw size={16} className="mr-2 text-blue-500" />
                          Rotate Right
                        </button>
                        <button
                          className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center"
                          onClick={() => rotate180(image.url, index)}
                          disabled={isLoading}
                        >
                          <RotateCw size={16} className="mr-2 text-blue-500" />
                          Rotate 180°
                        </button>
                        <button
                          className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center"
                          onClick={() => flipHorizontal(image.url, index)}
                          disabled={isLoading}
                        >
                          <FlipHorizontal size={16} className="mr-2 text-blue-500" />
                          Flip Horizontal
                        </button>
                        <button
                          className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center"
                          onClick={() => flipVertical(image.url, index)}
                          disabled={isLoading}
                        >
                          <FlipVertical size={16} className="mr-2 text-blue-500" />
                          Flip Vertical
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
            <p className="text-sm text-gray-500 mt-2">
              {images.length >= 5
                ? 'Maximum of 5 images reached'
                : `You can upload ${5 - images.length} more image${5 - images.length !== 1 ? 's' : ''}`}
            </p>
          </div>
        )}

        {/* Upload New Image Section */}
        {!imageSrc && images.length < 5 && (
          <div className="border-2 border-dashed border-gray-300 rounded-md p-4">
            <h3 className="text-sm font-medium mb-2">Upload New Image</h3>
            {images.length === 0 ? (
              <p className="text-sm text-gray-500 mb-4">
                Upload an image for this part
              </p>
            ) : (
              <p className="text-sm text-gray-500 mb-4">
                Select an image to add to this part
              </p>
            )}

            <Upload
              onFileAdded={handleFileAdded}
              acceptedFileTypes={['image/*']}
              maxFiles={1}
              showPreview={true}
            />
          </div>
        )}

        {/* Cropping UI */}
        {imageSrc && (
          <div className="mt-4 space-y-4 border-2 border-dashed border-blue-300 rounded-md p-4">
            <h3 className="text-sm font-medium">Crop Image</h3>
            <ReactCrop
              crop={crop}
              onChange={onChange}
              onComplete={onCropComplete}
              minWidth={50}
              minHeight={50}
            >
              <img
                src={imageSrc}
                onLoad={(e) => onImageLoaded(e.currentTarget)}
                alt="Part to crop"
                className="max-h-96 object-contain"
              />
            </ReactCrop>

            <div className="flex flex-col space-y-3">
              {/* Add Replace option if there's only one image */}
              {images.length === 1 && (
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="replace-image"
                    checked={isReplacing}
                    onChange={(e) => setIsReplacing(e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="replace-image" className="text-sm">
                    Replace existing image instead of adding a new one
                  </label>
                </div>
              )}

              <div className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancelCrop}
                  className="px-3 py-2"
                  disabled={isUploading}
                >
                  Cancel
                </Button>

                <Button
                  type="button"
                  onClick={handleUploadClick}
                  className="px-3 py-2"
                  disabled={isUploading}
                >
                  {isUploading ? (
                    <AnimatedEllipsisLoader text="Uploading" textColor="white" />
                  ) : (
                    isReplacing && images.length === 1 ? 'Replace Image' : 'Upload Image'
                  )}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Dialog */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <div className="flex items-center mb-4 text-red-500">
                <AlertTriangle className="mr-2" size={24} />
                <h3 className="text-lg font-semibold">Delete Image</h3>
              </div>
              <p className="mb-4">
                Are you sure you want to delete this image? This action cannot be undone.
              </p>
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowDeleteConfirm(null)}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  variant="danger"
                  onClick={() => showDeleteConfirm && handleDeleteImage(showDeleteConfirm)}
                  disabled={isLoading}
                >
                  {isLoading ? <AnimatedEllipsisLoader text="Deleting" textColor="white" /> : 'Delete'}
                </Button>
              </div>
            </div>
          </div>
        )}


      </div>
    </Modal>
  );
};

export default ManageImagesModal;
