import React from 'react';
import { Control, FieldErrors, useWatch } from 'react-hook-form';
import { PartFormValues } from '../../types';
import Label from '@/app/components/ui/inputs/Label';
import Input from '@/app/components/ui/inputs/Input';
import ButtonedInput from '@/app/components/ui/inputs/ButtonedInput';

interface PartNumberSectionProps {
  control: Control<PartFormValues>;
  errors: FieldErrors<PartFormValues>;
  isChecking: boolean;
  currentTask: string;
  onCheck: () => void;
  children?: React.ReactNode;
  requirePartNumber: boolean;
  setValue?: any; // Use any type to avoid type conflicts
  partNumberExists?: boolean;
  existingPartDetails?: {
    groupId?: number;
    table?: string;
  } | null;
}

export default function PartNumberSection({
  control,
  errors,
  isChecking,
  currentTask,
  onCheck,
  children,
  requirePartNumber,
  setValue,
  partNumberExists,
  existingPartDetails
}: PartNumberSectionProps) {
  // Use watch to get the current value of partNumber
  const partNumber = useWatch({
    control,
    name: 'partNumber',
    defaultValue: ''
  });

  // Handle part number change
  const handlePartNumberChange = (value: string) => {
    // Use setValue from props to update the form value
    if (setValue) {
      setValue('partNumber', value, { shouldValidate: true });
    }
  };

  // Handle verify button click
  const handleVerify = () => {
    console.log('🔍 PartNumberSection handleVerify called');
    console.log('📋 Current part number:', partNumber);
    console.log('🎯 onCheck function:', typeof onCheck);
    onCheck();
  };

  // Helper function to get table display name
  const getTableDisplayName = (table?: string) => {
    switch (table) {
      case 'part_to_group':
        return 'Compatibility System';
      case 'part_compatibility_groups':
        return 'Parts Catalog';
      default:
        return 'Database';
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <Label>{requirePartNumber ? "Part Number" : "Part Number (Optional)"}</Label>
        {requirePartNumber ? (
          <>
            <ButtonedInput
              value={partNumber}
              onChange={handlePartNumberChange}
              onSubmit={handleVerify}
              disabled={isChecking}
              isLoading={isChecking}
              buttonText={isChecking ? currentTask : 'Check Part'}
              placeholder={requirePartNumber ? "Enter part number" : "Part Number (Optional)"}
              className={errors.partNumber ? 'border-red-500' : ''}
            />
            {errors.partNumber && (
              <p className="text-sm text-red-500 mt-1">{errors.partNumber.message}</p>
            )}
          </>
        ) : (
          <>
            <Input
              type="text"
              placeholder="Part Number (Optional)"
              {...control.register('partNumber', {
                pattern: {
                  value: /^[A-Z0-9-]+$/,
                  message: 'Part number can only contain uppercase letters, numbers, and hyphens'
                }
              })}
              className={errors.partNumber ? 'border-red-500' : ''}
            />
            {errors.partNumber && (
              <p className="text-sm text-red-500 mt-1">{errors.partNumber.message}</p>
            )}
          </>
        )}

        {/* Part Number Exists Error Message */}
        {partNumberExists && partNumber && (
          <div className="mt-3 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0">
                <svg className="w-5 h-5 text-red-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-semibold text-red-800 mb-1">
                  🚨 Part Number Already Exists!
                </h4>
                <p className="text-sm text-red-700 mb-2">
                  Part number <span className="font-mono bg-red-100 px-2 py-1 rounded text-xs">{partNumber}</span> is already registered in our system.
                </p>
                {existingPartDetails && (
                  <div className="bg-red-100 border border-red-300 rounded p-2 mb-2">
                    <p className="text-xs font-medium text-red-800 mb-1">Found in:</p>
                    <p className="text-xs text-red-700">
                      📍 {getTableDisplayName(existingPartDetails.table)}
                      {existingPartDetails.groupId && (
                        <span className="ml-2 bg-red-200 px-2 py-1 rounded text-xs">
                          Group ID: {existingPartDetails.groupId}
                        </span>
                      )}
                    </p>
                  </div>
                )}
                <p className="text-xs text-red-600">
                  You can still add this as a separate inventory item if it's a different physical part.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
      {children}
    </div>
  );
}