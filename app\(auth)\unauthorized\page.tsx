'use client';

import React from 'react';
import { motion } from 'framer-motion'; // Import Framer Motion
import { AlertTriangle, Home } from 'lucide-react'; // Import icons
import { useRouter } from 'next/navigation';

// Define animation variants for Framer Motion
const containerVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      delay: 0.2,
      duration: 0.5,
      when: "beforeChildren", // Animate children after container
      staggerChildren: 0.2, // Stagger animation of children
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 15 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.4 },
  },
};

/**
 * Unauthorized Component
 *
 * Displays an unauthorized access message with animations.
 * Provides a button to navigate back to the homepage.
 *
 * Note: Assumes Tailwind CSS and Framer Motion are set up in your Next.js project.
 * Assumes `lucide-react` is installed for icons.
 * You might need to adjust the `handleGoHome` function based on your routing setup (e.g., using Next.js `useRouter`).
 */
export default function UnauthorizedPage() {
  const router = useRouter();

  // Function to handle navigation back home
  const handleGoHome = () => {
    router.push('/dashboard');
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 p-4 font-sans">
      {/* Animated container */}
      <motion.div
        className="bg-white dark:bg-gray-800 p-8 md:p-12 rounded-xl shadow-2xl text-center max-w-md w-full border border-gray-200 dark:border-gray-700"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Animated Icon */}
        <motion.div variants={itemVariants} className="mb-6 text-red-500 dark:text-red-400">
          <AlertTriangle size={64} className="mx-auto" />
        </motion.div>

        {/* Animated Title */}
        <motion.h1
          variants={itemVariants}
          className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-gray-100 mb-3"
        >
          Unauthorized Access
        </motion.h1>

        {/* Animated Description */}
        <motion.p
          variants={itemVariants}
          className="text-gray-600 dark:text-gray-400 mb-8 text-base md:text-lg"
        >
          Sorry, you don't have the necessary permissions to access this page.
          Please contact an administrator if you believe this is an error.
        </motion.p>

        {/* Animated Button */}
        <motion.button
          variants={itemVariants}
          onClick={handleGoHome}
          className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg shadow-md transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 group"
          whileHover={{ scale: 1.05 }} // Scale up on hover
          whileTap={{ scale: 0.95 }} // Scale down on tap
        >
          <Home size={20} className="mr-2 transition-transform duration-300 group-hover:rotate-[-15deg]" />
          Go Back Home
        </motion.button>
      </motion.div>
    </div>
  );
};

// No need for export default as we're using a named export Function