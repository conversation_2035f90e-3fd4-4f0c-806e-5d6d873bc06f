import React from 'react';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import ClientDetail from '../components/ClientDetail';
import DashboardHeader from '../components/DashboardHeader';

export default function ClientDetailPage({ params }: { params: { id: string } }) {
  const clientId = params.id;

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader title="Client Management" />

      <div className="container mx-auto px-4 py-8">
        {/* Back button */}
        <Link href="/clients" className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-6">
          <ArrowLeft className="w-4 h-4 mr-2" /> Back to Clients
        </Link>

        <ClientDetail clientId={clientId} />
      </div>
    </div>
  );
}
