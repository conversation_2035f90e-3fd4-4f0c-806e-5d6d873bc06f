'use client';

import React, { useState } from 'react';
import { Menu, Grid3X3, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import SearchBar from './SearchBar';
import Drawer from './Drawer';
import { cn } from '@/app/utils/cn';
import { useRouter, usePathname } from 'next/navigation';
import CategoriesMenu from './CategoriesMenu';

interface HeaderProps {
  onSearch?: (value: string) => void;
  onOpenSettings?: () => void;
}

const Header: React.FC<HeaderProps> = ({ onSearch, onOpenSettings }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isCategoryOpen, setIsCategoryOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchInput, setShowSearchInput] = useState(false);
  const router = useRouter();

  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: 'view-dashboard',
      library: 'mdi',
      href: '/dashboard'
    },
    {
      id: 'parts',
      label: 'Parts',
      icon: 'cog',
      library: 'mdi',
      href: '/parts'
    },
    {
      id: 'categories',
      label: 'Categories',
      icon: 'folder',
      library: 'mdi',
      children: [
        {
          id: 'all-categories',
          label: 'All Categories',
          href: '/categories'
        },
        {
          id: 'add-category',
          label: 'Add Category',
          href: '/categories/add'
        }
      ]
    }
  ];

  const renderMenuContent = () => (
    <div className="flex flex-col">
      {menuItems.map((item) => (
        <div key={item.id} className="border-b border-gray-100">
          {item.href ? (
            <Link
              href={item.href}
              className="flex items-center px-4 py-3 hover:bg-gray-50"
              onClick={() => setIsMenuOpen(false)}
            >
              <span className="text-gray-800">{item.label}</span>
            </Link>
          ) : (
            <div className="flex items-center justify-between px-4 py-3 hover:bg-gray-50">
              <span className="text-gray-800">{item.label}</span>
              <ChevronRight size={20} className="text-gray-400" />
            </div>
          )}
          {item.children && (
            <div className="pl-4 pb-2">
              {item.children.map((child) => (
                <Link
                  key={child.id}
                  href={child.href}
                  className="flex items-center px-4 py-2 text-sm text-gray-600 hover:bg-gray-50"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {child.label}
                </Link>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );

  const renderCategoryContent = () => (
    <CategoriesMenu />
  );

  return (
    <div className="bg-white">
      {/* Top Bar */}
      <div className="flex items-center justify-between p-4">
        <button
          onClick={() => setIsMenuOpen(true)}
          className="p-2 rounded-full hover:bg-gray-100"
        >
          <Menu size={24} />
        </button>
        <h1 className="text-xl font-semibold">AutoFlow</h1>
        <button
          onClick={() => setIsCategoryOpen(true)}
          className="p-2 rounded-full hover:bg-gray-100"
        >
          <Grid3X3 size={24} />
        </button>
      </div>

      {/* Search Bar */}
      <div className="px-4 pb-4">
        <SearchBar
          onSearch={onSearch}
          onOpenSettings={onOpenSettings}
          placeholder="Search parts..."
        />
      </div>

      {/* Menu Drawer */}
      <Drawer
        isOpen={isMenuOpen}
        onClose={() => setIsMenuOpen(false)}
        direction="left"
        title="Menu"
      >
        {renderMenuContent()}
      </Drawer>

      {/* Category Drawer */}
      <Drawer
        isOpen={isCategoryOpen}
        onClose={() => setIsCategoryOpen(false)}
        direction="right"
        title="Categories"
      >
        {renderCategoryContent()}
      </Drawer>
    </div>
  );
};

export default Header; 