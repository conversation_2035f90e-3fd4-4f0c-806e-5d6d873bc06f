import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { LucideIcon } from 'lucide-react';

interface LabelProps {
  children: React.ReactNode;
  className?: string;
  icon?: LucideIcon;
  isVisible?: boolean; // To demonstrate AnimatePresence, control visibility
}

const Label: React.FC<LabelProps> = ({
  children,
  className = '',
  icon: IconComponent,
  isVisible = true, // Default to visible
}) => {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className={`
            inline-flex items-center
            px-3 py-1.5 rounded-full text-sm font-medium
            bg-gray-100 text-gray-800
            dark:bg-gray-800 dark:text-gray-100
            ${className}
          `}
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2, ease: 'easeOut' }}
        >
          {IconComponent && (
            <IconComponent size={16} className="mr-2" />
          )}
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Label;