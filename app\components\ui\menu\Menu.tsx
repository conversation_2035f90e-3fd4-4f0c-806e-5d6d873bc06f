import React, { useState } from 'react';
import Link from 'next/link';
import Icon from '../Icon'; 

interface MenuItem {
  id: string;
  title: string;
  url?: string;
  iconName: string;
  iconLibrary?: 'mdi' | 'lucide';
  children?: MenuItem[];
}

interface MenuProps {
  items: MenuItem[];
  orientation: 'horizontal' | 'vertical';
}

const Menu: React.FC<MenuProps> = ({ items, orientation }) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleItem = (itemId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      newSet.has(itemId) ? newSet.delete(itemId) : newSet.add(itemId);
      return newSet;
    });
  };

  const renderMenuItems = (items: MenuItem[], level: number = 0) => (
    <>
      {items.map((item) => (
        <div
          key={item.id}
          className={`menu-item ${orientation} level-${level}`}
          data-testid={`menu-item-${item.id}`}
        >
          <div className="menu-item-content">
            {item.url ? (
              <Link href={item.url} passHref>
                <a className="menu-link">
                  <Icon
                    name={item.iconName}
                    library={item.iconLibrary}
                    size={20}
                    className="menu-icon"
                  />
                  <span className="menu-title">{item.title}</span>
                  {item.children && orientation === 'vertical' && (
                    <Icon
                      name={expandedItems.has(item.id) ? 'chevron-down' : 'chevron-right'}
                      size={16}
                      className="menu-arrow"
                    />
                  )}
                </a>
              </Link>
            ) : (
              <button
                className="menu-button"
                onClick={() => orientation === 'vertical' && toggleItem(item.id)}
                aria-expanded={orientation === 'vertical' && expandedItems.has(item.id)}
              >
                <Icon
                  name={item.iconName}
                  library={item.iconLibrary}
                  size={20}
                  className="menu-icon"
                />
                <span className="menu-title">{item.title}</span>
                {item.children && orientation === 'vertical' && (
                  <Icon
                    name={expandedItems.has(item.id) ? 'chevron-down' : 'chevron-right'}
                    size={16}
                    className="menu-arrow"
                  />
                )}
              </button>
            )}
          </div>

          {item.children && (
            <div
              className={`submenu ${
                orientation === 'vertical' && 
                !expandedItems.has(item.id) ? 'hidden' : ''
              }`}
            >
              {renderMenuItems(item.children, level + 1)}
            </div>
          )}
        </div>
      ))}
    </>
  );

  return (
    <nav className={`menu-container ${orientation}`} role="navigation">
      {renderMenuItems(items)}
    </nav>
  );
};

export default Menu;

// Styles (add to your CSS/SCSS)
/*

*/