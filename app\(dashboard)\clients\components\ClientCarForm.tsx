'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/app/libs/supabase/client';
import { motion } from 'framer-motion';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Car, Check, AlertTriangle } from 'lucide-react';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

// Types
interface Brand {
  brand_id: number;
  brand_name: string;
}

interface Model {
  id: number;
  brand_id: number;
  model_name: string;
}

interface Generation {
  id: number;
  model_id: number;
  name: string;
  start_production_year: number;
  end_production_year: number | null;
}

interface Variation {
  id: number;
  generation_id: number;
  variation: string;
}

interface Trim {
  id: number;
  variation_id: number;
  trim: string;
}

interface Engine {
  id: number;
  capacity: string;
  engine_code: string;
  fuel_type: string;
}

// Form schema
const carSchema = z.object({
  variation_trim_id: z.number().min(1, 'Car trim is required'),
  engine_id: z.number().optional(),
  registration_number: z.string().optional(),
  vin_number: z.string().optional(),
  notes: z.string().optional(),
});

type CarFormValues = z.infer<typeof carSchema>;

// Props
interface ClientCarFormProps {
  clientId: string;
  carId?: string; // If provided, we're editing an existing car
}

// Main Form Component
const ClientCarForm: React.FC<ClientCarFormProps> = ({ clientId, carId }) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Car data states
  const [brands, setBrands] = useState<Brand[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [generations, setGenerations] = useState<Generation[]>([]);
  const [variations, setVariations] = useState<Variation[]>([]);
  const [trims, setTrims] = useState<Trim[]>([]);
  const [engines, setEngines] = useState<Engine[]>([]);
  
  // Selected values for cascading dropdowns
  const [selectedBrandId, setSelectedBrandId] = useState<number | null>(null);
  const [selectedModelId, setSelectedModelId] = useState<number | null>(null);
  const [selectedGenerationId, setSelectedGenerationId] = useState<number | null>(null);
  const [selectedVariationId, setSelectedVariationId] = useState<number | null>(null);
  
  // Form setup
  const { 
    control, 
    handleSubmit, 
    setValue, 
    watch,
    formState: { errors } 
  } = useForm<CarFormValues>({
    resolver: zodResolver(carSchema),
    defaultValues: {
      variation_trim_id: 0,
      engine_id: undefined,
      registration_number: '',
      vin_number: '',
      notes: '',
    }
  });
  
  // Fetch car data
  useEffect(() => {
    const fetchCarData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const supabase = createClient();
        
        // Fetch brands
        const { data: brandsData, error: brandsError } = await supabase
          .from('car_brands')
          .select('*')
          .order('brand_name');
        
        if (brandsError) throw new Error(brandsError.message);
        setBrands(brandsData || []);
        
        // Fetch engines
        const { data: enginesData, error: enginesError } = await supabase
          .from('engines')
          .select('*')
          .order('engine_code');
        
        if (enginesError) throw new Error(enginesError.message);
        setEngines(enginesData || []);
        
        // If editing, fetch car data
        if (carId) {
          const { data: carData, error: carError } = await supabase
            .from('client_cars')
            .select(`
              *,
              variation_trim:variation_trim_id(
                id,
                trim,
                variation_id,
                car_variation:variation_id(
                  id,
                  variation,
                  generation_id,
                  car_generation:generation_id(
                    id,
                    name,
                    model_id,
                    car_models:model_id(
                      id,
                      model_name,
                      brand_id
                    )
                  )
                )
              )
            `)
            .eq('id', carId)
            .single();
          
          if (carError) throw new Error(carError.message);
          
          // Set form values
          setValue('variation_trim_id', carData.variation_trim_id);
          setValue('engine_id', carData.engine_id || undefined);
          setValue('registration_number', carData.registration_number || '');
          setValue('vin_number', carData.vin_number || '');
          setValue('notes', carData.notes || '');
          
          // Set selected values for cascading dropdowns
          const brandId = carData.variation_trim?.car_variation?.car_generation?.car_models?.brand_id;
          const modelId = carData.variation_trim?.car_variation?.car_generation?.model_id;
          const generationId = carData.variation_trim?.car_variation?.generation_id;
          const variationId = carData.variation_trim?.variation_id;
          
          if (brandId) {
            setSelectedBrandId(brandId);
            
            // Fetch models for this brand
            const { data: modelsData } = await supabase
              .from('car_models')
              .select('*')
              .eq('brand_id', brandId)
              .order('model_name');
            
            setModels(modelsData || []);
          }
          
          if (modelId) {
            setSelectedModelId(modelId);
            
            // Fetch generations for this model
            const { data: generationsData } = await supabase
              .from('car_generation')
              .select('*')
              .eq('model_id', modelId)
              .order('name');
            
            setGenerations(generationsData || []);
          }
          
          if (generationId) {
            setSelectedGenerationId(generationId);
            
            // Fetch variations for this generation
            const { data: variationsData } = await supabase
              .from('car_variation')
              .select('*')
              .eq('generation_id', generationId)
              .order('variation');
            
            setVariations(variationsData || []);
          }
          
          if (variationId) {
            setSelectedVariationId(variationId);
            
            // Fetch trims for this variation
            const { data: trimsData } = await supabase
              .from('variation_trim')
              .select('*')
              .eq('variation_id', variationId)
              .order('trim');
            
            setTrims(trimsData || []);
          }
        }
      } catch (err) {
        console.error('Error fetching car data:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchCarData();
  }, [carId, setValue]);
  
  // Handle brand change
  const handleBrandChange = async (brandId: number) => {
    setSelectedBrandId(brandId);
    setSelectedModelId(null);
    setSelectedGenerationId(null);
    setSelectedVariationId(null);
    setValue('variation_trim_id', 0);
    
    setModels([]);
    setGenerations([]);
    setVariations([]);
    setTrims([]);
    
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('car_models')
        .select('*')
        .eq('brand_id', brandId)
        .order('model_name');
      
      if (error) throw error;
      setModels(data || []);
    } catch (err) {
      console.error('Error fetching models:', err);
    }
  };
  
  // Handle model change
  const handleModelChange = async (modelId: number) => {
    setSelectedModelId(modelId);
    setSelectedGenerationId(null);
    setSelectedVariationId(null);
    setValue('variation_trim_id', 0);
    
    setGenerations([]);
    setVariations([]);
    setTrims([]);
    
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('car_generation')
        .select('*')
        .eq('model_id', modelId)
        .order('name');
      
      if (error) throw error;
      setGenerations(data || []);
    } catch (err) {
      console.error('Error fetching generations:', err);
    }
  };
  
  // Handle generation change
  const handleGenerationChange = async (generationId: number) => {
    setSelectedGenerationId(generationId);
    setSelectedVariationId(null);
    setValue('variation_trim_id', 0);
    
    setVariations([]);
    setTrims([]);
    
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('car_variation')
        .select('*')
        .eq('generation_id', generationId)
        .order('variation');
      
      if (error) throw error;
      setVariations(data || []);
    } catch (err) {
      console.error('Error fetching variations:', err);
    }
  };
  
  // Handle variation change
  const handleVariationChange = async (variationId: number) => {
    setSelectedVariationId(variationId);
    setValue('variation_trim_id', 0);
    
    setTrims([]);
    
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('variation_trim')
        .select('*')
        .eq('variation_id', variationId)
        .order('trim');
      
      if (error) throw error;
      setTrims(data || []);
    } catch (err) {
      console.error('Error fetching trims:', err);
    }
  };
  
  // Handle form submission
  const onSubmit = async (data: CarFormValues) => {
    setIsSaving(true);
    setError(null);
    
    try {
      const supabase = createClient();
      
      // Prepare car data
      const carData = {
        client_id: clientId,
        variation_trim_id: data.variation_trim_id,
        engine_id: data.engine_id || null,
        registration_number: data.registration_number || null,
        vin_number: data.vin_number || null,
        notes: data.notes || null,
      };
      
      if (carId) {
        // Update existing car
        const { error } = await supabase
          .from('client_cars')
          .update(carData)
          .eq('id', carId);
        
        if (error) throw error;
      } else {
        // Create new car
        const { error } = await supabase
          .from('client_cars')
          .insert(carData);
        
        if (error) throw error;
      }
      
      // Redirect back to client page
      router.push(`/clients/${clientId}`);
    } catch (err) {
      console.error('Error saving car:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setIsSaving(false);
    }
  };
  
  // Render loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }
  
  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p>{error}</p>
        </div>
      )}
      
      {/* Car Selection */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-lg font-medium text-gray-800 mb-4">Car Details</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Brand */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Brand <span className="text-red-500">*</span>
            </label>
            <select
              value={selectedBrandId || ''}
              onChange={(e) => handleBrandChange(Number(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
            >
              <option value="">Select Brand</option>
              {brands.map((brand) => (
                <option key={brand.brand_id} value={brand.brand_id}>
                  {brand.brand_name}
                </option>
              ))}
            </select>
          </div>
          
          {/* Model */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Model <span className="text-red-500">*</span>
            </label>
            <select
              value={selectedModelId || ''}
              onChange={(e) => handleModelChange(Number(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              disabled={!selectedBrandId}
            >
              <option value="">Select Model</option>
              {models.map((model) => (
                <option key={model.id} value={model.id}>
                  {model.model_name}
                </option>
              ))}
            </select>
            {!selectedBrandId && (
              <p className="mt-1 text-xs text-gray-500">Please select a brand first</p>
            )}
          </div>
          
          {/* Generation */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Generation <span className="text-red-500">*</span>
            </label>
            <select
              value={selectedGenerationId || ''}
              onChange={(e) => handleGenerationChange(Number(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              disabled={!selectedModelId}
            >
              <option value="">Select Generation</option>
              {generations.map((generation) => (
                <option key={generation.id} value={generation.id}>
                  {generation.name} ({generation.start_production_year} - {generation.end_production_year || 'Present'})
                </option>
              ))}
            </select>
            {!selectedModelId && (
              <p className="mt-1 text-xs text-gray-500">Please select a model first</p>
            )}
          </div>
          
          {/* Variation */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Variation <span className="text-red-500">*</span>
            </label>
            <select
              value={selectedVariationId || ''}
              onChange={(e) => handleVariationChange(Number(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              disabled={!selectedGenerationId}
            >
              <option value="">Select Variation</option>
              {variations.map((variation) => (
                <option key={variation.id} value={variation.id}>
                  {variation.variation}
                </option>
              ))}
            </select>
            {!selectedGenerationId && (
              <p className="mt-1 text-xs text-gray-500">Please select a generation first</p>
            )}
          </div>
          
          {/* Trim */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Trim <span className="text-red-500">*</span>
            </label>
            <Controller
              name="variation_trim_id"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  value={field.value || ''}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 ${
                    errors.variation_trim_id ? 'border-red-500' : 'border-gray-300'
                  }`}
                  disabled={!selectedVariationId}
                >
                  <option value="">Select Trim</option>
                  {trims.map((trim) => (
                    <option key={trim.id} value={trim.id}>
                      {trim.trim}
                    </option>
                  ))}
                </select>
              )}
            />
            {errors.variation_trim_id && (
              <p className="mt-1 text-xs text-red-500">{errors.variation_trim_id.message}</p>
            )}
            {!selectedVariationId && (
              <p className="mt-1 text-xs text-gray-500">Please select a variation first</p>
            )}
          </div>
          
          {/* Engine */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Engine
            </label>
            <Controller
              name="engine_id"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  value={field.value || ''}
                  onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                >
                  <option value="">Select Engine (Optional)</option>
                  {engines.map((engine) => (
                    <option key={engine.id} value={engine.id}>
                      {engine.engine_code} - {engine.capacity} {engine.fuel_type}
                    </option>
                  ))}
                </select>
              )}
            />
          </div>
        </div>
      </div>
      
      {/* Additional Information */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-lg font-medium text-gray-800 mb-4">Additional Information</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Registration Number */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Registration Number
            </label>
            <Controller
              name="registration_number"
              control={control}
              render={({ field }) => (
                <input
                  {...field}
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  placeholder="Enter registration number"
                />
              )}
            />
          </div>
          
          {/* VIN Number */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              VIN Number
            </label>
            <Controller
              name="vin_number"
              control={control}
              render={({ field }) => (
                <input
                  {...field}
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  placeholder="Enter VIN number"
                />
              )}
            />
          </div>
          
          {/* Notes */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <Controller
              name="notes"
              control={control}
              render={({ field }) => (
                <textarea
                  {...field}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  placeholder="Enter any additional notes about this car"
                  rows={3}
                />
              )}
            />
          </div>
        </div>
      </div>
      
      {/* Form Actions */}
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={() => router.back()}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          disabled={isSaving}
        >
          Cancel
        </button>
        
        <button
          type="submit"
          className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 flex items-center"
          disabled={isSaving}
        >
          {isSaving ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Saving...
            </>
          ) : (
            <>
              <Check className="w-4 h-4 mr-2" />
              {carId ? 'Update Car' : 'Add Car'}
            </>
          )}
        </button>
      </div>
    </form>
  );
};

export default ClientCarForm;
