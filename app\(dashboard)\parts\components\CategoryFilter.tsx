'use client';

import React from 'react';
import { cn } from '@/app/utils/cn';

interface Category {
  id: string;
  label: string;
}

interface CategoryFilterProps {
  categories: Category[];
  selectedCategory: string | null;
  onChange: (categoryId: string) => void;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  selectedCategory,
  onChange
}) => {
  return (
    <div className="flex gap-2 overflow-x-auto pb-1 hide-scrollbar">
      <button
        className={cn(
          "px-4 py-2 rounded-full text-sm whitespace-nowrap transition-colors",
          selectedCategory === null
            ? "bg-cyan-500 text-white"
            : "bg-gray-100 text-gray-700 hover:bg-gray-200"
        )}
        onClick={() => onChange('')}
      >
        Popular
      </button>
      
      {categories.map((category) => (
        <button
          key={category.id}
          className={cn(
            "px-4 py-2 rounded-full text-sm whitespace-nowrap transition-colors",
            selectedCategory === category.id
              ? "bg-cyan-500 text-white"
              : "bg-gray-100 text-gray-700 hover:bg-gray-200"
          )}
          onClick={() => onChange(category.id)}
        >
          {category.label}
        </button>
      ))}
    </div>
  );
};

export default CategoryFilter; 