import { createClient } from '@/app/libs/supabase/client';

/**
 * Get a public URL for a Supabase storage image with proper cache headers
 * @param path The path to the image in Supabase storage
 * @param bucketName The name of the storage bucket (defaults to 'car-part-images')
 * @param transform Optional image transformations
 * @returns The public URL for the image
 */
export function getSupabaseImageUrl(
  path: string,
  bucketName: string = 'car-part-images',
  transform?: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpg' | 'png';
  }
): string {
  // If the path is already a full URL, return it
  if (path.startsWith('http')) {
    return path;
  }

  // Create a Supabase client
  const supabase = createClient();

  // Get the public URL for the image
  const { data } = supabase.storage.from(bucketName).getPublicUrl(path, {
    download: false,
    transform: transform,
  });

  // Add cache control parameters to the URL
  const url = new URL(data.publicUrl);
  
  // Add cache control parameters to ensure the image is properly cached
  url.searchParams.append('cache-control', 'public, max-age=31536000, immutable');
  
  return url.toString();
}

/**
 * Get a direct image URL that bypasses Vercel's image optimization
 * @param path The path to the image (can be a Supabase storage path or a full URL)
 * @param userId Optional user ID to help construct the path if needed
 * @param bucketName The name of the storage bucket (defaults to 'car-part-images')
 * @returns The direct URL for the image
 */
export function getDirectImageUrl(
  path: string,
  userId?: string,
  bucketName: string = 'car-part-images'
): string {
  // If the path is already a full URL, return it
  if (path.startsWith('http')) {
    return path;
  }

  // Create a Supabase client
  const supabase = createClient();

  // If userId is provided and the path doesn't include it, add it
  let storagePath = path;
  if (userId && !path.includes(userId)) {
    storagePath = `${userId}/${path.split('/').pop()}`;
  }

  // Get the public URL for the image
  const { data } = supabase.storage.from(bucketName).getPublicUrl(storagePath);

  // Add cache control parameters to the URL
  const url = new URL(data.publicUrl);
  
  // Add cache control parameters to ensure the image is properly cached
  url.searchParams.append('cache-control', 'public, max-age=31536000, immutable');
  
  return url.toString();
}
