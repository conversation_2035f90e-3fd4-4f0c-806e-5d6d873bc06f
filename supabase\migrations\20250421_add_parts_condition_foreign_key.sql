-- Add foreign key constraint to parts_condition table
-- This migration adds a proper foreign key constraint to link parts_condition to parts table
-- Date: April 21, 2025

-- First, check if the parts_condition table exists
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'parts_condition'
    ) THEN
        -- Check if the constraint already exists
        IF NOT EXISTS (
            SELECT FROM information_schema.table_constraints 
            WHERE constraint_name = 'parts_condition_part_id_fkey' 
            AND table_name = 'parts_condition'
        ) THEN
            -- Add the foreign key constraint
            ALTER TABLE public.parts_condition
            ADD CONSTRAINT parts_condition_part_id_fkey
            FOREIGN KEY (part_id)
            REFERENCES public.parts(part_id)
            ON DELETE CASCADE;
            
            RAISE NOTICE 'Added foreign key constraint to parts_condition table';
        ELSE
            RAISE NOTICE 'Foreign key constraint already exists on parts_condition table';
        END IF;
    ELSE
        RAISE NOTICE 'parts_condition table does not exist, skipping foreign key addition';
    END IF;
END
$$;