import { NextRequest, NextResponse } from 'next/server';
import { googleSheetsService } from '@/app/services/googleSheets';
import { createClient } from '@/app/libs/supabase/server';
import { OAuth2Client } from 'google-auth-library';

/**
 * GET: Check authentication status and get sync status
 */
export async function GET(request: NextRequest) {
  try {
    // Get user ID from query parameter if available
    const url = new URL(request.url);
    const userIdParam = url.searchParams.get('userId');
    const userId = userIdParam || undefined; // Convert from string | null to string | undefined

    // Check if authenticated with the user ID if provided
    const isAuthenticated = await googleSheetsService.isAuthenticated(userId);

    // Get latest sync status
    const syncStatus = isAuthenticated ? await googleSheetsService.getLatestSyncStatus() : null;

    // Get the spreadsheet ID if authenticated
    // We need to use a method that accesses the spreadsheet ID since it's a private property
    let spreadsheetId = null;
    if (isAuthenticated) {
      try {
        // Get the spreadsheet ID from the service
        spreadsheetId = googleSheetsService.getSpreadsheetId();
      } catch (err) {
        console.error('Error fetching spreadsheet ID:', err);
      }
    }

    return NextResponse.json({
      authenticated: isAuthenticated,
      spreadsheetId,
      syncStatus
    });
  } catch (error: any) {
    console.error('Error checking Google Sheets status:', error);
    return NextResponse.json(
      { error: 'Failed to check Google Sheets status', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * POST: Sync products to Google Sheets
 */
export async function POST(request: NextRequest) {
  try {
    // Parse request body to get user ID
    const body = await request.json();
    const { action, userId } = body;

    // Check if we have a user ID
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required for authentication' },
        { status: 400 }
      );
    }

    // Check if authenticated with the provided user ID
    const isAuthenticated = await googleSheetsService.isAuthenticated(userId);

    if (!isAuthenticated) {
      // Generate the auth URL directly instead of making an internal API call
      try {
        // Create OAuth client
        const oauth2Client = new OAuth2Client(
          process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
          process.env.GOOGLE_CLIENT_SECRET,
          process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI || 'http://localhost:3000/api/google/sheets/auth/callback'
        );

        // Generate auth URL
        const scopes = [
          'https://www.googleapis.com/auth/spreadsheets',
          'https://www.googleapis.com/auth/drive.file'
        ];

        // Create a state parameter with the user ID
        const state = userId ? Buffer.from(JSON.stringify({ userId })).toString('base64') : '';

        const url = oauth2Client.generateAuthUrl({
          access_type: 'offline',
          scope: scopes,
          prompt: 'consent',
          state: state // Include the state parameter with the user ID
        });

        console.log('Generated auth URL directly:', url.substring(0, 100) + '...');

        return NextResponse.json(
          {
            error: 'Not authenticated with Google Sheets API',
            authUrl: url, // Include the auth URL so the client can redirect
            requiresAuth: true
          },
          { status: 401 }
        );
      } catch (error: any) {
        console.error('Error generating auth URL:', error);
        return NextResponse.json(
          { error: `Failed to generate authentication URL: ${error?.message || 'Unknown error'}` },
          { status: 500 }
        );
      }
    }

    // We already parsed the body and checked for userId above

    switch (action) {
      case 'sync_products':
        // Sync products to Google Sheets
        const syncStatus = await googleSheetsService.syncProductData(userId);

        return NextResponse.json({
          success: true,
          message: 'Products synced successfully',
          syncStatus
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: sync_products' },
          { status: 400 }
        );
    }
  } catch (error: any) {
    console.error('Error syncing products:', error);
    return NextResponse.json(
      { error: 'Failed to sync products', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
