-- Drop existing tables if they exist
DROP TABLE IF EXISTS google_tokens CASCADE;
DROP TABLE IF EXISTS google_spreadsheets CASCADE;
DROP TABLE IF EXISTS sync_status CASCADE;

-- Create google_tokens table
CREATE TABLE IF NOT EXISTS google_tokens (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    access_token TEXT NOT NULL,
    refresh_token TEXT NOT NULL,
    expiry_date BIGINT NOT NULL,
    spreadsheet_id TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create google_spreadsheets table
CREATE TABLE IF NOT EXISTS google_spreadsheets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    spreadsheet_name TEXT NOT NULL,
    spreadsheet_id TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create sync_status table
CREATE TABLE IF NOT EXISTS sync_status (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    service_name TEXT NOT NULL,
    status TEXT NOT NULL,
    last_sync_time TIMESTAMPTZ NOT NULL,
    details JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE google_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE google_spreadsheets ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_status ENABLE ROW LEVEL SECURITY;

-- Create policies for google_tokens
CREATE POLICY "Users can view their own tokens"
    ON google_tokens FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own tokens"
    ON google_tokens FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own tokens"
    ON google_tokens FOR UPDATE
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own tokens"
    ON google_tokens FOR DELETE
    USING (auth.uid() = user_id);

-- Create policies for google_spreadsheets
CREATE POLICY "Users can view their own spreadsheets"
    ON google_spreadsheets FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own spreadsheets"
    ON google_spreadsheets FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own spreadsheets"
    ON google_spreadsheets FOR UPDATE
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own spreadsheets"
    ON google_spreadsheets FOR DELETE
    USING (auth.uid() = user_id);

-- Create policies for sync_status
CREATE POLICY "Users can view their own sync status"
    ON sync_status FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own sync status"
    ON sync_status FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own sync status"
    ON sync_status FOR DELETE
    USING (auth.uid() = user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_google_tokens_updated_at
    BEFORE UPDATE ON google_tokens
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_google_spreadsheets_updated_at
    BEFORE UPDATE ON google_spreadsheets
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT ALL ON google_tokens TO authenticated;
GRANT ALL ON google_spreadsheets TO authenticated;
GRANT ALL ON sync_status TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_google_tokens_user_id ON google_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_google_spreadsheets_user_id ON google_spreadsheets(user_id);
CREATE INDEX IF NOT EXISTS idx_sync_status_user_id ON sync_status(user_id); 