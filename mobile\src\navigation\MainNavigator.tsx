import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { MainTabParamList, ShopStackParamList } from '@/types';
import { useUser } from '@/store/auth';
import { theme } from '@/theme';

// Import screens
import { HomeScreen } from '@/screens/main/HomeScreen';
import { ShopScreen } from '@/screens/shop/ShopScreen';
import { PartDetailsScreen } from '@/screens/shop/PartDetailsScreen';
import { CategoryPartsScreen } from '@/screens/shop/CategoryPartsScreen';
import { SearchScreen } from '@/screens/main/SearchScreen';
import { ProfileScreen } from '@/screens/main/ProfileScreen';
import { DashboardScreen } from '@/screens/main/DashboardScreen';

const Tab = createBottomTabNavigator<MainTabParamList>();
const ShopStack = createNativeStackNavigator<ShopStackParamList>();

// Shop Stack Navigator
const ShopNavigator: React.FC = () => {
  return (
    <ShopStack.Navigator
      screenOptions={{
        headerShown: false,
        animation: 'slide_from_right',
      }}
    >
      <ShopStack.Screen name="ShopHome" component={ShopScreen} />
      <ShopStack.Screen name="PartDetails" component={PartDetailsScreen} />
      <ShopStack.Screen name="CategoryParts" component={CategoryPartsScreen} />
    </ShopStack.Navigator>
  );
};

export const MainNavigator: React.FC = () => {
  const { isSuperAdmin, isEmployee } = useUser();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Home':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'Shop':
              iconName = focused ? 'store' : 'store-outline';
              break;
            case 'Search':
              iconName = focused ? 'magnify' : 'magnify';
              break;
            case 'Profile':
              iconName = focused ? 'account' : 'account-outline';
              break;
            case 'Dashboard':
              iconName = focused ? 'view-dashboard' : 'view-dashboard-outline';
              break;
            default:
              iconName = 'help-circle-outline';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.onSurface,
        tabBarStyle: {
          backgroundColor: theme.colors.surface,
          borderTopColor: theme.colors.outline,
          borderTopWidth: 1,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={HomeScreen}
        options={{ tabBarLabel: 'Home' }}
      />
      
      <Tab.Screen 
        name="Shop" 
        component={ShopNavigator}
        options={{ tabBarLabel: 'Shop' }}
      />
      
      <Tab.Screen 
        name="Search" 
        component={SearchScreen}
        options={{ tabBarLabel: 'Search' }}
      />
      
      <Tab.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{ tabBarLabel: 'Profile' }}
      />
      
      {/* Show Dashboard tab only for employees and super admins */}
      {(isEmployee || isSuperAdmin) && (
        <Tab.Screen 
          name="Dashboard" 
          component={DashboardScreen}
          options={{ tabBarLabel: 'Dashboard' }}
        />
      )}
    </Tab.Navigator>
  );
};
