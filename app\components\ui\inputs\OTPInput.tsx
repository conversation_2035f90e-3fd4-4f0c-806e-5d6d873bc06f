import React, { useState, useRef, useEffect } from 'react';

interface OTPInputProps {
  numInputs: number;
  onChange: (otp: string) => void;
  value?: string;
  isError?: boolean;
  disabled?: boolean;
  className?: string;
}

const OTPInput: React.FC<OTPInputProps> = ({
  numInputs,
  onChange,
  value = '',
  isError = false,
  disabled = false,
  className,
}) => {
  const [otp, setOtp] = useState<string[]>(Array(numInputs).fill(''));
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    if (value && value.length === numInputs) {
      setOtp(value.split(''));
    } else if (value === '') {
      setOtp(Array(numInputs).fill(''));
    }
  }, [value, numInputs]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const newOtp = [...otp];
    const inputVal = e.target.value;

    if (inputVal.match(/^[0-9]$/)) {
      newOtp[index] = inputVal;
      setOtp(newOtp);

      if (index < numInputs - 1 && inputVal) {
        inputRefs.current[index + 1]?.focus();
      }
    } else if (inputVal === '') {
      newOtp[index] = '';
      setOtp(newOtp);
    }

    onChange(newOtp.join(''));
  };

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.key === 'Backspace' && index > 0 && !otp[index]) {
      inputRefs.current[index - 1]?.focus();
    }
    if (e.key === 'ArrowRight' && index < numInputs - 1) {
      inputRefs.current[index + 1]?.focus();
    }
    if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pasteData = e.clipboardData.getData('text').slice(0, numInputs);
    if (pasteData.match(/^[0-9]+$/)) {
      const newOtp = pasteData.split('');
      while (newOtp.length < numInputs) {
        newOtp.push('');
      }
      setOtp(newOtp);
      onChange(newOtp.join(''));

      inputRefs.current[numInputs - 1]?.focus();
    }
  };

  return (
    <div className={`flex space-x-2 ${className}`}>
      {otp.map((digit, index) => (
        <input
          key={index}
          ref={(el) => {
            inputRefs.current[index] = el;
          }}
          type="text"
          maxLength={1}
          value={digit}
          onChange={(e) => handleChange(e, index)}
          onKeyDown={(e) => handleKeyDown(e, index)}
          onPaste={handlePaste}
          className={`w-12 h-12 text-center text-2xl font-medium border rounded-lg ${
            isError
              ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
              : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
          } ${disabled ? 'bg-gray-100' : ''} focus:outline-none`}
          disabled={disabled}
        />
      ))}
    </div>
  );
};

export default OTPInput;