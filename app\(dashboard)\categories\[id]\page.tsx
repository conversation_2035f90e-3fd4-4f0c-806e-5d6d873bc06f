'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { ArrowLeft, Cog, Edit, Tag, FolderTree, Package, List } from 'lucide-react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Category } from '@/app/types/database';
import EditCategoryModal from '@/app/(dashboard)/categories/components/EditCategoryModal';
import AttributesTab from '@/app/(dashboard)/categories/components/AttributesTab';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import { CategoryWithChildren } from '../../types';

interface TabData {
  id: string;
  label: string;
  icon: React.ElementType;
}

const tabs: TabData[] = [
  { id: 'general', label: 'General', icon: Cog },
  { id: 'attributes', label: 'Attributes', icon: List },
  { id: 'subcategories', label: 'Subcategories', icon: FolderTree }
];

export default function CategoryDetailPage({ params }: { params: { id: string } }) {
  const categoryId = parseInt(params.id);
  const [category, setCategory] = useState<CategoryWithChildren | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('general');
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  
  const supabase = createClient();

  // Fetch category details
  useEffect(() => {
    const fetchCategory = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // Fetch the category
        const { data: categoryData, error: categoryError } = await supabase
          .from('car_part_categories')
          .select('*')
          .eq('id', categoryId)
          .single();
          
        if (categoryError) throw categoryError;
        
        // Fetch subcategories
        const { data: subcategoriesData, error: subcategoriesError } = await supabase
          .from('car_part_categories')
          .select('*')
          .eq('parent_category_id', categoryId);
          
        if (subcategoriesError) throw subcategoriesError;
        
        // Transform to CategoryWithChildren
        const categoryWithChildren: CategoryWithChildren = {
          ...categoryData,
          children: subcategoriesData || []
        };
        
        setCategory(categoryWithChildren);
      } catch (err: any) {
        console.error('Error fetching category:', err);
        setError(err.message || 'Failed to fetch category details');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchCategory();
  }, [categoryId, supabase, refreshTrigger]);

  // Get tab from URL query parameter
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const tabParam = params.get('tab');
    if (tabParam && tabs.some(tab => tab.id === tabParam)) {
      setActiveTab(tabParam);
    }
  }, []);

  // Handle tab change
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  // Handle refresh after updates
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Animation variants
  const pageVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        when: "beforeChildren",
        staggerChildren: 0.1
      } 
    }
  };
  
  const childVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.3
      }
    }
  };

  // Display loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center">
        <LoadingSpinner size={40} />
      </div>
    );
  }

  // Display error state
  if (error || !category) {
    return (
      <div className="min-h-screen p-6">
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-red-700">
                {error || 'Category not found'}
              </p>
            </div>
          </div>
        </div>
        <Link href="/categories" className="text-teal-600 hover:text-teal-800 flex items-center">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Categories
        </Link>
      </div>
    );
  }

  return (
    <motion.div 
      className="min-h-screen bg-gray-50 p-6"
      variants={pageVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Back Button */}
      <motion.div variants={childVariants} className="mb-6">
        <Link href="/categories" className="text-gray-600 hover:text-gray-900 flex items-center">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Categories
        </Link>
      </motion.div>

      {/* Category Header */}
      <motion.div 
        variants={childVariants}
        className="bg-white rounded-lg shadow-sm p-6 mb-6"
      >
        <div className="flex justify-between items-start">
          <div className="flex items-center">
            <div className={`p-3 rounded-lg mr-4 ${category.isActive ? 'bg-teal-100 text-teal-600' : 'bg-gray-100 text-gray-500'}`}>
              <FolderTree size={24} />
            </div>
            <div>
              <h1 className="text-2xl font-semibold text-gray-800">{category.label}</h1>
              <p className="text-gray-500">{category.href}</p>
            </div>
          </div>
          <button
            onClick={() => setIsEditModalOpen(true)}
            className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-md flex items-center"
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit Category
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
          <div className="bg-gray-50 rounded-md p-3">
            <p className="text-xs text-gray-500">Status</p>
            <p className={`text-sm font-medium ${category.isActive ? 'text-green-600' : 'text-gray-500'}`}>
              {category.isActive ? 'Active' : 'Inactive'}
            </p>
          </div>
          
          <div className="bg-gray-50 rounded-md p-3">
            <p className="text-xs text-gray-500">Type</p>
            <p className="text-sm font-medium text-gray-700">
              {category.isEnginePart ? 'Engine Part' : 'Regular Part'}
            </p>
          </div>
          
          <div className="bg-gray-50 rounded-md p-3">
            <p className="text-xs text-gray-500">Part Number</p>
            <p className="text-sm font-medium text-gray-700">
              {category.requirePartNumber ? 'Required' : 'Optional'}
            </p>
          </div>
          
          <div className="bg-gray-50 rounded-md p-3">
            <p className="text-xs text-gray-500">Subcategories</p>
            <p className="text-sm font-medium text-gray-700">
              {category.children.length}
            </p>
          </div>
        </div>
      </motion.div>

      {/* Tabs */}
      <motion.div variants={childVariants} className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="flex -mb-px">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`py-4 px-6 border-b-2 font-medium text-sm flex items-center ${
                    activeTab === tab.id
                      ? 'border-teal-500 text-teal-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="mr-2 h-4 w-4" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>
      </motion.div>

      {/* Tab Content */}
      <motion.div variants={childVariants} className="bg-white rounded-lg shadow-sm p-6">
        {activeTab === 'general' && (
          <div className="space-y-6">
            <h2 className="text-lg font-medium text-gray-900">General Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Category Details</h3>
                <dl className="space-y-3">
                  <div>
                    <dt className="text-sm text-gray-500">ID</dt>
                    <dd className="text-sm font-medium text-gray-900">{category.id}</dd>
                  </div>
                  <div>
                    <dt className="text-sm text-gray-500">Name</dt>
                    <dd className="text-sm font-medium text-gray-900">{category.label}</dd>
                  </div>
                  <div>
                    <dt className="text-sm text-gray-500">URL Path</dt>
                    <dd className="text-sm font-medium text-gray-900">{category.href}</dd>
                  </div>
                  <div>
                    <dt className="text-sm text-gray-500">Parent Category</dt>
                    <dd className="text-sm font-medium text-gray-900">
                      {category.parent_category_id ? `ID: ${category.parent_category_id}` : 'None (Top-level Category)'}
                    </dd>
                  </div>
                </dl>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-2">Display Settings</h3>
                <dl className="space-y-3">
                  <div>
                    <dt className="text-sm text-gray-500">Icon</dt>
                    <dd className="text-sm font-medium text-gray-900">{category.icon || 'None'}</dd>
                  </div>
                  <div>
                    <dt className="text-sm text-gray-500">Icon Library</dt>
                    <dd className="text-sm font-medium text-gray-900">{category.library || 'None'}</dd>
                  </div>
                  <div>
                    <dt className="text-sm text-gray-500">Status</dt>
                    <dd className="text-sm font-medium text-gray-900">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        category.isActive 
                          ? 'bg-green-100 text-green-800 border border-green-200' 
                          : 'bg-gray-100 text-gray-800 border border-gray-200'
                      }`}>
                        {category.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm text-gray-500">Special Flags</dt>
                    <dd className="flex flex-wrap gap-2 mt-1">
                      {category.requirePartNumber && (
                        <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                          Requires Part Number
                        </span>
                      )}
                      {category.isEnginePart && (
                        <span className="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800 border border-purple-200">
                          Engine Part
                        </span>
                      )}
                      {!category.requirePartNumber && !category.isEnginePart && (
                        <span className="text-sm text-gray-500">None</span>
                      )}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'attributes' && (
          <AttributesTab 
            categoryId={category.id} 
            onRefresh={handleRefresh}
          />
        )}

        {activeTab === 'subcategories' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Subcategories</h2>
              <Link 
                href={`/categories?parent=${category.id}`}
                className="text-sm text-teal-600 hover:text-teal-800 font-medium flex items-center"
              >
                <Tag className="mr-1 h-4 w-4" />
                Add Subcategory
              </Link>
            </div>
            
            {category.children.length === 0 ? (
              <div className="bg-gray-50 rounded-lg p-6 text-center">
                <Package className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No subcategories</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Get started by adding a new subcategory to this category.
                </p>
                <div className="mt-6">
                  <Link
                    href={`/categories?parent=${category.id}`}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                  >
                    <Tag className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                    Add Subcategory
                  </Link>
                </div>
              </div>
            ) : (
              <div className="bg-white shadow overflow-hidden sm:rounded-md">
                <ul className="divide-y divide-gray-200">
                  {category.children.map((subcategory) => (
                    <li key={subcategory.id}>
                      <Link 
                        href={`/categories/${subcategory.id}`}
                        className="block hover:bg-gray-50 transition-colors"
                      >
                        <div className="px-4 py-4 sm:px-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <div className={`p-2 rounded-md mr-3 ${subcategory.isActive ? 'bg-teal-100 text-teal-600' : 'bg-gray-100 text-gray-500'}`}>
                                <FolderTree size={16} />
                              </div>
                              <div>
                                <p className="text-sm font-medium text-gray-900 truncate">{subcategory.label}</p>
                                <p className="text-xs text-gray-500 truncate">{subcategory.href}</p>
                              </div>
                            </div>
                            <div className="flex items-center">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                subcategory.isActive 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {subcategory.isActive ? 'Active' : 'Inactive'}
                              </span>
                              {subcategory.isEnginePart && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                  Engine Part
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </motion.div>

      {/* Edit Category Modal */}
      <EditCategoryModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        category={category}
        onSuccess={handleRefresh}
      />
    </motion.div>
  );
}