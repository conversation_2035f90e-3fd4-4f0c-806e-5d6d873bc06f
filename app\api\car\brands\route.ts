import { NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';

export async function GET() {
  try {
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from('car_brands')
      .select('*')
      .order('brand_name');
      
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    const brands = data.map((brand: any) => ({
      id: brand.brand_id,
      name: brand.brand_name
    }));
    
    return NextResponse.json(brands);
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}