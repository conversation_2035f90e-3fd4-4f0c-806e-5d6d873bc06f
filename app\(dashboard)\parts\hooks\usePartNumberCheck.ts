// hooks/usePartNumberCheck.ts
import { useState } from 'react';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { createClient } from '@/app/libs/supabase/client';
import { checkPartNumberExists, createCarLinkRecord } from '@/app/libs/data';
import type { PartFormValues } from '../types';
import { toast } from 'react-hot-toast';

const supabase = createClient();
const genAI = new GoogleGenerativeAI(process.env.NEXT_PUBLIC_GEMINI_API_KEY!);

// Add rate limiting
let lastApiCall = 0;
const MIN_API_CALL_INTERVAL = 30000; // 30 seconds minimum between calls

const waitForRateLimit = async () => {
  const now = Date.now();
  const timeSinceLastCall = now - lastApiCall;
  if (timeSinceLastCall < MIN_API_CALL_INTERVAL) {
    const waitTime = MIN_API_CALL_INTERVAL - timeSinceLastCall;
    console.log(`Rate limiting: waiting ${waitTime}ms before next API call`);
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }
  lastApiCall = Date.now();
};

// Add caching
const cache = new Map<string, any>();

interface UsePartNumberCheckProps {
  getValues: () => PartFormValues;
  setValue: (name: keyof PartFormValues, value: any) => void;
  setPartNumberExists: (exists: boolean) => void;
  setExistingTrims: (trims: any[]) => void;
  setCompatibilityData: (data: any) => void;
  setCurrentTask: (task: string) => void;
  flatCategories: any[];
  setShowExistingPartModal?: (show: boolean) => void;
  setExistingPartDetails?: (details: {groupId?: number; table?: string} | null) => void;
}

export const usePartNumberCheck = ({
  getValues,
  setValue,
  setPartNumberExists,
  setExistingTrims,
  setCompatibilityData,
  setCurrentTask,
  flatCategories,
  setShowExistingPartModal,
  setExistingPartDetails
}: UsePartNumberCheckProps) => {
  // Hook initialization
  console.log('🚀 usePartNumberCheck hook initialized');
  const checkPartExistence = async (partNumber: string) => {
    try {
      const supabase = createClient();

      // First check in part_to_group using maybeSingle to avoid 406 errors
      const { data: groupData, error: groupError } = await supabase
        .from('part_to_group')
        .select('partnumber, group_id')
        .eq('partnumber', partNumber)
        .maybeSingle();

      if (groupError) {
        console.error('Error checking part_to_group:', groupError);
        throw groupError;
      }

      if (groupData) {
        console.log(`✅ PART FOUND: "${partNumber}" exists in part_to_group table`);
        console.log("📊 Part details:", {
          partNumber: groupData.partnumber,
          groupId: groupData.group_id,
          table: 'part_to_group'
        });
        return {
          groupId: groupData.group_id,
          table: 'part_to_group' as const
        };
      }

      // If not found in part_to_group, check part_compatibility_groups
      const { data: compatibilityData, error: compatibilityError } = await supabase
        .from('part_compatibility_groups')
        .select('id')
        .eq('part_number', partNumber)
        .maybeSingle();

      if (compatibilityError) {
        console.error('Error checking part_compatibility_groups:', compatibilityError);
        throw compatibilityError;
      }

      if (compatibilityData) {
        console.log(`✅ PART FOUND: "${partNumber}" exists in part_compatibility_groups table`);
        console.log("📊 Part details:", {
          partNumber: partNumber,
          groupId: compatibilityData.id,
          table: 'part_compatibility_groups'
        });
        return {
          groupId: compatibilityData.id,
          table: 'part_compatibility_groups' as const
        };
      }

      console.log(`ℹ️ PART NOT FOUND: "${partNumber}" does not exist in either part_to_group or part_compatibility_groups tables`);
      return {
        groupId: null,
        table: null
      };
    } catch (error) {
      console.error('Error checking part existence:', error);
      throw error;
    }
  };

  // Function to fetch all valid car combinations with their trim IDs
  // Mapping functions to convert AI-generated values to database values
  const mapBrandToDatabase = (aiBrand: string): string | null => {
    const brandMap: { [key: string]: string } = {
      'VW': 'VW',
      'Volkswagen': 'VW',
      'VOLKSWAGEN': 'VW',
      'Audi': 'Audi',
      'AUDI': 'Audi',
      // Reject other brands
      'Skoda': null,
      'SKODA': null,
      'Seat': null,
      'SEAT': null
    };
    return brandMap[aiBrand] || null;
  };

  const mapGenerationToDatabase = (aiGeneration: string): string | null => {
    // Remove parentheses and content within them
    let cleanGeneration = aiGeneration.replace(/\s*\([^)]*\)/g, '').trim();

    // Valid generations from your database
    const validGenerations = [
      'Mk4', 'Mk5', 'Mk6', 'Mk7', 'Mk8', 'Mk1', 'Mk2', 'Mk3',
      'B6', 'B7', 'B8', 'B8.5', 'B9', 'Type 9C', 'Type 16',
      '8P', '8V', '8Y', 'C6', 'C7', 'C8', 'D3', 'D4', 'D5',
      '8J', '8S', 'Type 42', 'Type 4S', '7L', 'Mk2 7P',
      '5N Facelift', 'MK3 Facelift', '8R', '8R Facelift',
      '80A', '80A Facelift', '8U', '8U Facelift', 'F3'
    ];

    return validGenerations.includes(cleanGeneration) ? cleanGeneration : null;
  };

  const mapVariationToDatabase = (aiVariation: string): string | null => {
    // Handle combined variations by taking the first valid one
    const variationMap: { [key: string]: string } = {
      'Hatchback': 'Hatchback',
      'Sedan': 'Sedan',
      'Saloon': 'Sedan', // UK term for Sedan
      'SUV': 'SUV',
      'Coupe': 'Coupe',
      'Convertible': 'Convertible',
      'Van': 'Van',
      'Wagon/Variant': 'Wagon/Variant',
      'Wagon/Avant': 'Wagon/Avant',
      'Estate': 'Wagon/Variant', // UK term for Wagon
      // Handle combined variations
      'Sedan/Variant': 'Sedan',
      'Saloon/Estate': 'Sedan',
      'Hatchback/Sedan': 'Hatchback',
      'Hatchback/Cabriolet': 'Hatchback',
      'Hatchback/Estate': 'Hatchback',
      'Coupe/Roadster': 'Coupe',
      'Coupe/Convertible': 'Coupe',
      // Reject invalid variations
      'MPV': null,
      'Various': null
    };

    return variationMap[aiVariation] || null;
  };

  const mapTrimToDatabase = (aiTrim: string): string | null => {
    // Reject generic terms
    if (!aiTrim || aiTrim.includes('Various') || aiTrim.includes('Multiple') || aiTrim.includes('All')) {
      return 'Base'; // Default to Base trim
    }

    // Valid trims from your database (simplified list)
    const validTrims = [
      'Base', 'Trendline', 'Comfortline', 'Highline', 'R-Line', 'GTI', 'R',
      'S', 'SE', 'Life', 'Style', 'Pro', 'Business', 'Pure', 'GTX',
      'Sportback', 'Citycarver', 'SQ8', 'RSQ8', 'S line', 'Advanced',
      'Black Edition', 'TTS', 'TT RS', 'R8 V10', 'R8 V10 plus', 'R8 V8',
      'RS e-tron GT', 'SQ7', 'RSQ7', 'SQ5', 'RSQ3', 'RSQ3 Sportback',
      'S8', 'RS7', 'S6', 'RS6', 'RS5', 'S5', 'RS4', 'S4', 'S3', 'RS3',
      'S1', 'S Line', 'S-Line', 'R32', 'GTE'
    ];

    return validTrims.includes(aiTrim) ? aiTrim : 'Base';
  };

  const fetchValidCarCombinations = async () => {
    try {
      const supabase = createClient();

      // Use the fixed database function to get all car combinations
      const { data: carCombinations, error } = await supabase
        .rpc('get_all_car_combinations');

      if (error) {
        console.error('Error fetching car combinations:', error);
        return [];
      }

      // Transform to a more usable format with trim IDs
      const validCombinations = carCombinations?.map(item => ({
        trimId: item.trim_id,
        brand: item.brand,
        model: item.model,
        generation: item.generation,
        variation: item.variation,
        trim: item.trim_name,
        carString: `${item.brand} ${item.model} ${item.generation} ${item.variation} ${item.trim_name}`
      })) || [];

      console.log(`📊 Fetched ${validCombinations.length} valid car combinations from database`);

      // Debug: Log first few combinations to see exact database format
      console.log('🔍 First 3 database combinations:', validCombinations.slice(0, 3));

      // Check for any problematic data in the fetched combinations
      const hasParentheses = validCombinations.some(car => car.generation.includes('(') || car.generation.includes(')'));
      const hasSlashes = validCombinations.some(car => car.variation.includes('/'));
      const hasEmptyTrims = validCombinations.some(car => !car.trim || car.trim.trim() === '');

      console.log('🚨 Data quality check:');
      console.log('  - Has parentheses in generations:', hasParentheses);
      console.log('  - Has slashes in variations:', hasSlashes);
      console.log('  - Has empty trims:', hasEmptyTrims);

      return validCombinations;
    } catch (error) {
      console.error('Error fetching valid car combinations:', error);
      return [];
    }
  };

  const handlePartNumberCheck = async () => {
    console.log('🚀🚀🚀 HANDLE PART NUMBER CHECK CALLED - STARTING PROCESS 🚀🚀🚀');
    console.log('🔍 handlePartNumberCheck called');

    const values = getValues();
    const partNumber = values.partNumber;
    const selectedCategory = values.selectedCategory;

    console.log('📋 Form values:', { partNumber, selectedCategory });

    if (!partNumber) {
      console.log('❌ No part number provided, exiting');
      return;
    }

    try {
      // Set loading state
      setValue('isCheckingPartNumber', true);
      setCurrentTask('Checking part number...');
      
      // First check if part exists
      const existenceResult = await checkPartExistence(partNumber);
      
      if (existenceResult.groupId) {
        // Part exists, notify user and show modal
        setPartNumberExists(true);

        // Store the existing part details
        const partDetails = {
          groupId: existenceResult.groupId,
          table: existenceResult.table
        };

        if (setExistingPartDetails) {
          setExistingPartDetails(partDetails);
        }

        const tableSource = existenceResult.table === 'part_to_group' ? 'compatibility system' : 'parts catalog';
        const detailedMessage = `Part number "${partNumber}" already exists in the ${tableSource} (Group ID: ${existenceResult.groupId})`;

        setCurrentTask(detailedMessage);

        // Note: Error message is now displayed inline in the PartNumberSection component

        // Also log detailed information for debugging
        console.log(`🚨 PART EXISTS: "${partNumber}" found in ${existenceResult.table} with group ID ${existenceResult.groupId}`);

        // Show existing part modal if function is provided
        if (setShowExistingPartModal) {
          setShowExistingPartModal(true);
        }

        // Clear the loading state since we're done
        setValue('isCheckingPartNumber', false);
        setTimeout(() => {
          setCurrentTask('');
        }, 5000); // Longer timeout to show the detailed message
        return;
      }

      // If part doesn't exist, proceed with AI analysis
      setCurrentTask('Analyzing part compatibility...');

      // Check cache first (temporarily disabled for debugging)
      const cacheKey = `${partNumber}_${selectedCategory}_v2`; // v2 to bust old cache
      if (false && cache.has(cacheKey)) { // Temporarily disabled
        console.log('Using cached data for part number check');
        const cachedData = cache.get(cacheKey);
        setCompatibilityData(cachedData);
        setPartNumberExists(false);
        setExistingTrims([]);
        return;
      }

      // Fetch all valid car combinations from database
      setCurrentTask('Fetching valid car combinations...');
      const validCarCombinations = await fetchValidCarCombinations();

      console.log(`📊 Fetched ${validCarCombinations.length} valid car combinations`);
      console.log('Sample combinations:', validCarCombinations.slice(0, 5));

      // Log unique values to verify database relationships
      const uniqueBrands = [...new Set(validCarCombinations.map(car => car.brand))];
      const uniqueVariations = [...new Set(validCarCombinations.map(car => car.variation))];
      const uniqueTrims = [...new Set(validCarCombinations.map(car => car.trim))];
      console.log('🏢 Unique brands found:', uniqueBrands);
      console.log('📋 Unique variations found:', uniqueVariations);
      console.log('🏷️ Unique trims found:', uniqueTrims.slice(0, 20), '...');

      // Check if we have any VW/Audi combinations
      const vwAudiCombinations = validCarCombinations.filter(car => ['VW', 'Audi'].includes(car.brand));
      console.log(`🎯 VW/Audi combinations: ${vwAudiCombinations.length}/${validCarCombinations.length}`);

      if (vwAudiCombinations.length === 0) {
        console.error('🚫 No VW/Audi combinations found in database! Check the brand filter.');
        setCurrentTask('No valid VW/Audi combinations found in database.');
        return;
      }

      // Fetch sample engine data from database
      setCurrentTask('Fetching engine data...');
      const { data: engineData, error: engineError } = await supabase
        .from('engines')
        .select('engine_code, capacity, fuel_type')
        .limit(50);

      if (engineError) {
        console.error('Error fetching engine data:', engineError);
      }

      const sampleEngines = engineData || [];

      // Wait for rate limit
      await waitForRateLimit();

      const modelName = process.env.NEXT_PUBLIC_GEMINI_MODEL || 'gemini-2.0-flash';
      const model = genAI.getGenerativeModel({ model: `models/${modelName}` });

      const categoryDetails = flatCategories.find(cat => cat.id.toString() === selectedCategory);
      const categoryName = categoryDetails?.name || 'Unknown Category';
      
      // Filter to only VW/Audi combinations for the prompt
      const vwAudiOnly = validCarCombinations.filter(car => ['VW', 'Audi'].includes(car.brand));

      console.log(`🎯 VW/Audi combinations available: ${vwAudiOnly.length}`);
      console.log(`🎯 VW vehicles: ${vwAudiOnly.filter(car => car.brand === 'VW').length}`);
      console.log(`🎯 Audi vehicles: ${vwAudiOnly.filter(car => car.brand === 'Audi').length}`);

      if (vwAudiOnly.length === 0) {
        console.error('🚫 No VW/Audi combinations available!');
        setCurrentTask('No valid VW/Audi combinations found in database.');
        return;
      }

      // Check if this is a VW/Audi shared part based on common patterns
      const isSharedPart = partNumber.match(/^(1K|3C|5K|8P|8V|B6|B7|B8)/i) ||
                          categoryName.toLowerCase().includes('steering') ||
                          categoryName.toLowerCase().includes('radiator') ||
                          categoryName.toLowerCase().includes('brake');

      const prompt = `VEHICLE AND ENGINE COMPATIBILITY ANALYSIS

Part Number: ${partNumber}
Category: ${categoryName}
${isSharedPart ? '\n🚨 CRITICAL: This appears to be a VW/Audi shared platform part. You MUST include BOTH VW and Audi vehicles in your response.' : ''}

MANDATORY: You MUST ONLY use combinations from this exact list. NO OTHER COMBINATIONS ARE ALLOWED:

${vwAudiOnly.map(car =>
  `{"brand": "${car.brand}", "model": "${car.model}", "generation": "${car.generation}", "variation": "${car.variation}", "trim": "${car.trim}"}`
).join('\n')}

AVAILABLE ENGINE DATA (use realistic engine codes from this list):
${sampleEngines.slice(0, 30).map(engine =>
  `{"engineCode": "${engine.engine_code}", "capacity": "${engine.capacity}", "fuelType": "${engine.fuel_type}"}`
).join('\n')}

ABSOLUTE REQUIREMENTS - VIOLATION WILL RESULT IN REJECTION:
1. ONLY brands "VW" or "Audi" - NO Skoda, Seat, or any other brands
2. ONLY generations WITHOUT parentheses - NO "(1K)", "(3C)", "(5K)", etc.
3. ONLY single variations - NO "Sedan/Variant", "Hatchback/Cabriolet", "MPV", etc.
4. ONLY specific trim names - NO "Various trims", "Multiple", "All", etc.
5. COPY EXACTLY from the vehicle list above - NO modifications whatsoever
6. Use ONLY engine codes from the engine data list above
7. Each vehicle MUST be an EXACT MATCH to one line from the vehicle list above
8. BE COMPREHENSIVE - Include ALL relevant vehicles, not just a few examples
9. For shared platforms, include BOTH VW and Audi vehicles from the same platform

PLATFORM SHARING GUIDE (for comprehensive compatibility):
- PQ35 Platform: VW Golf Mk5/Mk6 ↔ Audi A3 8P (same parts often fit both)
- MQB Platform: VW Golf Mk7/Mk8 ↔ Audi A3 8V (same parts often fit both)
- B6/B7/B8 Platform: VW Passat B6/B7/B8 ↔ Audi A4 B7/B8 (same parts often fit both)
- PQ46 Platform: VW Passat B6/B7 ↔ Audi A4 B7 (same parts often fit both)

FORBIDDEN EXAMPLES (DO NOT USE):
- ❌ "VW Golf Mk5 (1K)" → Use "VW Golf Mk5"
- ❌ "Sedan/Variant" → Use "Sedan" OR "Wagon/Variant" (separate entries)
- ❌ "Various trims" → Use specific trim like "Base" or "GTI"
- ❌ "Skoda" or "Seat" → Only "VW" or "Audi"
- ❌ "MPV" → Use exact variation from list
- ❌ Made-up engine codes → Use only codes from the engine data list
- ❌ Limited selection → Include ALL relevant vehicles from both brands

TASK: Find ALL compatible vehicles from the EXACT list above that would be compatible with part "${partNumber}" in category "${categoryName}".

🚨 COMPREHENSIVE COMPATIBILITY REQUIREMENT:
- Include ALL relevant VW vehicles that would use this part
- Include ALL relevant Audi vehicles that would use this part
- Include ALL compatible part numbers (OEM numbers, alternative numbers, superseded numbers)
- Do NOT limit to just a few vehicles or part numbers - be comprehensive
- For shared platform parts, include ALL trims and variations from both brands
- Typical automotive parts have multiple OEM numbers and cross-references
- Include original part numbers, updated versions, and alternative suppliers

${isSharedPart ? '\n🚨 SHARED PLATFORM PART: This part is used across VW/Audi shared platforms. You MUST include:\n- ALL compatible VW vehicles (all trims: Base, GTI, R, S, SE, Comfortline, Highline, etc.)\n- ALL compatible Audi vehicles (all trims: Base, S3, S-Line, Sportback, etc.)\n- ALL compatible part numbers (original, superseded, alternative OEM numbers)\n- Platform examples: Golf Mk5/6 + A3 8P, Golf Mk7/8 + A3 8V, Passat B6/7/8 + A4 B7/8' : ''}

🚨 PART NUMBER REQUIREMENTS:
- Include the original part number: ${partNumber}
- Include ALL superseded/updated versions (e.g., if ${partNumber} was updated to newer versions)
- Include ALL alternative OEM numbers for the same part
- Include cross-reference numbers from different suppliers
- Typical parts have 3-8 compatible part numbers
- Examples: Original number, revision A/B/C, alternative suppliers, superseded versions

For engine compatibility:
- If this is an engine-related part (engine, transmission, cooling, fuel system, etc.), provide 2-5 realistic engine codes from the engine data list
- Use actual engine codes, capacities, and fuel types from the provided engine data
- Set "isEnginePart" to true for engine-related parts, false for body/exterior parts
- For engineType, use: "Naturally Aspirated", "Turbocharged", "Supercharged", or "Hybrid"

WARNING: Any response containing brands other than VW/Audi, parentheses in generations, combined variations, "Various trims", or non-existent engine codes will be completely rejected.

Return JSON format:
{
  "partName": "string",
  "compatiblePartNumbers": ["string"],
  "isEnginePart": boolean,
  "vehicleCompatibility": [
    {
      "brand": "string",
      "model": "string",
      "generation": "string",
      "variation": "string",
      "trim": "string"
    }
  ],
  "engineCompatibility": [
    {
      "engineCode": "string",
      "engineCapacity": number,
      "fuelType": "string",
      "engineType": "string"
    }
  ]
}

REMEMBER: Every vehicle must be an exact copy from the valid combinations list above, and every engine code must be from the engine data list above.`;

      const result = await model.generateContent(prompt);
      const text = result.response.text().replace(/```json/g, '').replace(/```/g, '').trim();
      console.log('🤖 AI Response received, length:', text.length);
      console.log('🤖 AI Response text:', text);

      try {
        console.log('🔄 Attempting to parse AI response as JSON...');
        const aiData = JSON.parse(text);
        console.log('✅ Successfully parsed AI data:', aiData);
        console.log('🔍 Raw AI vehicleCompatibility:', JSON.stringify(aiData.vehicleCompatibility, null, 2));
        console.log('🔍 Raw AI engineCompatibility:', JSON.stringify(aiData.engineCompatibility, null, 2));
        console.log('🔍 AI isEnginePart:', aiData.isEnginePart);

        // Process and validate vehicle compatibility against valid combinations
        console.log('🎯 STARTING VEHICLE COMPATIBILITY PROCESSING...');
        console.log('🔍 Checking if aiData has vehicleCompatibility:', !!aiData.vehicleCompatibility);
        console.log('🔍 Is vehicleCompatibility an array?', Array.isArray(aiData.vehicleCompatibility));
        console.log('🔍 vehicleCompatibility length:', aiData.vehicleCompatibility?.length);
        console.log('🔍 vehicleCompatibility type:', typeof aiData.vehicleCompatibility);

        if (aiData.vehicleCompatibility && Array.isArray(aiData.vehicleCompatibility)) {
          console.log('✅ CONDITION MET - Starting vehicle mapping process...');
          console.log('🔄 Processing AI-generated vehicles:', aiData.vehicleCompatibility);

          // Check if this is a shared part and validate brand diversity and coverage
          if (isSharedPart) {
            const brands = [...new Set(aiData.vehicleCompatibility.map((v: any) => v.brand))];
            const vwCount = aiData.vehicleCompatibility.filter((v: any) => v.brand === 'VW').length;
            const audiCount = aiData.vehicleCompatibility.filter((v: any) => v.brand === 'Audi').length;

            console.log('🔍 Brands in AI response:', brands);
            console.log('🔍 VW vehicles:', vwCount, 'Audi vehicles:', audiCount);

            if (brands.length === 1) {
              console.warn('⚠️ Shared part detected but AI only provided one brand:', brands[0]);
              console.warn('⚠️ This may indicate AI bias. Consider regenerating or manual review.');
            } else {
              console.log('✅ Good: AI provided multiple brands for shared part:', brands);

              if (vwCount < 2 || audiCount < 2) {
                console.warn('⚠️ Low vehicle count detected. VW:', vwCount, 'Audi:', audiCount);
                console.warn('⚠️ Consider regenerating for more comprehensive coverage.');
              } else {
                console.log('✅ Excellent: Comprehensive coverage with', vwCount, 'VW and', audiCount, 'Audi vehicles');
              }
            }
          } else {
            console.log('🔍 Total vehicles in response:', aiData.vehicleCompatibility.length);
            if (aiData.vehicleCompatibility.length < 3) {
              console.warn('⚠️ Low vehicle count for non-shared part. Consider more comprehensive coverage.');
            }
          }

          // Map AI-generated vehicles to valid database values
          const mappedVehicles = aiData.vehicleCompatibility.map((vehicle: any) => {
            console.log(`🔄 Starting mapping for vehicle:`, vehicle);

            const mappedBrand = mapBrandToDatabase(vehicle.brand);
            const mappedGeneration = mapGenerationToDatabase(vehicle.generation);
            const mappedVariation = mapVariationToDatabase(vehicle.variation);
            const mappedTrim = mapTrimToDatabase(vehicle.trim);

            console.log(`🔄 Individual mappings:`);
            console.log(`   Brand: "${vehicle.brand}" → "${mappedBrand}"`);
            console.log(`   Generation: "${vehicle.generation}" → "${mappedGeneration}"`);
            console.log(`   Variation: "${vehicle.variation}" → "${mappedVariation}"`);
            console.log(`   Trim: "${vehicle.trim}" → "${mappedTrim}"`);

            const mappedResult = {
              original: vehicle,
              mapped: {
                brand: mappedBrand,
                model: vehicle.model, // Keep model as-is for now
                generation: mappedGeneration,
                variation: mappedVariation,
                trim: mappedTrim
              }
            };

            console.log(`🔄 Final mapped result:`, mappedResult.mapped);
            return mappedResult;
          }).filter(item => {
            // Filter out vehicles where any mapping failed
            const isValid = item.mapped.brand && item.mapped.generation && item.mapped.variation && item.mapped.trim;
            if (!isValid) {
              console.warn('❌ Mapping failed for vehicle:', item.original);
              console.warn('❌ Mapped result:', item.mapped);
              console.warn('❌ Missing fields:', {
                brand: !item.mapped.brand,
                generation: !item.mapped.generation,
                variation: !item.mapped.variation,
                trim: !item.mapped.trim
              });
            } else {
              console.log('✅ Mapping successful for vehicle:', item.mapped);
            }
            return isValid;
          });

          // Now find exact database matches for the mapped vehicles
          const validVehicles = mappedVehicles.map(item => {
            // Find exact match in database
            const exactMatch = validCarCombinations.find(validCar =>
              validCar.brand === item.mapped.brand &&
              validCar.model === item.mapped.model &&
              validCar.generation === item.mapped.generation &&
              validCar.variation === item.mapped.variation &&
              validCar.trim === item.mapped.trim
            );

            if (exactMatch) {
              console.log('✅ Found exact database match:', exactMatch);
              return {
                brand: exactMatch.brand,
                model: exactMatch.model,
                generation: exactMatch.generation,
                variation: exactMatch.variation,
                trim: exactMatch.trim,
                trimId: exactMatch.trimId
              };
            } else {
              // Try to find a close match with Base trim if exact trim not found
              const baseMatch = validCarCombinations.find(validCar =>
                validCar.brand === item.mapped.brand &&
                validCar.model === item.mapped.model &&
                validCar.generation === item.mapped.generation &&
                validCar.variation === item.mapped.variation &&
                validCar.trim === 'Base'
              );

              if (baseMatch) {
                console.log('⚠️ Using Base trim fallback for:', item.original);
                return {
                  brand: baseMatch.brand,
                  model: baseMatch.model,
                  generation: baseMatch.generation,
                  variation: baseMatch.variation,
                  trim: baseMatch.trim,
                  trimId: baseMatch.trimId
                };
              } else {
                console.warn('❌ No database match found for mapped vehicle:', item.mapped);
                return null;
              }
            }
          }).filter(vehicle => vehicle !== null);

          console.log(`🔍 Vehicle processing: ${validVehicles.length}/${aiData.vehicleCompatibility.length} vehicles successfully mapped to database`);

          // If no valid vehicles remain, reject the response
          if (validVehicles.length === 0) {
            console.error('🚫 No valid vehicles found after mapping. Rejecting.');
            setCurrentTask('Could not map AI-generated vehicles to valid database combinations. Please try again.');
            return;
          }

          // Update the AI data with the mapped and validated vehicles
          // Remove trimId from the stored data since usePartSubmission expects the standard format
          const cleanedVehicles = validVehicles.map(vehicle => ({
            brand: vehicle.brand,
            model: vehicle.model,
            generation: vehicle.generation,
            variation: vehicle.variation,
            trim: vehicle.trim
          }));

          aiData.vehicleCompatibility = cleanedVehicles;
          console.log('✅ Final validated vehicles (cleaned for storage):', cleanedVehicles);
        } else {
          console.warn('🚫 Vehicle compatibility processing skipped - no valid vehicleCompatibility array found');
          console.warn('🚫 aiData structure:', Object.keys(aiData));
          console.warn('🚫 vehicleCompatibility value:', aiData.vehicleCompatibility);
        }

        // Validate and process engine compatibility data
        console.log('🎯 STARTING ENGINE COMPATIBILITY VALIDATION...');
        if (aiData.engineCompatibility && Array.isArray(aiData.engineCompatibility)) {
          console.log('✅ Engine compatibility data found:', aiData.engineCompatibility);

          // Validate that engine codes exist in our database
          const validEngines = [];
          for (const engine of aiData.engineCompatibility) {
            const engineExists = sampleEngines.find(dbEngine =>
              dbEngine.engine_code === engine.engineCode &&
              dbEngine.capacity === engine.engineCapacity.toString() &&
              dbEngine.fuel_type === engine.fuelType
            );

            if (engineExists) {
              console.log('✅ Valid engine found:', engine);
              validEngines.push(engine);
            } else {
              console.warn('❌ Invalid engine (not in database):', engine);
            }
          }

          aiData.engineCompatibility = validEngines;
          console.log(`✅ Final validated engines: ${validEngines.length}/${aiData.engineCompatibility.length} engines validated`);
        } else {
          console.warn('🚫 No engine compatibility data found or invalid format');
          console.warn('🚫 engineCompatibility value:', aiData.engineCompatibility);
          // Set empty array if no engine data
          aiData.engineCompatibility = [];
        }

        // Check if this should be an engine part but has no engine data
        if (aiData.isEnginePart && (!aiData.engineCompatibility || aiData.engineCompatibility.length === 0)) {
          console.warn('⚠️ Part marked as engine part but no valid engine compatibility data provided');
          console.warn('⚠️ This may indicate the AI prompt needs adjustment for this category:', categoryName);
        }

        // Log final engine compatibility summary
        console.log('🔧 Final engine compatibility summary:');
        console.log('   - Is engine part:', aiData.isEnginePart);
        console.log('   - Engine count:', aiData.engineCompatibility?.length || 0);
        console.log('   - Engine codes:', aiData.engineCompatibility?.map(e => e.engineCode).join(', ') || 'None');

        // Cache the result
        console.log('💾 Caching processed AI data with key:', cacheKey);
        cache.set(cacheKey, aiData);

        // Store the compatibility data in state
        console.log('📝 Setting compatibility data in state:', aiData);
        console.log('📝 Final vehicleCompatibility being stored:', JSON.stringify(aiData.vehicleCompatibility, null, 2));
        setCompatibilityData(aiData);

        console.log('Compatible part numbers from AI:', aiData.compatiblePartNumbers);
        console.log('Compatible part numbers count:', aiData.compatiblePartNumbers?.length || 0);

        // Validate part number comprehensiveness
        if (aiData.compatiblePartNumbers && Array.isArray(aiData.compatiblePartNumbers)) {
          if (aiData.compatiblePartNumbers.length < 2) {
            console.warn('⚠️ Low part number count detected:', aiData.compatiblePartNumbers.length);
            console.warn('⚠️ Most automotive parts have multiple compatible numbers. Consider regenerating.');
          } else {
            console.log('✅ Good part number coverage:', aiData.compatiblePartNumbers.length, 'compatible numbers');
          }

          // Also directly save the compatible part numbers to the database
          await saveCompatiblePartNumbers(partNumber, aiData.compatiblePartNumbers);
        } else {
          console.warn('⚠️ No compatible part numbers provided by AI');
        }

        // Store vehicle compatibility data for later use during part submission
        if (aiData.vehicleCompatibility && Array.isArray(aiData.vehicleCompatibility) && aiData.vehicleCompatibility.length > 0) {
          console.log('Vehicle compatibility data found:', aiData.vehicleCompatibility);
          // The vehicle compatibility will be used during part submission to save car data
        }
        
        setPartNumberExists(false);
        setExistingTrims([]);
      } catch (parseError) {
        console.error('Error parsing AI response:', parseError);
        setCurrentTask('Error parsing AI response');
        throw parseError;
      }
    } catch (error) {
      console.error('Error processing part number:', error);

      // Check if it's a quota exceeded error
      if (error instanceof Error && error.message.includes('quota')) {
        setCurrentTask('API quota exceeded. Please try again later.');
      } else if (error instanceof Error && error.message.includes('429')) {
        setCurrentTask('Rate limit exceeded. Please try again later.');
      } else {
        setCurrentTask('Error processing part number');
      }

      // Don't throw the error, just set the task message
      // throw error;
    } finally {
      // Clear loading state
      setValue('isCheckingPartNumber', false);
      // Don't clear the task immediately if there was an error, let user see the message
      setTimeout(() => {
        setCurrentTask('');
      }, 3000);
    }
  };

  // Function to directly save compatible part numbers to the database
  const saveCompatiblePartNumbers = async (mainPartNumber: string, compatiblePartNumbers: string[]) => {
    try {
      console.log('Directly saving compatible part numbers:', compatiblePartNumbers);
      
      // First, check if the main part number exists in part_to_group
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/part_to_group?select=group_id&partnumber=eq.${mainPartNumber}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
            'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!}`,
            'Accept': 'application/json'
          }
        }
      );
      
      if (!response.ok) {
        console.error('Error checking part in part_to_group:', response.status, response.statusText);
        return;
      }
      
      const existingParts = await response.json();
      let groupId: number;
      
      if (existingParts.length === 0) {
        // If not found in part_to_group, check part_compatibility_groups
        const compatGroupResponse = await fetch(
          `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/part_compatibility_groups?select=id&part_number=eq.${mainPartNumber}`,
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
              'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!}`,
              'Accept': 'application/json'
            }
          }
        );
        
        if (!compatGroupResponse.ok) {
          console.error('Error checking part in part_compatibility_groups:', compatGroupResponse.status, compatGroupResponse.statusText);
          // Create a new compatibility group if not found anywhere
          const createResponse = await fetch(
            `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/part_compatibility_groups`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
                'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!}`,
                'Prefer': 'return=representation'
              },
              body: JSON.stringify({ part_number: mainPartNumber })
            }
          );
          
          if (!createResponse.ok) {
            console.error('Error creating compatibility group:', createResponse.status, createResponse.statusText);
            return;
          }
          
          const newGroup = await createResponse.json();
          groupId = newGroup[0].id;
        } else {
          const existingGroups = await compatGroupResponse.json();
          if (existingGroups.length === 0) {
            // Create a new compatibility group
            const createResponse = await fetch(
              `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/part_compatibility_groups`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
                  'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!}`,
                  'Prefer': 'return=representation'
                },
                body: JSON.stringify({ part_number: mainPartNumber })
              }
            );
            
            if (!createResponse.ok) {
              console.error('Error creating compatibility group:', createResponse.status, createResponse.statusText);
              return;
            }
            
            const newGroup = await createResponse.json();
            groupId = newGroup[0].id;
          } else {
            groupId = existingGroups[0].id;
          }
        }
      } else {
        groupId = existingParts[0].group_id;
      }
      
      // Now add all compatible part numbers to the group
      const allPartNumbers = [mainPartNumber, ...compatiblePartNumbers];
      
      for (const partNumber of allPartNumbers) {
        // Check if the part number already exists in the table
        const checkResponse = await fetch(
          `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/part_to_group?select=partnumber,group_id&partnumber=eq.${partNumber}`,
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
              'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!}`,
              'Accept': 'application/json'
            }
          }
        );
        
        if (!checkResponse.ok) {
          console.error(`Error checking if part number ${partNumber} exists:`, checkResponse.status, checkResponse.statusText);
          continue;
        }
        
        const existingParts = await checkResponse.json();
        
        if (existingParts.length === 0) {
          // Insert the part number
          const insertResponse = await fetch(
            `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/part_to_group`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
                'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!}`,
                'Prefer': 'return=minimal'
              },
              body: JSON.stringify({
                partnumber: partNumber,
                group_id: groupId
              })
            }
          );
          
          if (!insertResponse.ok) {
            console.error(`Error inserting part number ${partNumber}:`, insertResponse.status, insertResponse.statusText);
          } else {
            console.log(`Successfully inserted part number ${partNumber} with group ID ${groupId}`);
          }
        } else if (existingParts[0].group_id !== groupId) {
          // Update the group ID if it's different
          const updateResponse = await fetch(
            `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/part_to_group?partnumber=eq.${partNumber}`,
            {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
                'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
                'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!}`,
                'Prefer': 'return=minimal'
              },
              body: JSON.stringify({
                group_id: groupId
              })
            }
          );
          
          if (!updateResponse.ok) {
            console.error(`Error updating part number ${partNumber}:`, updateResponse.status, updateResponse.statusText);
          } else {
            console.log(`Successfully updated part number ${partNumber} with group ID ${groupId}`);
          }
        } else {
          console.log(`Part number ${partNumber} already exists with correct group ID ${groupId}`);
        }
      }
    } catch (error) {
      console.error('Error saving compatible part numbers:', error);
    }
  };

  return { 
    handlePartNumberCheck
  };
};