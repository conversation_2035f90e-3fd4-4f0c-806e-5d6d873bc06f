import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '12', 10);
    const sort = searchParams.get('sort') || '';
    const filter = searchParams.get('filter') || '';
    const offset = (page - 1) * limit;

    console.log('Fetching parts with params:', { page, limit, offset, sort, filter });

    const supabase = createClient();

    // Start building the query
    let query = supabase
      .from('parts')
      .select(`
        *,
        part_compatibility_groups:partnumber_group(id, part_number)
      `, { count: 'exact' });

    // Apply sorting based on the sort parameter
    if (sort === 'newest') {
      query = query.order('createdAt', { ascending: false });
    } else {
      // Default sorting
      query = query.order('id', { ascending: false });
    }

    // Apply filtering if needed
    if (filter === 'reorder') {
      // For reorder filter, we need to join with parts_condition to check stock levels
      // This is a bit complex for the current query structure, so we'll fetch all parts first
      // and then filter them after getting conditions
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    // Execute the query
    console.log('Fetching parts...');
    const { data: partsWithGroups, error, count } = await query;

    if (error) {
      console.error('Error fetching parts:', error);
      return NextResponse.json(
        {
          error: `Failed to fetch parts: ${error.message}`,
          details: error
        },
        { status: 500 }
      );
    }

    // Now fetch images for these parts
    let partImages: Record<number, any[]> = {};
    let partConditions: Record<number, any[]> = {};

    if (partsWithGroups && partsWithGroups.length > 0) {
      const partIds = partsWithGroups.map(part => part.id);

      // Fetch images
      const { data: images, error: imagesError } = await supabase
        .from('part_images')
        .select('*')
        .in('part_id', partIds);

      if (imagesError) {
        console.error('Error fetching part images:', imagesError);
      } else if (images) {
        // Group images by part_id
        partImages = images.reduce((acc: Record<number, any[]>, img: any) => {
          const partId = img.part_id;
          if (!acc[partId]) {
            acc[partId] = [];
          }
          acc[partId].push(img);
          return acc;
        }, {});
      }

      // Fetch conditions and stock separately
      const { data: conditions, error: conditionsError } = await supabase
        .from('parts_condition')
        .select('*')
        .in('part_id', partIds);

      if (conditionsError) {
        console.error('Error fetching part conditions:', conditionsError);
      } else if (conditions) {
        // Group conditions by part_id
        partConditions = conditions.reduce((acc: Record<number, any[]>, condition: any) => {
          const partId = condition.part_id;
          if (!acc[partId]) {
            acc[partId] = [];
          }
          acc[partId].push(condition);
          return acc;
        }, {});
      }
    }

    // Transform the data to match our frontend model
    let transformedParts = partsWithGroups?.map(part => {
      // Find images for this part
      const images = partImages[part.id] || [];
      // Find the main image or use the first one
      const mainImage = images.find((img: any) => img.is_main_image === true) || images[0];
      // Get conditions for this part
      const conditions = partConditions[part.id] || [];
      // Calculate total stock
      const totalStock = conditions.length > 0
        ? conditions.reduce((total: number, condition: any) => total + (condition.stock || 0), 0)
        : 0;

      // Check if any condition is at or below reorder level
      const needsReorder = conditions.some(condition => {
        // If reorder_level is set, use it, otherwise use a default of 5
        const reorderLevel = condition.reorder_level || 5;
        return condition.stock > 0 && condition.stock <= reorderLevel;
      });

      return {
        id: part.id.toString(),
        title: part.title || 'Unnamed Part',
        partNumber: part.partnumber_group?.toString() || 'N/A',
        actualPartNumber: part.part_compatibility_groups?.part_number || 'N/A',
        stock: totalStock,
        needsReorder: needsReorder,
        conditions: conditions,
        thumbnailUrl: mainImage?.image_url || '',
        imageUrl: mainImage?.image_url || '',
        // Include all images for the part
        images: images.length > 0 ? images : undefined,
        // Include userId for Supabase Storage path handling
        userId: part.createdBy?.toString() || ''
      };
    }) || [];

    // Apply the reorder filter if needed
    if (filter === 'reorder') {
      transformedParts = transformedParts.filter(part => part.needsReorder);
    }

    // Calculate total pages
    const totalParts = count || 0;
    const totalPages = Math.ceil(totalParts / limit);

    console.log(`Successfully fetched ${transformedParts.length} parts`);

    return NextResponse.json({
      parts: transformedParts,
      totalParts,
      currentPage: page,
      totalPages
    });
  } catch (error) {
    console.error('Unexpected error in parts API:', error);
    return NextResponse.json(
      {
        error: 'An unexpected error occurred while fetching parts',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
