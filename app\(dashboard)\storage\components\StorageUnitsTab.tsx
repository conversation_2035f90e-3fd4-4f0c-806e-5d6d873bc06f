'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Search, RefreshCw, Edit, Trash2, Box, Layers, Grid, Clipboard, Truck } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { StorageUnit, StorageArea } from '../types';
import AddUnitModal from './modals/AddUnitModal';
import EditUnitModal from './modals/EditUnitModal';
import DeleteConfirmModal from './modals/DeleteConfirmModal';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

interface StorageUnitsTabProps {
  onRefresh: () => void;
}

const StorageUnitsTab: React.FC<StorageUnitsTabProps> = ({ onRefresh }) => {
  const [units, setUnits] = useState<StorageUnit[]>([]);
  const [areas, setAreas] = useState<StorageArea[]>([]);
  const [filteredUnits, setFilteredUnits] = useState<StorageUnit[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedUnit, setSelectedUnit] = useState<StorageUnit | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [selectedAreaFilter, setSelectedAreaFilter] = useState<number | 'all'>('all');

  const supabase = createClient();

  // Fetch storage units and areas
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch areas first
        const { data: areasData, error: areasError } = await supabase
          .from('storage_areas')
          .select('*')
          .order('name');

        if (areasError) throw areasError;
        setAreas(areasData || []);

        // Then fetch units with area information
        const { data: unitsData, error: unitsError } = await supabase
          .from('storage_units')
          .select('*')
          .order('identifier');

        if (unitsError) throw unitsError;

        // Enhance units with area name for display
        const enhancedUnits = unitsData?.map(unit => {
          const area = areasData?.find(a => a.area_id === unit.area_id);
          return {
            ...unit,
            area_name: area?.name || 'Unknown Area'
          };
        }) || [];

        setUnits(enhancedUnits);
        setFilteredUnits(enhancedUnits);
      } catch (error) {
        console.error('Error fetching storage data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [refreshTrigger, supabase]);

  // Filter units based on search query and area filter
  useEffect(() => {
    let filtered = units;
    
    // Apply area filter
    if (selectedAreaFilter !== 'all') {
      filtered = filtered.filter(unit => unit.area_id === selectedAreaFilter);
    }
    
    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        unit => 
          unit.identifier.toLowerCase().includes(query) ||
          unit.description?.toLowerCase().includes(query) ||
          unit.unit_type.toLowerCase().includes(query) ||
          unit.area_name?.toLowerCase().includes(query)
      );
    }

    setFilteredUnits(filtered);
  }, [searchQuery, units, selectedAreaFilter]);

  // Handle refresh
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
    onRefresh();
  };

  // Handle edit
  const handleEdit = (unit: StorageUnit) => {
    setSelectedUnit(unit);
    setIsEditModalOpen(true);
  };

  // Handle delete
  const handleDelete = (unit: StorageUnit) => {
    setSelectedUnit(unit);
    setIsDeleteModalOpen(true);
  };

  // Get icon for unit type
  const getUnitTypeIcon = (unitType: string) => {
    switch (unitType) {
      case 'shelf':
        return <Layers size={20} className="text-teal-600" />;
      case 'cage':
        return <Grid size={20} className="text-orange-600" />;
      case 'hanging_line':
        return <Clipboard size={20} className="text-gray-600" />;
      case 'open_space':
        return <Truck size={20} className="text-teal-600" />;
      case 'engine_area':
        return <Box size={20} className="text-orange-600" />;
      default:
        return <Box size={20} className="text-gray-600" />;
    }
  };

  // Format unit type for display
  const formatUnitType = (unitType: string) => {
    switch (unitType) {
      case 'shelf':
        return 'Shelf';
      case 'cage':
        return 'Cage';
      case 'hanging_line':
        return 'Hanging Line';
      case 'open_space':
        return 'Open Space';
      case 'engine_area':
        return 'Engine Area';
      default:
        return unitType;
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3
      }
    },
    hover: {
      y: -5,
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      transition: {
        duration: 0.2
      }
    }
  };

  return (
    <div>
      {/* Search and Actions */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
        <div className="flex flex-col md:flex-row gap-4 w-full md:w-auto">
          <div className="relative w-full md:w-96">
            <input
              type="text"
              placeholder="Search storage units..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
          </div>

          <select
            className="w-full md:w-64 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
            value={selectedAreaFilter === 'all' ? 'all' : selectedAreaFilter.toString()}
            onChange={(e) => setSelectedAreaFilter(e.target.value === 'all' ? 'all' : parseInt(e.target.value))}
          >
            <option value="all">All Areas</option>
            {areas.map(area => (
              <option key={area.area_id} value={area.area_id}>
                {area.name}
              </option>
            ))}
          </select>
        </div>

        <div className="flex gap-2 w-full md:w-auto">
          <button
            onClick={handleRefresh}
            className="flex items-center gap-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
          >
            <RefreshCw size={18} />
            <span>Refresh</span>
          </button>

          <button
            onClick={() => setIsAddModalOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <Plus size={18} />
            <span>Add Unit</span>
          </button>
        </div>
      </div>

      {/* Units Grid */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size={40} />
        </div>
      ) : filteredUnits.length > 0 ? (
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {filteredUnits.map((unit) => (
            <motion.div
              key={unit.unit_id}
              className="bg-white rounded-lg shadow-md overflow-hidden"
              variants={itemVariants}
              whileHover="hover"
            >
              <div className="p-5 border-b border-gray-100">
                <div className="flex justify-between items-start mb-3">
                  <div className="flex items-center">
                    <div className="p-2 rounded-md mr-3 bg-gray-100">
                      {getUnitTypeIcon(unit.unit_type)}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-800">{unit.identifier}</h3>
                      <p className="text-sm text-gray-500">{formatUnitType(unit.unit_type)}</p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleEdit(unit)}
                      className="p-1.5 rounded-md hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
                      aria-label="Edit unit"
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={() => handleDelete(unit)}
                      className="p-1.5 rounded-md hover:bg-gray-100 text-gray-500 hover:text-red-500 transition-colors"
                      aria-label="Delete unit"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-2 mb-3">
                  <div className="bg-gray-50 p-2 rounded-md">
                    <p className="text-xs text-gray-500">Storage Area</p>
                    <p className="text-sm font-medium text-gray-700">
                      {unit.area_name}
                    </p>
                  </div>
                </div>

                {unit.description && (
                  <div className="bg-gray-50 p-2 rounded-md">
                    <p className="text-xs text-gray-500">Description</p>
                    <p className="text-sm text-gray-700 line-clamp-2">{unit.description}</p>
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </motion.div>
      ) : (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <h3 className="text-xl font-semibold text-gray-700 mb-2">No storage units found</h3>
          <p className="text-gray-500 mb-4">
            {searchQuery || selectedAreaFilter !== 'all' 
              ? 'No units match your search criteria.' 
              : 'Start by adding a new storage unit.'}
          </p>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <Plus size={18} />
            <span>Add Storage Unit</span>
          </button>
        </div>
      )}

      {/* Add Unit Modal */}
      <AddUnitModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSuccess={handleRefresh}
        areas={areas}
      />

      {/* Edit Unit Modal */}
      {selectedUnit && (
        <EditUnitModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          unit={selectedUnit}
          onSuccess={handleRefresh}
          areas={areas}
        />
      )}

      {/* Delete Confirmation Modal */}
      {selectedUnit && (
        <DeleteConfirmModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          itemId={selectedUnit.unit_id}
          itemName={selectedUnit.identifier}
          itemType="unit"
          onSuccess={handleRefresh}
        />
      )}
    </div>
  );
};

export default StorageUnitsTab;
