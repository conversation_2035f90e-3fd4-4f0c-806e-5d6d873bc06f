'use client';

import { createContext, useContext, useState, useEffect, ReactNode, useMemo } from 'react';
import { useAuth } from '@/app/hooks/useAuth';
import { checkPermission } from '@/app/libs/rbac/api';

type PermissionCache = {
  [key: string]: {
    hasPermission: boolean;
    timestamp: number;
  };
};

interface PermissionContextType {
  checkPermission: (permissionName: string) => Promise<boolean>;
  getPermissionSync: (permissionName: string) => boolean | null;
  clearCache: () => void;
  isLoading: boolean;
}

const PermissionContext = createContext<PermissionContextType | undefined>(undefined);

// Cache expiration time (30 minutes)
const CACHE_EXPIRATION = 30 * 60 * 1000;

export function PermissionProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth();
  const [permissionCache, setPermissionCache] = useState<PermissionCache>({});
  const [isLoading, setIsLoading] = useState(false);

  // Clear cache when user changes
  useEffect(() => {
    setPermissionCache({});
  }, [user?.id]);

  // Check if a permission is in the cache and still valid
  const isPermissionCached = (permissionName: string): boolean => {
    const cachedPermission = permissionCache[permissionName];
    if (!cachedPermission) return false;
    
    const now = Date.now();
    return (now - cachedPermission.timestamp) < CACHE_EXPIRATION;
  };

  // Get a permission synchronously from cache (returns null if not cached)
  const getPermissionSync = (permissionName: string): boolean | null => {
    if (!user) return false;
    
    const cachedPermission = permissionCache[permissionName];
    if (!cachedPermission) return null;
    
    const now = Date.now();
    if ((now - cachedPermission.timestamp) >= CACHE_EXPIRATION) {
      return null;
    }
    
    return cachedPermission.hasPermission;
  };

  // Check permission (with caching)
  const checkPermissionWithCache = async (permissionName: string): Promise<boolean> => {
    if (!user) return false;
    
    // Return from cache if available
    if (isPermissionCached(permissionName)) {
      return permissionCache[permissionName].hasPermission;
    }
    
    // Otherwise fetch from API
    setIsLoading(true);
    try {
      console.log(`PermissionContext: Checking permission ${permissionName} for user ${user.id}`);
      const { data, error } = await checkPermission(user.id, permissionName);
      
      if (error) {
        console.error('Error checking permission:', error);
        return false;
      }
      
      // Update cache
      setPermissionCache(prev => ({
        ...prev,
        [permissionName]: {
          hasPermission: !!data,
          timestamp: Date.now()
        }
      }));
      
      return !!data;
    } catch (err) {
      console.error('Error checking permission:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Clear the permission cache
  const clearCache = () => {
    setPermissionCache({});
  };

  // Create the context value
  const contextValue = useMemo(() => ({
    checkPermission: checkPermissionWithCache,
    getPermissionSync,
    clearCache,
    isLoading
  }), [permissionCache, isLoading, user?.id]);

  return (
    <PermissionContext.Provider value={contextValue}>
      {children}
    </PermissionContext.Provider>
  );
}

export function usePermissionContext() {
  const context = useContext(PermissionContext);
  if (context === undefined) {
    throw new Error('usePermissionContext must be used within a PermissionProvider');
  }
  return context;
}
