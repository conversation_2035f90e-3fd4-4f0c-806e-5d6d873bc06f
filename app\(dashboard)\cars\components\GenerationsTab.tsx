'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Plus, Search, RefreshCw, Edit, Trash2, Calendar, Tag } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { Generation, Model, Brand } from '../types';
import AddGenerationModal from './modals/AddGenerationModal';
import EditGenerationModal from './modals/EditGenerationModal';
import DeleteConfirmModal from './modals/DeleteConfirmModal';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

interface GenerationsTabProps {
  onGenerationUpdated: () => void;
}

const GenerationsTab: React.FC<GenerationsTabProps> = ({ onGenerationUpdated }) => {
  const [generations, setGenerations] = useState<Generation[]>([]);
  const [filteredGenerations, setFilteredGenerations] = useState<Generation[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedBrandId, setSelectedBrandId] = useState<number | ''>('');
  const [selectedModelId, setSelectedModelId] = useState<number | ''>('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedGeneration, setSelectedGeneration] = useState<Generation | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const supabase = createClient();

  // Fetch brands from Supabase
  useEffect(() => {
    const fetchBrands = async () => {
      try {
        const { data, error } = await supabase
          .from('car_brands')
          .select('*')
          .order('brand_name');

        if (error) throw error;

        setBrands(data || []);
      } catch (error) {
        console.error('Error fetching brands:', error);
      }
    };

    fetchBrands();
  }, [supabase]);

  // Fetch models based on selected brand
  useEffect(() => {
    const fetchModels = async () => {
      try {
        let query = supabase
          .from('car_models')
          .select('*')
          .order('model_name');
          
        if (selectedBrandId) {
          query = query.eq('brand_id', selectedBrandId);
        }
        
        const { data, error } = await query;

        if (error) throw error;

        setModels(data || []);
        
        // Reset selected model if it doesn't belong to the selected brand
        if (selectedBrandId && selectedModelId) {
          const modelExists = data?.some(model => model.id === selectedModelId);
          if (!modelExists) {
            setSelectedModelId('');
          }
        }
      } catch (error) {
        console.error('Error fetching models:', error);
      }
    };

    fetchModels();
  }, [refreshTrigger, selectedBrandId, supabase]);

  // Fetch generations from Supabase
  useEffect(() => {
    const fetchGenerations = async () => {
      setIsLoading(true);
      try {
        let query = supabase
          .from('car_generation')
          .select('*')
          .order('name');
          
        if (selectedModelId) {
          query = query.eq('model_id', selectedModelId);
        }
        
        const { data, error } = await query;

        if (error) throw error;

        setGenerations(data || []);
        setFilteredGenerations(data || []);
      } catch (error) {
        console.error('Error fetching generations:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchGenerations();
  }, [refreshTrigger, selectedModelId, supabase]);

  // Filter generations based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredGenerations(generations);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = generations.filter(generation => 
      generation.name.toLowerCase().includes(query)
    );
    setFilteredGenerations(filtered);
  }, [searchQuery, generations]);

  // Refresh generations
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Get model name by ID
  const getModelName = (modelId: number) => {
    const model = models.find(m => m.id === modelId);
    return model ? model.model_name : 'Unknown Model';
  };

  // Get brand name by model ID
  const getBrandNameByModelId = (modelId: number) => {
    const model = models.find(m => m.id === modelId);
    if (!model) return 'Unknown Brand';
    
    const brand = brands.find(b => b.brand_id === model.brand_id);
    return brand ? brand.brand_name : 'Unknown Brand';
  };

  // Open edit modal
  const handleEdit = (generation: Generation) => {
    setSelectedGeneration(generation);
    setIsEditModalOpen(true);
  };

  // Open delete modal
  const handleDelete = (generation: Generation) => {
    setSelectedGeneration(generation);
    setIsDeleteModalOpen(true);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.3,
        ease: "easeOut"
      }
    },
    hover: {
      y: -5,
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      transition: { 
        duration: 0.2
      }
    }
  };

  return (
    <div>
      {/* Search and Actions */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
        <div className="flex flex-col md:flex-row gap-4 w-full md:w-auto">
          <div className="relative w-full md:w-64">
            <input
              type="text"
              placeholder="Search generations..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
          </div>
          
          <div className="w-full md:w-48">
            <select
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
              value={selectedBrandId}
              onChange={(e) => {
                setSelectedBrandId(e.target.value ? Number(e.target.value) : '');
                setSelectedModelId('');
              }}
            >
              <option value="">All Brands</option>
              {brands.map((brand) => (
                <option key={brand.brand_id} value={brand.brand_id}>
                  {brand.brand_name}
                </option>
              ))}
            </select>
          </div>
          
          <div className="w-full md:w-48">
            <select
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
              value={selectedModelId}
              onChange={(e) => setSelectedModelId(e.target.value ? Number(e.target.value) : '')}
              disabled={!selectedBrandId}
            >
              <option value="">All Models</option>
              {models.map((model) => (
                <option key={model.id} value={model.id}>
                  {model.model_name}
                </option>
              ))}
            </select>
          </div>
        </div>
        
        <div className="flex gap-2 w-full md:w-auto">
          <button
            onClick={handleRefresh}
            className="flex items-center gap-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
          >
            <RefreshCw size={18} />
            <span>Refresh</span>
          </button>
          
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <Plus size={18} />
            <span>Add Generation</span>
          </button>
        </div>
      </div>
      
      {/* Generations Grid */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size={40} />
        </div>
      ) : filteredGenerations.length > 0 ? (
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {filteredGenerations.map((generation) => (
            <motion.div
              key={generation.id}
              className="bg-white rounded-lg shadow-md p-6"
              variants={itemVariants}
              whileHover="hover"
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-xl font-semibold text-gray-800">{generation.name}</h3>
                  <div className="flex items-center mt-1">
                    <Tag size={16} className="text-teal-600 mr-2" />
                    <span className="text-sm text-gray-600">
                      {getBrandNameByModelId(generation.model_id)} {getModelName(generation.model_id)}
                    </span>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleEdit(generation)}
                    className="p-2 rounded-md hover:bg-gray-100 text-gray-500 hover:text-teal-600 transition-colors"
                    aria-label="Edit generation"
                  >
                    <Edit size={18} />
                  </button>
                  <button
                    onClick={() => handleDelete(generation)}
                    className="p-2 rounded-md hover:bg-gray-100 text-gray-500 hover:text-red-600 transition-colors"
                    aria-label="Delete generation"
                  >
                    <Trash2 size={18} />
                  </button>
                </div>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg mb-4">
                <div className="flex items-center">
                  <Calendar size={18} className="text-orange-500 mr-2" />
                  <span className="text-gray-700">
                    {generation.start_production_year} - {generation.end_production_year || 'Present'}
                  </span>
                </div>
              </div>
              
              <div className="mt-4 pt-4 border-t border-gray-100">
                <p className="text-sm text-gray-500">Generation ID: {generation.id}</p>
              </div>
            </motion.div>
          ))}
        </motion.div>
      ) : (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <h3 className="text-xl font-semibold text-gray-700 mb-2">No generations found</h3>
          <p className="text-gray-500 mb-4">
            {searchQuery || selectedModelId ? 'No generations match your search criteria.' : 'Start by adding a new generation.'}
          </p>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <Plus size={18} />
            <span>Add Generation</span>
          </button>
        </div>
      )}
      
      {/* Add Generation Modal */}
      <AddGenerationModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        models={models}
        brands={brands}
        onSuccess={() => {
          handleRefresh();
          onGenerationUpdated();
        }}
      />
      
      {/* Edit Generation Modal */}
      {selectedGeneration && (
        <EditGenerationModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          generation={selectedGeneration}
          models={models}
          brands={brands}
          onSuccess={() => {
            handleRefresh();
            onGenerationUpdated();
          }}
        />
      )}
      
      {/* Delete Confirmation Modal */}
      {selectedGeneration && (
        <DeleteConfirmModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          itemId={selectedGeneration.id}
          itemName={selectedGeneration.name}
          itemType="generation"
          tableName="car_generation"
          idField="id"
          onSuccess={() => {
            handleRefresh();
            onGenerationUpdated();
          }}
        />
      )}
    </div>
  );
};

export default GenerationsTab;
