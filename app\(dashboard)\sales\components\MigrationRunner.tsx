'use client';

import React, { useState } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { toast } from 'react-hot-toast';
import { Database, Play, CheckCircle, AlertCircle } from 'lucide-react';

export default function MigrationRunner() {
  const [isRunning, setIsRunning] = useState(false);
  const [migrationStatus, setMigrationStatus] = useState<'idle' | 'running' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const runMigration = async () => {
    setIsRunning(true);
    setMigrationStatus('running');
    setErrorMessage(null);

    try {
      const supabase = createClient();

      // SQL to add created_by column
      const migrationSQL = `
        -- Add created_by column to sales table
        DO $$ 
        BEGIN
            IF NOT EXISTS (
                SELECT 1 
                FROM information_schema.columns 
                WHERE table_name = 'sales' 
                AND column_name = 'created_by'
                AND table_schema = 'public'
            ) THEN
                -- Add the created_by column
                ALTER TABLE public.sales 
                ADD COLUMN created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL;
                
                -- Add an index for better performance
                CREATE INDEX IF NOT EXISTS idx_sales_created_by ON public.sales(created_by);
                
                -- Add a comment to document the column
                COMMENT ON COLUMN public.sales.created_by IS 'User ID of the staff member who created this sale';
                
                RAISE NOTICE 'Successfully added created_by column to sales table';
            ELSE
                RAISE NOTICE 'created_by column already exists in sales table';
            END IF;
        END $$;
      `;

      // Execute the migration
      const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });

      if (error) {
        // If exec_sql doesn't exist, try alternative approach
        if (error.code === 'PGRST301' || error.message.includes('function exec_sql')) {
          console.log('exec_sql function not found, trying alternative approach...');

          // Try to check if column already exists by trying to select it
          try {
            const { error: checkError } = await supabase
              .from('sales')
              .select('created_by')
              .limit(1);

            if (checkError && checkError.code === '42703') {
              // Column doesn't exist, provide manual instructions
              throw new Error(`The exec_sql function is not available. Please run the migration manually:

1. Go to your Supabase Dashboard → SQL Editor
2. Copy and paste the SQL from: app/(dashboard)/sales/migrations/manual_migration.sql
3. Execute the SQL commands one by one
4. Refresh this page and try the PDF generation

The key SQL command is:
ALTER TABLE public.sales ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL;`);
            } else {
              // Column already exists or other error
              if (!checkError) {
                setMigrationStatus('success');
                toast.success('The created_by column already exists in the sales table.');
                return;
              } else {
                throw new Error(`Unable to verify column existence: ${checkError.message}`);
              }
            }
          } catch (altError) {
            throw altError; // Re-throw the detailed error message
          }
        } else {
          throw new Error(`Migration failed: ${error.message}`);
        }
      }

      setMigrationStatus('success');
      toast.success('Migration completed successfully! The created_by column has been added to the sales table.');
    } catch (error) {
      console.error('Migration error:', error);
      const errorMsg = error instanceof Error ? error.message : 'Unknown error occurred';
      setErrorMessage(errorMsg);
      setMigrationStatus('error');
      toast.error(`Migration failed: ${errorMsg}`);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = () => {
    switch (migrationStatus) {
      case 'running':
        return <Database className="h-5 w-5 text-blue-600 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Database className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusText = () => {
    switch (migrationStatus) {
      case 'running':
        return 'Running migration...';
      case 'success':
        return 'Migration completed successfully';
      case 'error':
        return 'Migration failed';
      default:
        return 'Ready to run migration';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
      <div className="flex items-center mb-4">
        <Database className="h-6 w-6 text-blue-600 mr-2" />
        <h3 className="text-lg font-medium text-gray-900">Database Migration</h3>
      </div>
      
      <div className="space-y-4">
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Migration: Add created_by Column</h4>
          <p className="text-sm text-blue-700 mb-3">
            This migration will add a <code className="bg-blue-100 px-1 rounded">created_by</code> column
            to the sales table to track which staff member created each sale.
          </p>
          <div className="bg-blue-100 border border-blue-300 rounded p-3">
            <p className="text-xs text-blue-800 font-medium mb-2">Manual Migration Option:</p>
            <p className="text-xs text-blue-700">
              If the automatic migration fails, you can run the SQL manually in your Supabase SQL Editor.
              Check the file: <code className="bg-blue-200 px-1 rounded text-xs">app/(dashboard)/sales/migrations/manual_migration.sql</code>
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {getStatusIcon()}
          <span className="text-sm text-gray-700">{getStatusText()}</span>
        </div>

        {errorMessage && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-700">{errorMessage}</p>
          </div>
        )}

        <div className="flex space-x-3">
          <button
            onClick={runMigration}
            disabled={isRunning || migrationStatus === 'success'}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Play className="h-4 w-4 mr-2" />
            {isRunning ? 'Running...' : 'Run Migration'}
          </button>
        </div>
      </div>
    </div>
  );
}
