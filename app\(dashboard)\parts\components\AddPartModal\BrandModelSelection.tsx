'use client';

import React from 'react';
import { Control, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { Controller } from 'react-hook-form';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/app/components/ui/inputs/Select';
import { useBrandsAndModels } from '../../hooks/useBrandsAndModels';
import AnimatedEllipsisLoader from '@/app/components/ui/AnimatedEllipsisLoader';

interface BrandModelSelectionProps {
  control: Control<any>;
  watch: UseFormWatch<any>;
  setValue: UseFormSetValue<any>;
  onTrimSelect?: (trimId: string | null) => void;
  loadingStates?: {
    brands: boolean;
    models: boolean;
    generations: boolean;
    variations: boolean;
    trims: boolean;
  };
}

const BrandModelSelection: React.FC<BrandModelSelectionProps> = ({
  control,
  watch,
  setValue,
  onTrimSelect,
  loadingStates = {
    brands: false,
    models: false,
    generations: false,
    variations: false,
    trims: false
  }
}) => {
  const {
    brands,
    models,
    generations,
    variations,
    trims,
    handleBrandChange,
    handleModelChange,
    handleGenerationChange,
    handleVariationChange,
    handleTrimChange,
  } = useBrandsAndModels({ setValue, watch });

  // Modified trim change handler to call onTrimSelect if provided
  const handleTrimSelection = (value: string) => {
    handleTrimChange(value);
    if (onTrimSelect) {
      onTrimSelect(value);
    }
  };

  return (
    <div className="space-y-4">
      {/* Brand Selection */}
      <Controller
        name="brandId"
        control={control}
        render={({ field }) => (
          <>
            <Select
              value={field.value || ''}
              onValueChange={(value) => {
                field.onChange(value);
                handleBrandChange(value);
              }}
              disabled={loadingStates.brands}
            >
              <SelectTrigger 
                className="w-full" 
                placeholder="Select a brand"
              />
              <SelectContent>
                {brands.map((brand) => (
                  <SelectItem 
                    key={brand.id} 
                    value={brand.id.toString()}
                  >
                    {brand.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {loadingStates.brands && (
              <div className="mt-2">
                <AnimatedEllipsisLoader 
                  text="Loading brands"
                  bgColor="#f3f4f6"
                  textColor="#4b5563"
                />
              </div>
            )}
          </>
        )}
      />

      {/* Model Selection */}
      {models.length > 0 && (
        <Controller
          name="modelId"
          control={control}
          render={({ field }) => (
            <>
              <Select
                value={field.value || ''}
                onValueChange={(value) => {
                  field.onChange(value);
                  handleModelChange(value);
                }}
                disabled={loadingStates.models}
              >
                <SelectTrigger
                  className="w-full"
                  placeholder="Select a model"
                />
                <SelectContent>
                  {models.map((model) => (
                    <SelectItem 
                      key={model.id} 
                      value={model.id.toString()}
                    >
                      {model.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {loadingStates.models && (
                <div className="mt-2">
                  <AnimatedEllipsisLoader 
                    text="Loading models"
                    bgColor="#f3f4f6"
                    textColor="#4b5563"
                  />
                </div>
              )}
            </>
          )}
        />
      )}

      {/* Generation Selection */}
      {generations.length > 0 && (
        <Controller
          name="generationId"
          control={control}
          render={({ field }) => (
            <>
              <Select
                value={field.value || ''}
                onValueChange={(value) => {
                  field.onChange(value);
                  handleGenerationChange(value);
                }}
                disabled={loadingStates.generations}
              >
                <SelectTrigger
                  className="w-full"
                  placeholder="Select a generation"
                />
                <SelectContent>
                  {generations.map((generation) => (
                    <SelectItem 
                      key={generation.id} 
                      value={generation.id.toString()}
                    >
                      {generation.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {loadingStates.generations && (
                <div className="mt-2">
                  <AnimatedEllipsisLoader 
                    text="Loading generations"
                    bgColor="#f3f4f6"
                    textColor="#4b5563"
                  />
                </div>
              )}
            </>
          )}
        />
      )}

      {/* Variation Selection */}
      {variations.length > 0 && (
        <Controller
          name="variationId"
          control={control}
          render={({ field }) => (
            <>
              <Select
                value={field.value || ''}
                onValueChange={(value) => {
                  field.onChange(value);
                  handleVariationChange(value);
                  // Also call onTrimSelect with variation ID if no trim is selected
                  if (onTrimSelect && (!watch('trimId') || watch('trimId') === '')) {
                    onTrimSelect(value);
                  }
                }}
                disabled={loadingStates.variations}
              >
                <SelectTrigger
                  className="w-full"
                  placeholder="Select a variation"
                />
                <SelectContent>
                  {variations.map((variation) => (
                    <SelectItem 
                      key={variation.id} 
                      value={variation.id.toString()}
                    >
                      {variation.variation}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {loadingStates.variations && (
                <div className="mt-2">
                  <AnimatedEllipsisLoader 
                    text="Loading variations"
                    bgColor="#f3f4f6"
                    textColor="#4b5563"
                  />
                </div>
              )}
            </>
          )}
        />
      )}

      {/* Trim Selection */}
      {trims.length > 0 && (
        <Controller
          name="trimId"
          control={control}
          render={({ field }) => (
            <>
              <Select
                value={field.value || ''}
                onValueChange={(value) => {
                  field.onChange(value);
                  handleTrimSelection(value);
                }}
                disabled={loadingStates.trims}
              >
                <SelectTrigger
                  className="w-full"
                  placeholder="Select a trim"
                />
                <SelectContent>
                  {trims.map((trim) => (
                    <SelectItem 
                      key={trim.id} 
                      value={trim.id.toString()}
                    >
                      {trim.trim}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {loadingStates.trims && (
                <div className="mt-2">
                  <AnimatedEllipsisLoader 
                    text="Loading trims"
                    bgColor="#f3f4f6"
                    textColor="#4b5563"
                  />
                </div>
              )}
            </>
          )}
        />
      )}
    </div>
  );
};

export default BrandModelSelection;