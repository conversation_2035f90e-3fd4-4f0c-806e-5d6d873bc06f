import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Send } from 'lucide-react';
import Spinner from '../Spinner';

interface ButtonedInputProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onSubmit?: () => void;
  disabled?: boolean;
  buttonText?: string;
  className?: string;
  isLoading?: boolean;
}

const ButtonedInput: React.FC<ButtonedInputProps> = ({
  placeholder = 'Enter text...',
  value = '',
  onChange,
  onSubmit,
  disabled = false,
  buttonText = 'Submit',
  className = '',
  isLoading = false,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = () => {
    console.log('🔘 ButtonedInput handleSubmit called', { disabled, hasOnSubmit: !!onSubmit });
    if (onSubmit && !disabled) {
      console.log('✅ Calling onSubmit function');
      onSubmit();
    } else {
      console.log('❌ Not calling onSubmit:', { disabled, hasOnSubmit: !!onSubmit });
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !disabled) {
      handleSubmit();
    }
  };

  return (
    <div className={`relative flex w-full ${className}`}>
      {/* Input Container */}
      <div className="relative flex-[2]">
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onKeyPress={handleKeyPress}
          disabled={disabled}
          className={`
            w-full h-12 pl-4 pr-2 text-gray-900 border border-r-0 rounded-l-lg
            focus:outline-none focus:ring-0
            transition-colors duration-200
            ${isFocused || value ? 'pt-4 pb-2' : 'py-3'}
            ${
              isFocused
                ? 'border-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:border-blue-500'
                : 'border-gray-300 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400'
            }
            ${disabled ? 'bg-gray-100 dark:bg-gray-600' : ''}
            dark:text-white
          `}
        />
        <AnimatePresence>
          <motion.label
            initial={false}
            animate={{
              opacity: 1,
              scale: isFocused || value ? 0.75 : 1,
            }}
            transition={{ duration: 0.2 }}
            className={`absolute transition-all duration-300 ease-in-out ${
              isFocused || value
                ? 'text-sm -top-0 left-0 px-1 pt-1 bg-white dark:bg-gray-800 z-10'
                : 'text-m top-1/4 -translate-y-1/2 left-4'
            } text-gray-500 dark:text-gray-400 pointer-events-none`}
          >
            {placeholder}
          </motion.label>
        </AnimatePresence>
      </div>

      {/* Button */}
      <motion.button
        type="button"
        onClick={handleSubmit}
        disabled={disabled || isLoading}
        whileTap={{ scale: 0.98 }}
        className="flex-[1] h-12 px-6 text-sm font-medium text-white bg-indigo-600 rounded-r-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:bg-gray-300 disabled:cursor-not-allowed"
      >
        <div className="flex items-center justify-center">
          {isLoading ? (
            <Spinner size="sm" color="text-white" extraClasses="mr-2" />
          ) : (
            <Send className="w-4 h-4 mr-2" />
          )}
          {buttonText}
        </div>
      </motion.button>
    </div>
  );
};

export default ButtonedInput; 