'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Edit, Trash2, ChevronDown, ChevronUp, FolderTree, Tag, ExternalLink } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { CategoryWithChildren } from '../types';
import EditCategoryModal from './EditCategoryModal';
import DeleteConfirmModal from './DeleteConfirmModal';
import BatchRenameModal from './BatchRenameModal';
import SubcategoryItem from './SubcategoryItem';
import Link from 'next/link';

interface CategoryCardProps {
  category: CategoryWithChildren;
  onRefresh: () => void;
  onUpdateCategory?: (updatedCategory: any) => void;
  isExpanded: boolean;
  onToggleExpand: () => void;
  expandedSubcategories: Record<number, Record<number, boolean>>;
  onToggleSubcategoryExpand: (parentId: number, childId: number) => void;
  onShowBatchRename: (categoryId: number, categoryName: string) => void;
}

const CategoryCard: React.FC<CategoryCardProps> = ({
  category: initialCategory,
  onRefresh,
  isExpanded,
  onToggleExpand,
  expandedSubcategories,
  onToggleSubcategoryExpand,
  onShowBatchRename
}) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [activeModalTab, setActiveModalTab] = useState<string | number>('general');
  const [category, setCategory] = useState(initialCategory);

  // Update local state when initialCategory changes
  useEffect(() => {
    setCategory(initialCategory);
  }, [initialCategory]);

  const hasChildren = category.children && category.children.length > 0;

  // Memoize the toggle function for each subcategory
  const createToggleFunction = useCallback((subcategoryId: number) => {
    return () => onToggleSubcategoryExpand(category.id, subcategoryId);
  }, [category.id, onToggleSubcategoryExpand]);

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    },
    hover: {
      // Only apply hover effects when modal is not open to prevent stacking context issues
      y: isEditModalOpen ? 0 : -5,
      boxShadow: isEditModalOpen
        ? "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)"
        : "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      transition: {
        duration: 0.2
      }
    }
  };

  const childrenVariants = {
    hidden: { opacity: 0, height: 0 },
    visible: {
      opacity: 1,
      height: "auto",
      transition: {
        duration: 0.3,
        ease: "easeInOut"
      }
    }
  };

  return (
    <>
      <motion.div
        className="bg-white rounded-lg shadow-md overflow-hidden"
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        whileHover={!isEditModalOpen ? "hover" : undefined}
        style={{
          // Prevent creating new stacking context when modal is open
          transform: isEditModalOpen ? 'none' : undefined,
          zIndex: isEditModalOpen ? 'auto' : undefined
        }}
      >
        <div className="p-5 border-b border-gray-100">
          <div className="flex justify-between items-start mb-3">
            <Link
              href={`/categories/${category.id}`}
              className="flex items-center group flex-1"
            >
              <div className={`p-2 rounded-md mr-3 ${category.isActive ? 'bg-teal-100 text-teal-600' : 'bg-gray-100 text-gray-500'}`}>
                <FolderTree size={20} />
              </div>
              <div>
                <div className="flex items-center">
                  <h3 className={`font-semibold text-gray-800 transition-colors ${!isEditModalOpen ? 'group-hover:text-teal-600' : ''}`}>{category.label}</h3>
                  <ExternalLink size={14} className={`ml-1 text-gray-400 transition-all ${!isEditModalOpen ? 'group-hover:text-teal-600 opacity-0 group-hover:opacity-100' : 'opacity-0'}`} />
                </div>
                <p className="text-sm text-gray-500">{category.href}</p>
              </div>
            </Link>
            <div className="flex space-x-2">
              <button
                onClick={() => setIsEditModalOpen(true)}
                className={`p-1.5 rounded-md text-gray-500 transition-colors ${!isEditModalOpen ? 'hover:bg-gray-100 hover:text-gray-700' : ''}`}
                aria-label="Edit category"
              >
                <Edit size={16} />
              </button>
              <button
                onClick={() => setIsDeleteModalOpen(true)}
                className={`p-1.5 rounded-md text-gray-500 transition-colors ${!isEditModalOpen ? 'hover:bg-gray-100 hover:text-red-500' : ''}`}
                aria-label="Delete category"
              >
                <Trash2 size={16} />
              </button>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2 mb-3">
            <div className="bg-gray-50 p-2 rounded-md">
              <p className="text-xs text-gray-500">Status</p>
              <p className={`text-sm font-medium ${category.isActive ? 'text-green-600' : 'text-gray-500'}`}>
                {category.isActive ? 'Active' : 'Inactive'}
              </p>
            </div>
            <div className="bg-gray-50 p-2 rounded-md">
              <p className="text-xs text-gray-500">Type</p>
              <p className="text-sm font-medium text-gray-700">
                {category.isEnginePart ? 'Engine Part' : 'Regular Part'}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div className="bg-gray-50 p-2 rounded-md">
              <p className="text-xs text-gray-500">Part Number</p>
              <p className="text-sm font-medium text-gray-700">
                {category.requirePartNumber ? 'Required' : 'Optional'}
              </p>
            </div>
            <button
              onClick={() => {
                setActiveModalTab('attributes');
                setIsEditModalOpen(true);
              }}
              className={`bg-gray-50 p-2 rounded-md transition-colors group text-left ${!isEditModalOpen ? 'hover:bg-teal-50' : ''}`}
            >
              <p className={`text-xs text-gray-500 transition-colors flex items-center justify-between ${!isEditModalOpen ? 'group-hover:text-teal-600' : ''}`}>
                <span>Attributes</span>
                <Tag size={12} className={`transition-opacity ${!isEditModalOpen ? 'opacity-0 group-hover:opacity-100' : 'opacity-0'}`} />
              </p>
              <p className={`text-sm font-medium text-gray-700 transition-colors ${!isEditModalOpen ? 'group-hover:text-teal-700' : ''}`}>
                Manage Attributes
              </p>
            </button>
          </div>
        </div>

        {hasChildren && (
          <div className="bg-gray-50 px-5 py-3 border-t border-gray-100">
            <button
              onClick={(e) => {
                e.stopPropagation(); // Stop event propagation
                onToggleExpand(); // Use the prop function instead of local state
              }}
              className={`flex items-center justify-between w-full text-left text-sm font-medium text-gray-700 transition-colors ${!isEditModalOpen ? 'hover:text-teal-600' : ''}`}
            >
              <span>Subcategories ({category.children.length})</span>
              {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </button>

            {isExpanded && (
              <div
                className="mt-3 space-y-2"
              >
                {category.children.map((subcategory) => (
                  <SubcategoryItem
                    key={subcategory.id}
                    subcategory={subcategory}
                    parentId={category.id}
                    level={1}
                    onEdit={() => {}}
                    onRefresh={onRefresh}
                    isExpanded={Boolean(expandedSubcategories?.[category.id]?.[subcategory.id])}
                    onToggleExpand={createToggleFunction(subcategory.id)}
                    expandedSubcategories={expandedSubcategories}
                    onToggleSubcategoryExpand={onToggleSubcategoryExpand}
                    onShowBatchRename={onShowBatchRename}
                  />
                ))}
              </div>
            )}
          </div>
        )}
      </motion.div>

      {/* Edit Modal */}
      <EditCategoryModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setActiveModalTab('general'); // Reset to default tab when closing
        }}
        category={category}
        onSuccess={(updatedCategory) => {
          if (updatedCategory) {
            // Update the local category state with the updated data
            setCategory({
              ...category,
              ...updatedCategory,
              // Preserve the children array since it's not included in the updatedCategory
              children: category.children
            });
          }
          // Still call the parent's onRefresh to update any other components if needed
          onRefresh();
        }}
        initialActiveTab={activeModalTab}
        onShowBatchRename={onShowBatchRename}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        categoryId={category.id}
        categoryName={category.label}
        hasChildren={hasChildren}
        onSuccess={onRefresh}
      />
    </>
  );
};

export default CategoryCard;
