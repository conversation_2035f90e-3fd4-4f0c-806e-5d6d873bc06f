// app/(dashboard)/parts/add/components/AddPartModal/CompatibilitySection.tsx
//  This component is not currently used in the refactored code.  It can be added later
//  if you decide to separate the compatibility logic into its own component.

import React from 'react';
import StatusLabel from '@/app/components/ui/inputs/StatusLabel';

interface CompatibilitySectionProps {
    currentTask: string;
    isProcessing: boolean;
    engineCompatibilityData: any[];
}

const CompatibilitySection: React.FC<CompatibilitySectionProps> = ({
    currentTask,
    isProcessing,
    engineCompatibilityData
}) => {
    return (
        <>
          <StatusLabel
                currentTask={currentTask}
                isProcessing={isProcessing}
                className="mb-4"
                ellipsisProps={{
                    textColor: "text-gray-600",
                    bgColor: "bg-gray-600",
                    numDots: 3,
                    animationDuration: 1
                }}
            />
           {engineCompatibilityData.length > 0 && (
                <div className="mt-4 p-4 bg-white rounded-lg shadow">
                    <h3 className="font-semibold mb-2">Engine Compatibility:</h3>
                    {engineCompatibilityData.map((engine, index) => (
                        <div key={index} className="mb-4">
                            <div className="font-medium text-gray-700">
                                {engine.engineCode} ({engine.family} - {engine.generation})
                            </div>
                            <ul className="list-disc pl-5 text-sm text-gray-600">
                                {engine.vehicles.map((vehicle: string, vIndex: number) => (
                                    <li key={vIndex}>{vehicle}</li>
                                ))}
                            </ul>
                        </div>
                    ))}
                </div>
            )}
        </>
    )
};

export default CompatibilitySection;