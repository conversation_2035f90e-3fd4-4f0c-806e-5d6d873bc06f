{"headless": false, "batchSize": 5, "delayBetweenActions": 3000, "maxRetries": 3, "skipExisting": true, "testMode": false, "browser": {"timeout": 30000, "slowMo": 100, "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"}, "images": {"maxPerListing": 10, "downloadTimeout": 10000, "supportedFormats": ["jpg", "jpeg", "png", "webp"]}, "rateLimiting": {"minDelayBetweenActions": 1000, "maxDelayBetweenActions": 5000, "delayBetweenParts": 3000, "randomizeDelays": true}, "selectors": {"login": {"loginButton": "a[href*=\"login\"], button:has-text(\"Login\"), .login-btn", "emailInput": "input[type=\"email\"], input[name=\"email\"], input[placeholder*=\"email\" i]", "passwordInput": "input[type=\"password\"], input[name=\"password\"], input[placeholder*=\"password\" i]", "submitButton": "button[type=\"submit\"], input[type=\"submit\"], button:has-text(\"Login\")"}, "postAd": {"postAdButton": "a[href*=\"post\"], a[href*=\"sell\"], button:has-text(\"Post\"), button:has-text(\"Sell\")", "categorySelection": ".category-selection, .post-form, [data-testid=\"category-select\"]"}, "form": {"titleInput": "input[name=\"title\"], input[placeholder*=\"title\" i], #title", "descriptionInput": "textarea[name=\"description\"], textarea[placeholder*=\"description\" i], #description", "priceInput": "input[name=\"price\"], input[placeholder*=\"price\" i], #price", "conditionSelect": "select[name=\"condition\"], select[name=\"state\"], #condition", "makeSelect": "select[name=\"make\"], select[name=\"brand\"], #make, #brand", "imageInput": "input[type=\"file\"][accept*=\"image\"], input[type=\"file\"][name*=\"image\"]", "submitButton": "button[type=\"submit\"], input[type=\"submit\"], button:has-text(\"Post\")"}, "categories": {"vehicleParts": ["text=\"Vehicle Parts & Accessories\"", "text=\"Vehicles\"", "text=\"Auto Parts\"", "text=\"Car Parts\"", "[data-category*=\"vehicle\"], [data-category*=\"auto\"], [data-category*=\"car\"]"]}}, "categoryMapping": {"Engine Parts": "Engine & Engine Parts", "Transmission": "Transmission & Drivetrain", "Brakes": "Brakes & Brake Parts", "Suspension": "Suspension & Steering", "Electrical": "Electrical & Lighting", "Body Parts": "Body Parts & Accessories", "Interior": "Interior Accessories", "Exterior": "Exterior Accessories", "Wheels": "Wheels & Tires", "Filters": "Filters & Fluids"}, "conditionMapping": {"new": ["New", "Brand New", "new"], "used": ["Used", "Second Hand", "Pre-owned", "used"], "refurbished": ["Refurbished", "Reconditioned", "refurbished"]}, "makeMapping": {"Toyota": "Toyota", "Nissan": "Nissan", "Honda": "Honda", "Mazda": "Mazda", "Mitsubishi": "Mitsubishi", "Subaru": "Subaru", "Volkswagen": "Volkswagen", "BMW": "BMW", "Mercedes": "Mercedes-Benz", "Audi": "Audi"}, "logging": {"level": "info", "fileRotation": true, "maxFileSize": "10MB", "maxFiles": 5}, "database": {"connectionTimeout": 10000, "queryTimeout": 30000, "retryAttempts": 3}, "errorHandling": {"captchaDetection": true, "formValidationRetries": 2, "networkErrorRetries": 3, "screenshotOnError": true}}