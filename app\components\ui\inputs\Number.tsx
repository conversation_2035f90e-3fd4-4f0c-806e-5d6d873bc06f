import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Plus, Minus } from 'lucide-react';
import { Control, Controller } from 'react-hook-form';

const hideArrowsStyle = `
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  input[type=number] {
    -moz-appearance: textfield;
  }
`;

interface NumberInputProps {
  name: string;
  control?: Control<any>;
  label: string;
  errorMessage?: string;
  className?: string;
  max?: number;
  placeholder?: string;
}

const NumberInput: React.FC<NumberInputProps> = ({ 
  name,
  control,
  label,
  errorMessage,
  className,
  max 
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const labelVariants = {
    focused: { 
      y: -5, 
      scale: 0.8, 
      transition: { type: 'spring', stiffness: 100, damping: 15 } 
    },
    idle: { 
      y: 0, 
      scale: 1, 
      transition: { type: 'spring', stiffness: 100, damping: 15 } 
    }
  };

  const formatNumber = (value: string): string => {
    const numericValue = value.replace(/[^0-9]/g, '');
    if (!numericValue) return '';
    const number = parseInt(numericValue, 10);
    return number.toLocaleString('en-US');
  };

  const parseNumber = (value: string): number => {
    return parseInt(value.replace(/[^0-9]/g, ''), 10) || 0;
  };

  if (control) {
    return (
      <Controller
        control={control}
        name={name}
        render={({ field }) => (
          <div className={`relative w-full ${className}`}>
            <style>{hideArrowsStyle}</style>
            <motion.div
              className={`
                relative rounded-md border transition-colors duration-200 px-4 py-0
                ${errorMessage 
                  ? 'border-red-500 dark:border-red-400'
                  : isFocused
                  ? 'border-blue-500 dark:border-blue-500'
                  : 'border-gray-300 dark:border-gray-600'
                }
                ${errorMessage ? 'dark:bg-red-100' : 'dark:bg-gray-700'}
              `}
            >
              <motion.label
                initial={false}
                animate="focused"
                variants={labelVariants}
                className="pointer-events-none absolute top-2 left-4 text-gray-500 text-xs origin-top-left"
                htmlFor={name}
              >
                {label}
              </motion.label>
              <div className="relative flex items-center">
                <input
                  {...field}
                  type="text"
                  id={name}
                  className="peer block w-full appearance-none bg-transparent py-2 pt-3 pr-10 text-gray-900 placeholder-transparent focus:outline-none focus:ring-0 dark:text-white"
                  placeholder=" "
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => setIsFocused(false)}
                  ref={inputRef}
                  value={field.value ? formatNumber(field.value.toString()) : ''}
                  onChange={(e) => {
                    const formattedValue = formatNumber(e.target.value);
                    const numericValue = parseNumber(formattedValue);
                    field.onChange(Math.min(numericValue, max || Infinity));
                  }}
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 space-x-0.5">
                  <button
                    type="button"
                    className="group relative rounded-md focus:outline-none p-1.5 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                    onClick={() => {
                      const currentValue = parseNumber(field.value?.toString() || '0');
                      field.onChange(Math.max(currentValue - 1, 0));
                    }}
                  >
                    <Minus className="text-gray-500 group-hover:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-200 h-4 w-4" />
                  </button>
                  <div className="h-5 w-0.5 bg-gray-300 mx-0.5 dark:bg-gray-500" />
                  <button
                    type="button"
                    className="group relative rounded-md focus:outline-none p-1.5 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                    onClick={() => {
                      const currentValue = parseNumber(field.value?.toString() || '0');
                      field.onChange(Math.min(currentValue + 1, max || Infinity));
                    }}
                  >
                    <Plus className="text-gray-500 group-hover:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-200 h-4 w-4" />
                  </button>
                </div>
              </div>
            </motion.div>
            {errorMessage && (
              <p className="mt-1 text-sm text-red-700 dark:text-red-400">{errorMessage}</p>
            )}
          </div>
        )}
      />
    );
  }

  return (
    <div className={`relative w-full ${className}`}>
      {/* Uncontrolled version implementation */}
    </div>
  );
};

export default NumberInput;