import { NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/server';

export async function POST(request: Request) {
  try {
    const subscription = await request.json();
    const supabase = createClient();

    // Remove subscription from database
    const { error } = await supabase
      .from('push_subscriptions')
      .delete()
      .eq('endpoint', subscription.endpoint);

    if (error) throw error;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error removing push subscription:', error);
    return NextResponse.json(
      { error: 'Failed to remove subscription' },
      { status: 500 }
    );
  }
} 