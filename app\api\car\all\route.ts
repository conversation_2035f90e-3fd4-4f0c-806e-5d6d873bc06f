import { NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';

export async function GET() {
  try {
    const supabase = createClient();
    
    // Fetch all car data in parallel
    const [brandsResponse, modelsResponse, generationsResponse, variationsResponse, trimsResponse] = await Promise.all([
      // Brands
      supabase
        .from('car_brands')
        .select('*')
        .order('brand_name'),
      
      // Models (limited to 100 most common)
      supabase
        .from('car_models')
        .select('*')
        .order('model_name')
        .limit(100),
      
      // Generations (limited to 100 most common)
      supabase
        .from('car_generation')
        .select('*')
        .order('name')
        .limit(100),
      
      // Variations (limited to 100 most common)
      supabase
        .from('car_variation')
        .select('*')
        .order('variation')
        .limit(100),
      
      // Trims (limited to 100 most common)
      supabase
        .from('variation_trim')
        .select('*')
        .order('trim')
        .limit(100)
    ]);
    
    // Check for errors
    if (brandsResponse.error) {
      return NextResponse.json({ error: brandsResponse.error.message }, { status: 500 });
    }
    
    if (modelsResponse.error) {
      return NextResponse.json({ error: modelsResponse.error.message }, { status: 500 });
    }
    
    if (generationsResponse.error) {
      return NextResponse.json({ error: generationsResponse.error.message }, { status: 500 });
    }
    
    if (variationsResponse.error) {
      return NextResponse.json({ error: variationsResponse.error.message }, { status: 500 });
    }
    
    if (trimsResponse.error) {
      return NextResponse.json({ error: trimsResponse.error.message }, { status: 500 });
    }
    
    // Format the data
    const brands = brandsResponse.data.map((brand: any) => ({
      id: brand.brand_id,
      name: brand.brand_name
    }));
    
    const models = modelsResponse.data.map((model: any) => ({
      id: model.id,
      brandId: model.brand_id,
      name: model.model_name
    }));
    
    const generations = generationsResponse.data.map((generation: any) => ({
      id: generation.id,
      modelId: generation.model_id,
      name: generation.name,
      start_production_year: generation.start_production_year,
      end_production_year: generation.end_production_year
    }));
    
    const variations = variationsResponse.data.map((variation: any) => ({
      id: variation.id,
      generationId: variation.generation_id,
      name: variation.variation,
      variation: variation.variation
    }));
    
    const trims = trimsResponse.data.map((trim: any) => ({
      id: trim.id,
      variationId: trim.variation_id,
      name: trim.trim,
      trim: trim.trim
    }));
    
    return NextResponse.json({
      brands,
      models,
      generations,
      variations,
      trims
    });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
