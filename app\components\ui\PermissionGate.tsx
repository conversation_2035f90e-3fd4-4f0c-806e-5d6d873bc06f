'use client';

import React from 'react';
import { useMultiplePermissions } from '@/app/hooks/useMultiplePermissions';
import { PermissionLoading } from './PermissionLoading';

interface PermissionGateProps {
  permissions: string[];
  groups?: string[];
  mode?: 'all' | 'any';
  fallback?: React.ReactNode;
  loadingFallback?: React.ReactNode;
  children: React.ReactNode;
}

export function PermissionGate({
  permissions,
  groups,
  mode = 'all',
  fallback = null,
  loadingFallback = <PermissionLoading />,
  children,
}: PermissionGateProps) {
  const { permissions: permissionResults, isLoading, hasAllPermissions, hasAnyPermission } = useMultiplePermissions(
    permissions,
    { groups }
  );

  if (isLoading) {
    return <>{loadingFallback}</>;
  }

  const hasPermission = mode === 'all'
    ? hasAllPermissions(permissions)
    : hasAnyPermission(permissions);

  return hasPermission ? <>{children}</> : <>{fallback}</>;
}

interface PermissionGroupGateProps {
  group: string;
  fallback?: React.ReactNode;
  loadingFallback?: React.ReactNode;
  children: React.ReactNode;
}

export function PermissionGroupGate({
  group,
  fallback = null,
  loadingFallback = <PermissionLoading />,
  children,
}: PermissionGroupGateProps) {
  const { isLoading, hasPermissionGroup } = useMultiplePermissions([], {
    groups: [group],
  });

  if (isLoading) {
    return <>{loadingFallback}</>;
  }

  return hasPermissionGroup(group) ? <>{children}</> : <>{fallback}</>;
}

interface PermissionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  permissions: string[];
  groups?: string[];
  mode?: 'all' | 'any';
  fallback?: React.ReactNode;
  loadingFallback?: React.ReactNode;
}

export function PermissionButton({
  permissions,
  groups,
  mode = 'all',
  fallback = null,
  loadingFallback = <PermissionLoading />,
  children,
  ...props
}: PermissionButtonProps) {
  const { permissions: permissionResults, isLoading, hasAllPermissions, hasAnyPermission } = useMultiplePermissions(
    permissions,
    { groups }
  );

  if (isLoading) {
    return <>{loadingFallback}</>;
  }

  const hasPermission = mode === 'all'
    ? hasAllPermissions(permissions)
    : hasAnyPermission(permissions);

  if (!hasPermission) {
    return <>{fallback}</>;
  }

  return (
    <button
      {...props}
      disabled={props.disabled || !hasPermission}
      className={`${props.className || ''} ${!hasPermission ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      {children}
    </button>
  );
} 