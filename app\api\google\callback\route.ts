import { NextRequest, NextResponse } from 'next/server';
import { googleMerchantService } from '@/app/services/googleMerchant';

/**
 * GET: Handle Google OAuth callback
 */
export async function GET(request: NextRequest) {
  try {
    // Get authorization code from query parameters
    const url = new URL(request.url);
    const code = url.searchParams.get('code');
    const error = url.searchParams.get('error');

    // Check for OAuth error response
    if (error) {
      console.error('OAuth error:', error);
      const errorDescription = url.searchParams.get('error_description') || 'Unknown error';

      return new Response(
        `<html><body><h1>Authentication Error</h1><p>${error}: ${errorDescription}</p></body></html>`,
        {
          status: 400,
          headers: {
            'Content-Type': 'text/html',
          },
        }
      );
    }

    if (!code) {
      return new Response(
        '<html><body><h1>Error</h1><p>No authorization code provided</p></body></html>',
        {
          status: 400,
          headers: {
            'Content-Type': 'text/html',
          },
        }
      );
    }

    // Get the current URL to use as the redirect URI
    const currentUrl = new URL(request.url);
    const redirectUri = `${currentUrl.origin}/api/google/callback`;

    console.log('Callback using redirect URI:', redirectUri);

    // Authenticate with Google
    const merchantId = await googleMerchantService.authenticate(code, redirectUri);

    // Return a success page with postMessage communication
    return new Response(
      `<!DOCTYPE html>
      <html>
        <head>
          <title>Authentication Successful</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 100vh;
              margin: 0;
              padding: 20px;
              text-align: center;
              background-color: #f9fafb;
            }
            .card {
              background: white;
              border-radius: 8px;
              box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
              padding: 40px;
              max-width: 500px;
              width: 100%;
            }
            h1 {
              color: #10b981;
              margin-bottom: 16px;
            }
            p {
              color: #4b5563;
              margin-bottom: 24px;
              line-height: 1.5;
            }
            .success-icon {
              width: 64px;
              height: 64px;
              margin-bottom: 24px;
              color: #10b981;
            }
            button {
              padding: 8px 16px;
              background: #10b981;
              color: white;
              border: none;
              border-radius: 4px;
              cursor: pointer;
            }
          </style>
          <script>
            window.onload = function() {
              try {
                // Store authentication success in localStorage
                localStorage.setItem('googleAuthSuccess', 'true');
                localStorage.setItem('googleMerchantId', '${merchantId || 'Not available'}');

                // Notify the opener (parent window) that auth was successful
                if (window.opener) {
                  console.log('Sending success message to parent window');
                  window.opener.postMessage({
                    type: 'GOOGLE_MERCHANT_AUTH_SUCCESS',
                    merchantId: '${merchantId || 'Not available'}'
                  }, '*');

                  // Close this popup window after a short delay
                  setTimeout(() => {
                    window.close();
                  }, 2000);
                } else {
                  console.log('No opener window found');
                  // If no opener, redirect to the integration page
                  window.location.href = '/integrations/google-merchant?success=true';
                }
              } catch (e) {
                console.error('Failed to communicate with parent window:', e);
                // Add a manual close button as fallback
                document.getElementById('closeButton').style.display = 'block';
              }
            };
          </script>
        </head>
        <body>
          <div class="card">
            <svg class="success-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            <h1>Authentication Successful</h1>
            <p>Your Google Merchant Center account has been successfully connected. This window will close automatically.</p>
            <p>Merchant ID: ${String(merchantId || 'Not available')}</p>
            <button id="closeButton" onclick="window.close()" style="display: none;">
              Close Window
            </button>
          </div>
        </body>
      </html>`,
      {
        headers: {
          'Content-Type': 'text/html',
        },
      }
    );
  } catch (error) {
    console.error('Error handling Google callback:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';

    // Always use the merchant ID from environment variables
    let merchantId = process.env.GOOGLE_MERCHANT_ID || process.env.GOOGLE_MERCHANT_API_MERCHANT_ID || 'Not available';
    let authSuccess = true;

    // Only show error if it's not related to merchant ID
    if (!errorMessage.includes('Failed to get merchant ID') && !errorMessage.includes('credentials is not defined')) {
      authSuccess = false;
    } else {
      console.log('Using merchant ID from environment:', merchantId);
    }

    return new Response(
      `<html>
        <head>
          <title>${authSuccess ? 'Authentication Successful' : 'Error'}</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              height: 100vh;
              margin: 0;
              padding: 20px;
              text-align: center;
              background-color: #f9fafb;
            }
            .card {
              background: white;
              border-radius: 8px;
              box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
              padding: 40px;
              max-width: 500px;
              width: 100%;
            }
            h1 {
              color: ${authSuccess ? '#10b981' : '#dc2626'};
              margin-bottom: 16px;
            }
            p {
              color: #4b5563;
              margin-bottom: 24px;
              line-height: 1.5;
            }
            .icon {
              width: 64px;
              height: 64px;
              margin-bottom: 24px;
              color: ${authSuccess ? '#10b981' : '#dc2626'};
            }
            .note {
              font-size: 0.9em;
              color: #6b7280;
              font-style: italic;
            }
          </style>
        </head>
        <body>
          <div class="card">
            ${authSuccess
              ? `<svg class="icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                <h1>Authentication Successful</h1>
                <p>Your Google Merchant Center account has been successfully connected. You can now close this window and return to the Autoflow dashboard.</p>
                <p>Merchant ID: ${merchantId}</p>
                <p class="note">Note: Using merchant ID from environment variables.</p>`
              : `<svg class="icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <h1>Error</h1>
                <p>Failed to authenticate with Google Merchant Center. Please try again.</p>
                <p>Error: ${errorMessage}</p>`
            }
          </div>
          <script>
            ${authSuccess
              ? `// Store authentication success in localStorage
                 try {
                   localStorage.setItem('googleAuthSuccess', 'true');
                   localStorage.setItem('googleMerchantId', '${merchantId}');
                   localStorage.setItem('justAuthenticated', 'true');

                   // Clear the authentication in progress flag
                   localStorage.removeItem('authInProgress');

                   // Notify the opener (parent window) that auth was successful
                   if (window.opener && !window.opener.closed) {
                     console.log('Sending success message to parent window');
                     window.opener.postMessage({
                       type: 'GOOGLE_MERCHANT_AUTH_SUCCESS',
                       merchantId: '${merchantId}'
                     }, '*');
                   }
                 } catch (e) {
                   console.error('Failed to store auth data in localStorage:', e);
                 }`
              : `// Notify the opener (parent window) that auth failed
                 if (window.opener && !window.opener.closed) {
                   console.log('Sending error message to parent window');
                   window.opener.postMessage({
                     type: 'GOOGLE_MERCHANT_AUTH_ERROR',
                     error: '${errorMessage.replace(/'/g, "\\'")}'
                   }, '*');
                 }`
            }

            // Close window after a short delay
            setTimeout(() => {
              window.close();
            }, 3000);
          </script>
        </body>
      </html>`,
      {
        status: authSuccess ? 200 : 500,
        headers: {
          'Content-Type': 'text/html',
        },
      }
    );
  }
}
