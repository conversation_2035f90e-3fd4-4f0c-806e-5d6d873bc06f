export const dynamic = 'force-dynamic';

import { Suspense } from 'react';
import Spinner from '@/app/components/ui/Spinner';
import ProfileContent from './components/ProfileContent';
import MainLayout from '@/app/layouts/MainLayout';

export default function ProfilePage() {
    return (
        <MainLayout>
            <Suspense fallback={<div className="flex min-h-[300px] items-center justify-center">
                <Spinner size="lg" />
            </div>}>
                <ProfileContent />
            </Suspense>
        </MainLayout>
    );
}
