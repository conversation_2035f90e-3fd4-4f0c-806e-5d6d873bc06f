// app/(dashboard)/parts/add/hooks/usePartForm.ts
'use client';

import { useCallback, useState } from 'react';
import { UseFormSetValue, UseFormReset, UseFormGetValues } from 'react-hook-form';
import { PartFormValues } from '../types';
import { usePartExistenceCheck } from './usePartExistenceCheck';
import { createClient } from '@/app/libs/supabase/client';
import { toast } from 'react-hot-toast';

interface usePartFormProps {
  setValue: UseFormSetValue<PartFormValues>;
  reset: UseFormReset<PartFormValues>;
  getValues: UseFormGetValues<PartFormValues>;
  onSuccess: () => void;
  onClose: () => void;
}

export const usePartForm = ({ setValue, reset, getValues, onSuccess, onClose }: usePartFormProps) => {
  const [isImageUploading, setIsImageUploading] = useState(false);
  const [croppedImageUrl, setCroppedImageUrl] = useState<string | null>(null);
  const [showCategorySelection, setShowCategorySelection] = useState(false);
  const [showExistingPartModal, setShowExistingPartModal] = useState(false);
  const [existingPartData, setExistingPartData] = useState<{
    groupId: number | null;
    table: 'part_compatibility_groups' | 'part_to_group' | null;
  } | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadedImages, setUploadedImages] = useState<Array<{ url: string; isMain: boolean }>>([]);

  const handleImageUploadSuccess = useCallback(async (imageUrl: string | null = null, isMain: boolean = false, isLastImage: boolean = false): Promise<string | null> => {
    setIsImageUploading(true);
    try {
      console.log('handleImageUploadSuccess called with URL data');

      if (!imageUrl) {
        console.log('No image data provided');
        return null;
      }

      // If it's a data URL, we need to upload it to Supabase
      if (imageUrl.startsWith('data:image/')) {
        console.log('Uploading data URL to Supabase storage...');

        try {
          // We'll use the uploadImage server action
          const { uploadImage } = await import('@/app/libs/actions');
          const result = await uploadImage(imageUrl);

          if (!result.success || !result.imageUrl) {
            console.error('Failed to upload image:', result.error);
            toast.error('Failed to upload image: ' + (result.error || 'Unknown error'));
            return null;
          }

          // Log the complete URL without truncation
          console.log('Image uploaded successfully, complete URL:', result.imageUrl);
          imageUrl = result.imageUrl;
        } catch (uploadError) {
          console.error('Error during image upload:', uploadError);
          toast.error('Error uploading image');
          return null;
        }
      }

      // At this point, imageUrl should be a proper http URL
      if (!imageUrl.startsWith('http')) {
        console.error('Invalid image URL format after processing:', imageUrl.substring(0, 50) + '...');
        toast.error('Invalid image URL format');
        return null;
      }

      // Update images array in form state
      const newImages = [...uploadedImages];

      // If this is set as the main image, update other images to not be main
      if (isMain) {
        newImages.forEach(img => img.isMain = false);
      }

      // If this is the first image, make it the main image by default
      const shouldBeMain = isMain || newImages.length === 0;

      // Add new image
      newImages.push({ url: imageUrl, isMain: shouldBeMain });

      // Update form values and state
      setValue('images', newImages);
      setUploadedImages(newImages);

      // Store the main image URL in the form's imageUrl field
      if (shouldBeMain) {
        setValue('imageUrl', imageUrl);
        // Don't set croppedImageUrl here to prevent auto-advancing
        // We'll set it in handleAllImagesProcessed instead
        console.log('Set main image URL in form:', imageUrl);
      }

      // If this is the last image and we're not in a batch upload, trigger the handleAllImagesProcessed
      if (isLastImage) {
        console.log('This is the last image, will trigger handleAllImagesProcessed soon');
        // For single image uploads, directly call handleAllImagesProcessed here
        // This ensures it gets called even if the ImageUploadSection doesn't call it
        setTimeout(() => {
          console.log('Directly calling handleAllImagesProcessed from handleImageUploadSuccess');
          handleAllImagesProcessed();

          // Force show category selection as a fallback
          setTimeout(() => {
            console.log('FORCE FALLBACK: Setting showCategorySelection to true');
            setShowCategorySelection(true);
          }, 300);
        }, 200);
      }

      console.log('Images updated in form state:', newImages.length, 'images');

      toast.success('Image uploaded successfully');
      return imageUrl;

    } catch (error) {
      console.error("Error handling image URL:", error);
      toast.error('Failed to process image');
      return null;
    } finally {
      setIsImageUploading(false);
    }
  }, [setValue, uploadedImages, croppedImageUrl]);

  // Function to set showCategorySelection after all images are processed
  const handleAllImagesProcessed = useCallback(() => {
    console.log("All images processed, showing category selection");
    console.log("Current uploadedImages:", uploadedImages);

    // Force show category selection regardless of images
    // This is a direct fix for the single image upload issue
    setShowCategorySelection(true);
    console.log("FORCED Category selection to be visible");

    // Now set croppedImageUrl to the main image once all images are processed
    const mainImage = uploadedImages.find(img => img.isMain);
    if (mainImage) {
      console.log("Setting croppedImageUrl to main image:", mainImage.url);
      setCroppedImageUrl(mainImage.url);
    } else if (uploadedImages.length > 0) {
      console.log("Setting croppedImageUrl to first image:", uploadedImages[0].url);
      setCroppedImageUrl(uploadedImages[0].url);
    } else {
      // If no images are in the uploadedImages array yet, check the form values
      const formImages = getValues('images');
      console.log("Checking form images:", formImages);

      if (formImages && formImages.length > 0) {
        const mainFormImage = formImages.find(img => img.isMain);
        if (mainFormImage) {
          console.log("Setting croppedImageUrl to main form image:", mainFormImage.url);
          setCroppedImageUrl(mainFormImage.url);
        } else {
          console.log("Setting croppedImageUrl to first form image:", formImages[0].url);
          setCroppedImageUrl(formImages[0].url);
        }
      } else {
        // Last resort - use the imageUrl field directly
        const imageUrl = getValues('imageUrl');
        if (imageUrl) {
          console.log("Setting croppedImageUrl to imageUrl field:", imageUrl);
          setCroppedImageUrl(imageUrl);
        }
      }
    }
  }, [uploadedImages, getValues]);

  // Handle removing an image from the list
  const handleRemoveImage = useCallback((imageUrl: string) => {
    const filteredImages = uploadedImages.filter(img => img.url !== imageUrl);

    // If we removed the main image, set the first remaining image as main (if any)
    if (filteredImages.length > 0) {
      const wasMainImage = uploadedImages.find(img => img.url === imageUrl)?.isMain || false;

      if (wasMainImage) {
        filteredImages[0].isMain = true;
        setValue('imageUrl', filteredImages[0].url);
        setCroppedImageUrl(filteredImages[0].url);
      }
    } else {
      // No images left
      setValue('imageUrl', '');
      setCroppedImageUrl(null);
    }

    setUploadedImages(filteredImages);
    setValue('images', filteredImages);
  }, [uploadedImages, setValue]);

  // Handle setting an image as the main image
  const handleSetMainImage = useCallback((imageUrl: string) => {
    const updatedImages = uploadedImages.map(img => ({
      ...img,
      isMain: img.url === imageUrl
    }));

    setUploadedImages(updatedImages);
    setValue('images', updatedImages);
    setValue('imageUrl', imageUrl);
    setCroppedImageUrl(imageUrl);
  }, [uploadedImages, setValue]);

  // Add function to get part record by group ID
  const getPartByGroupId = async (groupId: number) => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('parts')
        .select('*')
        .eq('partnumber_group', groupId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching part by group ID:', error);
      throw error;
    }
  };

  // Add function to create new part from existing
  const createNewPartFromExisting = async (
    existingPart: any,
    newPartNumber: string,
    userId: string
  ) => {
    try {
      const supabase = createClient();

      // Create new title by replacing the part number
      const newTitle = existingPart.title.replace(
        /^\[.*?\]/, // Match the part number in square brackets at the start
        `[${newPartNumber}]`
      );

      // Create new part record
      const { data, error } = await supabase
        .from('parts')
        .insert({
          title: newTitle,
          category_id: existingPart.category_id,
          partnumber_group: existingPart.partnumber_group,
          createdBy: userId,
          createdAt: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating new part from existing:', error);
      throw error;
    }
  };

  // Add function to check part existence
  const checkPartExistence = async (partNumber: string) => {
    const supabase = createClient();
    console.log("Checking part existence for:", partNumber);

    try {
      // First check in part_to_group table
      console.log("Querying part_to_group with partnumber =", partNumber);
      const { data: partToGroupData, error: partToGroupError } = await supabase
        .from('part_to_group')
        .select('group_id')
        .eq('partnumber', partNumber)
        .single();

      if (partToGroupError) {
        if (partToGroupError.code === 'PGRST116') { // No rows returned
          console.log("No rows found in part_to_group for partnumber =", partNumber);
        } else {
          console.error("Error querying part_to_group:", partToGroupError);
          throw partToGroupError;
        }
      }

      if (partToGroupData) {
        console.log("Found part in part_to_group:", partToGroupData);
        // Set the existing part data immediately
        setExistingPartData({
          groupId: partToGroupData.group_id,
          table: 'part_to_group' as const
        });
        return {
          groupId: partToGroupData.group_id,
          table: 'part_to_group' as const
        };
      }

      // If we reached here, part was not found in part_to_group
      console.log("Part not found in part_to_group");
      return null;
    } catch (error) {
      console.error("Error in checkPartExistence:", error);
      throw error;
    }
  };

  // Modify the form submission logic
  const onSubmit = async (values: PartFormValues) => {
    try {
      setIsSubmitting(true);
      setError(null);

      if (!values.partNumber) {
        throw new Error('Part number is required');
      }

      // Check part existence
      const existenceResult = await checkPartExistence(values.partNumber);

      if (existenceResult) {
        console.log("Part exists:", existenceResult);
        // Part exists, show modal
        setShowExistingPartModal(true);
        return;
      }

      // If part doesn't exist, continue with the form submission
      // ... rest of the existing onSubmit code ...
    } catch (error) {
      console.error('Error in form submission:', error);
      setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add type for verifyPartNumber return value
  type VerifyPartNumberResult =
    | { exists: false; message: string }
    | { exists: true; message: string; groupId: number; table: 'part_to_group' };

  // We need to provide a specific function for part verification from UI
  const verifyPartNumber = async (partNumber: string): Promise<VerifyPartNumberResult> => {
    try {
      console.log("Verifying part number:", partNumber);
      if (!partNumber) {
        return { exists: false, message: "Part number is required" };
      }

      const supabase = createClient();

      // ONLY check part_to_group table to avoid 406 errors
      console.log("Checking part_to_group table for partnumber:", partNumber);
      const { data: partToGroupData, error: partToGroupError } = await supabase
        .from('part_to_group')
        .select('group_id')
        .eq('partnumber', partNumber)
        .maybeSingle(); // Use maybeSingle instead of single to avoid errors

      if (partToGroupError) {
        console.error("Error verifying part in part_to_group:", partToGroupError);
        return { exists: false, message: `Error verifying part: ${partToGroupError.message}` };
      }

      if (partToGroupData) {
        console.log("Part found in part_to_group:", partToGroupData);

        // Create the part data object
        const partData = {
          groupId: partToGroupData.group_id,
          table: 'part_to_group' as const
        };

        // Set the existing part data and show modal in sequence
        await new Promise<void>((resolve) => {
          setExistingPartData(partData);
          setShowExistingPartModal(true);
          resolve();
        });

        console.log("Set existing part data:", partData);
        return {
          exists: true,
          ...partData,
          message: "Part exists in database"
        };
      }

      console.log("Part not found in database");
      return { exists: false, message: "Part not found in database" };
    } catch (error) {
      console.error("Error in verifyPartNumber:", error);
      return { exists: false, message: error instanceof Error ? error.message : "Unknown error occurred" };
    }
  };

  // Add handler for existing part modal confirmation
  const handleExistingPartConfirm = async () => {
    try {
      console.log("Starting handleExistingPartConfirm");

      const partNumber = getValues('partNumber');
      if (!partNumber) {
        throw new Error('Part number is required');
      }

      // Verify the part number directly
      const result = await verifyPartNumber(partNumber);
      if (!result.exists || !result.groupId) {
        throw new Error('Part not found in database');
      }

      const supabase = createClient();
      console.log("Supabase client created");
      console.log("Using group ID:", result.groupId);

      // Get an existing part record with the same group ID
      console.log("Fetching existing part record with group ID:", result.groupId);
      const { data: existingPart, error: existingPartError } = await supabase
        .from('parts')
        .select('category_id, title, partnumber_group')
        .eq('partnumber_group', result.groupId)
        .limit(1)
        .single();

      if (existingPartError) {
        console.error("Error fetching existing part:", existingPartError);
        throw new Error(`Failed to fetch existing part: ${existingPartError.message}`);
      }

      if (!existingPart) {
        console.error("No existing part found with group ID:", result.groupId);
        throw new Error('No existing part found');
      }

      console.log("Found existing part:", existingPart);

      // Create new title by replacing the part number
      // First, find the part number in the existing title
      const partNumberMatch = existingPart.title.match(/\b[A-Z0-9]{6,}\b/);
      if (!partNumberMatch) {
        console.error("Could not find part number in title:", existingPart.title);
        throw new Error('Could not find part number in existing title');
      }

      const oldPartNumber = partNumberMatch[0];
      console.log("Found old part number in title:", oldPartNumber);

      // Replace the old part number with the new one
      const newTitle = existingPart.title.replace(
        new RegExp(`\\b${oldPartNumber}\\b`, 'g'),
        partNumber
      );

      console.log("Original title:", existingPart.title);
      console.log("New title:", newTitle);

      // Create new part record
      const { error: insertError } = await supabase
        .from('parts')
        .insert({
          title: newTitle,
          category_id: existingPart.category_id,
          partnumber_group: existingPart.partnumber_group,
          createdAt: new Date().toISOString()
        });

      if (insertError) {
        console.error("Error creating new part:", insertError);
        throw new Error(`Failed to create new part: ${insertError.message}`);
      }

      console.log("Successfully created new part record");

      // First close the modal
      setShowExistingPartModal(false);

      // Show success message
      toast.success('Part created successfully');

      // Reset all states and form data
      setExistingPartData(null);
      setShowCategorySelection(false);
      setCroppedImageUrl(null);
      setError(null);
      setIsSubmitting(false);
      setIsImageUploading(false);

      // Reset the form
      reset();

      // Close the modal
      onClose();

      // Wait for state updates to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      // Finally call onSuccess
      onSuccess();

    } catch (error) {
      console.error('Error handling existing part confirmation:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create part');
    }
  };

  return {
    handleImageUploadSuccess,
    handleRemoveImage,
    handleSetMainImage,
    isImageUploading,
    croppedImageUrl,
    showCategorySelection,
    setShowCategorySelection,
    showExistingPartModal,
    setShowExistingPartModal,
    handleExistingPartConfirm,
    existingPartData,
    isSubmitting,
    error,
    verifyPartNumber,
    uploadedImages,
    handleAllImagesProcessed
  };
};