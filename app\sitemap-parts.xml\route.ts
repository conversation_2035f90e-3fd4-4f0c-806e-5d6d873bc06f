import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://autoflow.co.ke';

    // Get URL parameters for pagination
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = 10000; // 10k parts per sitemap
    const offset = (page - 1) * limit;

    console.log(`Generating parts sitemap - Page: ${page}, Offset: ${offset}`);

    // Fetch parts with pagination
    const { data: parts, error } = await supabase
      .from('parts')
      .select('id, title, updated_at, created_at')
      .eq('is_active', true)
      .order('updated_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching parts for sitemap:', error);
      throw error;
    }

    const partsPages: any[] = [];

    if (parts && parts.length > 0) {
      parts.forEach(part => {
        // Create SEO-friendly slug from title
        const slug = part.title
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
          .replace(/\s+/g, '-') // Replace spaces with hyphens
          .replace(/-+/g, '-') // Replace multiple hyphens with single
          .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens

        partsPages.push({
          url: `${baseUrl}/parts/${slug}-${part.id}`,
          lastModified: part.updated_at || part.created_at || new Date().toISOString(),
          changeFrequency: 'weekly',
          priority: '0.6'
        });
      });
    }

    // Generate XML sitemap
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${partsPages.map(page => `  <url>
    <loc>${page.url}</loc>
    <lastmod>${page.lastModified}</lastmod>
    <changefreq>${page.changeFrequency}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('\n')}
</urlset>`;

    console.log(`Generated parts sitemap with ${partsPages.length} parts`);

    return new NextResponse(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
      },
    });

  } catch (error) {
    console.error('Error generating parts sitemap:', error);
    
    // Return empty sitemap on error
    const emptySitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
</urlset>`;

    return new NextResponse(emptySitemap, {
      headers: {
        'Content-Type': 'application/xml',
      },
    });
  }
}
