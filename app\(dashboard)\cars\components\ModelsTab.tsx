'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Search, RefreshCw, Edit, Trash2, Car, Tag } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { Model, Brand } from '../types';
import AddModelModal from './modals/AddModelModal';
import EditModelModal from './modals/EditModelModal';
import DeleteConfirmModal from './modals/DeleteConfirmModal';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import Image from 'next/image';

interface ModelsTabProps {
  onModelUpdated: () => void;
}

const ModelsTab: React.FC<ModelsTabProps> = ({ onModelUpdated }) => {
  const [models, setModels] = useState<Model[]>([]);
  const [filteredModels, setFilteredModels] = useState<Model[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedBrandId, setSelectedBrandId] = useState<number | ''>('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedModel, setSelectedModel] = useState<Model | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const supabase = createClient();

  // Fetch brands from Supabase
  useEffect(() => {
    const fetchBrands = async () => {
      try {
        const { data, error } = await supabase
          .from('car_brands')
          .select('*')
          .order('brand_name');

        if (error) throw error;

        setBrands(data || []);
      } catch (error) {
        console.error('Error fetching brands:', error);
      }
    };

    fetchBrands();
  }, [supabase]);

  // Fetch models from Supabase
  useEffect(() => {
    const fetchModels = async () => {
      setIsLoading(true);
      try {
        let query = supabase
          .from('car_models')
          .select('*')
          .order('model_name');
          
        if (selectedBrandId) {
          query = query.eq('brand_id', selectedBrandId);
        }
        
        const { data, error } = await query;

        if (error) throw error;

        setModels(data || []);
        setFilteredModels(data || []);
      } catch (error) {
        console.error('Error fetching models:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchModels();
  }, [refreshTrigger, selectedBrandId, supabase]);

  // Filter models based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredModels(models);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = models.filter(model => 
      model.model_name.toLowerCase().includes(query)
    );
    setFilteredModels(filtered);
  }, [searchQuery, models]);

  // Refresh models
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Get brand name by ID
  const getBrandName = (brandId: number) => {
    const brand = brands.find(b => b.brand_id === brandId);
    return brand ? brand.brand_name : 'Unknown Brand';
  };

  // Open edit modal
  const handleEdit = (model: Model) => {
    setSelectedModel(model);
    setIsEditModalOpen(true);
  };

  // Open delete modal
  const handleDelete = (model: Model) => {
    setSelectedModel(model);
    setIsDeleteModalOpen(true);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.3,
        ease: "easeOut"
      }
    },
    hover: {
      y: -5,
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      transition: { 
        duration: 0.2
      }
    }
  };

  return (
    <div>
      {/* Search and Actions */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
        <div className="flex flex-col md:flex-row gap-4 w-full md:w-auto">
          <div className="relative w-full md:w-64">
            <input
              type="text"
              placeholder="Search models..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
          </div>
          
          <div className="w-full md:w-64">
            <select
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
              value={selectedBrandId}
              onChange={(e) => setSelectedBrandId(e.target.value ? Number(e.target.value) : '')}
            >
              <option value="">All Brands</option>
              {brands.map((brand) => (
                <option key={brand.brand_id} value={brand.brand_id}>
                  {brand.brand_name}
                </option>
              ))}
            </select>
          </div>
        </div>
        
        <div className="flex gap-2 w-full md:w-auto">
          <button
            onClick={handleRefresh}
            className="flex items-center gap-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
          >
            <RefreshCw size={18} />
            <span>Refresh</span>
          </button>
          
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <Plus size={18} />
            <span>Add Model</span>
          </button>
        </div>
      </div>
      
      {/* Models Grid */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size={40} />
        </div>
      ) : filteredModels.length > 0 ? (
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {filteredModels.map((model) => (
            <motion.div
              key={model.id}
              className="bg-white rounded-lg shadow-md overflow-hidden"
              variants={itemVariants}
              whileHover="hover"
            >
              <div className="relative h-40 bg-gray-100">
                {model.model_image ? (
                  <Image
                    src={model.model_image}
                    alt={model.model_name}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <Car size={64} className="text-gray-300" />
                  </div>
                )}
              </div>
              
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800">{model.model_name}</h3>
                    <div className="flex items-center mt-1">
                      <Tag size={16} className="text-teal-600 mr-2" />
                      <span className="text-sm text-gray-600">{getBrandName(model.brand_id)}</span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleEdit(model)}
                      className="p-2 rounded-md hover:bg-gray-100 text-gray-500 hover:text-teal-600 transition-colors"
                      aria-label="Edit model"
                    >
                      <Edit size={18} />
                    </button>
                    <button
                      onClick={() => handleDelete(model)}
                      className="p-2 rounded-md hover:bg-gray-100 text-gray-500 hover:text-red-600 transition-colors"
                      aria-label="Delete model"
                    >
                      <Trash2 size={18} />
                    </button>
                  </div>
                </div>
                
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <p className="text-sm text-gray-500">Model ID: {model.id}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      ) : (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <h3 className="text-xl font-semibold text-gray-700 mb-2">No models found</h3>
          <p className="text-gray-500 mb-4">
            {searchQuery || selectedBrandId ? 'No models match your search criteria.' : 'Start by adding a new model.'}
          </p>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <Plus size={18} />
            <span>Add Model</span>
          </button>
        </div>
      )}
      
      {/* Add Model Modal */}
      <AddModelModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        brands={brands}
        onSuccess={() => {
          handleRefresh();
          onModelUpdated();
        }}
      />
      
      {/* Edit Model Modal */}
      {selectedModel && (
        <EditModelModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          model={selectedModel}
          brands={brands}
          onSuccess={() => {
            handleRefresh();
            onModelUpdated();
          }}
        />
      )}
      
      {/* Delete Confirmation Modal */}
      {selectedModel && (
        <DeleteConfirmModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          itemId={selectedModel.id}
          itemName={selectedModel.model_name}
          itemType="model"
          tableName="car_models"
          idField="id"
          onSuccess={() => {
            handleRefresh();
            onModelUpdated();
          }}
        />
      )}
    </div>
  );
};

export default ModelsTab;
