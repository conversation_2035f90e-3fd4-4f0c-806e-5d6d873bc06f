'use client';

import React from 'react';
// import type { PartDetails, PartAttributeType } from '@/app/shop/[param]/page'; // Adjust path as needed
type PartDetails = any;
type PartAttributeType = any;
import Icon from '@/app/components/ui/Icon'; // Assuming Icon component path

interface AttributeItemProps {
  icon: string;
  label: string;
  value: string | number;
}

const AttributeItem: React.FC<AttributeItemProps> = ({ icon, label, value }) => (
  <div className="flex items-start space-x-3 p-3 bg-white rounded-lg border border-gray-200 hover:shadow-sm transition-shadow duration-200">
    <Icon name={icon} size={20} className="text-blue-500 mt-1 flex-shrink-0" />
    <div>
      <p className="text-xs text-gray-500 font-medium">{label}</p>
      <p className="text-sm text-gray-800 font-semibold">{value}</p>
    </div>
  </div>
);

interface ProductAttributesListingProps {
  partDetails: PartDetails | null;
  getIconForAttribute?: (attributeName: string) => string;
}

const ProductAttributesListing: React.FC<ProductAttributesListingProps> = ({ partDetails, getIconForAttribute }) => {
  if (!partDetails || !partDetails.attributes || partDetails.attributes.length === 0) {
    return null;
  }

  // Determine the number of columns based on the number of attributes
  const numAttributes = partDetails.attributes.length;
  let gridColsClass = 'grid-cols-1 sm:grid-cols-2';
  if (numAttributes > 4) {
    gridColsClass = 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3';
  }
  if (numAttributes > 6) {
    gridColsClass = 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4';
  }

  return (
    <div className="mt-6 py-6 border-t border-gray-200">
      <h3 className="text-xl font-semibold text-gray-800 mb-4">Additional Specifications</h3>
      <div className={`grid ${gridColsClass} gap-x-6 gap-y-4`}>
        {partDetails.attributes.map((attr: PartAttributeType, index: number) => {
          const iconName = attr.icon || (getIconForAttribute ? getIconForAttribute(attr.name) : 'settings');
          return (
            <div key={index} className="flex items-start p-3 bg-gray-50 rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <Icon name={iconName} size={22} className="text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-gray-800">{attr.name}</p>
                <p className="text-sm text-gray-600">{attr.value}</p>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ProductAttributesListing;
