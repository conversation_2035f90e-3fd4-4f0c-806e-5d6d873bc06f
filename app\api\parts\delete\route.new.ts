import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';
import { directImageDelete } from '@/app/utils/directImageDelete';
import { deleteSupabaseImage } from '@/app/utils/deleteSupabaseImage';

export async function DELETE(request: NextRequest) {
  try {
    const { partId, imagePath, userId } = await request.json();
    
    if (!partId) {
      return NextResponse.json({ error: 'Part ID is required' }, { status: 400 });
    }

    const supabase = createClient();
    console.log('Deleting part with ID:', partId);
    console.log('Image path to delete:', imagePath);
    console.log('User ID:', userId);
    
    let imageDeleteSuccess = false;
    let partDeleteSuccess = false;
    let partDeleteError = null;
    let deletedImages: string[] = [];
    let deletionLogs: string[] = [];
    
    // Helper function to add logs (similar to addLog in the image-deletion-tool)
    const addLog = (message: string) => {
      const logMessage = `[${new Date().toLocaleTimeString()}] ${message}`;
      console.log(logMessage);
      deletionLogs.push(logMessage);
    };
    
    // First, get the actual part data from the database to verify the image paths
    const { data: partData, error: partError } = await supabase
      .from('parts')
      .select('*')
      .eq('id', partId)
      .single();
      
    if (partError) {
      addLog(`Error fetching part data: ${partError.message}`);
    } else {
      addLog(`Part data from database: ${JSON.stringify(partData)}`);
    }
    
    // Get the part_images data to find all associated images
    const { data: imageData, error: imageError } = await supabase
      .from('part_images')
      .select('*')
      .eq('part_id', partId);
      
    if (imageError) {
      addLog(`Error fetching part images: ${imageError.message}`);
    } else {
      addLog(`Found ${imageData?.length || 0} images associated with this part`);
      
      // Try to delete all images associated with this part
      if (imageData && imageData.length > 0) {
        for (const img of imageData) {
          if (img.image_path) {
            // ===== USING EXACT SAME CODE FROM IMAGE-DELETION-TOOL =====
            addLog(`Starting deletion of image: ${img.image_path}`);
            
            // First check if the image exists (without cache-busting)
            try {
              addLog('Checking if image exists...');
              const checkResponse = await fetch(img.image_path, { method: 'HEAD' });
              if (checkResponse.ok) {
                addLog('✅ Image exists and is accessible');
              } else {
                addLog(`⚠️ Image returned status ${checkResponse.status} - might not exist or be accessible`);
              }
            } catch (checkError) {
              addLog(`⚠️ Error checking if image exists: ${checkError}`);
            }
            
            // First attempt: Try the standard approach
            addLog('Attempting to delete image using standard approach...');
            const deleteResult = await deleteSupabaseImage(img.image_path, userId || undefined);
            
            if (deleteResult.success) {
              addLog(`✅ Standard approach: Successfully deleted image from path: ${deleteResult.path}`);
            } else {
              addLog(`❌ Standard approach failed: ${deleteResult.error}`);
            }
            
            // Second attempt: Try the direct approach regardless of first result
            addLog('Attempting direct deletion as a backup method...');
            const directResult = await directImageDelete(img.image_path);
            
            if (directResult.success) {
              addLog(`✅ Direct approach: Successfully deleted image`);
              if (directResult.details?.path) {
                addLog(`  Path: ${directResult.details.path}`);
              }
            } else {
              addLog(`❌ Direct approach failed: ${directResult.error}`);
            }
            
            // Determine overall success
            const overallSuccess = deleteResult.success || directResult.success;
            
            if (overallSuccess) {
              addLog('Image deleted successfully using at least one method');
              imageDeleteSuccess = true;
              deletedImages.push(img.image_path);
              
              // Add a delay to allow CDN cache to update
              addLog('Waiting 3 seconds for CDN cache to update...');
              await new Promise(resolve => setTimeout(resolve, 3000));
            } else {
              addLog('Failed to delete image using both methods');
            }
            
            // Check if the image still exists with cache-busting
            try {
              const cacheBustUrl = `${img.image_path}?t=${Date.now()}`;
              addLog('Checking if image still exists (with cache-busting)...');
              const afterCheckResponse = await fetch(cacheBustUrl, { method: 'HEAD' });
              
              if (afterCheckResponse.ok) {
                addLog('⚠️ Image still appears to exist and is accessible after deletion attempt');
                addLog('Note: This might be due to CDN caching. The image may have been deleted from storage but still be cached.');
              } else if (afterCheckResponse.status === 404) {
                addLog('✅ Image confirmed deleted (returned 404 Not Found)');
                imageDeleteSuccess = true;
                if (!deletedImages.includes(img.image_path)) {
                  deletedImages.push(img.image_path);
                }
              } else {
                addLog(`ℹ️ Image returned status ${afterCheckResponse.status} after deletion attempt`);
              }
              
              // Try a direct check with the Supabase client
              try {
                addLog('Checking directly with Supabase storage API...');
                
                // Extract the path from the success result
                let path = '';
                if (deleteResult.success && deleteResult.path) {
                  path = deleteResult.path;
                } else if (directResult.success && directResult.details?.path) {
                  path = directResult.details.path;
                } else if (img.image_path.includes('/v1/object/public/')) {
                  path = img.image_path.split('/v1/object/public/')[1];
                }
                
                if (path) {
                  addLog(`Checking if file exists at path: ${path}`);
                  try {
                    const { data: fileExists } = await supabase.storage
                      .from('car-part-images')
                      .download(path);
                      
                    if (fileExists) {
                      addLog('⚠️ File still exists in Supabase storage according to direct API check');
                      
                      // Try one more direct deletion
                      addLog('Attempting one final direct deletion...');
                      const { error: finalDeleteError } = await supabase.storage
                        .from('car-part-images')
                        .remove([path]);
                        
                      if (!finalDeleteError) {
                        addLog('✅ Final direct deletion successful');
                        imageDeleteSuccess = true;
                        if (!deletedImages.includes(img.image_path)) {
                          deletedImages.push(img.image_path);
                        }
                      } else {
                        addLog(`❌ Final direct deletion failed: ${finalDeleteError.message}`);
                      }
                    } else {
                      addLog('✅ File confirmed deleted via direct Supabase storage API check');
                      imageDeleteSuccess = true;
                      if (!deletedImages.includes(img.image_path)) {
                        deletedImages.push(img.image_path);
                      }
                    }
                  } catch (downloadError) {
                    // If we get an error, it likely means the file doesn't exist
                    addLog('✅ File confirmed deleted via direct Supabase storage API (error on download attempt)');
                    imageDeleteSuccess = true;
                    if (!deletedImages.includes(img.image_path)) {
                      deletedImages.push(img.image_path);
                    }
                  }
                } else {
                  addLog('⚠️ Could not determine path for direct API check');
                }
              } catch (supabaseError) {
                addLog(`❌ Error checking with Supabase API: ${supabaseError}`);
              }
            } catch (afterCheckError) {
              addLog(`ℹ️ Error checking if image exists after deletion: ${afterCheckError}`);
            }
            // ===== END OF CODE FROM IMAGE-DELETION-TOOL =====
          }
        }
      }
    }
    
    // Also handle the specific imagePath if provided (for backward compatibility)
    if (imagePath && !deletedImages.includes(imagePath)) {
      // ===== USING EXACT SAME CODE FROM IMAGE-DELETION-TOOL =====
      addLog(`Starting deletion of specific image: ${imagePath}`);
      
      // First check if the image exists (without cache-busting)
      try {
        addLog('Checking if specific image exists...');
        const checkResponse = await fetch(imagePath, { method: 'HEAD' });
        if (checkResponse.ok) {
          addLog('✅ Specific image exists and is accessible');
        } else {
          addLog(`⚠️ Specific image returned status ${checkResponse.status} - might not exist or be accessible`);
        }
      } catch (checkError) {
        addLog(`⚠️ Error checking if specific image exists: ${checkError}`);
      }
      
      // First attempt: Try the standard approach
      addLog('Attempting to delete specific image using standard approach...');
      const deleteResult = await deleteSupabaseImage(imagePath, userId || undefined);
      
      if (deleteResult.success) {
        addLog(`✅ Standard approach: Successfully deleted specific image from path: ${deleteResult.path}`);
      } else {
        addLog(`❌ Standard approach failed for specific image: ${deleteResult.error}`);
      }
      
      // Second attempt: Try the direct approach regardless of first result
      addLog('Attempting direct deletion as a backup method for specific image...');
      const directResult = await directImageDelete(imagePath);
      
      if (directResult.success) {
        addLog(`✅ Direct approach: Successfully deleted specific image`);
        if (directResult.details?.path) {
          addLog(`  Path: ${directResult.details.path}`);
        }
      } else {
        addLog(`❌ Direct approach failed for specific image: ${directResult.error}`);
      }
      
      // Determine overall success
      const overallSuccess = deleteResult.success || directResult.success;
      
      if (overallSuccess) {
        addLog('Specific image deleted successfully using at least one method');
        imageDeleteSuccess = true;
        deletedImages.push(imagePath);
        
        // Add a delay to allow CDN cache to update
        addLog('Waiting 3 seconds for CDN cache to update...');
        await new Promise(resolve => setTimeout(resolve, 3000));
      } else {
        addLog('Failed to delete specific image using both methods');
      }
      
      // Check if the image still exists with cache-busting
      try {
        const cacheBustUrl = `${imagePath}?t=${Date.now()}`;
        addLog('Checking if specific image still exists (with cache-busting)...');
        const afterCheckResponse = await fetch(cacheBustUrl, { method: 'HEAD' });
        
        if (afterCheckResponse.ok) {
          addLog('⚠️ Specific image still appears to exist and is accessible after deletion attempt');
          addLog('Note: This might be due to CDN caching. The image may have been deleted from storage but still be cached.');
        } else if (afterCheckResponse.status === 404) {
          addLog('✅ Specific image confirmed deleted (returned 404 Not Found)');
          imageDeleteSuccess = true;
          if (!deletedImages.includes(imagePath)) {
            deletedImages.push(imagePath);
          }
        } else {
          addLog(`ℹ️ Specific image returned status ${afterCheckResponse.status} after deletion attempt`);
        }
        
        // Try a direct check with the Supabase client
        try {
          addLog('Checking directly with Supabase storage API for specific image...');
          
          // Extract the path from the success result
          let path = '';
          if (deleteResult.success && deleteResult.path) {
            path = deleteResult.path;
          } else if (directResult.success && directResult.details?.path) {
            path = directResult.details.path;
          } else if (imagePath.includes('/v1/object/public/')) {
            path = imagePath.split('/v1/object/public/')[1];
          }
          
          if (path) {
            addLog(`Checking if specific file exists at path: ${path}`);
            try {
              const { data: fileExists } = await supabase.storage
                .from('car-part-images')
                .download(path);
                
              if (fileExists) {
                addLog('⚠️ Specific file still exists in Supabase storage according to direct API check');
                
                // Try one more direct deletion
                addLog('Attempting one final direct deletion for specific image...');
                const { error: finalDeleteError } = await supabase.storage
                  .from('car-part-images')
                  .remove([path]);
                  
                if (!finalDeleteError) {
                  addLog('✅ Final direct deletion successful for specific image');
                  imageDeleteSuccess = true;
                  if (!deletedImages.includes(imagePath)) {
                    deletedImages.push(imagePath);
                  }
                } else {
                  addLog(`❌ Final direct deletion failed for specific image: ${finalDeleteError.message}`);
                }
              } else {
                addLog('✅ Specific file confirmed deleted via direct Supabase storage API check');
                imageDeleteSuccess = true;
                if (!deletedImages.includes(imagePath)) {
                  deletedImages.push(imagePath);
                }
              }
            } catch (downloadError) {
              // If we get an error, it likely means the file doesn't exist
              addLog('✅ Specific file confirmed deleted via direct Supabase storage API (error on download attempt)');
              imageDeleteSuccess = true;
              if (!deletedImages.includes(imagePath)) {
                deletedImages.push(imagePath);
              }
            }
          } else {
            addLog('⚠️ Could not determine path for direct API check of specific image');
          }
        } catch (supabaseError) {
          addLog(`❌ Error checking specific image with Supabase API: ${supabaseError}`);
        }
      } catch (afterCheckError) {
        addLog(`ℹ️ Error checking if specific image exists after deletion: ${afterCheckError}`);
      }
      // ===== END OF CODE FROM IMAGE-DELETION-TOOL =====
    }
    
    // Delete the part - the database trigger will automatically handle all related data cleanup
    try {
      addLog('Deleting part - trigger will handle related data cleanup automatically...');
      const { error } = await supabase
        .from('parts')
        .delete()
        .eq('id', partId);

      if (error) {
        addLog(`Error deleting part: ${error.message}`);
        partDeleteError = error;
      } else {
        addLog('Successfully deleted part - trigger handled all related data cleanup');
        partDeleteSuccess = true;
      }
    } catch (deleteError: any) {
      addLog(`Error during deletion process for part ID ${partId}: ${deleteError.message || String(deleteError)}`);
      partDeleteError = deleteError;
      
      // Last resort: try to just delete the part directly
      if (!partDeleteSuccess) {
        try {
          addLog('Last resort: attempting to delete just the part record...');
          const { error: lastResortError } = await supabase
            .from('parts')
            .delete()
            .eq('id', partId);
            
          if (lastResortError) {
            addLog(`Last resort deletion failed: ${lastResortError.message}`);
          } else {
            addLog('Last resort deletion succeeded');
            partDeleteSuccess = true;
          }
        } catch (lastErr) {
          addLog(`Error in last resort deletion: ${lastErr}`);
        }
      }
      
      if (!partDeleteSuccess) {
        return NextResponse.json({ 
          success: false,
          error: `Error during deletion process for part ID ${partId}: ${deleteError.message || String(deleteError)}`,
          details: {
            imageDeleteSuccess,
            partDeleteSuccess,
            partDeleteError: partDeleteError ? String(partDeleteError) : null,
            logs: deletionLogs
          }
        }, { status: 500 });
      }
    }
    
    if (!partDeleteSuccess) {
      return NextResponse.json({ 
        success: false,
        error: `Failed to delete part ID ${partId}`,
        details: {
          imageDeleteSuccess,
          partDeleteSuccess,
          partDeleteError: partDeleteError ? String(partDeleteError) : null,
          logs: deletionLogs
        }
      }, { status: 500 });
    }
    
    return NextResponse.json({
      success: partDeleteSuccess,
      imageDeleteSuccess,
      deletedImages,
      message: partDeleteSuccess 
        ? `Part deleted successfully${imageDeleteSuccess ? ' with images' : ''}`
        : 'Failed to delete part',
      logs: deletionLogs
    });
  } catch (error) {
    console.error('Error in DELETE handler:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
