'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { generateProductSlug } from '@/app/utils/slugify';

export default function DebugPage() {
  const [partId, setPartId] = useState('593');
  const [partTitle, setPartTitle] = useState('VW Passat B8 2015-2022 WagonVariant Pro Used Boot Lights Right LED');
  const [generatedSlug, setGeneratedSlug] = useState('');
  const router = useRouter();

  const handleGenerateSlug = () => {
    const slug = generateProductSlug(partTitle, partId);
    setGeneratedSlug(slug);
  };

  const handleNavigateToOldFormat = () => {
    router.push(`/shop/p/${partId}`);
  };

  const handleNavigateToNewFormat = () => {
    const slug = generateProductSlug(partTitle, partId);
    router.push(`/shop/product/${slug}`);
  };

  return (
    <div className="container mx-auto px-4 py-16">
      <h2 className="text-2xl font-bold mb-4">URL Debug Tool</h2>
      <p className="mb-8 text-gray-600">
        Use this tool to test different URL formats for product pages.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
          <h3 className="text-xl font-semibold mb-4">Generate Slug</h3>

          <div className="mb-4">
            <label className="block text-gray-700 mb-2">Part ID</label>
            <input
              type="text"
              value={partId}
              onChange={(e) => setPartId(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>

          <div className="mb-4">
            <label className="block text-gray-700 mb-2">Part Title</label>
            <input
              type="text"
              value={partTitle}
              onChange={(e) => setPartTitle(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded"
            />
          </div>

          <button
            onClick={handleGenerateSlug}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Generate Slug
          </button>

          {generatedSlug && (
            <div className="mt-4 p-3 bg-gray-100 rounded">
              <p className="font-mono break-all">{generatedSlug}</p>
            </div>
          )}
        </div>

        <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
          <h3 className="text-xl font-semibold mb-4">Test Navigation</h3>

          <div className="space-y-4">
            <button
              onClick={handleNavigateToOldFormat}
              className="w-full px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
            >
              Navigate to Old URL Format (/shop/p/{partId})
            </button>

            <button
              onClick={handleNavigateToNewFormat}
              className="w-full px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
            >
              Navigate to New URL Format (/shop/product/...)
            </button>

            <Link
              href="/shop"
              className="block w-full text-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              Back to Shop
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
