import { useState, useCallback, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';

export interface AlternativePartNumber {
  id: number;
  part_number: string;
  source?: string;
  created_at?: string;
}

export const useAlternativePartNumbers = (partId: string | number, onAiGenerated?: (alternatives: string[]) => void) => {
  const [alternativeNumbers, setAlternativeNumbers] = useState<AlternativePartNumber[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClient();

  // Fetch alternative part numbers from part_to_group table
  const fetchAlternativeNumbers = useCallback(async () => {
    if (!partId) return;

    setIsLoading(true);
    setError(null);

    try {
      console.log('🔍 Fetching alternative part numbers for part:', partId);

      // Get the part's compatibility group first
      const { data: partData, error: partError } = await supabase
        .from('parts')
        .select('partnumber_group')
        .eq('id', parseInt(partId.toString()))
        .single();

      if (partError || !partData?.partnumber_group) {
        console.log('📝 No compatibility group found for this part');
        setAlternativeNumbers([]);
        return;
      }

      console.log('🔍 Part compatibility group:', partData.partnumber_group);

      // Get all alternative part numbers from part_to_group table for this group
      const { data: partToGroup, error: ptgError } = await supabase
        .from('part_to_group')
        .select('partnumber, created_at')
        .eq('group_id', partData.partnumber_group);

      if (ptgError) {
        console.warn('⚠️ Error fetching from part_to_group:', ptgError);
        setAlternativeNumbers([]);
        return;
      }

      console.log('🔍 Part to group data:', partToGroup);

      if (partToGroup && partToGroup.length > 0) {
        console.log('🔍 Raw part_to_group data:', partToGroup);

        // Get the current part's main part number to exclude it from alternatives
        const { data: currentPartGroup, error: currentError } = await supabase
          .from('part_compatibility_groups')
          .select('part_number')
          .eq('id', partData.partnumber_group)
          .single();

        const currentPartNumber = currentPartGroup?.part_number || '';
        console.log('🔍 Current part number from compatibility group:', currentPartNumber);

        // Create alternatives list, excluding the current part number
        const alternatives: AlternativePartNumber[] = partToGroup
          .filter(ptg => {
            const shouldInclude = ptg.partnumber !== currentPartNumber;
            console.log(`🔍 Part number ${ptg.partnumber}: ${shouldInclude ? 'included' : 'excluded (current part)'}`);
            return shouldInclude;
          })
          .map((ptg, index) => ({
            id: Date.now() + index, // Generate unique ID
            part_number: ptg.partnumber,
            source: 'part_to_group'
          }));

        // Remove duplicates (shouldn't be any due to PRIMARY KEY, but just in case)
        const uniqueAlternatives = alternatives.filter((alt, index, self) =>
          index === self.findIndex(a => a.part_number === alt.part_number)
        );

        console.log('✅ Found alternative part numbers:', uniqueAlternatives);
        setAlternativeNumbers(uniqueAlternatives);
      } else {
        console.log('📝 No alternative part numbers found in part_to_group table');
        setAlternativeNumbers([]);
      }

    } catch (err: any) {
      console.error('🚨 Error fetching alternative part numbers:', err);
      setError(err.message || 'Failed to fetch alternative part numbers');
      setAlternativeNumbers([]);
    } finally {
      setIsLoading(false);
    }
  }, [partId, supabase]);

  // Generate alternative part numbers using Gemini AI
  const generateAlternativeNumbers = useCallback(async (currentPartNumber: string) => {
    setIsGenerating(true);
    setError(null);

    try {
      console.log('🤖 Generating alternative part numbers with Gemini AI');
      console.log('📝 Current part number:', currentPartNumber);

      const response = await fetch('/api/ai/generate-alternative-part-numbers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          partNumber: currentPartNumber,
          partId: partId
        }),
      });

      if (!response.ok) {
        throw new Error(`AI generation failed: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.error) {
        throw new Error(data.error);
      }

      console.log('🤖 AI generated alternatives:', data.alternatives);

      if (data.alternatives && data.alternatives.length > 0) {
        const newAlternatives: AlternativePartNumber[] = data.alternatives.map((partNum: string, index: number) => ({
          id: Date.now() + index, // Temporary ID for new items
          part_number: partNum,
          source: 'ai_generated'
        }));

        // Add to existing alternatives (avoiding duplicates)
        setAlternativeNumbers(prev => {
          const combined = [...prev, ...newAlternatives];
          return combined.filter((alt, index, self) =>
            index === self.findIndex(a => a.part_number === alt.part_number)
          );
        });

        // Notify parent component about AI-generated alternatives
        if (onAiGenerated) {
          const aiPartNumbers = newAlternatives.map(alt => alt.part_number);
          onAiGenerated(aiPartNumbers);
          console.log('🔔 Notified parent about AI alternatives:', aiPartNumbers);
        }

        return newAlternatives;
      } else {
        console.log('🤖 No alternative part numbers generated');
        return [];
      }

    } catch (err: any) {
      console.error('🚨 Error generating alternative part numbers:', err);
      setError(err.message || 'Failed to generate alternative part numbers');
      return [];
    } finally {
      setIsGenerating(false);
    }
  }, [partId]);

  // Delete an alternative part number
  const deleteAlternativeNumber = useCallback(async (partNumberToDelete: string) => {
    try {
      console.log('🗑️ Deleting alternative part number:', partNumberToDelete);

      // Find the alternative to delete
      const alternativeToDelete = alternativeNumbers.find(alt => alt.part_number === partNumberToDelete);
      if (!alternativeToDelete) {
        console.warn('Alternative part number not found:', partNumberToDelete);
        return;
      }

      // Remove from local state immediately for better UX
      setAlternativeNumbers(prev =>
        prev.filter(alt => alt.part_number !== partNumberToDelete)
      );

      // If it's an AI-generated alternative (not yet in database), just remove from UI
      if (alternativeToDelete.source === 'ai_generated') {
        console.log('🗑️ Removed AI-generated alternative from UI only');

        // Also notify parent to remove from pending save list
        if (onAiGenerated) {
          const remainingAiAlternatives = alternativeNumbers
            .filter(alt => alt.source === 'ai_generated' && alt.part_number !== partNumberToDelete)
            .map(alt => alt.part_number);
          onAiGenerated(remainingAiAlternatives);
          console.log('🔔 Updated parent with remaining AI alternatives:', remainingAiAlternatives);
        }
        return;
      }

      // If it's from database, delete from part_to_group table
      const { error: deleteError } = await supabase
        .from('part_to_group')
        .delete()
        .eq('partnumber', partNumberToDelete);

      if (deleteError) {
        console.error('🚨 Error deleting alternative part number from database:', deleteError);

        // Restore the item in local state since deletion failed
        setAlternativeNumbers(prev => {
          if (!prev.find(alt => alt.part_number === partNumberToDelete)) {
            return [...prev, alternativeToDelete];
          }
          return prev;
        });

        throw new Error(`Failed to delete alternative part number: ${deleteError.message}`);
      }

      console.log('✅ Successfully deleted alternative part number from database');

    } catch (err: any) {
      console.error('🚨 Error in deleteAlternativeNumber:', err);
      setError(err.message || 'Failed to delete alternative part number');
      throw err;
    }
  }, [alternativeNumbers, onAiGenerated, supabase]);

  // Load alternative numbers when component mounts or partId changes
  useEffect(() => {
    fetchAlternativeNumbers();
  }, [fetchAlternativeNumbers]);

  return {
    alternativeNumbers,
    isLoading,
    isGenerating,
    error,
    fetchAlternativeNumbers,
    generateAlternativeNumbers,
    deleteAlternativeNumber
  };
};
