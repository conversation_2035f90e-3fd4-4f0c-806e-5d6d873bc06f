'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Search, User, ShoppingCart, Heart, Menu, X } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import MegaMenu from './MegaMenu';

interface Category {
  id: number;
  label: string;
  href: string;
  parent_category_id: number | null;
}

const Header = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [categories, setCategories] = useState<Category[]>([]);
  const [rootCategories, setRootCategories] = useState<Category[]>([]);
  const [activeCategory, setActiveCategory] = useState<number | null>(null);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const megaMenuRef = useRef<HTMLDivElement>(null);

  // Fetch categories from Supabase
  useEffect(() => {
    const fetchCategories = async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('car_part_categories')
        .select('*')
        .eq('isActive', true);

      if (error) {
        console.error('Error fetching categories:', error);
        return;
      }

      if (data) {
        setCategories(data);
        // Filter root categories (those without a parent)
        const roots = data.filter(cat => cat.parent_category_id === null);

        // Update the href property to point to the shop page with the category parameter and page=1
        const rootsWithUpdatedHrefs = roots.map(cat => ({
          ...cat,
          href: `/shop?category=${cat.id}&page=1`
        }));

        setRootCategories(rootsWithUpdatedHrefs);

        // Also update all categories with the correct href
        const updatedCategories = data.map(cat => ({
          ...cat,
          href: `/shop?category=${cat.id}&page=1`
        }));

        setCategories(updatedCategories);
      }
    };

    fetchCategories();
  }, []);

  // Handle search form submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Redirect to shop page with search query
      // Using advanced_part_search function via our API
      window.location.href = `/shop?query=${encodeURIComponent(searchQuery)}&page=1`;
    }
  };

  // Handle category hover
  const handleCategoryHover = (categoryId: number) => {
    setActiveCategory(categoryId);
  };

  // Handle click outside to close mega menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (megaMenuRef.current && !megaMenuRef.current.contains(event.target as Node)) {
        setActiveCategory(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Get subcategories for a given parent category
  const getSubcategories = (parentId: number) => {
    return categories.filter(cat => cat.parent_category_id === parentId);
  };

  return (
    <header className="w-full bg-white shadow-md">
      {/* Top Bar */}
      <div className="container mx-auto px-4 py-3 flex items-center justify-between">
        {/* Mobile Menu Button */}
        <div className="lg:hidden">
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="p-2 text-gray-700 hover:text-gray-900"
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Logo */}
        <div className="flex-shrink-0">
          <Link href="/">
            <div className="flex items-center">
              <div className="text-black text-2xl font-semibold tracking-tighter">
                Autoflow
              </div>
            </div>
          </Link>
        </div>

        {/* Search Bar */}
        <div className="hidden md:block flex-grow max-w-md mx-4">
          <form onSubmit={handleSearchSubmit} className="relative">
            <input
              ref={searchInputRef}
              type="text"
              placeholder="What can we help you find?"
              className="w-full py-2 pl-4 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-teal-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <button
              type="submit"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              <Search size={18} />
            </button>
          </form>
        </div>

        {/* Right Side Icons */}
        <div className="flex items-center space-x-4">
          <Link href="/login" className="text-gray-700 hover:text-gray-900">
            <div className="flex flex-col items-center">
              <User size={20} />
              <span className="text-xs mt-1">Sign In</span>
            </div>
          </Link>
        </div>
      </div>

      {/* Mobile Search (only visible on mobile) */}
      <div className="md:hidden px-4 py-2">
        <form onSubmit={handleSearchSubmit} className="relative">
          <input
            type="text"
            placeholder="What can we help you find?"
            className="w-full py-2 pl-4 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-teal-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <button
            type="submit"
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
          >
            <Search size={18} />
          </button>
        </form>
      </div>

      {/* Navigation Bar */}
      <nav className="bg-gray-100 hidden lg:block">
        <div className="container mx-auto">
          <ul className="flex">
            {rootCategories.map((category) => (
              <li key={category.id} className="relative group">
                <Link
                  href={category.href}
                  className="block px-4 py-3 text-gray-800 hover:text-teal-600 font-medium text-sm"
                  onMouseEnter={() => handleCategoryHover(category.id)}
                >
                  {category.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </nav>

      {/* Mega Menu */}
      {activeCategory !== null && (
        <div
          ref={megaMenuRef}
          className="absolute left-0 right-0 bg-white shadow-lg z-50 border-t border-gray-200"
          onMouseLeave={() => setActiveCategory(null)}
        >
          <MegaMenu
            categoryId={activeCategory}
            categories={categories}
            getSubcategories={getSubcategories}
          />
        </div>
      )}

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="lg:hidden bg-white shadow-lg absolute left-0 right-0 z-50 border-t border-gray-200">
          <ul className="py-2">
            {rootCategories.map((category) => (
              <li key={category.id}>
                <Link
                  href={`/shop?category=${category.id}&page=1`}
                  className="block px-4 py-3 text-gray-800 hover:bg-gray-100"
                >
                  {category.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      )}
    </header>
  );
};

export default Header;
