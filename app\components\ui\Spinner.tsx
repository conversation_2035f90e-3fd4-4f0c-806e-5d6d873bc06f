// Spinner.tsx
import React from 'react';

interface SpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string; // Tailwind color class, e.g., 'text-blue-500'
  extraClasses?: string;
}

const Spinner: React.FC<SpinnerProps> = ({
  size = 'md',
  color = 'text-blue-500',
  extraClasses = '',
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
  };

  return (
    <div
      className={`animate-spin rounded-full border-t-4 border-b-4 ${sizeClasses[size]} ${color} border-opacity-25 ${extraClasses}`}
      role="status"
    >
      <span className="sr-only">Loading...</span>
    </div>
  );
};

export default Spinner;

// SAMPLE USE

// <div>
//   <Spinner /> {/* Default: medium size, blue color */}
//   <Spinner size="lg" color="text-red-500" /> {/* Large, red spinner */}
//   <Spinner size="sm" color="text-green-600" extraClasses="ml-2" /> {/* Small, green spinner with margin to the left*/}
// </div>