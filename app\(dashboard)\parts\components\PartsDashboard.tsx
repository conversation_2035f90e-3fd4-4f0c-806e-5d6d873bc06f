'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { createClient } from '@/app/libs/supabase/client';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Package, AlertTriangle, TrendingUp, Search, MoreVertical, ChevronRight, Plus } from 'lucide-react';
import BottomNavigation from '@/app/components/ui/menu/BottomNavigation';
import AddPartModal from './AddPartModal/index';
import Notification from '@/app/components/ui/Notification';
import { useUserCookie } from '@/app/hooks/useUserCookie';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      when: "beforeChildren",
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { duration: 0.5 }
  }
};

// Types
interface Part {
  id: number;
  title: string;
  stock: number;
  reorder_level?: number;
  price: number;
  createdAt: string;
  image_url?: string;
  condition?: string;
  category_id?: number;
}

interface StatsCardProps {
  title: string;
  value: string | number;
  change?: number;
  icon: React.ReactNode;
  color: 'teal' | 'orange' | 'gray';
  href?: string;
}

interface PartsListCardProps {
  title: string;
  parts: Part[];
  viewMoreLink: string;
  emptyMessage: string;
}

// Card Components
const Card = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
  <motion.div
    className={`bg-white p-5 md:p-6 rounded-lg shadow-sm border border-gray-200 w-full ${className}`}
    variants={itemVariants}
  >
    {children}
  </motion.div>
);

const StatsCard: React.FC<StatsCardProps> = ({ title, value, change, icon, color, href }) => {
  const colorClasses = {
    teal: 'bg-teal-50 border-teal-200',
    orange: 'bg-orange-50 border-orange-200',
    gray: 'bg-gray-50 border-gray-200',
  };
  const iconColorClasses = {
    teal: 'text-teal-600',
    orange: 'text-orange-600',
    gray: 'text-gray-600',
  };
  const changeColor = change && change > 0 ? 'text-green-600' : 'text-red-600';

  return (
    <motion.div
      className={`p-4 md:p-6 rounded-lg shadow-sm border ${colorClasses[color]} flex flex-col justify-between w-full`}
      variants={itemVariants}
    >
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-medium text-gray-500">{title}</h3>
        <div className={`p-2 rounded-full bg-white ${iconColorClasses[color]}`}>
          {icon}
        </div>
      </div>
      <p className="text-2xl md:text-3xl font-semibold text-gray-800 mb-1">{value}</p>
      {change !== undefined && (
        <div className={`flex items-center text-xs ${changeColor}`}>
          <span>{change > 0 ? '+' : ''}{change}% vs last month</span>
        </div>
      )}
      {href && (
        <Link href={href} className="mt-3 text-sm text-teal-600 hover:text-teal-800 font-medium flex items-center">
          View Details <ChevronRight size={16} className="ml-1" />
        </Link>
      )}
    </motion.div>
  );
};

const PartsListCard: React.FC<PartsListCardProps> = ({ title, parts, viewMoreLink, emptyMessage }) => {
  return (
    <Card className="h-full flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
        <button className="text-gray-400 hover:text-gray-600">
          <MoreVertical size={18} />
        </button>
      </div>

      {parts.length === 0 ? (
        <div className="flex-grow flex items-center justify-center text-center p-6">
          <p className="text-gray-500">{emptyMessage}</p>
        </div>
      ) : (
        <div className="space-y-3 overflow-y-auto flex-grow mb-4">
          {parts.map((part, index) => (
            <motion.div
              key={part.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <Link
                href={`/parts/${part.id}`}
                className="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-100 rounded-md flex items-center justify-center text-gray-500">
                    {part.image_url ? (
                      <img src={part.image_url} alt={part.title} className="w-full h-full object-cover rounded-md" />
                    ) : (
                      <Package size={20} />
                    )}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">{part.title}</p>
                    <p className="text-xs text-gray-500">ID: {part.id}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-gray-900">${part.price?.toLocaleString() || '0'}</p>
                  <p className="text-xs text-gray-500">Stock: {part.stock}</p>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      )}

      <Link href={viewMoreLink} className="text-sm text-teal-600 hover:text-teal-800 font-medium flex items-center mt-auto">
        View All <ChevronRight size={16} className="ml-1" />
      </Link>
    </Card>
  );
};

// Dashboard Header Component
const DashboardHeader = ({ title }: { title: string }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white p-6 border-b border-gray-200 mb-6 shadow-sm"
    >
      <div className="container mx-auto flex items-center">
        <Package size={32} className="text-teal-600 mr-4" />
        <div>
          <h1 className="text-2xl font-bold text-gray-800">{title}</h1>
          <p className="text-gray-600">Manage your parts inventory</p>
        </div>
      </div>
    </motion.div>
  );
};

const PartsDashboard: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [latestParts, setLatestParts] = useState<Part[]>([]);
  const [reorderParts, setReorderParts] = useState<Part[]>([]);
  const [totalParts, setTotalParts] = useState(0);
  const [totalValue, setTotalValue] = useState(0);
  const [stockChange, setStockChange] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showNotification, setShowNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState('');
  const [searchInput, setSearchInput] = useState('');
  const router = useRouter();

  // Get user role from cookie
  const { roleName, isLoading: cookieLoading } = useUserCookie();

  // Check if user is an employee
  const isEmployee = roleName?.toLowerCase() === 'employee';

  // Log role information for debugging
  console.log('[PartsDashboard] User role:', roleName);
  console.log('[PartsDashboard] Is employee:', isEmployee);

  // Function to show notification
  const displayNotification = (message: string) => {
    setNotificationMessage(message);
    setShowNotification(true);

    // Auto-hide notification after 3 seconds
    setTimeout(() => {
      setShowNotification(false);
    }, 3000);
  };

  // Handle search
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchInput.trim()) {
      router.push(`/parts?query=${encodeURIComponent(searchInput.trim())}`);
    }
  };

  // Handle modal open/close
  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSuccess = () => {
    setIsModalOpen(false);
    displayNotification('Part has been added successfully');
    // Refresh the dashboard data
    fetchDashboardData();
  };

  // Define fetchDashboardData at component scope so it can be called from handleSuccess
  const fetchDashboardData = async () => {
      try {
        setIsLoading(true);
        const supabase = createClient();

        // Fetch latest parts (modified query)
        const { data: latest, error: latestError } = await supabase
          .from('parts')
          .select(`
            id,
            title,
            createdAt,
            category_id
          `)
          .order('createdAt', { ascending: false })
          .limit(5);

        if (latestError) {
          console.error('Error fetching latest parts:', latestError.message);
          throw latestError;
        }

        // Separately fetch conditions for these parts
        let partsConditions = {};
        if (latest && latest.length > 0) {
          const partIds = latest.map(part => part.id);

          const { data: conditions, error: conditionsError } = await supabase
            .from('parts_condition')
            .select(`
              id,
              part_id,
              stock,
              condition
            `)
            .in('part_id', partIds);

          if (conditionsError) {
            console.error('Error fetching part conditions:', conditionsError.message);
          } else if (conditions) {
            // Group conditions by part_id
            conditions.forEach(condition => {
              if (!partsConditions[condition.part_id]) {
                partsConditions[condition.part_id] = [];
              }
              partsConditions[condition.part_id].push(condition);
            });
          }
        }

        // Fetch condition IDs to get prices
        let partPrices = {};
        if (Object.keys(partsConditions).length > 0) {
          // Extract all condition IDs from all parts
          const conditionIds = Object.values(partsConditions)
            .flat()
            .map(condition => condition.id)
            .filter(id => id);

          if (conditionIds.length > 0) {
            // Fetch prices for these condition IDs
            const { data: priceData, error: priceError } = await supabase
              .from('part_price')
              .select('condition_id, price')
              .in('condition_id', conditionIds);

            if (priceError) {
              console.error('Error fetching part prices:', priceError.message);
            } else if (priceData) {
              // Create a map of condition_id to price
              partPrices = priceData.reduce((acc, item) => {
                acc[item.condition_id] = item.price || 0;
                return acc;
              }, {});
            }
          }
        }

        // Fetch parts with low stock (modified approach)
        const { data: lowStockConditions, error: lowStockError } = await supabase
          .from('parts_condition')
          .select(`
            id,
            stock,
            condition,
            part_id
          `)
          .lt('stock', 5) // Using fixed threshold as reorder_level might not be set
          .gt('stock', 0) // Only include items that are in stock
          .order('stock', { ascending: true })
          .limit(5);

        if (lowStockError) {
          console.error('Error fetching low stock parts:', lowStockError.message);
          throw lowStockError;
        }

        // Fetch the actual parts data for the low stock items
        let lowStock = [];
        if (lowStockConditions && lowStockConditions.length > 0) {
          const partIds = lowStockConditions
            .map(condition => condition.part_id)
            .filter(id => id);

          if (partIds.length > 0) {
            const { data: partsData, error: partsError } = await supabase
              .from('parts')
              .select(`
                id,
                title,
                createdAt,
                category_id
              `)
              .in('id', partIds);

            if (partsError) {
              console.error('Error fetching parts data for low stock:', partsError.message);
            } else if (partsData) {
              // Combine the parts data with the condition data
              lowStock = lowStockConditions.map(condition => {
                const matchingPart = partsData.find(part => part.id === condition.part_id) || {};
                return {
                  ...condition,
                  parts: matchingPart
                };
              });
            }
          }
        }

        // Fetch total parts count
        const { count, error: countError } = await supabase
          .from('parts')
          .select('*', { count: 'exact', head: true });

        if (countError) {
          console.error('Error fetching parts count:', countError.message);
          throw countError;
        }

        // Calculate inventory value
        const { data: inventoryData, error: inventoryError } = await supabase
          .from('parts_condition')
          .select(`
            id,
            stock
          `);

        if (inventoryError) {
          console.error('Error fetching inventory data:', inventoryError.message);
          throw inventoryError;
        }

        // If we haven't fetched all condition IDs for inventory value calculation
        let allPartPrices = {...partPrices};
        if (inventoryData && inventoryData.length > 0) {
          // Get all condition IDs that we don't already have prices for
          const inventoryConditionIds = inventoryData
            .map(item => item.id)
            .filter(id => id && !allPartPrices[id]);

          if (inventoryConditionIds.length > 0) {
            const { data: additionalPrices, error: additionalPriceError } = await supabase
              .from('part_price')
              .select('condition_id, price')
              .in('condition_id', inventoryConditionIds);

            if (additionalPriceError) {
              console.error('Error fetching additional prices:', additionalPriceError.message);
            } else if (additionalPrices) {
              // Add to our price map
              additionalPrices.forEach(item => {
                allPartPrices[item.condition_id] = item.price || 0;
              });
            }
          }
        }

        // Process the latest parts data
        const formattedLatest = latest.map(part => {
          // Get conditions for this part
          const conditions = partsConditions[part.id] || [];
          // Get the first condition (or null if none)
          const condition = conditions.length > 0 ? conditions[0] : null;
          // Look up the price for this condition
          const price = condition && partPrices[condition.id] ? partPrices[condition.id] : 0;

          return {
            id: part.id,
            title: part.title || 'Unnamed Part',
            stock: condition?.stock || 0,
            condition: condition?.condition || 'Unknown',
            price: price,
            createdAt: part.createdAt,
            category_id: part.category_id
          };
        });

        // Process the low stock parts data
        const formattedReorder = lowStock.map(item => {
          const price = partPrices[item.id] || 0;

          return {
            id: item.parts?.id || item.part_id || 0,
            title: item.parts?.title || 'Unnamed Part',
            stock: item.stock || 0,
            condition: item.condition || 'Unknown',
            price: price,
            createdAt: item.parts?.createdAt || '',
            category_id: item.parts?.category_id
          };
        });

        // Calculate total inventory value using our price map
        const totalInventoryValue = inventoryData.reduce((sum, part) => {
          try {
            const price = allPartPrices[part.id] || 0;
            return sum + ((part.stock || 0) * price);
          } catch (err) {
            console.warn('Error calculating inventory value for part:', part.id, err);
            return sum;
          }
        }, 0);

        // Set state with fetched data
        setLatestParts(formattedLatest);
        setReorderParts(formattedReorder);
        setTotalParts(count || 0);
        setTotalValue(totalInventoryValue || 0);

        // Mock change percentage for now - in a real app this would be calculated from historical data
        setStockChange(5);
      } catch (error) {
        console.error('Error fetching dashboard data:', error instanceof Error ? error.message : JSON.stringify(error));
      } finally {
        setIsLoading(false);
      }
    };

  // Call fetchDashboardData when component mounts
  useEffect(() => {
    // Only fetch data if user is not an employee or if we're still loading the role
    // This ensures we have the data ready if the user is not an employee
    if (!isEmployee || cookieLoading) {
      fetchDashboardData();
    }
  }, [isEmployee, cookieLoading]);

  // Skeleton loader for cards
  const SkeletonCard = () => (
    <div className="p-4 md:p-6 rounded-lg shadow-sm border border-gray-200 animate-pulse">
      <div className="flex items-center justify-between mb-2">
        <div className="h-4 bg-gray-200 rounded w-1/3"></div>
        <div className="w-10 h-10 rounded-full bg-gray-200"></div>
      </div>
      <div className="h-8 bg-gray-200 rounded mb-1 w-1/2 mt-2"></div>
      <div className="h-3 bg-gray-200 rounded w-1/4 mt-2"></div>
    </div>
  );

  const SkeletonListCard = () => (
    <div className="bg-white p-5 md:p-6 rounded-lg shadow-sm border border-gray-200 h-full animate-pulse">
      <div className="flex justify-between items-center mb-4">
        <div className="h-5 bg-gray-200 rounded w-1/3"></div>
        <div className="w-6 h-6 rounded bg-gray-200"></div>
      </div>
      <div className="space-y-3 mb-4">
        {[...Array(5)].map((_, idx) => (
          <div key={idx} className="flex items-center justify-between p-2">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-200 rounded-md"></div>
              <div>
                <div className="h-4 bg-gray-200 rounded w-24"></div>
                <div className="h-3 bg-gray-200 rounded w-16 mt-1"></div>
              </div>
            </div>
            <div className="text-right">
              <div className="h-4 bg-gray-200 rounded w-12"></div>
              <div className="h-3 bg-gray-200 rounded w-10 mt-1"></div>
            </div>
          </div>
        ))}
      </div>
      <div className="h-4 bg-gray-200 rounded w-16 mt-auto"></div>
    </div>
  );

  return (
    <>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <DashboardHeader title="Parts Dashboard" />

        <div className="flex flex-col w-full max-w-7xl mx-auto py-3 pb-24">
          {/* Intro Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-lg shadow-md p-6 mb-3 w-full"
        >
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800">Quick links</h2>
            <Link
              href="/parts?view=all"
              className="hidden md:flex items-center text-teal-600 hover:text-teal-800 font-medium"
            >
              View All Parts <ChevronRight size={16} className="ml-1" />
            </Link>
          </div>

          {/* Action Buttons - Desktop */}
          <div className="hidden md:grid md:grid-cols-3 gap-4 mb-6">
            <button
              onClick={handleOpenModal}
              className="flex items-center justify-center px-4 py-3 bg-teal-600 text-white rounded-md hover:bg-teal-700 transition-colors text-center"
            >
              <Plus className="w-5 h-5 mr-2" />
              Add New Part
            </button>
            <Link
              href="/parts?view=all"
              className="flex items-center justify-center px-4 py-3 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-md transition-colors text-center"
            >
              <Package className="w-5 h-5 mr-2" />
              View All Parts
            </Link>
            <Link
              href="/parts?filter=reorder"
              className="flex items-center justify-center px-4 py-3 bg-orange-100 hover:bg-orange-200 text-orange-800 rounded-md transition-colors text-center"
            >
              <AlertTriangle className="w-5 h-5 mr-2" />
              View Low Stock
            </Link>
          </div>

          {/* Action Buttons - Mobile (Card Style) */}
          <div className="md:hidden w-full mb-6">
            <div className="grid grid-cols-3 gap-4">
              <Link href="/parts?view=all" className="flex flex-col items-center group">
                <div className="bg-pink-50 w-full aspect-square rounded-xl flex items-center justify-center mb-2 shadow-sm transition-all duration-200 group-hover:shadow group-hover:bg-pink-100 group-active:scale-95">
                  <Package className="w-6 h-6 text-pink-500" />
                </div>
                <span className="text-xs font-medium text-gray-700">All Parts</span>
              </Link>

              <button onClick={handleOpenModal} className="flex flex-col items-center group">
                <div className="bg-teal-50 w-full aspect-square rounded-xl flex items-center justify-center mb-2 shadow-sm transition-all duration-200 group-hover:shadow group-hover:bg-teal-100 group-active:scale-95">
                  <Plus className="w-6 h-6 text-teal-500" />
                </div>
                <span className="text-xs font-medium text-gray-700">Add Part</span>
              </button>

              <Link href="/parts?filter=reorder" className="flex flex-col items-center group">
                <div className="bg-orange-50 w-full aspect-square rounded-xl flex items-center justify-center mb-2 shadow-sm transition-all duration-200 group-hover:shadow group-hover:bg-orange-100 group-active:scale-95">
                  <AlertTriangle className="w-6 h-6 text-orange-500" />
                </div>
                <span className="text-xs font-medium text-gray-700">Low Stock</span>
              </Link>
            </div>
          </div>

          {/* Search - Desktop only */}
          <form onSubmit={handleSearchSubmit} className="relative w-full hidden md:block">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search parts by name or part number..."
              className="pl-10 pr-4 py-3 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              value={searchInput}
              onChange={handleSearchChange}
            />
            <button
              type="submit"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              aria-label="Search parts"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </form>
        </motion.div>

        {/* Content Grid - Only visible to non-employee users */}
        {!isEmployee && (
          <motion.div
            className="bg-white rounded-lg shadow-md p-6 mb-3 w-full"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full"
            >
            {/* Stat Cards */}
            {isLoading ? (
              <>
                <SkeletonCard />
                <SkeletonCard />
                <SkeletonCard />
              </>
            ) : (
              <>
                <StatsCard
                  title="Total Parts"
                  value={totalParts.toLocaleString()}
                  change={stockChange}
                  icon={<Package className="w-5 h-5" />}
                  color="teal"
                  href="/parts"
                />
                <StatsCard
                  title="Inventory Value"
                  value={`$${totalValue.toLocaleString()}`}
                  icon={<TrendingUp className="w-5 h-5" />}
                  color="gray"
                  href="/parts"
                />
                <StatsCard
                  title="Reorder Alert"
                  value={reorderParts.length}
                  icon={<AlertTriangle className="w-5 h-5" />}
                  color="orange"
                  href="/parts?filter=reorder"
                />
              </>
            )}

            {/* List Cards */}
            {isLoading ? (
              <>
                <SkeletonListCard />
                <SkeletonListCard />
                <SkeletonListCard />
              </>
            ) : (
              <>
                <PartsListCard
                  title="Latest Parts"
                  parts={latestParts}
                  viewMoreLink="/parts?sort=newest"
                  emptyMessage="No parts added recently"
                />
                <PartsListCard
                  title="Reorder Level Parts"
                  parts={reorderParts}
                  viewMoreLink="/parts?filter=reorder"
                  emptyMessage="No parts at reorder level"
                />
                <Card className="h-full flex flex-col">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold text-gray-800">Inventory Summary</h3>
                    <button className="text-gray-400 hover:text-gray-600">
                      <MoreVertical size={18} />
                    </button>
                  </div>

                  <div className="space-y-4 flex-grow">
                    <motion.div
                      className="bg-gray-50 p-4 rounded-md"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.1 }}
                    >
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Total Inventory Value</h4>
                      <p className="text-2xl font-bold text-gray-900">${totalValue.toLocaleString()}</p>
                    </motion.div>

                    <motion.div
                      className="bg-gray-50 p-4 rounded-md"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.2 }}
                    >
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Average Part Value</h4>
                      <p className="text-2xl font-bold text-gray-900">
                        ${totalParts > 0 ? (totalValue / totalParts).toLocaleString(undefined, {maximumFractionDigits: 2}) : '0'}
                      </p>
                    </motion.div>

                    <motion.div
                      className="bg-gray-50 p-4 rounded-md"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.3 }}
                    >
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Parts Needing Reorder</h4>
                      <div className="flex items-center">
                        <p className="text-2xl font-bold text-gray-900 mr-2">{reorderParts.length}</p>
                        <span className="text-sm text-orange-600">Attention needed</span>
                      </div>
                    </motion.div>
                  </div>

                  <Link href="/parts/inventory" className="text-sm text-teal-600 hover:text-teal-800 font-medium flex items-center mt-4">
                    View Full Inventory Report <ChevronRight size={16} className="ml-1" />
                  </Link>
                </Card>
              </>
            )}
            </motion.div>
          </motion.div>
        )}
      </div>
    </div>

    {/* Bottom Navigation (Mobile only) */}
    <BottomNavigation onOpenAddModal={handleOpenModal} />

    {/* Add Part Modal */}
    <AddPartModal
      isOpen={isModalOpen}
      onClose={handleCloseModal}
      onSuccess={handleSuccess}
    />

    {/* Notification */}
    <AnimatePresence>
      {showNotification && (
        <Notification
          header="Parts Notification"
          body={notificationMessage}
          type="info"
          duration={3000}
          position="bottom-middle"
          onClose={() => setShowNotification(false)}
        />
      )}
    </AnimatePresence>
    </>
  );
};

export default PartsDashboard;