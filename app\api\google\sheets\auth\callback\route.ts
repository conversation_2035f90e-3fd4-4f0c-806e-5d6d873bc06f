import { NextRequest, NextResponse } from 'next/server';
import { googleSheetsService } from '@/app/services/googleSheets';
import { createClient } from '@/app/libs/supabase/server';

/**
 * GET: Handle Google OAuth callback
 */
export async function GET(request: NextRequest) {
  try {
    // Get code and state from query params
    const url = new URL(request.url);
    const code = url.searchParams.get('code');
    const error = url.searchParams.get('error');
    const state = url.searchParams.get('state');

    // Extract user ID from state parameter
    let userId = '';
    if (state) {
      try {
        const decodedState = JSON.parse(Buffer.from(state, 'base64').toString());
        userId = decodedState.userId || '';
      } catch (stateError) {
        console.error('Error parsing state parameter:', stateError);
      }
    }

    // Check for error
    if (error) {
      console.error('Google auth error:', error);
      return NextResponse.redirect(new URL('/integrations/google-sheets?error=' + error, request.url));
    }

    // Check for code
    if (!code) {
      console.error('No code provided in callback');
      return NextResponse.redirect(new URL('/integrations/google-sheets?error=no_code', request.url));
    }

    // Exchange code for tokens
    const redirectUri = process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI || 'http://localhost:3000/api/google/sheets/auth/callback';

    // If we don't have a user ID from the state parameter, redirect to login
    if (!userId) {
      console.error('No user ID found in state parameter');
      return NextResponse.redirect(new URL('/login?error=missing_user_id', request.url));
    }

    // Use the user ID from the state parameter
    try {
      await googleSheetsService.authenticate(code, redirectUri, userId);
    } catch (authError: any) {
      console.error('Error authenticating with Google:', authError);

      // Handle specific OAuth errors
      let errorMessage = authError?.message || 'Unknown authentication error';
      let errorCode = 'auth_error';

      // Check for common OAuth errors
      if (errorMessage.includes('invalid_grant')) {
        errorMessage = 'Authentication session expired or already used. Please try authenticating again.';
        errorCode = 'invalid_grant';
      }

      // Return HTML that will close the popup and notify the parent window of the error
      const html = `
        <!DOCTYPE html>
        <html>
          <head>
            <title>Authentication Error</title>
            <script>
              window.onload = function() {
                // Notify the opener (parent window) that auth failed with specific error
                if (window.opener) {
                  window.opener.postMessage({
                    type: 'GOOGLE_AUTH_ERROR',
                    error: ${JSON.stringify(errorCode)},
                    message: ${JSON.stringify(errorMessage)}
                  }, '*');
                  // Close this popup window
                  window.close();
                } else {
                  // If no opener, redirect to the integration page
                  window.location.href = '/integrations/google-sheets?error=${encodeURIComponent(errorCode)}';
                }
              };
            </script>
          </head>
          <body>
            <h3>Authentication failed</h3>
            <p>Error: ${errorMessage}</p>
            <p>This window should close automatically. If it doesn't, you can close it and return to the application.</p>
          </body>
        </html>
      `;

      return new NextResponse(html, {
        status: 200,
        headers: {
          'Content-Type': 'text/html',
        },
      });
    }

    // Instead of redirecting, return HTML that will close the popup and notify the parent window
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Authentication Successful</title>
          <script>
            window.onload = function() {
              // Notify the opener (parent window) that auth was successful
              if (window.opener) {
                window.opener.postMessage({ type: 'GOOGLE_AUTH_SUCCESS' }, '*');
                // Close this popup window
                window.close();
              } else {
                // If no opener, redirect to the integration page
                window.location.href = '/integrations/google-sheets?success=true';
              }
            };
          </script>
        </head>
        <body>
          <h3>Authentication successful!</h3>
          <p>This window should close automatically. If it doesn't, you can close it and return to the application.</p>
        </body>
      </html>
    `;

    return new NextResponse(html, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
      },
    });
  } catch (error: any) {
    console.error('Error in Google auth callback:', error);
    // Return HTML that will close the popup and notify the parent window of the error
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Authentication Error</title>
          <script>
            window.onload = function() {
              // Notify the opener (parent window) that auth failed
              if (window.opener) {
                window.opener.postMessage({ type: 'GOOGLE_AUTH_ERROR', error: ${JSON.stringify(error.message)} }, '*');
                // Close this popup window
                window.close();
              } else {
                // If no opener, redirect to the integration page
                window.location.href = '/integrations/google-sheets?error=${encodeURIComponent(error.message)}';
              }
            };
          </script>
        </head>
        <body>
          <h3>Authentication failed</h3>
          <p>Error: ${error.message}</p>
          <p>This window should close automatically. If it doesn't, you can close it and return to the application.</p>
        </body>
      </html>
    `;

    return new NextResponse(html, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
      },
    });
  }
}
