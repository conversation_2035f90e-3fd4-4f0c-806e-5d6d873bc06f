'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Plus, Search, RefreshCw, Edit, Trash2, Tag, Layers } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { Variation, Generation, Model, Brand } from '../types';
import AddVariationModal from './modals/AddVariationModal';
import EditVariationModal from './modals/EditVariationModal';
import DeleteConfirmModal from './modals/DeleteConfirmModal';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

interface VariationsTabProps {
  onVariationUpdated: () => void;
}

const VariationsTab: React.FC<VariationsTabProps> = ({ onVariationUpdated }) => {
  const [variations, setVariations] = useState<Variation[]>([]);
  const [filteredVariations, setFilteredVariations] = useState<Variation[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [generations, setGenerations] = useState<Generation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedBrandId, setSelectedBrandId] = useState<number | ''>('');
  const [selectedModelId, setSelectedModelId] = useState<number | ''>('');
  const [selectedGenerationId, setSelectedGenerationId] = useState<number | ''>('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedVariation, setSelectedVariation] = useState<Variation | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const supabase = createClient();

  // Fetch brands from Supabase
  useEffect(() => {
    const fetchBrands = async () => {
      try {
        const { data, error } = await supabase
          .from('car_brands')
          .select('*')
          .order('brand_name');

        if (error) throw error;

        setBrands(data || []);
      } catch (error) {
        console.error('Error fetching brands:', error);
      }
    };

    fetchBrands();
  }, [supabase]);

  // Fetch models based on selected brand
  useEffect(() => {
    const fetchModels = async () => {
      try {
        let query = supabase
          .from('car_models')
          .select('*')
          .order('model_name');

        if (selectedBrandId) {
          query = query.eq('brand_id', selectedBrandId);
        }

        const { data, error } = await query;

        if (error) throw error;

        setModels(data || []);

        // Reset selected model if it doesn't belong to the selected brand
        if (selectedBrandId && selectedModelId) {
          const modelExists = data?.some(model => model.id === selectedModelId);
          if (!modelExists) {
            setSelectedModelId('');
            setSelectedGenerationId('');
          }
        }
      } catch (error) {
        console.error('Error fetching models:', error);
      }
    };

    fetchModels();
  }, [refreshTrigger, selectedBrandId, supabase]);

  // Fetch generations based on selected model
  useEffect(() => {
    const fetchGenerations = async () => {
      try {
        let query = supabase
          .from('car_generation')
          .select('*')
          .order('name');

        if (selectedModelId) {
          query = query.eq('model_id', selectedModelId);
        }

        const { data, error } = await query;

        if (error) throw error;

        setGenerations(data || []);

        // Reset selected generation if it doesn't belong to the selected model
        if (selectedModelId && selectedGenerationId) {
          const generationExists = data?.some(generation => generation.id === selectedGenerationId);
          if (!generationExists) {
            setSelectedGenerationId('');
          }
        }
      } catch (error) {
        console.error('Error fetching generations:', error);
      }
    };

    fetchGenerations();
  }, [refreshTrigger, selectedModelId, supabase]);

  // Fetch all variations from Supabase
  useEffect(() => {
    const fetchVariations = async () => {
      setIsLoading(true);
      try {
        // Always fetch all variations and handle filtering in the filter useEffect
        const { data, error } = await supabase
          .from('car_variation')
          .select('*')
          .order('variation');

        if (error) throw error;

        setVariations(data || []);
        // Don't set filtered variations here, let the filter useEffect handle it
      } catch (error) {
        console.error('Error fetching variations:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchVariations();
  }, [refreshTrigger, supabase]);

  // Filter variations based on search query and selected filters
  useEffect(() => {
    // Start with all variations
    let filtered = [...variations];

    // Apply search query filter if present
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(variation =>
        variation.variation.toLowerCase().includes(query)
      );
    }

    // Apply generation filter if selected
    if (selectedGenerationId) {
      filtered = filtered.filter(variation =>
        variation.generation_id === selectedGenerationId
      );
    }
    // If generation is not selected but model is selected, filter by model
    else if (selectedModelId) {
      filtered = filtered.filter(variation => {
        const generation = generations.find(g => g.id === variation.generation_id);
        return generation && generation.model_id === selectedModelId;
      });
    }
    // If model is not selected but brand is selected, filter by brand
    else if (selectedBrandId) {
      filtered = filtered.filter(variation => {
        const generation = generations.find(g => g.id === variation.generation_id);
        if (!generation) return false;

        const model = models.find(m => m.id === generation.model_id);
        return model && model.brand_id === selectedBrandId;
      });
    }

    setFilteredVariations(filtered);

    // Log for debugging
    console.log('Filtering variations:', {
      total: variations.length,
      filtered: filtered.length,
      searchQuery,
      selectedBrandId,
      selectedModelId,
      selectedGenerationId
    });

  }, [searchQuery, variations, selectedBrandId, selectedModelId, selectedGenerationId, generations, models]);

  // Refresh variations
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Get generation name by ID
  const getGenerationName = (generationId: number) => {
    const generation = generations.find(g => g.id === generationId);
    return generation ? generation.name : 'Unknown Generation';
  };

  // Get model name by generation ID
  const getModelNameByGenerationId = (generationId: number) => {
    const generation = generations.find(g => g.id === generationId);
    if (!generation) return 'Unknown Model';

    const model = models.find(m => m.id === generation.model_id);
    return model ? model.model_name : 'Unknown Model';
  };

  // Get brand name by generation ID
  const getBrandNameByGenerationId = (generationId: number) => {
    const generation = generations.find(g => g.id === generationId);
    if (!generation) return 'Unknown Brand';

    const model = models.find(m => m.id === generation.model_id);
    if (!model) return 'Unknown Brand';

    const brand = brands.find(b => b.brand_id === model.brand_id);
    return brand ? brand.brand_name : 'Unknown Brand';
  };

  // Open edit modal
  const handleEdit = (variation: Variation) => {
    setSelectedVariation(variation);
    setIsEditModalOpen(true);
  };

  // Open delete modal
  const handleDelete = (variation: Variation) => {
    setSelectedVariation(variation);
    setIsDeleteModalOpen(true);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    },
    hover: {
      y: -5,
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      transition: {
        duration: 0.2
      }
    }
  };

  return (
    <div>
      {/* Search and Actions */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
        <div className="flex flex-col md:flex-row gap-4 w-full md:w-auto">
          <div className="relative w-full md:w-64">
            <input
              type="text"
              placeholder="Search variations..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
          </div>

          <div className="w-full md:w-48">
            <select
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
              value={selectedBrandId}
              onChange={(e) => {
                setSelectedBrandId(e.target.value ? Number(e.target.value) : '');
                setSelectedModelId('');
                setSelectedGenerationId('');
              }}
            >
              <option value="">All Brands</option>
              {brands.map((brand) => (
                <option key={brand.brand_id} value={brand.brand_id}>
                  {brand.brand_name}
                </option>
              ))}
            </select>
          </div>

          <div className="w-full md:w-48">
            <select
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
              value={selectedModelId}
              onChange={(e) => {
                setSelectedModelId(e.target.value ? Number(e.target.value) : '');
                setSelectedGenerationId('');
              }}
              disabled={!selectedBrandId}
            >
              <option value="">All Models</option>
              {models.map((model) => (
                <option key={model.id} value={model.id}>
                  {model.model_name}
                </option>
              ))}
            </select>
          </div>

          <div className="w-full md:w-48">
            <select
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
              value={selectedGenerationId}
              onChange={(e) => setSelectedGenerationId(e.target.value ? Number(e.target.value) : '')}
              disabled={!selectedModelId}
            >
              <option value="">All Generations</option>
              {generations.map((generation) => (
                <option key={generation.id} value={generation.id}>
                  {generation.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="flex gap-2 w-full md:w-auto">
          <button
            onClick={handleRefresh}
            className="flex items-center gap-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
          >
            <RefreshCw size={18} />
            <span>Refresh</span>
          </button>

          <button
            onClick={() => setIsAddModalOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <Plus size={18} />
            <span>Add Variation</span>
          </button>
        </div>
      </div>

      {/* Variations Grid */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size={40} />
        </div>
      ) : filteredVariations.length > 0 ? (
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {filteredVariations.map((variation) => (
            <motion.div
              key={variation.id}
              className="bg-white rounded-lg shadow-md p-6"
              variants={itemVariants}
              whileHover="hover"
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-xl font-semibold text-gray-800">{variation.variation}</h3>
                  <div className="flex items-center mt-1">
                    <Tag size={16} className="text-teal-600 mr-2" />
                    <span className="text-sm text-gray-600">
                      {getBrandNameByGenerationId(variation.generation_id)} {getModelNameByGenerationId(variation.generation_id)}
                    </span>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleEdit(variation)}
                    className="p-2 rounded-md hover:bg-gray-100 text-gray-500 hover:text-teal-600 transition-colors"
                    aria-label="Edit variation"
                  >
                    <Edit size={18} />
                  </button>
                  <button
                    onClick={() => handleDelete(variation)}
                    className="p-2 rounded-md hover:bg-gray-100 text-gray-500 hover:text-red-600 transition-colors"
                    aria-label="Delete variation"
                  >
                    <Trash2 size={18} />
                  </button>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg mb-4">
                <div className="flex items-center">
                  <Layers size={18} className="text-orange-500 mr-2" />
                  <span className="text-gray-700">
                    Generation: {getGenerationName(variation.generation_id)}
                  </span>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-100">
                <p className="text-sm text-gray-500">Variation ID: {variation.id}</p>
              </div>
            </motion.div>
          ))}
        </motion.div>
      ) : (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <h3 className="text-xl font-semibold text-gray-700 mb-2">No variations found</h3>
          <p className="text-gray-500 mb-4">
            {searchQuery || selectedGenerationId ? 'No variations match your search criteria.' : 'Start by adding a new variation.'}
          </p>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <Plus size={18} />
            <span>Add Variation</span>
          </button>
        </div>
      )}

      {/* Add Variation Modal */}
      <AddVariationModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        generations={generations}
        models={models}
        brands={brands}
        onSuccess={() => {
          handleRefresh();
          onVariationUpdated();
        }}
      />

      {/* Edit Variation Modal */}
      {selectedVariation && (
        <EditVariationModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          variation={selectedVariation}
          generations={generations}
          models={models}
          brands={brands}
          onSuccess={() => {
            handleRefresh();
            onVariationUpdated();
          }}
        />
      )}

      {/* Delete Confirmation Modal */}
      {selectedVariation && (
        <DeleteConfirmModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          itemId={selectedVariation.id}
          itemName={selectedVariation.variation}
          itemType="variation"
          tableName="car_variation"
          idField="id"
          onSuccess={() => {
            handleRefresh();
            onVariationUpdated();
          }}
        />
      )}
    </div>
  );
};

export default VariationsTab;
