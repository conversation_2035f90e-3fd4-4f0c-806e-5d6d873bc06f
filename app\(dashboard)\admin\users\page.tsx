export const dynamic = 'force-dynamic';

import { Suspense } from 'react';
import Spinner from '@/app/components/ui/Spinner';
import UsersContent from './components/UsersContent';

export default function UsersPage() {
    return (
        <Suspense fallback={<div className="flex min-h-[300px] items-center justify-center">
            <Spinner size="lg" />
        </div>}>
            <UsersContent />
        </Suspense>
    );
}
