import { NextRequest, NextResponse } from 'next/server';
import { googleMerchantService, ProductData } from '@/app/services/googleMerchant';
import { createClient } from '@/app/libs/supabase/client';

/**
 * GET: Check authentication status and get sync status
 */
export async function GET(request: NextRequest) {
  try {
    // Check if authenticated
    const isAuthenticated = await googleMerchantService.isAuthenticated();

    if (!isAuthenticated) {
      // Check if we have an access token even if not fully authenticated
      const hasAccessToken = !!googleMerchantService.getAuth().credentials.access_token;

      return NextResponse.json({
        authenticated: false,
        hasAccessToken,
        message: 'Not authenticated with Google Merchant API'
      });
    }

    // Get merchant ID
    const merchantId = googleMerchantService.getMerchantId();

    // Get latest sync status
    const syncStatus = await googleMerchantService.getLatestSyncStatus();

    // Check if we have an access token
    const hasAccessToken = !!googleMerchantService.getAuth().credentials.access_token;

    return NextResponse.json({
      authenticated: true,
      merchantId,
      syncStatus,
      hasAccessToken
    });
  } catch (error) {
    console.error('Error checking Google Merchant status:', error);
    // Try to get access token status even in error case
    let hasAccessToken = false;
    try {
      hasAccessToken = !!googleMerchantService.getAuth().credentials.access_token;
    } catch (tokenError) {
      console.error('Error checking access token:', tokenError);
    }

    return NextResponse.json(
      {
        error: 'Failed to check Google Merchant status',
        details: error.message,
        authenticated: false,
        hasAccessToken
      },
      { status: 500 }
    );
  }
}

/**
 * POST: Sync products with Google Merchant
 */
export async function POST(request: NextRequest) {
  try {
    // Check if authenticated
    const isAuthenticated = await googleMerchantService.isAuthenticated();

    if (!isAuthenticated) {
      return NextResponse.json(
        { error: 'Not authenticated with Google Merchant API' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { action, partId, forceFullSync } = body;

    // Handle different actions
    switch (action) {
      case 'sync_all':
        // Sync all products (with optional force full sync)
        const syncStatus = await googleMerchantService.syncAllProducts(forceFullSync);
        return NextResponse.json({
          success: true,
          message: forceFullSync ? 'Full product sync initiated' : 'Incremental product sync initiated',
          syncStatus
        });

      case 'sync_single':
        // Validate part ID
        if (!partId) {
          return NextResponse.json(
            { error: 'Part ID is required for syncing a single product' },
            { status: 400 }
          );
        }

        // Get part data
        const supabase = createClient();
        console.log('Fetching part with ID:', partId);

        // Use a simple query to get all columns
        const { data: part, error } = await supabase
          .from('parts')
          .select('*')
          .eq('id', partId)
          .single();

        if (part) {
          console.log('Part data:', part);
        }

        // If part exists, get its images
        if (part) {
          // Get images for this part
          const { data: images } = await supabase
            .from('part_images')
            .select('image_url, is_main_image')
            .eq('part_id', part.id);

          // Add images to the part object
          part.images = images || [];
        }

        if (error || !part) {
          return NextResponse.json(
            { error: 'Part not found', details: error?.message },
            { status: 404 }
          );
        }

        // Convert part to Google Merchant format
        const productData = googleMerchantService.convertToMerchantFormat(part);

        // Create or update product
        const productId = `online:en:KE:${part.id}`;
        let result;

        try {
          // Check if product exists
          await googleMerchantService.shoppingContent.products.get({
            merchantId: googleMerchantService.getMerchantId(),
            productId
          });

          // Update existing product
          result = await googleMerchantService.updateProduct(productId, productData);
        } catch (error) {
          // Product doesn't exist, create it
          result = await googleMerchantService.createProduct(productData);
        }

        return NextResponse.json({
          success: true,
          message: 'Product synced successfully',
          productId,
          result
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: sync_all, sync_single' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error syncing products with Google Merchant:', error);
    return NextResponse.json(
      { error: 'Failed to sync products', details: error.message },
      { status: 500 }
    );
  }
}

/**
 * DELETE: Delete a product from Google Merchant
 */
export async function DELETE(request: NextRequest) {
  try {
    // Check if authenticated
    const isAuthenticated = await googleMerchantService.isAuthenticated();

    if (!isAuthenticated) {
      return NextResponse.json(
        { error: 'Not authenticated with Google Merchant API' },
        { status: 401 }
      );
    }

    // Get part ID from query params
    const url = new URL(request.url);
    const partId = url.searchParams.get('partId');

    if (!partId) {
      return NextResponse.json(
        { error: 'Part ID is required' },
        { status: 400 }
      );
    }

    // Delete product
    const productId = `online:en:KE:${partId}`;
    await googleMerchantService.deleteProduct(productId);

    return NextResponse.json({
      success: true,
      message: 'Product deleted successfully',
      productId
    });
  } catch (error) {
    console.error('Error deleting product from Google Merchant:', error);
    return NextResponse.json(
      { error: 'Failed to delete product', details: error.message },
      { status: 500 }
    );
  }
}
