// Types for the Categories Dashboard

export interface Category {
  id: number;
  label: string;
  href: string;
  icon?: string | null;
  library?: string | null;
  parent_category_id?: number | null;
  isActive?: boolean;
  requirePartNumber?: boolean;
  isEnginePart?: boolean;
  title_template?: string | null;
}

export interface CategoryWithChildren extends Category {
  children: CategoryWithChildren[];
}

export interface CategoryFormData {
  label: string;
  href: string;
  icon?: string;
  library?: string;
  parent_category_id?: number | null;
  isActive: boolean;
  requirePartNumber: boolean;
  isEnginePart: boolean;
  title_template?: string;
}

export interface CategoryStats {
  totalCategories: number;
  activeCategories: number;
  parentCategories: number;
  childCategories: number;
  maxDepth: number;
  engineParts: number;
}

// Attribute-related types
export interface CategoryAttribute {
  id?: number;
  category_id: number;
  attribute: string;
  input_type: 'text' | 'radio' | 'dropdown' | 'checkbox' | 'number' | 'date';
  depends_on_attribute_id?: number | null;
  depends_on_option_id?: number | null;
}

export interface AttributeOption {
  id?: number;
  attribute_id: number;
  option_value: string;
}

export interface AttributeWithOptions extends CategoryAttribute {
  options: AttributeOption[];
}
