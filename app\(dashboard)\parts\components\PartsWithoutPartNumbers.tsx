'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { motion, AnimatePresence } from 'framer-motion';
import { Hash, ArrowLeft, Save, X, Package, AlertTriangle } from 'lucide-react';
import Button from '@/app/components/ui/inputs/Button';
import Spinner from '@/app/components/ui/Spinner';
import { useForm, Controller } from 'react-hook-form';
import toast from 'react-hot-toast';
import { useUserCookie } from '@/app/hooks/useUserCookie';

interface Part {
  id: number;
  title: string;
  thumbnail_url?: string;
  partnumber_group: number | null;
}

interface PartNumberFormValues {
  partNumber: string;
}

const PartsWithoutPartNumbers = ({ onBack }: { onBack: () => void }) => {
  const [parts, setParts] = useState<Part[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPart, setSelectedPart] = useState<Part | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [showNotification, setShowNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState('');
  const supabase = createClient();
  const { isSuperAdmin, cookieLoading } = useUserCookie();
  
  const { control, handleSubmit, reset } = useForm<PartNumberFormValues>({
    defaultValues: {
      partNumber: ''
    }
  });

  // Check if user is super admin
  useEffect(() => {
    if (!cookieLoading && !isSuperAdmin) {
      toast.error('Only Super Admins can access this feature');
      onBack();
    }
  }, [cookieLoading, isSuperAdmin, onBack]);

  // Fetch parts without part numbers
  const fetchPartsWithoutPartNumbers = async () => {
    setIsLoading(true);
    try {
      console.log('Fetching parts without part numbers...');
      
      // Get parts without partnumber_group
      const { data: partsData, error: partsError } = await supabase
        .from('parts')
        .select(`
          id,
          title,
          partnumber_group
        `)
        .is('partnumber_group', null);

      if (partsError) {
        console.error('Error fetching parts:', JSON.stringify(partsError));
        throw partsError;
      }

      console.log(`Found ${partsData?.length || 0} parts without part numbers`);

      if (!partsData || partsData.length === 0) {
        setParts([]);
        return;
      }
      
      // Get part IDs
      const partIds = partsData.map(part => part.id);
      
      // Fetch part images
      const { data: partImages, error: imagesError } = await supabase
        .from('part_images')
        .select('part_id, image_url, is_main_image')
        .in('part_id', partIds);
        
      if (imagesError) {
        console.error('Error fetching part images:', JSON.stringify(imagesError));
        // Continue without images rather than failing
      }
      
      console.log(`Found ${partImages?.length || 0} part images`);
      
      // Create a map of part_id to main image
      const imageMap: Record<number, string> = {};
      if (partImages && partImages.length > 0) {
        // First try to find main images
        const mainImages = partImages.filter(img => img.is_main_image);
        mainImages.forEach(img => {
          imageMap[img.part_id] = img.image_url;
        });
        
        // For parts without main images, use the first available image
        partImages.forEach(img => {
          if (!imageMap[img.part_id]) {
            imageMap[img.part_id] = img.image_url;
          }
        });
      }
      
      // Transform the data
      const partsWithoutPartNumbers = partsData.map(part => ({
        id: part.id,
        title: part.title || 'Unnamed Part',
        partnumber_group: part.partnumber_group,
        thumbnail_url: imageMap[part.id] || ''
      }));
      
      console.log(`Final list: ${partsWithoutPartNumbers.length} parts without part numbers`);
      
      // Log a sample part for debugging
      if (partsWithoutPartNumbers.length > 0) {
        console.log('Sample part:', JSON.stringify(partsWithoutPartNumbers[0]));
      }
      
      setParts(partsWithoutPartNumbers);
    } catch (error) {
      console.error('Error fetching parts without part numbers:', JSON.stringify(error, null, 2));
      toast.error('Failed to load parts without part numbers');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!cookieLoading && isSuperAdmin) {
      fetchPartsWithoutPartNumbers();
    }
  }, [cookieLoading, isSuperAdmin]);

  const handleAddPartNumber = (part: Part) => {
    setSelectedPart(part);
    reset({ partNumber: '' });
  };

  const handleSavePartNumber = async (data: PartNumberFormValues) => {
    if (!selectedPart) return;
    
    setIsSaving(true);
    try {
      console.log(`Adding part number ${data.partNumber} for part ${selectedPart.id}`);
      
      // First, check if the part number already exists in part_compatibility_groups
      const { data: existingGroup, error: groupError } = await supabase
        .from('part_compatibility_groups')
        .select('id')
        .eq('part_number', data.partNumber)
        .maybeSingle();
        
      if (groupError) {
        console.error('Error checking existing part number:', JSON.stringify(groupError));
        throw groupError;
      }
      
      let groupId: number;
      
      if (existingGroup) {
        // Use existing group
        groupId = existingGroup.id;
        console.log(`Using existing part number group: ${groupId}`);
      } else {
        // Create new group with part number
        const { data: newGroup, error: createError } = await supabase
          .from('part_compatibility_groups')
          .insert({ part_number: data.partNumber })
          .select('id')
          .single();
          
        if (createError) {
          console.error('Error creating part number group:', JSON.stringify(createError));
          throw createError;
        }
        
        groupId = newGroup.id;
        console.log(`Created new part number group: ${groupId}`);
      }
      
      // Update the part with the group ID
      const { error: updateError } = await supabase
        .from('parts')
        .update({ partnumber_group: groupId })
        .eq('id', selectedPart.id);
        
      if (updateError) {
        console.error('Error updating part:', JSON.stringify(updateError));
        throw updateError;
      }
      
      console.log('Part number added successfully');
      toast.success(`Part number added for ${selectedPart.title}`);
      
      // Remove the part from the list
      setParts(prevParts => prevParts.filter(p => p.id !== selectedPart.id));
      
      // Close the form
      setSelectedPart(null);
    } catch (error) {
      console.error('Error saving part number:', JSON.stringify(error, null, 2));
      toast.error('Failed to save part number. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  if (cookieLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Spinner size="lg" />
      </div>
    );
  }

  if (!isSuperAdmin) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">Parts Without Part Numbers</h2>
          <p className="text-gray-600">Add part numbers to parts that don't have them yet</p>
        </div>
        <Button onClick={onBack} variant="outline" className="flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Dashboard
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Spinner size="lg" />
        </div>
      ) : parts.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <Hash className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-xl font-semibold text-gray-700 mb-2">All Parts Have Part Numbers</h3>
          <p className="text-gray-500">There are no parts without part numbers in your inventory.</p>
        </div>
      ) : (
        <div>
          <div className="mb-4">
            <p className="text-gray-600">Found {parts.length} parts without part numbers</p>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Part</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {parts.map((part) => (
                  <tr key={part.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div 
                        className="flex-shrink-0 h-12 w-12 rounded-md overflow-hidden bg-gray-100 border border-gray-200"
                      >
                        {part.thumbnail_url ? (
                          <img 
                            src={part.thumbnail_url} 
                            alt={part.title}
                            className="h-full w-full object-cover"
                            onError={(e) => {
                              // Replace broken image with a placeholder
                              (e.target as HTMLImageElement).src = '/images/placeholder.jpg';
                            }}
                          />
                        ) : (
                          <div className="h-full w-full flex items-center justify-center bg-gray-100 text-gray-400">
                            <Package className="h-6 w-6" />
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{part.title}</div>
                      <div className="text-xs text-gray-500">ID: {part.id}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <Button 
                        onClick={() => handleAddPartNumber(part)} 
                        variant="outline" 
                        size="sm"
                        className="text-blue-600 hover:text-blue-800"
                      >
                        Add Part Number
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Part Number Form Modal */}
      <AnimatePresence>
        {selectedPart && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md"
            >
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Add Part Number for {selectedPart.title}</h3>
                <button
                  onClick={() => setSelectedPart(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              
              <form onSubmit={handleSubmit(handleSavePartNumber)} className="space-y-4">
                <Controller
                  name="partNumber"
                  control={control}
                  rules={{ 
                    required: "Part number is required",
                    pattern: {
                      value: /^[A-Z0-9-]+$/,
                      message: "Part number should only contain uppercase letters, numbers, and hyphens"
                    }
                  }}
                  render={({ field, fieldState }) => (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Part Number
                      </label>
                      <input
                        type="text"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="e.g. ABC-123456"
                        {...field}
                      />
                      {fieldState.error && (
                        <p className="mt-1 text-sm text-red-600">{fieldState.error.message}</p>
                      )}
                    </div>
                  )}
                />
                
                <div className="flex justify-end gap-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setSelectedPart(null)}
                    disabled={isSaving}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSaving}
                    className="flex items-center gap-2"
                  >
                    {isSaving ? <Spinner size="sm" /> : <Save className="h-4 w-4" />}
                    Save Part Number
                  </Button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PartsWithoutPartNumbers;
