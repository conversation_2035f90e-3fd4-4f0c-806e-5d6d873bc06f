'use client';

import React, { useEffect, useState } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { useClientAuth } from '../components/ClientAuthContext';
import { Car, ShoppingCart, Clock, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

interface ClientCar {
  id: string;
  registration_number: string;
  brand_name?: string;
  model_name?: string;
  generation_name?: string;
  variation_name?: string;
  trim_name?: string;
}

interface ClientOrder {
  id: string;
  order_number: string;
  status: string;
  total_amount: number;
  created_at: string;
}

const ClientDashboard: React.FC = () => {
  const { user, clientData, isLoading: authLoading } = useClientAuth();
  const [cars, setCars] = useState<ClientCar[]>([]);
  const [orders, setOrders] = useState<ClientOrder[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchClientData = async () => {
      if (authLoading || !user || !clientData) return;

      setIsLoading(true);
      setError(null);

      try {
        const supabase = createClient();

        // Fetch client cars
        const { data: carsData, error: carsError } = await supabase
          .from('client_cars')
          .select(`
            *,
            variation_trim:variation_trim_id(
              trim,
              car_variation:variation_id(
                variation,
                car_generation:generation_id(
                  name,
                  car_models:model_id(
                    model_name,
                    car_brands:brand_id(
                      brand_name
                    )
                  )
                )
              )
            )
          `)
          .eq('client_id', clientData.id)
          .limit(3);

        if (carsError) throw new Error(carsError.message);

        // Process cars data
        const processedCars = carsData.map(car => ({
          id: car.id,
          registration_number: car.registration_number,
          brand_name: car.variation_trim?.car_variation?.car_generation?.car_models?.car_brands?.brand_name,
          model_name: car.variation_trim?.car_variation?.car_generation?.car_models?.model_name,
          generation_name: car.variation_trim?.car_variation?.car_generation?.name,
          variation_name: car.variation_trim?.car_variation?.variation,
          trim_name: car.variation_trim?.trim
        }));

        setCars(processedCars);

        // For now, use mock orders data
        // In a real implementation, you would fetch orders from the database
        setOrders([
          {
            id: '1',
            order_number: 'ORD-2023-001',
            status: 'completed',
            total_amount: 1250.00,
            created_at: '2023-05-15T10:30:00Z'
          },
          {
            id: '2',
            order_number: 'ORD-2023-002',
            status: 'processing',
            total_amount: 750.50,
            created_at: '2023-06-20T14:45:00Z'
          }
        ]);
      } catch (err) {
        console.error('Error fetching client data:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchClientData();
  }, [user, clientData, authLoading]);

  // Format car details
  const formatCarDetails = (car: ClientCar) => {
    const parts = [];

    if (car.brand_name) parts.push(car.brand_name);
    if (car.model_name) parts.push(car.model_name);
    if (car.generation_name) parts.push(car.generation_name);
    if (car.variation_name) parts.push(car.variation_name);
    if (car.trim_name) parts.push(car.trim_name);

    return parts.join(' ');
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (authLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size={24} />
      </div>
    );
  }

  if (!user || !clientData) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <p>You must be logged in as a client to view this page.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h1 className="text-2xl font-semibold text-gray-900 mb-2">
          Welcome, {clientData.name || user.user_metadata?.full_name || 'Client'}
        </h1>
        <p className="text-gray-600">
          Welcome to your Autoflow client portal. Here you can manage your cars, view orders, and more.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-teal-100 text-teal-600 mr-4">
              <Car className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Your Cars</p>
              <p className="text-2xl font-semibold text-gray-900">{isLoading ? '-' : cars.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
              <ShoppingCart className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Orders</p>
              <p className="text-2xl font-semibold text-gray-900">{isLoading ? '-' : orders.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-orange-100 text-orange-600 mr-4">
              <Clock className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Client Since</p>
              <p className="text-2xl font-semibold text-gray-900">
                {clientData.created_at ? formatDate(clientData.created_at) : '-'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Cars Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-gray-800">Your Cars</h2>
          <Link href="/client/cars" className="text-teal-600 hover:text-teal-800 text-sm font-medium">
            View All
          </Link>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <LoadingSpinner size={24} />
          </div>
        ) : error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <p>{error}</p>
          </div>
        ) : cars.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {cars.map((car) => (
              <div key={car.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center mb-2">
                  <Car className="w-5 h-5 text-gray-500 mr-2" />
                  <h3 className="font-medium text-gray-900">
                    {formatCarDetails(car)}
                  </h3>
                </div>

                {car.registration_number && (
                  <div className="text-sm text-gray-600 mb-1">
                    <span className="font-medium">Reg:</span> {car.registration_number}
                  </div>
                )}

                <Link
                  href={`/client/cars/${car.id}`}
                  className="mt-2 text-sm text-teal-600 hover:text-teal-800 font-medium"
                >
                  View Details
                </Link>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Car className="w-12 h-12 mx-auto mb-3 text-gray-400" />
            <p>No cars added yet</p>
            <Link
              href="/client/cars/add"
              className="mt-2 inline-block text-teal-600 hover:underline"
            >
              Add a car
            </Link>
          </div>
        )}
      </div>

      {/* Recent Orders */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-gray-800">Recent Orders</h2>
          <Link href="/client/orders" className="text-teal-600 hover:text-teal-800 text-sm font-medium">
            View All
          </Link>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <LoadingSpinner size={24} />
          </div>
        ) : orders.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Order #
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {orders.map((order) => (
                  <tr key={order.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-teal-600">
                      {order.order_number}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(order.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${order.total_amount.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs rounded-full border ${getStatusColor(order.status)}`}>
                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <Link
                        href={`/client/orders/${order.id}`}
                        className="text-teal-600 hover:text-teal-800"
                      >
                        View
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <ShoppingCart className="w-12 h-12 mx-auto mb-3 text-gray-400" />
            <p>No orders yet</p>
          </div>
        )}
      </div>

      {/* Help Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border-l-4 border-teal-500">
        <div className="flex items-start">
          <div className="p-2 bg-teal-100 rounded-full text-teal-600 mr-4">
            <AlertCircle className="w-6 h-6" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-1">Need Help?</h3>
            <p className="text-gray-600 mb-3">
              Our support team is here to assist you with any questions or issues you may have.
            </p>
            <Link
              href="/client/help"
              className="text-teal-600 hover:text-teal-800 font-medium"
            >
              Contact Support
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientDashboard;
