import jsPDF from 'jspdf';
import 'jspdf-autotable';

// Extend jsPDF type to include autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

interface SaleData {
  id: string;
  sale_timestamp: string;
  sale_type: 'cash' | 'credit';
  payment_method: string;
  total_amount: number;
  discount_total?: number;
  total_before_vat?: number;
  vat_amount?: number;
  vat_rate?: number;
  total_with_vat?: number;
  one_off_client_name?: string;
  one_off_client_phone?: string;
  clients?: {
    name?: string;
    phone_number?: string;
  };
  profiles?: {
    full_name?: string;
  };
  sale_items: Array<{
    id: string;
    quantity: number;
    price_at_sale: number;
    discount_amount: number;
    discount_reason?: string;
    parts?: {
      title?: string;
    };
    part_id: number;
  }>;
  mpesa_payments?: Array<{
    mpesa_confirmation_message?: string;
  }>;
}

export const generatePDFReceipt = async (saleData: SaleData) => {
  try {
    const doc = new jsPDF();

    // Modern color scheme
    const primaryColor = '#1E40AF';   // Professional blue
    const accentColor = '#3B82F6';    // Lighter blue
    const textColor = '#111827';      // Almost black
    const grayColor = '#6B7280';      // Medium gray
    const lightGray = '#F3F4F6';     // Very light gray
    const borderColor = '#D1D5DB';    // Light border

    // Page dimensions
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const margin = 20;

    // Clean white background
    doc.setFillColor('#FFFFFF');
    doc.rect(0, 0, pageWidth, pageHeight, 'F');

    // Gray header with height for receipt info underneath
    const headerHeight = 35; // Increased to accommodate receipt info below "RECEIPT"
    doc.setFillColor('#9CA3AF'); // Gray background
    doc.rect(0, 0, pageWidth, headerHeight, 'F');

    // Company name in header (left side)
    doc.setTextColor('#FFFFFF');
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(14);
    const companyName = 'AUTOFLOW LIMITED';
    doc.text(companyName, margin, 16);

    // Get the actual rendered width of the company name at its font size
    const companyNameWidth = doc.getTextWidth(companyName); // This gets width at current font size (14pt)

    // Byline centered under company name (reduced spacing)
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(8);
    const byline = 'VW • Audi • Porsche';
    const bylineWidth = doc.getTextWidth(byline); // This gets width at current font size (8pt)
    const bylineX = margin + (companyNameWidth - bylineWidth) / 2;
    doc.text(byline, bylineX, 19); // Reduced from 24 to 19 (8px gap reduced to ~3px)

    // "Receipt" text in top right of header
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(16);
    const receiptText = 'RECEIPT';
    const receiptWidth = doc.getTextWidth(receiptText);
    const receiptX = pageWidth - margin - receiptWidth;
    doc.text(receiptText, receiptX, 16);

    // Receipt number and date positioned underneath "RECEIPT"
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(9);
    doc.setTextColor('#FFFFFF');

    // Receipt number (underneath "RECEIPT", right-aligned to it)
    const receiptNumText = `Receipt #: ${saleData.id.substring(0, 8).toUpperCase()}`;
    const receiptNumWidth = doc.getTextWidth(receiptNumText);
    const receiptNumX = pageWidth - margin - receiptNumWidth;
    doc.text(receiptNumText, receiptNumX, 22);

    // Date (underneath receipt number, right-aligned to it)
    const dateText = `Date: ${new Date(saleData.sale_timestamp).toLocaleDateString()}`;
    const dateWidth = doc.getTextWidth(dateText);
    const dateX = pageWidth - margin - dateWidth;
    doc.text(dateText, dateX, 27);

    // Start content below header (reduced spacing)
    let yPos = headerHeight + 10; // Cut by half (was 20px)

    // Customer Information section (compact)
    doc.setTextColor(textColor);
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(10); // Reduced from 12
    doc.text('Customer Information', margin, yPos);

    // Customer details (no line, reduced spacing)
    yPos += 10; // Reduced from 20 (5 + 15)
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(9); // Reduced from 10
    const clientName = saleData.clients?.name || saleData.one_off_client_name || 'Walk-in Customer';
    doc.setTextColor(grayColor);
    doc.text('Name:', margin, yPos);
    doc.setTextColor(textColor);
    doc.text(clientName, margin + 25, yPos);

    const clientPhone = saleData.clients?.phone_number || saleData.one_off_client_phone || '';
    if (clientPhone) {
      yPos += 8; // Reduced from 10
      doc.setTextColor(grayColor);
      doc.text('Phone:', margin, yPos);
      doc.setTextColor(textColor);
      doc.text(clientPhone, margin + 25, yPos);
    }

    // Items Section (10px spacing from customer info)
    yPos += 10; // Exactly 10px spacing
    doc.setTextColor(textColor);
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(12);
    doc.text('Items Purchased', margin, yPos);

    // Add proper spacing between title and table
    yPos += 12; // Increased from 7 to 12 to prevent overlap
    const tableStartY = yPos;

    // Define column positions for better spacing
    const itemX = margin + 2;                    // Item name (left aligned)
    const qtyX = margin + 100;                   // Quantity (center aligned)
    const priceX = margin + 125;                 // Price (right aligned)
    const totalX = pageWidth - margin - 2;      // Total (right aligned)

    // Table header background
    doc.setFillColor(primaryColor);
    doc.rect(margin, yPos - 8, pageWidth - (2 * margin), 15, 'F');

    // Table header text with proper alignment
    doc.setTextColor('#FFFFFF');
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(9);

    // Headers
    doc.text('ITEM', itemX, yPos);

    // Center-align QTY header
    const qtyHeaderWidth = doc.getTextWidth('QTY');
    doc.text('QTY', qtyX - (qtyHeaderWidth / 2), yPos);

    // Right-align PRICE header
    const priceHeaderWidth = doc.getTextWidth('PRICE');
    doc.text('PRICE', priceX - priceHeaderWidth, yPos);

    // Right-align TOTAL header
    const totalHeaderWidth = doc.getTextWidth('TOTAL');
    doc.text('TOTAL', totalX - totalHeaderWidth, yPos);

    // Table content with proper alignment
    yPos += 15;
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(9);

    let subtotal = 0;
    let totalDiscount = 0;

    saleData.sale_items.forEach((item, index) => {
      const itemPrice = parseFloat(item.price_at_sale.toString());
      const itemDiscount = parseFloat(item.discount_amount.toString());
      const itemTotal = (itemPrice * item.quantity) - itemDiscount;
      subtotal += itemPrice * item.quantity;
      totalDiscount += itemDiscount;

      // Alternate row background
      if (index % 2 === 1) {
        doc.setFillColor(lightGray);
        doc.rect(margin, yPos - 6, pageWidth - (2 * margin), 14, 'F');
      }

      doc.setTextColor(textColor);

      // Item name (left aligned, properly truncated)
      const itemName = item.parts?.title || `Part ID: ${item.part_id}`;
      const maxItemLength = 40; // Increased for better readability
      const truncatedName = itemName.length > maxItemLength ?
        itemName.substring(0, maxItemLength - 3) + '...' : itemName;
      doc.text(truncatedName, itemX, yPos);

      // Quantity (center aligned)
      const qtyText = item.quantity.toString();
      const qtyWidth = doc.getTextWidth(qtyText);
      doc.text(qtyText, qtyX - (qtyWidth / 2), yPos);

      // Price (right aligned with Kshs prefix)
      const priceText = `Kshs ${itemPrice.toLocaleString()}`;
      const priceWidth = doc.getTextWidth(priceText);
      doc.text(priceText, priceX - priceWidth, yPos);

      // Total (right aligned with Kshs prefix)
      const totalText = `Kshs ${itemTotal.toLocaleString()}`;
      const itemTotalWidth = doc.getTextWidth(totalText);
      doc.text(totalText, totalX - itemTotalWidth, yPos);

      yPos += 14;
    });

    // Table bottom border
    doc.setDrawColor(borderColor);
    doc.setLineWidth(0.5);
    doc.line(margin, yPos, pageWidth - margin, yPos);

    // Payment Method and Subtotal on same row
    yPos += 15;
    doc.setTextColor(textColor);
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(9);

    // Payment Method in Item column
    const paymentMethod = saleData.sale_type === 'cash' ?
      (saleData.cash_payment_method === 'mpesa' ? 'M-PESA' : 'Cash') : 'Credit';
    doc.text(`Payment Method: ${paymentMethod}`, itemX, yPos);

    // Subtotal in Price and Total columns (same row)
    doc.text('Subtotal:', priceX - doc.getTextWidth('Subtotal:'), yPos);
    const subtotalText = `Kshs ${subtotal.toLocaleString()}`;
    const subtotalWidth = doc.getTextWidth(subtotalText);
    doc.text(subtotalText, totalX - subtotalWidth, yPos);

    // VAT on next row
    const vatAmount = saleData.vat_amount || 0;
    const vatRate = saleData.vat_rate || 0;
    if (vatAmount > 0) {
      yPos += 12;
      const vatLabel = `VAT (${vatRate}%):`;
      doc.text(vatLabel, priceX - doc.getTextWidth(vatLabel), yPos);

      const vatText = `Kshs ${vatAmount.toLocaleString()}`;
      const vatWidth = doc.getTextWidth(vatText);
      doc.text(vatText, totalX - vatWidth, yPos);
    }

    // Line above total (full table width)
    yPos += 15;
    doc.setDrawColor(borderColor);
    doc.setLineWidth(0.5);
    doc.line(margin, yPos - 5, pageWidth - margin, yPos - 5);

    // Final Total
    doc.setTextColor(primaryColor);
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(10);
    doc.text('TOTAL:', priceX - doc.getTextWidth('TOTAL:'), yPos);

    const finalTotal = saleData.total_amount || (subtotal - totalDiscount + vatAmount);
    const totalText = `Kshs ${finalTotal.toLocaleString()}`;
    const finalTotalWidth = doc.getTextWidth(totalText);
    doc.setTextColor(primaryColor);
    doc.text(totalText, totalX - finalTotalWidth, yPos);

    // Line below total (full table width)
    yPos += 8;
    doc.setDrawColor(borderColor);
    doc.setLineWidth(0.5);
    doc.line(margin, yPos, pageWidth - margin, yPos);

    // Minimal footer
    const footerY = pageHeight - 25;

    // Thank you message
    yPos = footerY;
    doc.setTextColor(accentColor);
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(11);
    doc.text('Thank you for your business!', margin, yPos);

    // Compact contact info on one line
    yPos += 12;
    doc.setTextColor(grayColor);
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(8);
    doc.text('Autoflow Limited  |  Nairobi, Kenya  |  +254 700 000 000  |  <EMAIL>', margin, yPos);

    // Staff information (if available) - same line, right aligned
    if (saleData.profiles?.full_name) {
      const staffText = `Served by: ${saleData.profiles.full_name}`;
      const staffWidth = doc.getTextWidth(staffText);
      doc.text(staffText, pageWidth - margin - staffWidth, yPos);
    }

    // Save the PDF with descriptive filename
    const fileName = `autoflow-receipt-${saleData.id.substring(0, 8)}-${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(fileName);
  } catch (error) {
    console.error('Error generating PDF receipt:', error);
    throw new Error('Failed to generate PDF receipt. Please try again.');
  }
};
