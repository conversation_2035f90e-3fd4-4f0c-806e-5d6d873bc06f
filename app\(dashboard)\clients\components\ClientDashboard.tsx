import React from 'react';
import { Users, UserPlus, TrendingUp, Search, Filter, ChevronDown, ArrowUpRight, ArrowDownRight, Briefcase, Building, ShoppingCart } from 'lucide-react';

// --- Mock Data ---
// Replace with your actual data fetching and types
interface Client {
  id: string;
  name: string;
  avatar: string; // URL or placeholder generator
  status: 'Active' | 'Pending' | 'Inactive';
  joined: string; // Date string
  revenue: number; // Example metric
  growth: number; // Example metric for fast rising
  industry: string;
}

const mockClients: Client[] = [
  { id: '1', name: 'Tech Solutions Inc.', avatar: 'https://placehold.co/40x40/E2E8F0/4A5568?text=TS', status: 'Active', joined: '2023-01-15', revenue: 15000, growth: 15, industry: 'Technology' },
  { id: '2', name: 'Green Energy Co.', avatar: 'https://placehold.co/40x40/A7F3D0/15803D?text=GE', status: 'Active', joined: '2022-11-30', revenue: 25000, growth: 5, industry: 'Energy' },
  { id: '3', name: 'Marketing Masters', avatar: 'https://placehold.co/40x40/FDBA74/C2410C?text=MM', status: 'Active', joined: '2023-03-01', revenue: 12000, growth: 25, industry: 'Marketing' },
  { id: '4', name: 'Innovate Labs', avatar: 'https://placehold.co/40x40/A5B4FC/4338CA?text=IL', status: 'Pending', joined: '2023-04-10', revenue: 0, growth: 0, industry: 'Research' },
  { id: '5', name: 'Retail Hub', avatar: 'https://placehold.co/40x40/F9A8D4/9D174D?text=RH', status: 'Active', joined: '2022-09-05', revenue: 18000, growth: 8, industry: 'Retail' },
  { id: '6', name: 'Finance Forward', avatar: 'https://placehold.co/40x40/FDE68A/B45309?text=FF', status: 'Inactive', joined: '2022-05-20', revenue: 5000, growth: -5, industry: 'Finance' },
  { id: '7', name: 'BuildRight Const.', avatar: 'https://placehold.co/40x40/99F6E4/0D9488?text=BR', status: 'Active', joined: '2023-02-28', revenue: 22000, growth: 30, industry: 'Construction' },
  { id: '8', name: 'HealthFirst Group', avatar: 'https://placehold.co/40x40/FCA5A5/991B1B?text=HF', status: 'Active', joined: '2023-04-01', revenue: 9000, growth: 18, industry: 'Healthcare' },
];

// Helper to get industry icon
const IndustryIcon = ({ industry }: { industry: string }) => {
  switch (industry.toLowerCase()) {
    case 'technology': return <Briefcase className="w-4 h-4 text-blue-500" />;
    case 'energy': return <TrendingUp className="w-4 h-4 text-green-500" />;
    case 'marketing': return <ShoppingCart className="w-4 h-4 text-orange-500" />;
    case 'retail': return <ShoppingCart className="w-4 h-4 text-pink-500" />;
    case 'finance': return <Building className="w-4 h-4 text-yellow-500" />;
    case 'construction': return <Building className="w-4 h-4 text-teal-500" />;
    case 'healthcare': return <Briefcase className="w-4 h-4 text-red-500" />;
    default: return <Briefcase className="w-4 h-4 text-gray-500" />;
  }
};

// --- Components ---

// Reusable Stat Card
interface StatsCardProps {
  title: string;
  value: string | number;
  change?: number; // Percentage change (optional)
  icon: React.ReactNode;
  color: 'teal' | 'orange' | 'gray';
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, change, icon, color }) => {
  const colorClasses = {
    teal: 'bg-teal-50 border-teal-200',
    orange: 'bg-orange-50 border-orange-200',
    gray: 'bg-gray-50 border-gray-200',
  };
  const iconColorClasses = {
    teal: 'text-teal-600',
    orange: 'text-orange-600',
    gray: 'text-gray-600',
  };
  const changeColor = change && change > 0 ? 'text-green-600' : 'text-red-600';
  const ChangeIcon = change && change > 0 ? ArrowUpRight : ArrowDownRight;

  return (
    <div className={`p-4 md:p-6 rounded-lg shadow-sm border ${colorClasses[color]} flex flex-col justify-between`}>
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-medium text-gray-500">{title}</h3>
        <div className={`p-2 rounded-full bg-white ${iconColorClasses[color]}`}>
          {icon}
        </div>
      </div>
      <p className="text-2xl md:text-3xl font-semibold text-gray-800 mb-1">{value}</p>
      {change !== undefined && (
        <div className={`flex items-center text-xs ${changeColor}`}>
          <ChangeIcon className="w-3 h-3 mr-1" />
          <span>{Math.abs(change)}% vs last period</span>
        </div>
      )}
    </div>
  );
};

// Reusable Client List Card
interface ClientListCardProps {
  title: string;
  clients: Client[];
  sortBy?: keyof Client; // Optional sort key for display logic
}

const ClientListCard: React.FC<ClientListCardProps> = ({ title, clients, sortBy }) => {
  // Sort clients if needed (example: by revenue for top clients)
  const sortedClients = [...clients].sort((a, b) => {
    if (sortBy === 'revenue') return b.revenue - a.revenue;
    if (sortBy === 'joined') return new Date(b.joined).getTime() - new Date(a.joined).getTime();
    if (sortBy === 'growth') return b.growth - a.growth;
    return 0;
  }).slice(0, 5); // Limit to top 5 for display

  return (
    <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm border border-gray-200 h-full flex flex-col">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">{title}</h3>
      <div className="space-y-3 overflow-y-auto flex-grow">
        {sortedClients.map((client) => (
          <div
            key={client.id}
            className="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors duration-150"
          >
            <div className="flex items-center space-x-3">
              <img src={client.avatar} alt={client.name} className="w-8 h-8 rounded-full object-cover" />
              <div>
                <p className="text-sm font-medium text-gray-700">{client.name}</p>
                <p className="text-xs text-gray-500 flex items-center">
                  <IndustryIcon industry={client.industry} />
                  <span className="ml-1">{client.industry}</span>
                </p>
              </div>
            </div>
            {/* Display relevant metric based on list type */}
            {sortBy === 'revenue' && <span className="text-sm font-semibold text-teal-600">${client.revenue.toLocaleString()}</span>}
            {sortBy === 'joined' && <span className="text-xs text-gray-500">{client.joined}</span>}
            {sortBy === 'growth' && (
              <span className={`text-sm font-semibold ${client.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {client.growth > 0 ? '+' : ''}{client.growth}%
              </span>
            )}
          </div>
        ))}
      </div>
      <button className="mt-4 text-sm text-teal-600 hover:text-teal-800 font-medium self-start">
        View All
      </button>
    </div>
  );
};


// --- Main Dashboard Component ---
const ClientDashboard: React.FC = () => {
  // In a real app, fetch data here using useEffect
  const totalClients = mockClients.filter(c => c.status === 'Active').length;
  const newClientsThisMonth = mockClients.filter(c => new Date(c.joined) > new Date(new Date().setDate(1))).length; // Example logic
  const potentialValue = mockClients.reduce((sum, c) => sum + c.revenue, 0); // Example logic

  return (
    <div className="p-4 md:p-6 lg:p-8 bg-gray-100">
      {/* Header */}
      <header className="flex flex-col md:flex-row items-center justify-between mb-6 md:mb-8">
        <h1 className="text-2xl md:text-3xl font-semibold text-gray-800 mb-4 md:mb-0">Client Dashboard</h1>
        <div className="flex items-center space-x-2 md:space-x-4 w-full md:w-auto">
          <div className="relative flex-grow md:flex-grow-0">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search clients..."
              className="pl-10 pr-4 py-2 w-full md:w-64 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent text-sm"
            />
          </div>
          <button className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
            <Filter className="w-4 h-4 mr-2 text-gray-500" /> Filter <ChevronDown className="w-4 h-4 ml-1 text-gray-500" />
          </button>
          {/* Add other header buttons if needed */}
        </div>
      </header>

      {/* Content Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
        {/* Stat Cards */}
        <StatsCard
          title="Total Active Clients"
          value={totalClients}
          change={5} // Example change
          icon={<Users className="w-5 h-5" />}
          color="teal"
        />
        <StatsCard
          title="New Clients (This Month)"
          value={newClientsThisMonth}
          change={12} // Example change
          icon={<UserPlus className="w-5 h-5" />}
          color="orange"
        />
         <StatsCard
          title="Total Client Value"
          value={`$${potentialValue.toLocaleString()}`}
          change={-2} // Example change
          icon={<TrendingUp className="w-5 h-5" />}
          color="gray"
        />

        {/* Client Lists - Span across columns on larger screens */}
        <div className="lg:col-span-1">
           <ClientListCard title="Top Clients (by Revenue)" clients={mockClients} sortBy="revenue" />
        </div>
         <div className="lg:col-span-1">
           <ClientListCard title="Latest Clients" clients={mockClients} sortBy="joined" />
        </div>
         <div className="lg:col-span-1">
          <ClientListCard title="Fast Rising Clients" clients={mockClients} sortBy="growth" />
        </div>
      </div>
    </div>
  );
};

export default ClientDashboard;
