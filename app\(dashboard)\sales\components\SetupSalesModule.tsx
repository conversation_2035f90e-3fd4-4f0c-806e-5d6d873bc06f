import React, { useState } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { AlertTriangle, CheckCircle, Database, RefreshCw } from 'lucide-react';
import toast from 'react-hot-toast';

interface SetupSalesModuleProps {
  onSetupComplete: () => void;
}

const SetupSalesModule: React.FC<SetupSalesModuleProps> = ({ onSetupComplete }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [setupStatus, setSetupStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const setupSalesModule = async () => {
    setIsLoading(true);
    setSetupStatus('loading');
    setErrorMessage(null);

    try {
      const supabase = createClient();

      // SQL script to set up the sales module
      const setupSql = `
        -- Create sales table if it doesn't exist
        CREATE TABLE IF NOT EXISTS public.sales (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            sale_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            sale_type TEXT NOT NULL CHECK (sale_type IN ('cash', 'credit')),
            payment_method TEXT NOT NULL CHECK (payment_method IN ('cash', 'mpesa', 'bank_transfer', 'credit')),
            total_amount NUMERIC(10, 2) NOT NULL,
            discount_total NUMERIC(10, 2) DEFAULT 0,
            client_id UUID REFERENCES public.clients(id),
            one_off_client_name TEXT,
            one_off_client_phone TEXT,
            notes TEXT,
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        );

        -- Create sale_items table if it doesn't exist
        CREATE TABLE IF NOT EXISTS public.sale_items (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            sale_id UUID NOT NULL REFERENCES public.sales(id) ON DELETE CASCADE,
            part_id INTEGER NOT NULL REFERENCES public.parts(id) ON DELETE RESTRICT,
            quantity INTEGER NOT NULL CHECK (quantity > 0),
            price_at_sale NUMERIC(10, 2) NOT NULL,
            discount_amount NUMERIC(10, 2) DEFAULT 0,
            discount_reason TEXT,
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        );

        -- Create mpesa_payments table if it doesn't exist
        CREATE TABLE IF NOT EXISTS public.mpesa_payments (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            sale_id UUID NOT NULL REFERENCES public.sales(id) ON DELETE CASCADE,
            transaction_id TEXT NOT NULL,
            amount NUMERIC(10, 2) NOT NULL,
            phone_number TEXT NOT NULL,
            payment_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            status TEXT NOT NULL DEFAULT 'completed',
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        );

        -- Create indexes for better performance
        CREATE INDEX IF NOT EXISTS idx_sales_client_id ON public.sales(client_id);
        CREATE INDEX IF NOT EXISTS idx_sales_sale_timestamp ON public.sales(sale_timestamp);
        CREATE INDEX IF NOT EXISTS idx_sale_items_sale_id ON public.sale_items(sale_id);
        CREATE INDEX IF NOT EXISTS idx_sale_items_part_id ON public.sale_items(part_id);
        CREATE INDEX IF NOT EXISTS idx_mpesa_payments_sale_id ON public.mpesa_payments(sale_id);
      `;

      // Execute the SQL script to set up the sales module
      const { error } = await supabase.rpc('exec_sql', { sql: setupSql });

      if (error) {
        console.error('Error setting up sales module:', JSON.stringify(error, null, 2));
        setErrorMessage(`Failed to set up sales module: ${error.message}`);
        setSetupStatus('error');
        toast.error('Failed to set up sales module');
        return;
      }

      // Verify that the tables were created
      const { data: salesTable, error: salesError } = await supabase
        .from('sales')
        .select('id', { count: 'exact', head: true });

      if (salesError) {
        console.error('Error verifying sales table:', JSON.stringify(salesError, null, 2));
        setErrorMessage(`Failed to verify sales table: ${salesError.message}`);
        setSetupStatus('error');
        toast.error('Failed to verify sales module setup');
        return;
      }

      setSetupStatus('success');
      toast.success('Sales module set up successfully');

      // Notify parent component that setup is complete
      onSetupComplete();
    } catch (error) {
      console.error('Error in setup process:', error);

      if (error instanceof Error) {
        setErrorMessage(`An unexpected error occurred: ${error.message}`);
      } else {
        setErrorMessage('An unexpected error occurred during setup');
      }

      setSetupStatus('error');
      toast.error('Failed to set up sales module');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 max-w-2xl mx-auto my-8">
      <div className="flex items-center mb-4">
        <Database className="w-8 h-8 text-teal-600 mr-3" />
        <h2 className="text-2xl font-semibold text-gray-800">Sales Module Setup</h2>
      </div>

      <div className="bg-gray-50 p-4 rounded-md mb-6">
        <div className="flex items-start">
          <AlertTriangle className="w-6 h-6 text-amber-500 mr-3 flex-shrink-0 mt-0.5" />
          <div>
            <h3 className="font-medium text-gray-900">Sales Module Not Configured</h3>
            <p className="text-gray-600 mt-1">
              The sales module requires database tables that haven't been set up yet.
              Click the button below to automatically create the necessary database structure.
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex justify-center">
          <button
            onClick={setupSalesModule}
            disabled={isLoading || setupStatus === 'success'}
            className={`px-4 py-2 rounded-md flex items-center ${
              setupStatus === 'success'
                ? 'bg-green-100 text-green-700 cursor-default'
                : isLoading
                ? 'bg-gray-300 text-gray-700 cursor-wait'
                : 'bg-teal-600 text-white hover:bg-teal-700'
            }`}
          >
            {isLoading ? (
              <>
                <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
                Setting Up...
              </>
            ) : setupStatus === 'success' ? (
              <>
                <CheckCircle className="w-5 h-5 mr-2" />
                Setup Complete
              </>
            ) : (
              <>
                <Database className="w-5 h-5 mr-2" />
                Set Up Sales Module
              </>
            )}
          </button>
        </div>

        {errorMessage && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <p className="font-medium">Setup failed</p>
            <p className="text-sm">{errorMessage}</p>
          </div>
        )}

        {setupStatus === 'success' && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md">
            <p className="font-medium">Setup completed successfully</p>
            <p className="text-sm">The sales module is now ready to use.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SetupSalesModule;
