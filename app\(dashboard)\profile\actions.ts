// app/(dashboard)/profile/actions.ts
'use server';

import { createClient } from '@/app/libs/supabase/client';
import { UserProfile } from '@/app/types/profile';
import { User } from '@/app/types/authTypes';

export async function fetchProfile(userId: User['id']): Promise<UserProfile | null> {
  const supabase = await createClient();

  const { data: profile, error } = await supabase
    .from('profiles')
    .select()
    .eq('id', userId)
    .single();

  if (error) {
    console.error('Error fetching profile:', error);
    return null;
  }

  return profile;
}