'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { usePermissions } from '@/app/hooks/usePermissions';
import { getPermissions, getPermissionCategories, createPermission, updatePermission, deletePermission } from '@/app/libs/rbac/api';
import Spinner from '@/app/components/ui/Spinner';
import { Input } from '@/app/components/ui/Input';
import { Button } from '@/app/components/ui/Button';

interface Permission {
  id: string;
  name: string;
  description: string | null;
  category: string;
  created_at: string;
}

const PermissionsContent = () => {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentPermission, setCurrentPermission] = useState<Permission | null>(null);
  const [formData, setFormData] = useState({ name: '', description: '', category: '' });
  const [newCategory, setNewCategory] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deleteConfirmPermission, setDeleteConfirmPermission] = useState<Permission | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('');

  const { hasPermission, isLoading: isPermissionLoading, error: permissionError } = usePermissions('admin:manage_permissions');
  const router = useRouter();

  useEffect(() => {
    if (!isPermissionLoading) {
      console.log(`PermissionsPage: Permission check complete. hasPermission('admin:manage_permissions'): ${hasPermission}`);
      if (!hasPermission) {
        console.log('PermissionsPage: Redirecting to /admin due to missing permission.');
        setError('You do not have permission to manage permissions.');
        setTimeout(() => router.push('/admin'), 50);
      }
    }
  }, [hasPermission, isPermissionLoading, router]);

  useEffect(() => {
    if (hasPermission && !isPermissionLoading) {
      console.log('PermissionsPage: Permission granted. Fetching permissions...');
      fetchPermissionsAndCategories();
    } else if (!isPermissionLoading && !hasPermission) {
      setIsLoading(false); 
    }
  }, [hasPermission, isPermissionLoading]);

  const fetchPermissionsAndCategories = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const [permissionsResult, categoriesResult] = await Promise.all([
        getPermissions(),
        getPermissionCategories(),
      ]);
      
      if (permissionsResult.error) throw permissionsResult.error;
      if (categoriesResult.error) throw categoriesResult.error;
      
      setPermissions(permissionsResult.data || []);
      setCategories(categoriesResult.data || []);
    } catch (err) {
      setError('Failed to fetch permissions data. Please try again.');
      console.error('Error fetching permissions data:', err);
      setPermissions([]);
      setCategories([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenModal = (permission: Permission | null = null) => {
    setCurrentPermission(permission);
    setFormData({
      name: permission?.name || '',
      description: permission?.description || '',
      category: permission?.category || (categories.length > 0 ? categories[0] : ''),
    });
    setNewCategory('');
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setCurrentPermission(null);
    setFormData({ name: '', description: '', category: '' });
    setNewCategory('');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    
    try {
      const category = newCategory || formData.category;
      
      if (currentPermission) {
        const { error } = await updatePermission(currentPermission.id, {
          name: formData.name,
          description: formData.description,
          category,
        });
        if (error) throw error;
      } else {
        const { error } = await createPermission(formData.name, category, formData.description);
        if (error) throw error;
      }
      
      await fetchPermissionsAndCategories();
      handleCloseModal();
    } catch (err: any) {
      setError(`Failed to ${currentPermission ? 'update' : 'create'} permission: ${err.message || 'Please try again.'}`);
      console.error(`Error ${currentPermission ? 'updating' : 'creating'} permission:`, err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteConfirm = (permission: Permission) => {
    setDeleteConfirmPermission(permission);
  };

  const handleDelete = async () => {
    if (!deleteConfirmPermission) return;
    
    setIsSubmitting(true);
    setError(null);
    try {
      const { error } = await deletePermission(deleteConfirmPermission.id);
      if (error) throw error;
      
      await fetchPermissionsAndCategories();
      setDeleteConfirmPermission(null);
    } catch (err: any) {
      setError(`Failed to delete permission: ${err.message || 'Please try again.'}`);
      console.error('Error deleting permission:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const filteredPermissions = permissions.filter(permission => {
    const matchesSearch = 
      permission.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (permission.description?.toLowerCase() || '').includes(searchQuery.toLowerCase());
    
    const matchesCategory = categoryFilter ? permission.category === categoryFilter : true;
    
    return matchesSearch && matchesCategory;
  });

  if (isPermissionLoading || isLoading) {
    return (
      <div className="flex min-h-[300px] items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  if (!isPermissionLoading && !hasPermission) { 
    return (
      <div className="rounded-md border border-red-200 bg-red-50 p-6 text-center shadow-sm">
        <h2 className="mb-2 text-xl font-semibold text-red-700">Access Denied</h2>
        <p className="mb-4 text-red-600">
          {error || 'You do not have the required permissions (admin:manage_permissions) to view or manage permissions.'}
        </p>
        {permissionError && (
          <p className="mb-4 text-sm text-red-500">Details: {permissionError.message}</p>
        )}
        <Button
          onClick={() => router.push('/admin')}
          variant="destructive"
          className="px-4 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        >
          Return to Admin Home
        </Button>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <h2 className="text-xl font-semibold">Permission Management</h2>
        <Button
          onClick={() => handleOpenModal()}
          variant="default"
          className="px-4 py-2"
        >
          Add New Permission
        </Button>
      </div>

      {error && (
        <div className="mb-4 rounded-md bg-red-50 p-4 text-red-800">
          <p>{error}</p>
          <button
            onClick={() => setError(null)}
            className="ml-2 text-sm font-medium text-red-600 hover:text-red-800"
          >
            Dismiss
          </button>
        </div>
      )}

      <div className="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
        <div>
          <label htmlFor="search" className="block text-sm font-medium text-gray-700">
            Search
          </label>
          <Input
            type="text"
            id="search"
            placeholder="Search permissions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="mt-1 block w-full"
          />
        </div>
        <div>
          <label htmlFor="category-filter" className="block text-sm font-medium text-gray-700">
            Filter by Category
          </label>
          <select
            id="category-filter"
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
          >
            <option value="">All Categories</option>
            {categories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Category
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Description
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 bg-white">
            {filteredPermissions.length === 0 ? (
              <tr>
                <td colSpan={4} className="px-6 py-4 text-center text-sm text-gray-500">
                  No permissions found. {searchQuery || categoryFilter ? 'Try adjusting your filters.' : 'Create your first permission to get started.'}
                </td>
              </tr>
            ) : (
              filteredPermissions.map((permission) => (
                <tr key={permission.id}>
                  <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                    {permission.name}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                    {permission.category}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {permission.description || '-'}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                    <button
                      onClick={() => handleOpenModal(permission)}
                      className="mr-2 text-blue-600 hover:text-blue-900"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDeleteConfirm(permission)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {isModalOpen && (
        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">
              &#8203;
            </span>

            <div className="inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle">
              <form onSubmit={handleSubmit}>
                <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                  <h3 className="mb-4 text-lg font-medium text-gray-900">
                    {currentPermission ? 'Edit Permission' : 'Create New Permission'}
                  </h3>
                  <div className="mb-4">
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                      Name
                    </label>
                    <Input
                      type="text"
                      name="name"
                      id="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="mt-1 block w-full"
                      placeholder="Permission name (e.g., admin:view_logs)"
                    />
                  </div>
                  <div className="mb-4">
                    <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                      Category
                    </label>
                    <div className="flex items-center space-x-2">
                      <select
                        name="category"
                        id="category"
                        value={formData.category}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                        disabled={!!newCategory}
                      >
                        <option value="" disabled>
                          Select a category
                        </option>
                        {categories.map((category) => (
                          <option key={category} value={category}>
                            {category}
                          </option>
                        ))}
                      </select>
                      <span className="text-sm text-gray-500">or</span>
                      <Input
                        type="text"
                        placeholder="New category"
                        value={newCategory}
                        onChange={(e) => setNewCategory(e.target.value)}
                        className="mt-1 block w-full"
                        disabled={!!formData.category}
                      />
                    </div>
                  </div>
                  <div className="mb-4">
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                      Description
                    </label>
                    <textarea
                      name="description"
                      id="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={3}
                      className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
                      placeholder="Permission description"
                    />
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    variant="default"
                    className="inline-flex w-full justify-center sm:ml-3 sm:w-auto"
                  >
                    {isSubmitting ? (
                      <>
                        <Spinner size="sm" /> <span className="ml-2">Saving...</span>
                      </>
                    ) : currentPermission ? (
                      'Update Permission'
                    ) : (
                      'Create Permission'
                    )}
                  </Button>
                  <Button
                    type="button"
                    onClick={handleCloseModal}
                    variant="outline"
                    className="mt-3 inline-flex w-full justify-center sm:ml-3 sm:mt-0 sm:w-auto"
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {deleteConfirmPermission && (
        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">
              &#8203;
            </span>

            <div className="inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle">
              <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                    <h3 className="text-lg font-medium leading-6 text-gray-900">Delete Permission</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete the permission "{deleteConfirmPermission.name}"? This action cannot be undone.
                      </p>
                      <p className="mt-2 text-sm text-red-500">
                        Note: This will also remove this permission from all roles that currently have it assigned.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                <Button
                  type="button"
                  onClick={handleDelete}
                  disabled={isSubmitting}
                  variant="destructive"
                  className="inline-flex w-full justify-center sm:ml-3 sm:w-auto"
                >
                  {isSubmitting ? (
                    <>
                      <Spinner size="sm" /> <span className="ml-2">Deleting...</span>
                    </>
                  ) : (
                    'Delete'
                  )}
                </Button>
                <Button
                  type="button"
                  onClick={() => setDeleteConfirmPermission(null)}
                  variant="outline"
                  className="mt-3 inline-flex w-full justify-center sm:ml-3 sm:mt-0 sm:w-auto"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PermissionsContent;
