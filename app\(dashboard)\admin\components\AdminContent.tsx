'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePermissions } from '@/app/hooks/usePermissions';
import { UserPlus } from 'lucide-react';
import InviteUserModal from './InviteUserModal';

const AdminContent = () => {
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [inviteStatus, setInviteStatus] = useState<{ success?: string; error?: string } | null>(null);

  const { hasPermission: canManageRoles } = usePermissions('admin:manage_roles');
  const { hasPermission: canManagePermissions } = usePermissions('admin:manage_permissions');
  const { hasPermission: canAssignRoles } = usePermissions('admin:assign_roles');
  const { hasPermission: canAssignPermissions } = usePermissions('admin:assign_permissions');
  const { hasPermission: canViewAuditLog } = usePermissions('admin:view_audit_log');

  const handleInviteUser = async (email: string, roleId: string) => {
    try {
      const response = await fetch('/api/auth/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, roleId }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send invitation');
      }

      setInviteStatus({ success: `Invitation sent to ${email}` });

      // Clear status after 3 seconds
      setTimeout(() => {
        setInviteStatus(null);
      }, 3000);

      return data;
    } catch (error: any) {
      setInviteStatus({ error: error.message });
      throw error;
    }
  };

  return (
    <>
      <div>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">RBAC Management</h2>
          <button
            onClick={() => setIsInviteModalOpen(true)}
            className="flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
          >
            <UserPlus size={16} className="mr-2" />
            Invite User
          </button>
        </div>

        {inviteStatus && (
          <div className={`mb-4 p-3 rounded-md ${inviteStatus.error ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'}`}>
            {inviteStatus.error || inviteStatus.success}
          </div>
        )}

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {canManageRoles && (
          <AdminCard
            title="Role Management"
            description="Create, edit, and delete roles in the system."
            href="/admin/roles"
            icon="👥"
          />
        )}

        {canManagePermissions && (
          <AdminCard
            title="Permission Management"
            description="Manage system permissions and categories."
            href="/admin/permissions"
            icon="🔑"
          />
        )}

        {canAssignRoles && (
          <AdminCard
            title="User Management"
            description="Assign roles to users and manage user accounts."
            href="/admin/users"
            icon="👤"
          />
        )}

        {(canAssignPermissions || canAssignRoles) && (
          <AdminCard
            title="Role & User Permissions"
            description="Assign permissions to roles and manage user-specific overrides."
            href="/admin/role-permissions"
            icon="🛡️"
          />
        )}

        {canViewAuditLog && (
          <AdminCard
            title="Audit Log"
            description="View system audit logs for security and compliance."
            href="/admin/audit-log"
            icon="📋"
          />
        )}
      </div>
    </div>

    {/* Invite User Modal */}
    <InviteUserModal
      isOpen={isInviteModalOpen}
      onClose={() => setIsInviteModalOpen(false)}
      onInvite={handleInviteUser}
    />
    </>
  );
};

interface AdminCardProps {
  title: string;
  description: string;
  href: string;
  icon: string;
}

const AdminCard: React.FC<AdminCardProps> = ({ title, description, href, icon }) => {
  return (
    <Link
      href={href}
      className="block rounded-lg border border-gray-200 bg-white p-6 shadow-sm transition-all hover:shadow-md"
    >
      <div className="mb-2 text-3xl">{icon}</div>
      <h3 className="mb-2 text-lg font-medium text-gray-900">{title}</h3>
      <p className="text-sm text-gray-600">{description}</p>
    </Link>
  );
};

export default AdminContent;
