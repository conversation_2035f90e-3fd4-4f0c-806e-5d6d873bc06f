// components/Compatibility/ExistingTrimsList.tsx
import React from 'react';

interface ExistingTrimsListProps {
  trims: Array<{ id: string; name: string }>;
}

export default function ExistingTrimsList({ trims }: ExistingTrimsListProps) {
  return (
    <>
      <p className="mt-2">This part number already exists with the following trims:</p>
      <ul className="list-disc pl-5 mt-2">
        {trims.map(trim => (
          <li key={trim.id} className="text-gray-700">{trim.name}</li>
        ))}
      </ul>
    </>
  );
}