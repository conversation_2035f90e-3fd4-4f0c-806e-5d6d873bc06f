-- Create mpesa_transactions table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.mpesa_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    merchant_request_id TEXT NOT NULL,
    checkout_request_id TEXT NOT NULL UNIQUE,
    phone_number TEXT NOT NULL,
    amount NUMERIC(10, 2) NOT NULL,
    reference TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed')),
    result_code TEXT,
    result_description TEXT,
    mpesa_receipt_number TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_mpesa_transactions_checkout_request_id ON public.mpesa_transactions(checkout_request_id);
CREATE INDEX IF NOT EXISTS idx_mpesa_transactions_merchant_request_id ON public.mpesa_transactions(merchant_request_id);
CREATE INDEX IF NOT EXISTS idx_mpesa_transactions_status ON public.mpesa_transactions(status);
CREATE INDEX IF NOT EXISTS idx_mpesa_transactions_reference ON public.mpesa_transactions(reference);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION public.set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS set_mpesa_transactions_updated_at ON public.mpesa_transactions;
CREATE TRIGGER set_mpesa_transactions_updated_at
BEFORE UPDATE ON public.mpesa_transactions
FOR EACH ROW
EXECUTE FUNCTION public.set_updated_at();

-- Add comments
COMMENT ON TABLE public.mpesa_transactions IS 'Stores M-PESA transaction details';
COMMENT ON COLUMN public.mpesa_transactions.merchant_request_id IS 'Merchant request ID from M-PESA';
COMMENT ON COLUMN public.mpesa_transactions.checkout_request_id IS 'Checkout request ID from M-PESA';
COMMENT ON COLUMN public.mpesa_transactions.phone_number IS 'Customer phone number';
COMMENT ON COLUMN public.mpesa_transactions.amount IS 'Transaction amount';
COMMENT ON COLUMN public.mpesa_transactions.reference IS 'Transaction reference (e.g., sale ID)';
COMMENT ON COLUMN public.mpesa_transactions.description IS 'Transaction description';
COMMENT ON COLUMN public.mpesa_transactions.status IS 'Transaction status (pending, completed, failed)';
COMMENT ON COLUMN public.mpesa_transactions.result_code IS 'Result code from M-PESA';
COMMENT ON COLUMN public.mpesa_transactions.result_description IS 'Result description from M-PESA';
COMMENT ON COLUMN public.mpesa_transactions.mpesa_receipt_number IS 'M-PESA receipt number for successful transactions';
