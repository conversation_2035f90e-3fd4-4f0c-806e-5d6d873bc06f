'use client'

// components/forms/ProfileForm.tsx
import { useState, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import Input from '@/app/components/ui/inputs/Input';
import Button from '@/app/components/ui/inputs/Button';
import toast, { Toaster } from 'react-hot-toast'
import { User } from "@/app/types/authTypes"
import { UserProfile } from '@/app/types/profile';

export default function ProfileForm({ user, initialProfile, onProfileUpdate }: { user: User | null, initialProfile: UserProfile | null, onProfileUpdate: (updatedProfile: Partial<UserProfile>) => void; }) {
  const [profile, setProfile] = useState<UserProfile | null>(initialProfile);
  const [loading, setLoading] = useState(false);
  const supabase = createClient();

  useEffect(() => {
    setProfile(initialProfile);
  }, [initialProfile]);

  const handleProfileUpdate = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    if (!profile || !user) {
      setLoading(false);
      return;
    }

    const updates = {
      ...profile,
      id: user.id,
      updated_at: new Date().toISOString(),
    };

    const { error } = await supabase.from('profiles').upsert([updates]);

    if (error) {
      toast.error('Error updating profile: ' + error.message);
    } else {
      toast.success('Profile updated successfully!');
      onProfileUpdate(updates); // Notify parent component about the update
    }

    setLoading(false);
  };

  // Handle the case where initialProfile is null initially
  if (!profile) {
    return <div></div>; // Render nothing or a loading indicator if you prefer
  }

  return (
    <form onSubmit={handleProfileUpdate} className="space-y-4">
      <Input
        label="Full Name"
        value={profile.full_name || ''}
        onChange={(e) => setProfile({ ...profile, full_name: e.target.value })}
      />
      <Input
        label="Phone"
        value={profile.phone || ''}
        onChange={(e) => setProfile({ ...profile, phone: e.target.value })}
      />

      {/* Session Settings */}
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="idleTimeout"
          checked={profile.idle_timeout}
          onChange={(e) =>
            setProfile({ ...profile, idle_timeout: e.target.checked })
          }
          className="form-checkbox h-5 w-5 text-blue-600"
        />
        <label htmlFor="idleTimeout" className="text-sm">
          Log out after 20 minutes of inactivity
        </label>
      </div>

      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="logoutOnly"
          checked={profile.logout_only}
          onChange={(e) =>
            setProfile({ ...profile, logout_only: e.target.checked })
          }
          className="form-checkbox h-5 w-5 text-blue-600"
        />
        <label htmlFor="logoutOnly" className="text-sm">
          Only log out when I manually log out
        </label>
      </div>

      <Button type="submit" disabled={loading}>
        {loading ? 'Updating...' : 'Update Profile'}
      </Button>
      <Toaster />
    </form>
  );
}