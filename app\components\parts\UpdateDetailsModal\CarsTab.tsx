'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { CarsTabProps } from './types';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import { Car, Trash2, Plus, Sparkles } from 'lucide-react';
import { generatePartTitle } from '@/app/utils/titleGenerator';

interface CompatibleVehicle {
  id: number;
  variation_trim_id: number;
  brand: string;
  model: string;
  generation: string;
  variation: string;
  trim: string;
  years: string;
}

const CarsTab: React.FC<CarsTabProps> = ({
  register,
  control,
  errors,
  setValue,
  watch,
  initialCarData,
  isLoadingCars,
  partId,
  compatibleVehicles: externalCompatibleVehicles = [],
  setCompatibleVehicles: setExternalCompatibleVehicles
}) => {
  // Use external compatible vehicles state if provided, otherwise use local state
  const [localCompatibleVehicles, setLocalCompatibleVehicles] = useState<CompatibleVehicle[]>([]);
  const compatibleVehicles = setExternalCompatibleVehicles ? externalCompatibleVehicles : localCompatibleVehicles;

  // Create a wrapper for setCompatibleVehicles that includes debugging
  const setCompatibleVehicles = (vehicles: CompatibleVehicle[]) => {
    console.log('=== CARS TAB UPDATING COMPATIBLE VEHICLES ===');
    console.log('Setting compatible vehicles:', vehicles);
    console.log('Using external state:', !!setExternalCompatibleVehicles);
    console.log('Vehicle count:', vehicles.length);

    if (setExternalCompatibleVehicles) {
      console.log('Updating external state (shared with PartInfoTab)');
      setExternalCompatibleVehicles(vehicles);
    } else {
      console.log('Updating local state only');
      setLocalCompatibleVehicles(vehicles);
    }
  };
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdatingWithAI, setIsUpdatingWithAI] = useState(false);
  const [partNumber, setPartNumber] = useState<string | null>(null);
  const [showManualForm, setShowManualForm] = useState(false);
  const [brands, setBrands] = useState<any[]>([]);
  const [models, setModels] = useState<any[]>([]);
  const [generations, setGenerations] = useState<any[]>([]);
  const [variations, setVariations] = useState<any[]>([]);
  const [trims, setTrims] = useState<any[]>([]);
  const [selectedBrandId, setSelectedBrandId] = useState<string>('');
  const [selectedModelId, setSelectedModelId] = useState<string>('');
  const [selectedGenerationId, setSelectedGenerationId] = useState<string>('');
  const [selectedVariationId, setSelectedVariationId] = useState<string>('');
  const [selectedTrimId, setSelectedTrimId] = useState<string>('');
  const [isAddingVehicle, setIsAddingVehicle] = useState(false);
  const [statusMessage, setStatusMessage] = useState<string>('');
  const [successMessage, setSuccessMessage] = useState<string>('');

  const supabase = createClient();

  // Function to update part title with current compatible vehicles
  const updatePartTitle = async () => {
    try {
      setStatusMessage('Updating title...');
      console.log('🏷️ Updating part title with compatible vehicles...');

      // Get current part data with part number from compatibility group
      const { data: partData, error: partError } = await supabase
        .from('parts')
        .select(`
          title,
          category_id,
          partnumber_group,
          part_compatibility_groups!inner(part_number)
        `)
        .eq('id', parseInt(partId.toString()))
        .single();

      if (partError) {
        console.error('Error fetching part data:', partError);
        console.error('Part ID being queried:', partId);
        return;
      }

      if (!partData) {
        console.error('No part data found for part ID:', partId);
        return;
      }

      // Get category template
      const { data: categoryData, error: categoryError } = await supabase
        .from('car_part_categories')
        .select('title_template, name')
        .eq('id', partData.category_id)
        .single();

      if (categoryError || !categoryData?.title_template) {
        console.log('No title template found, skipping title update');
        return;
      }

      // Get all current compatible vehicles for this part with DISTINCT
      const { data: partCars, error: partCarsError } = await supabase
        .from('parts_car')
        .select(`
          variation_trim_id,
          variation_trim (
            trim,
            car_variation (
              variation,
              car_generation (
                name,
                car_models (
                  model_name,
                  car_brands (
                    brand_name
                  )
                )
              )
            )
          )
        `)
        .eq('part_id', parseInt(partId.toString()));

      if (partCarsError) {
        console.error('Error fetching compatible vehicles:', partCarsError);
        return;
      }

      // Remove duplicates and format compatible vehicles for title
      let compatibleVehiclesText = '';
      if (partCars && partCars.length > 0) {
        const uniqueVehicles = Array.from(new Set(partCars.map(pc => {
          const vt = pc.variation_trim;
          const cv = (vt as any)?.car_variation;
          const cg = cv?.car_generation;
          const cm = cg?.car_models;
          const cb = cm?.car_brands;

          if (!cb?.brand_name || !cm?.model_name) return '';

          const parts = [
            cb.brand_name,
            cm.model_name,
            cg?.name,
            cv?.variation,
            (vt as any)?.trim
          ].filter(part => part && part.trim() !== '');

          return parts.join(' ').trim();
        }))).filter(vehicle => vehicle.length > 0);

        compatibleVehiclesText = uniqueVehicles.join(', ');
      }

      // Get part number from the joined data
      const partNumber = (partData.part_compatibility_groups as any)?.part_number || '';

      // Update title using template
      let newTitle = categoryData.title_template
        .replace(/\{category_name\}/g, categoryData.name || '')
        .replace(/\{part_number\}/g, partNumber)
        .replace(/\{compatible_part_numbers\}/g, partNumber)
        .replace(/\{compatible_vehicles\}/g, compatibleVehiclesText);

      // Clean up extra spaces
      newTitle = newTitle.replace(/\s+/g, ' ').trim();

      // Update the part title in database
      const { error: updateError } = await supabase
        .from('parts')
        .update({ title: newTitle })
        .eq('id', parseInt(partId.toString()));

      if (updateError) {
        console.error('Error updating part title:', updateError);
      } else {
        console.log('✅ Successfully updated part title');
        console.log(`📝 New title: ${newTitle}`);

        // Update the form title field in the Part Info tab
        if (setValue) {
          setStatusMessage('Updating title field...');

          // Update the form field with the new title
          setValue('title', newTitle, {
            shouldDirty: true,
            shouldValidate: true,
            shouldTouch: true
          });

          console.log('📝 Updated title field in Part Info tab');
          console.log(`📝 New title in form: ${newTitle}`);

          // Show success message and clear status
          setSuccessMessage('Title updated successfully!');
          setTimeout(() => {
            setStatusMessage('');
            setSuccessMessage('');
          }, 2000);
        } else {
          console.warn('⚠️ setValue function not available - title field not updated');
        }
      }

    } catch (error) {
      console.error('Error in updatePartTitle:', error);
    } finally {
      setStatusMessage('');
    }
  };



  // Function to fetch compatible vehicles and part number for this part
  const fetchCompatibleVehicles = async () => {
    if (!partId) return;
      setIsLoading(true);
      setError(null);
      setStatusMessage('Getting compatible vehicles...');

      try {
        // First, get the part details to check if it has a part number
        const { data: partData, error: partError } = await supabase
          .from('parts')
          .select(`
            id,
            partnumber_group,
            part_compatibility_groups:partnumber_group(id, part_number)
          `)
          .eq('id', partId)
          .single();

        if (partError) throw partError;

        // Set part number if it exists
        if ((partData?.part_compatibility_groups as any)?.part_number) {
          setPartNumber((partData.part_compatibility_groups as any).part_number);
        }

        // Get all compatible vehicles for this part with DISTINCT to avoid duplicates
        const { data: partCars, error: partCarsError } = await supabase
          .from('parts_car')
          .select('id, variation_trim_id')
          .eq('part_id', parseInt(partId.toString()));

        if (partCarsError) throw partCarsError;

        if (!partCars || partCars.length === 0) {
          setCompatibleVehicles([]);
          setIsLoading(false);
          return;
        }

        // Remove duplicates based on variation_trim_id
        const uniquePartCars = partCars.filter((partCar, index, self) =>
          index === self.findIndex(pc => pc.variation_trim_id === partCar.variation_trim_id)
        );

        console.log(`🚗 Found ${partCars.length} total records, ${uniquePartCars.length} unique vehicles`);

        // Get detailed car information for each unique compatible vehicle
        const vehicleDetails = await Promise.all(
          uniquePartCars.map(async (partCar) => {
            try {
              // Get variation_trim details
              const { data: variationTrim, error: trimError } = await supabase
                .from('variation_trim')
                .select('id, trim, variation_id')
                .eq('id', partCar.variation_trim_id)
                .single();

              if (trimError || !variationTrim) {
                console.error('Error fetching variation trim:', trimError);
                return null;
              }

              // Get variation details
              const { data: variation, error: variationError } = await supabase
                .from('car_variation')
                .select('id, variation, generation_id')
                .eq('id', variationTrim.variation_id)
                .single();

              if (variationError || !variation) {
                console.error('Error fetching variation:', variationError);
                return null;
              }

              // Get generation details
              const { data: generation, error: generationError } = await supabase
                .from('car_generation')
                .select('id, name, start_production_year, end_production_year, model_id')
                .eq('id', variation.generation_id)
                .single();

              if (generationError || !generation) {
                console.error('Error fetching generation:', generationError);
                return null;
              }

              // Get model details
              const { data: model, error: modelError } = await supabase
                .from('car_models')
                .select('id, model_name, brand_id')
                .eq('id', generation.model_id)
                .single();

              if (modelError || !model) {
                console.error('Error fetching model:', modelError);
                return null;
              }

              // Get brand details
              const { data: brand, error: brandError } = await supabase
                .from('car_brands')
                .select('brand_id, brand_name')
                .eq('brand_id', model.brand_id)
                .single();

              if (brandError || !brand) {
                console.error('Error fetching brand:', brandError);
                return null;
              }

              return {
                id: partCar.id,
                variation_trim_id: partCar.variation_trim_id,
                brand: brand.brand_name,
                model: model.model_name,
                generation: generation.name,
                variation: variation.variation,
                trim: variationTrim.trim,
                years: `${generation.start_production_year || ''}-${generation.end_production_year || 'Present'}`
              };
            } catch (error) {
              console.error('Error fetching car details for part car:', partCar.id, error);
              return null;
            }
          })
        );

        // Filter out null results
        const validVehicles = vehicleDetails.filter(v => v !== null) as CompatibleVehicle[];
        setCompatibleVehicles(validVehicles);

      } catch (err: any) {
        console.error('Error fetching compatible vehicles:', err);
        setError('Failed to load compatible vehicles');
      } finally {
        setIsLoading(false);
        setStatusMessage('');
      }
  };

  // Fetch compatible vehicles and part number for this part
  useEffect(() => {
    fetchCompatibleVehicles();
  }, [partId, supabase]);

  // Fetch car data for manual form
  useEffect(() => {
    const fetchCarData = async () => {
      try {
        // Fetch brands
        const { data: brandsData, error: brandsError } = await supabase
          .from('car_brands')
          .select('*')
          .in('brand_name', ['VW', 'Audi'])
          .order('brand_name');

        if (brandsError) throw brandsError;
        setBrands(brandsData || []);

        // Fetch all models, generations, variations, and trims for VW/Audi
        const response = await fetch('/api/car/all');
        if (!response.ok) throw new Error('Failed to fetch car data');

        const data = await response.json();
        setModels(data.models?.filter((m: any) => ['VW', 'Audi'].includes(m.brandName)) || []);
        setGenerations(data.generations || []);
        setVariations(data.variations || []);
        setTrims(data.trims || []);

      } catch (error) {
        console.error('Error fetching car data:', error);
      }
    };

    if (showManualForm) {
      fetchCarData();
    }
  }, [showManualForm, supabase]);

  // Handle removing a compatible vehicle
  const handleRemoveVehicle = async (vehicleId: number) => {
    try {
      const { error } = await supabase
        .from('parts_car')
        .delete()
        .eq('id', vehicleId);

      if (error) throw error;

      // Update local state
      setCompatibleVehicles(compatibleVehicles.filter(v => v.id !== vehicleId));

      // Update part title with new compatible vehicles
      await updatePartTitle();
    } catch (err: any) {
      console.error('Error removing vehicle:', err);
      setError('Failed to remove vehicle');
    }
  };

  // Handle updating compatible vehicles with AI
  const handleUpdateWithAI = async () => {
    setIsUpdatingWithAI(true);
    setError(null);
    setStatusMessage('Generating compatible vehicles with AI...');

    try {
      // Call AI endpoint to regenerate compatible vehicles
      const response = await fetch('/api/parts/generate-compatibility', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          partId: partId,
          regenerate: true
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update with AI');
      }

      const result = await response.json();

      // Handle case where manual entry is required
      if (result.requiresManualEntry) {
        setError(null);
        alert(result.message);
        setShowManualForm(true);
        return;
      }

      if (result.success) {
        // Show success message
        setError(null);
        setStatusMessage('Saving to database...');

        // Log success message to console instead of showing alert
        console.log(`✅ ${result.message}`);
        console.log(`📋 Part Analysis: ${result.partAnalysis || 'No analysis provided'}`);
        console.log(`🚗 Generated ${result.generatedVehicles || 0} compatible vehicles for part ${result.partNumber}`);

        // Refresh the compatible vehicles list by re-running the fetch function
        try {
          setIsLoading(true);
          setStatusMessage('Refreshing vehicle list...');

          // Re-fetch compatible vehicles
          const { data: partCars, error: partCarsError } = await supabase
            .from('parts_car')
            .select('id, variation_trim_id')
            .eq('part_id', parseInt(partId.toString()));

          if (partCarsError) throw partCarsError;

          if (!partCars || partCars.length === 0) {
            setCompatibleVehicles([]);
            setIsLoading(false);
            return;
          }

          // Remove duplicates based on variation_trim_id
          const uniquePartCars = partCars.filter((partCar, index, self) =>
            index === self.findIndex(pc => pc.variation_trim_id === partCar.variation_trim_id)
          );

          console.log(`🔄 Refresh: Found ${partCars.length} total records, ${uniquePartCars.length} unique vehicles`);

          // Get detailed car information for each unique compatible vehicle (same logic as useEffect)
          const vehicleDetails = await Promise.all(
            uniquePartCars.map(async (partCar) => {
              try {
                // Get variation_trim details
                const { data: variationTrim, error: trimError } = await supabase
                  .from('variation_trim')
                  .select('id, trim, variation_id')
                  .eq('id', partCar.variation_trim_id)
                  .single();

                if (trimError || !variationTrim) {
                  console.error('Error fetching variation trim:', trimError);
                  return null;
                }

                // Get variation details
                const { data: variation, error: variationError } = await supabase
                  .from('car_variation')
                  .select('id, variation, generation_id')
                  .eq('id', variationTrim.variation_id)
                  .single();

                if (variationError || !variation) {
                  console.error('Error fetching variation:', variationError);
                  return null;
                }

                // Get generation details
                const { data: generation, error: generationError } = await supabase
                  .from('car_generation')
                  .select('id, name, start_production_year, end_production_year, model_id')
                  .eq('id', variation.generation_id)
                  .single();

                if (generationError || !generation) {
                  console.error('Error fetching generation:', generationError);
                  return null;
                }

                // Get model details
                const { data: model, error: modelError } = await supabase
                  .from('car_models')
                  .select('id, model_name, brand_id')
                  .eq('id', generation.model_id)
                  .single();

                if (modelError || !model) {
                  console.error('Error fetching model:', modelError);
                  return null;
                }

                // Get brand details
                const { data: brand, error: brandError } = await supabase
                  .from('car_brands')
                  .select('brand_id, brand_name')
                  .eq('brand_id', model.brand_id)
                  .single();

                if (brandError || !brand) {
                  console.error('Error fetching brand:', brandError);
                  return null;
                }

                return {
                  id: partCar.id,
                  variation_trim_id: partCar.variation_trim_id,
                  brand: brand.brand_name,
                  model: model.model_name,
                  generation: generation.name,
                  variation: variation.variation,
                  trim: variationTrim.trim,
                  years: `${generation.start_production_year || ''}-${generation.end_production_year || 'Present'}`
                };
              } catch (error) {
                console.error('Error fetching car details for part car:', partCar.id, error);
                return null;
              }
            })
          );

          // Filter out null results
          const validVehicles = vehicleDetails.filter(v => v !== null) as CompatibleVehicle[];
          setCompatibleVehicles(validVehicles);

          // Update part title with new compatible vehicles
          await updatePartTitle();

        } catch (refreshError: any) {
          console.error('Error refreshing data:', refreshError);
          setError('Data updated successfully, but failed to refresh the display. Please reload the page.');
        } finally {
          setIsLoading(false);
        }
      } else {
        throw new Error(result.error || 'AI update failed');
      }

    } catch (err: any) {
      console.error('Error updating with AI:', err);
      setError('Failed to update with AI: ' + err.message);
    } finally {
      setIsUpdatingWithAI(false);
      setStatusMessage('');
    }
  };

  // Handle manual vehicle addition
  const handleAddManualVehicle = async () => {
    if (!selectedTrimId) {
      setError('Please select all vehicle details before adding');
      return;
    }

    setIsAddingVehicle(true);
    setError(null);
    setStatusMessage('Adding vehicle...');

    try {
      const trimId = parseInt(selectedTrimId);

      // Check if this combination already exists
      const { data: existingRecord, error: checkError } = await supabase
        .from('parts_car')
        .select('id')
        .eq('part_id', parseInt(partId.toString()))
        .eq('variation_trim_id', trimId)
        .maybeSingle();

      if (checkError) throw checkError;

      if (existingRecord) {
        setError('This vehicle is already added to the compatible vehicles list');
        return;
      }

      // Insert into parts_car table using auto-increment (sequence is now synced)
      const { error: insertError } = await supabase
        .from('parts_car')
        .insert({
          part_id: parseInt(partId.toString()),
          variation_trim_id: trimId
        });

      if (insertError) {
        // Check if it's a duplicate constraint violation
        if (insertError.code === '23505' && insertError.message.includes('parts_car_part_trim_unique')) {
          setError('This vehicle is already added to the compatible vehicles list');
          return;
        }
        throw insertError;
      }

      // Reset form
      setSelectedBrandId('');
      setSelectedModelId('');
      setSelectedGenerationId('');
      setSelectedVariationId('');
      setSelectedTrimId('');

      // Refresh the compatible vehicles list
      await fetchCompatibleVehicles();

      // Update part title with new compatible vehicles
      await updatePartTitle();

    } catch (err: any) {
      console.error('Error adding manual vehicle:', err);
      setError('Failed to add vehicle: ' + err.message);
    } finally {
      setIsAddingVehicle(false);
      setStatusMessage('');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner />
        <span className="ml-2 text-gray-600">Loading compatible vehicles...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Status Ticker */}
      {statusMessage && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <div className="text-sm font-medium text-blue-900">{statusMessage}</div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {successMessage && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <div className="text-green-600">✅</div>
            <div className="text-sm font-medium text-green-900">{successMessage}</div>
          </div>
        </div>
      )}

      {/* Part Number Display */}
      {partNumber && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <div className="text-sm font-medium text-blue-900">Part Number:</div>
            <div className="text-sm text-blue-700 font-mono bg-white px-2 py-1 rounded border">
              {partNumber}
            </div>
          </div>
        </div>
      )}

      {/* Header with Update AI button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Car className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-medium text-gray-900">Compatible Vehicles</h3>
          <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
            {compatibleVehicles.length} vehicle{compatibleVehicles.length !== 1 ? 's' : ''}
          </span>
        </div>
        <button
          type="button"
          onClick={handleUpdateWithAI}
          disabled={isUpdatingWithAI}
          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Sparkles className="h-4 w-4 mr-1" />
          {isUpdatingWithAI ? 'Updating...' : 'Update with AI'}
        </button>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Compatible vehicles list */}
      {compatibleVehicles.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <Car className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-sm font-medium text-gray-900 mb-2">No compatible vehicles</h3>
          <p className="text-sm text-gray-500 mb-4">
            This part doesn't have any compatible vehicles assigned yet.
            {partNumber ? (
              <span className="block mt-1">
                Use AI to generate compatibility for part number: <strong>{partNumber}</strong>
              </span>
            ) : (
              <span className="block mt-1 text-amber-600">
                This part has no part number. You'll need to add compatible vehicles manually.
              </span>
            )}
          </p>

          {partNumber ? (
            <button
              type="button"
              onClick={handleUpdateWithAI}
              disabled={isUpdatingWithAI}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
            >
              <Sparkles className="h-4 w-4 mr-2" />
              {isUpdatingWithAI ? 'Generating...' : 'Generate with AI'}
            </button>
          ) : (
            <button
              type="button"
              onClick={() => setShowManualForm(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Vehicles Manually
            </button>
          )}
        </div>
      ) : (
        <div className="space-y-3">
          {compatibleVehicles.map((vehicle) => (
            <div
              key={vehicle.id}
              className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:shadow-sm transition-shadow"
            >
              <div className="flex items-center space-x-3">
                <Car className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {vehicle.brand} {vehicle.model}
                  </p>
                  <p className="text-xs text-gray-500">
                    {vehicle.generation} • {vehicle.variation} • {vehicle.trim} • {vehicle.years}
                  </p>
                </div>
              </div>
              <button
                type="button"
                onClick={() => handleRemoveVehicle(vehicle.id)}
                className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                title="Remove vehicle"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Manual Vehicle Selection Form */}
      {showManualForm && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium text-gray-900">Add Compatible Vehicle</h4>
            <button
              type="button"
              onClick={() => setShowManualForm(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <div className="bg-amber-50 border border-amber-200 rounded-md p-3 mb-4">
            <p className="text-sm text-amber-700">
              Since this part doesn't have a part number, please manually select the compatible vehicles.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
            {/* Brand Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Brand</label>
              <select
                value={selectedBrandId}
                onChange={(e) => {
                  setSelectedBrandId(e.target.value);
                  setSelectedModelId('');
                  setSelectedGenerationId('');
                  setSelectedVariationId('');
                  setSelectedTrimId('');
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select Brand</option>
                {brands.map((brand) => (
                  <option key={brand.brand_id} value={brand.brand_id}>
                    {brand.brand_name}
                  </option>
                ))}
              </select>
            </div>

            {/* Model Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Model</label>
              <select
                value={selectedModelId}
                onChange={(e) => {
                  setSelectedModelId(e.target.value);
                  setSelectedGenerationId('');
                  setSelectedVariationId('');
                  setSelectedTrimId('');
                }}
                disabled={!selectedBrandId}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              >
                <option value="">Select Model</option>
                {models
                  .filter((model) => model.brandId == selectedBrandId)
                  .map((model) => (
                    <option key={model.id} value={model.id}>
                      {model.name}
                    </option>
                  ))}
              </select>
            </div>

            {/* Generation Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Generation</label>
              <select
                value={selectedGenerationId}
                onChange={(e) => {
                  setSelectedGenerationId(e.target.value);
                  setSelectedVariationId('');
                  setSelectedTrimId('');
                }}
                disabled={!selectedModelId}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              >
                <option value="">Select Generation</option>
                {generations
                  .filter((generation) => generation.modelId == selectedModelId)
                  .map((generation) => (
                    <option key={generation.id} value={generation.id}>
                      {generation.name} ({generation.start_production_year}-{generation.end_production_year || 'Present'})
                    </option>
                  ))}
              </select>
            </div>

            {/* Variation Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Variation</label>
              <select
                value={selectedVariationId}
                onChange={(e) => {
                  setSelectedVariationId(e.target.value);
                  setSelectedTrimId('');
                }}
                disabled={!selectedGenerationId}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              >
                <option value="">Select Variation</option>
                {variations
                  .filter((variation) => variation.generationId == selectedGenerationId)
                  .map((variation) => (
                    <option key={variation.id} value={variation.id}>
                      {variation.variation}
                    </option>
                  ))}
              </select>
            </div>

            {/* Trim Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Trim</label>
              <select
                value={selectedTrimId}
                onChange={(e) => setSelectedTrimId(e.target.value)}
                disabled={!selectedVariationId}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              >
                <option value="">Select Trim</option>
                {trims
                  .filter((trim) => trim.variationId == selectedVariationId)
                  .map((trim) => (
                    <option key={trim.id} value={trim.id}>
                      {trim.trim}
                    </option>
                  ))}
              </select>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => setShowManualForm(false)}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleAddManualVehicle}
              disabled={!selectedTrimId || isAddingVehicle}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isAddingVehicle ? 'Adding...' : 'Add Vehicle'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CarsTab;
