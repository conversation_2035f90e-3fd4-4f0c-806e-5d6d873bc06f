import React, { useEffect } from 'react';
import { StatusBar } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { PaperProvider } from 'react-native-paper';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { QueryClient, QueryClientProvider } from 'react-query';

import { AppNavigator } from './navigation/AppNavigator';
import { useAuth } from './store/auth';
import { authService } from './services/auth';
import { theme } from './theme';
import { LoadingScreen } from './components/LoadingScreen';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

const App: React.FC = () => {
  const { isAuthenticated, setLoading, completeLogin } = useAuth();
  const [isInitializing, setIsInitializing] = React.useState(true);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      setLoading(true);

      // Check if user is already authenticated
      const user = await authService.getCurrentUser();
      
      if (user && !isAuthenticated) {
        // User exists but not in store, load profile
        const userProfile = await authService.getUserProfile();
        // Note: You might need to update the auth store here
        // This is a simplified version
      }

      // Listen to auth state changes
      const { data: { subscription } } = authService.onAuthStateChange((user) => {
        if (user && !isAuthenticated) {
          // Handle automatic login on auth state change
          console.log('User authenticated:', user.email);
        } else if (!user && isAuthenticated) {
          // Handle logout
          console.log('User signed out');
        }
      });

      return () => {
        subscription?.unsubscribe();
      };
    } catch (error) {
      console.error('App initialization error:', error);
    } finally {
      setLoading(false);
      setIsInitializing(false);
    }
  };

  if (isInitializing) {
    return <LoadingScreen />;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <PaperProvider theme={theme}>
          <QueryClientProvider client={queryClient}>
            <NavigationContainer>
              <StatusBar
                barStyle="dark-content"
                backgroundColor={theme.colors.surface}
                translucent={false}
              />
              <AppNavigator />
            </NavigationContainer>
          </QueryClientProvider>
        </PaperProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
};

export default App;
