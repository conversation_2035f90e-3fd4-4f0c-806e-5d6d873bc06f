// User Types
export interface User {
  id: string;
  email: string;
  full_name?: string;
  phone?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface UserRole {
  role_id: string;
  roles: {
    id: string;
    name: string;
    description?: string;
  };
}

export interface UserProfile extends User {
  user_roles: UserRole[];
}

// Part Types
export interface Part {
  id: number;
  title: string;
  description?: string;
  category_id?: number;
  partnumber_group?: string;
  createdAt?: string;
  updatedAt?: string;
  part_images?: PartImage[];
  parts_condition?: PartCondition[];
  part_price?: PartPrice[];
  parts_category_attribute_values?: PartAttribute[];
}

export interface PartImage {
  id: number;
  part_id: number;
  image_url: string;
  is_main_image: boolean;
}

export interface PartCondition {
  id: number;
  part_id: number;
  condition: string;
  stock: number;
}

export interface PartPrice {
  id: number;
  condition_id: number;
  price: number;
  discounted_price?: number;
}

export interface PartAttribute {
  attribute_id: number;
  value?: string;
  selection_value?: string;
  parts_category_attributes: {
    attribute: string;
    input_type: string;
  };
}

// Car Types
export interface CarBrand {
  id: number;
  name: string;
  brand_id?: number;
  brand_name?: string;
}

export interface CarModel {
  id: number;
  brand_id: number;
  model_name: string;
  model_image?: string;
}

export interface CarGeneration {
  id: number;
  model_id: number;
  name: string;
  start_production_year?: number;
  end_production_year?: number;
}

export interface CarVariation {
  id: number;
  generation_id: number;
  variation: string;
}

export interface CarTrim {
  id: number;
  variation_id: number;
  trim: string;
}

// Category Types
export interface Category {
  id: number;
  name: string;
  label?: string;
  href?: string;
  icon?: string;
  library?: string;
  parent_category_id?: number;
  isActive?: boolean;
  requirePartNumber?: boolean;
  isEnginePart?: boolean;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Search and Filter Types
export interface SearchFilters {
  query?: string;
  category?: number;
  brand?: number;
  model?: number;
  generation?: number;
  variation?: number;
  trim?: number;
  minPrice?: number;
  maxPrice?: number;
  condition?: string;
  sort?: 'newest' | 'price-low' | 'price-high' | 'featured';
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Login: undefined;
  OTP: { email: string };
  Register: undefined;
  ForgotPassword: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Shop: undefined;
  Search: undefined;
  Profile: undefined;
  Dashboard: undefined;
};

export type ShopStackParamList = {
  ShopHome: undefined;
  PartDetails: { partId: number };
  CategoryParts: { categoryId: number; categoryName: string };
  SearchResults: { filters: SearchFilters };
};

// M-PESA Types
export interface MpesaPaymentRequest {
  amount: number;
  phoneNumber: string;
  accountReference: string;
  transactionDescription: string;
}

export interface MpesaPaymentResponse {
  success: boolean;
  checkoutRequestId?: string;
  responseCode?: string;
  responseDescription?: string;
  customerMessage?: string;
}

// Sales Types
export interface Sale {
  id: string;
  sale_timestamp: string;
  sale_type: 'cash' | 'credit';
  payment_method: 'cash' | 'mpesa' | 'bank_transfer' | 'credit';
  total_amount: number;
  discount_total: number;
  client_id?: string;
  one_off_client_name?: string;
  one_off_client_phone?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  sale_items: SaleItem[];
}

export interface SaleItem {
  id: string;
  sale_id: string;
  part_id: number;
  quantity: number;
  price_at_sale: number;
  discount_amount: number;
  discount_reason?: string;
  created_at: string;
  updated_at: string;
  part?: Part;
}

// Client Types
export interface Client {
  id: string;
  category_id: string;
  name: string;
  phone_number: string;
  can_receive_credit: boolean;
  created_at: string;
  updated_at: string;
}
