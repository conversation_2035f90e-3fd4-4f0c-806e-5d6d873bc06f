const CACHE_NAME = 'autoflow-cache-v1';
const STATIC_CACHE = 'static-cache-v1';
const DYNAMIC_CACHE = 'dynamic-cache-v1';

// Static assets to cache
const STATIC_ASSETS = [
  '/',
  '/offline.html',
  '/manifest.json',
  '/images/placeholder.jpg',
  '/images/autoflow-logo.png',
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  event.waitUntil(
    Promise.all([
      caches.open(STATIC_CACHE).then((cache) => {
        return cache.addAll(STATIC_ASSETS);
      }),
      caches.open(DYNAMIC_CACHE).then((cache) => {
        return cache.addAll([]);
      }),
    ])
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames
          .filter((name) => name !== STATIC_CACHE && name !== DYNAMIC_CACHE)
          .map((name) => caches.delete(name))
      );
    })
  );
});

// Fetch event - handle requests
self.addEventListener('fetch', (event) => {
  const request = event.request;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Handle API requests
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(
      fetch(request)
        .then((response) => {
          // Clone the response
          const responseToCache = response.clone();

          // Cache the response
          caches.open(DYNAMIC_CACHE).then((cache) => {
            cache.put(request, responseToCache);
          });

          return response;
        })
        .catch(() => {
          // If offline, try to serve from cache
          return caches.match(request).then((cachedResponse) => {
            if (cachedResponse) {
              return cachedResponse;
            }

            // If not in cache, return offline response
            return caches.match('/offline.html');
          });
        })
    );
    return;
  }

  // Handle static assets
  if (STATIC_ASSETS.includes(url.pathname)) {
    event.respondWith(
      caches.match(request).then((cachedResponse) => {
        if (cachedResponse) {
          return cachedResponse;
        }
        return fetch(request);
      })
    );
    return;
  }

  // Handle other requests with network-first strategy
  event.respondWith(
    fetch(request)
      .then((response) => {
        // Clone the response
        const responseToCache = response.clone();

        // Cache the response
        caches.open(DYNAMIC_CACHE).then((cache) => {
          cache.put(request, responseToCache);
        });

        return response;
      })
      .catch(() => {
        // If offline, try to serve from cache
        return caches.match(request).then((cachedResponse) => {
          if (cachedResponse) {
            return cachedResponse;
          }

          // If not in cache, return offline response
          return caches.match('/offline.html');
        });
      })
  );
});

// Handle push notifications
self.addEventListener('push', (event) => {
  if (!event.data) return;

  const data = event.data.json();
  const options = {
    body: data.body,
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-192x192.png',
    data: data.data,
    actions: data.actions || [],
    vibrate: [100, 50, 100],
    requireInteraction: true,
  };

  event.waitUntil(
    self.registration.showNotification(data.title, options)
  );
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  if (event.action) {
    // Handle custom actions
    switch (event.action) {
      case 'view':
        event.waitUntil(
          clients.openWindow(event.notification.data.url)
        );
        break;
      // Add more actions as needed
    }
  } else {
    // Default click behavior
    event.waitUntil(
      clients.openWindow(event.notification.data.url || '/')
    );
  }
});

// Background sync event
self.addEventListener('sync', (event) => {
  if (event.tag.startsWith('sync-')) {
    event.waitUntil(handleSync(event.tag));
  }
});

// Periodic sync event
self.addEventListener('periodicsync', (event) => {
  if (event.tag === 'sync-parts') {
    event.waitUntil(syncPartsData());
  }
});

// Handle sync events
async function handleSync(tag) {
  const [_, type, id] = tag.split('-');
  const db = await openDB();
  const task = await getTaskFromDB(db, id);

  if (!task) return;

  try {
    switch (type) {
      case 'cart':
        await syncCart(task.data);
        break;
      case 'favorites':
        await syncFavorites(task.data);
        break;
      case 'orders':
        await syncOrders(task.data);
        break;
    }
    await removeTaskFromDB(db, id);
  } catch (error) {
    console.error('Sync failed:', error);
    // Retry later
    await registration.sync.register(tag);
  }
}

// Sync parts data periodically
async function syncPartsData() {
  try {
    const response = await fetch('/api/parts/sync');
    if (!response.ok) throw new Error('Sync failed');
    
    const data = await response.json();
    const cache = await caches.open(DYNAMIC_CACHE);
    
    // Cache updated parts data
    for (const part of data) {
      const request = new Request(`/api/parts/${part.id}`);
      const response = new Response(JSON.stringify(part));
      await cache.put(request, response);
    }
  } catch (error) {
    console.error('Periodic sync failed:', error);
  }
}

// Helper functions for IndexedDB
function openDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('AutoFlowSync', 1);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);

    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains('syncTasks')) {
        db.createObjectStore('syncTasks', { keyPath: 'id' });
      }
    };
  });
}

function getTaskFromDB(db, id) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['syncTasks'], 'readonly');
    const store = transaction.objectStore('syncTasks');
    const request = store.get(id);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
  });
}

function removeTaskFromDB(db, id) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['syncTasks'], 'readwrite');
    const store = transaction.objectStore('syncTasks');
    const request = store.delete(id);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve();
  });
}

// Sync functions for different data types
async function syncCart(data) {
  const response = await fetch('/api/cart/sync', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error('Cart sync failed');
}

async function syncFavorites(data) {
  const response = await fetch('/api/favorites/sync', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error('Favorites sync failed');
}

async function syncOrders(data) {
  const response = await fetch('/api/orders/sync', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!response.ok) throw new Error('Orders sync failed');
} 