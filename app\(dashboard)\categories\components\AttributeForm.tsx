'use client';

import React, { useState, useEffect } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { createClient } from '@/app/libs/supabase/client';
import { Plus, Minus, X, Save, Loader2 } from 'lucide-react';
import { CategoryAttribute } from '../types';

interface AttributeFormProps {
  categoryId: number;
  attributeId: number | null;
  attribute?: CategoryAttribute & { options?: { id: number; option_value: string }[] };
  onSuccess: (updatedCategory?: any) => void;
  onCancel: () => void;
}

interface AttributeFormData {
  attribute: string;
  input_type: 'text' | 'radio' | 'dropdown' | 'checkbox' | 'number' | 'date';
  depends_on_attribute_id?: number | null;
  depends_on_option_id?: number | null;
}

const AttributeForm: React.FC<AttributeFormProps> = ({
  categoryId,
  attributeId,
  attribute,
  onSuccess,
  onCancel
}) => {
  const supabase = createClient();
  const [dependentAttributes, setDependentAttributes] = useState<CategoryAttribute[]>([]);
  const [dependentOptions, setDependentOptions] = useState<{ id: number; option_value: string }[]>([]);
  const [options, setOptions] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDependencyFields, setShowDependencyFields] = useState(false);

  const { register, handleSubmit, watch, setValue, formState: { errors } } = useForm<AttributeFormData>({
    defaultValues: {
      attribute: attribute?.attribute || '',
      input_type: attribute?.input_type || 'text',
      depends_on_attribute_id: attribute?.depends_on_attribute_id || null,
      depends_on_option_id: attribute?.depends_on_option_id || null
    }
  });

  const inputType = watch('input_type');
  const selectedDependsOnAttribute = watch('depends_on_attribute_id');

  // Load existing options for edit mode
  useEffect(() => {
    if (attribute && ['radio', 'dropdown', 'checkbox'].includes(attribute.input_type) && attribute.options) {
      setOptions(attribute.options.map(opt => opt.option_value));
    }
  }, [attribute]);

  // Load dependent attributes (all attributes in this category except the current one)
  useEffect(() => {
    const fetchDependentAttributes = async () => {
      try {
        const { data, error } = await supabase
          .from('parts_category_attributes')
          .select('*')
          .eq('category_id', categoryId);

        if (error) throw error;

        // Filter out the current attribute to avoid circular dependencies
        const filteredAttributes = attributeId
          ? data?.filter(attr => attr.id !== attributeId)
          : data || [];

        setDependentAttributes(filteredAttributes);
      } catch (err) {
        console.error('Error fetching dependent attributes:', err);
      }
    };

    fetchDependentAttributes();
  }, [categoryId, attributeId, supabase]);

  // Load dependent options when a parent attribute is selected
  useEffect(() => {
    const fetchDependentOptions = async () => {
      if (!selectedDependsOnAttribute) {
        setDependentOptions([]);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('parts_category_attribute_input_option')
          .select('*')
          .eq('attribute_id', selectedDependsOnAttribute);

        if (error) throw error;
        setDependentOptions(data || []);
      } catch (err) {
        console.error('Error fetching dependent options:', err);
      }
    };

    fetchDependentOptions();
  }, [selectedDependsOnAttribute, supabase]);

  // Add an option
  const addOption = () => {
    setOptions([...options, '']);
  };

  // Remove an option
  const removeOption = (index: number) => {
    const newOptions = [...options];
    newOptions.splice(index, 1);
    setOptions(newOptions);
  };

  // Update an option
  const updateOption = (index: number, value: string) => {
    const newOptions = [...options];
    newOptions[index] = value;
    setOptions(newOptions);
  };

  // Submit form
  const onSubmit: SubmitHandler<AttributeFormData> = async (data) => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Prepare attribute data
      const attributeData: CategoryAttribute = {
        category_id: categoryId,
        attribute: data.attribute,
        input_type: data.input_type,
        depends_on_attribute_id: data.depends_on_attribute_id || null,
        depends_on_option_id: data.depends_on_option_id || null
      };

      let createdAttributeId: number;

      // Create or update attribute
      if (attributeId === null) {
        // Create new attribute
        const { data: newAttribute, error: attributeError } = await supabase
          .from('parts_category_attributes')
          .insert(attributeData)
          .select('id')
          .single();

        if (attributeError) throw attributeError;
        createdAttributeId = newAttribute.id;
      } else {
        // Update existing attribute
        const { error: attributeError } = await supabase
          .from('parts_category_attributes')
          .update(attributeData)
          .eq('id', attributeId);

        if (attributeError) throw attributeError;
        createdAttributeId = attributeId;
      }

      // Handle options for select/radio/checkbox inputs
      if (['radio', 'dropdown', 'checkbox'].includes(data.input_type) && options.length > 0) {
        // Delete existing options
        const { error: deleteError } = await supabase
          .from('parts_category_attribute_input_option')
          .delete()
          .eq('attribute_id', createdAttributeId);

        if (deleteError) throw deleteError;

        // Insert new options
        const optionsToInsert = options
          .filter(option => option.trim() !== '') // Remove empty options
          .map(option => ({
            attribute_id: createdAttributeId,
            option_value: option
          }));

        if (optionsToInsert.length > 0) {
          const { error: optionsError } = await supabase
            .from('parts_category_attribute_input_option')
            .insert(optionsToInsert);

          if (optionsError) throw optionsError;
        }
      }

      // Fetch the updated category data to pass back to the parent
      try {
        const { data: updatedCategory, error: fetchError } = await supabase
          .from('car_part_categories')
          .select('*')
          .eq('id', categoryId)
          .single();

        if (fetchError) throw fetchError;

        // Success - pass the updated category data
        onSuccess(updatedCategory);
      } catch (fetchErr) {
        console.error('Error fetching updated category:', fetchErr);
        // Still call onSuccess even if there's an error fetching the updated category
        onSuccess();
      }
    } catch (err: any) {
      console.error('Error saving attribute:', err);
      setError(err.message || 'Failed to save attribute');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-5">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        {attributeId ? 'Edit Attribute' : 'Add New Attribute'}
      </h3>

      {error && (
        <div className="mb-4 bg-red-50 border-l-4 border-red-400 p-4 text-red-700 text-sm">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-4">
          {/* Attribute Name */}
          <div>
            <label htmlFor="attribute" className="block text-sm font-medium text-gray-700 mb-1">
              Attribute Name *
            </label>
            <input
              id="attribute"
              type="text"
              {...register('attribute', { required: 'Attribute name is required' })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              placeholder="e.g., Color, Size, Material"
              disabled={isSubmitting}
            />
            {errors.attribute && (
              <p className="mt-1 text-sm text-red-600">{errors.attribute.message}</p>
            )}
          </div>

          {/* Input Type */}
          <div>
            <label htmlFor="input_type" className="block text-sm font-medium text-gray-700 mb-1">
              Input Type *
            </label>
            <select
              id="input_type"
              {...register('input_type', { required: 'Input type is required' })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              disabled={isSubmitting}
            >
              <option value="text">Text</option>
              <option value="number">Number</option>
              <option value="date">Date</option>
              <option value="dropdown">Dropdown</option>
              <option value="radio">Radio Buttons</option>
              <option value="checkbox">Checkboxes</option>
            </select>
          </div>

          {/* Options for dropdown, radio, or checkbox */}
          {['dropdown', 'radio', 'checkbox'].includes(inputType) && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Options
              </label>
              {options.map((option, index) => (
                <div key={index} className="flex mb-2">
                  <input
                    type="text"
                    value={option}
                    onChange={(e) => updateOption(index, e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    placeholder={`Option ${index + 1}`}
                    disabled={isSubmitting}
                  />
                  <button
                    type="button"
                    onClick={() => removeOption(index)}
                    className="ml-2 p-2 text-red-500 hover:text-red-700 rounded-md"
                    disabled={isSubmitting}
                  >
                    <X size={18} />
                  </button>
                </div>
              ))}
              <button
                type="button"
                onClick={addOption}
                className="mt-1 flex items-center text-sm text-teal-600 hover:text-teal-800"
                disabled={isSubmitting}
              >
                <Plus size={16} className="mr-1" />
                Add Option
              </button>
            </div>
          )}

          {/* Dependency Fields Toggle */}
          <div className="mt-6">
            <button
              type="button"
              onClick={() => setShowDependencyFields(!showDependencyFields)}
              className="flex items-center text-sm text-gray-600 hover:text-gray-800"
              disabled={isSubmitting}
            >
              {showDependencyFields ? <Minus size={16} className="mr-1" /> : <Plus size={16} className="mr-1" />}
              {showDependencyFields ? 'Hide Dependency Options' : 'Add Dependency'}
            </button>
          </div>

          {/* Dependency Fields */}
          {showDependencyFields && (
            <div className="mt-4 p-4 bg-gray-50 rounded-md">
              <h4 className="font-medium text-gray-800 mb-3">Dependency Settings</h4>
              <p className="text-sm text-gray-600 mb-4">
                Make this attribute only appear when another attribute has a specific value.
              </p>

              <div className="space-y-4">
                {/* Parent Attribute */}
                <div>
                  <label htmlFor="depends_on_attribute_id" className="block text-sm font-medium text-gray-700 mb-1">
                    Parent Attribute
                  </label>
                  <select
                    id="depends_on_attribute_id"
                    {...register('depends_on_attribute_id')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    disabled={isSubmitting || dependentAttributes.length === 0}
                  >
                    <option value="">None</option>
                    {dependentAttributes.map((attr) => (
                      <option key={attr.id} value={attr.id}>
                        {attr.attribute}
                      </option>
                    ))}
                  </select>
                  {dependentAttributes.length === 0 && (
                    <p className="mt-1 text-sm text-gray-500">No other attributes available for dependency.</p>
                  )}
                </div>

                {/* Parent Option - Only show if parent attribute is selected */}
                {selectedDependsOnAttribute && (
                  <div>
                    <label htmlFor="depends_on_option_id" className="block text-sm font-medium text-gray-700 mb-1">
                      Parent Option
                    </label>
                    <select
                      id="depends_on_option_id"
                      {...register('depends_on_option_id')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                      disabled={isSubmitting || dependentOptions.length === 0}
                    >
                      <option value="">Any value</option>
                      {dependentOptions.map((option) => (
                        <option key={option.id} value={option.id}>
                          {option.option_value}
                        </option>
                      ))}
                    </select>
                    {dependentOptions.length === 0 && (
                      <p className="mt-1 text-sm text-gray-500">Selected attribute has no options defined.</p>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="mt-6 flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 transition-colors flex items-center disabled:opacity-50"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 size={16} className="mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save size={16} className="mr-2" />
                Save Attribute
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AttributeForm;