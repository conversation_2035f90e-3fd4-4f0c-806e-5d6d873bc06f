'use client';

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { X, RefreshCw, AlertTriangle, CheckCircle, Eye } from 'lucide-react';
import { batchRenamePartsInCategory, previewBatchRename } from '@/app/utils/batchRenameUtils';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import toast from 'react-hot-toast';

interface BatchRenameModalProps {
  isOpen: boolean;
  onClose: () => void;
  categoryId: number;
  categoryName: string;
  onComplete: () => void;
}

interface PreviewItem {
  title: string;
  newTitle: string;
  partId: number;
}

const BatchRenameModal: React.FC<BatchRenameModalProps> = ({
  isOpen,
  onClose,
  categoryId,
  categoryName,
  onComplete
}) => {
  const [isLoadingPreview, setIsLoadingPreview] = useState(false);
  const [isRenaming, setIsRenaming] = useState(false);
  const [preview, setPreview] = useState<PreviewItem[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load preview when modal opens
  useEffect(() => {
    if (isOpen && categoryId) {
      loadPreview();
    }
  }, [isOpen, categoryId]);

  const loadPreview = async () => {
    setIsLoadingPreview(true);
    setError(null);
    
    try {
      const previewData = await previewBatchRename(categoryId);
      setPreview(previewData);
    } catch (error: any) {
      console.error('Error loading preview:', error);
      setError(error.message || 'Failed to load preview');
    } finally {
      setIsLoadingPreview(false);
    }
  };

  const handleBatchRename = async () => {
    setIsRenaming(true);
    setError(null);

    try {
      const result = await batchRenamePartsInCategory(categoryId);
      
      if (result.success) {
        toast.success(`Successfully renamed ${result.renamedParts} parts in ${categoryName}`);
        onComplete();
        onClose();
      } else {
        const errorMessage = result.errors.length > 0 
          ? result.errors.join(', ') 
          : 'Unknown error occurred';
        setError(`Batch rename completed with errors: ${errorMessage}`);
        
        if (result.renamedParts > 0) {
          toast.success(`Renamed ${result.renamedParts}/${result.totalParts} parts. Some errors occurred.`);
        }
      }
    } catch (error: any) {
      console.error('Error during batch rename:', error);
      setError(error.message || 'Failed to rename parts');
      toast.error('Failed to rename parts');
    } finally {
      setIsRenaming(false);
    }
  };

  const handleClose = () => {
    if (!isRenaming) {
      setPreview([]);
      setShowPreview(false);
      setError(null);
      onClose();
    }
  };

  // Only render portal on client side
  if (typeof window === 'undefined') {
    return null;
  }

  return createPortal(
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-[99999] overflow-y-auto" style={{ position: 'fixed', zIndex: 99999 }}>
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
              onClick={handleClose}
            />

            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full relative"
              style={{ position: 'relative', zIndex: 99999 }}
            >
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <RefreshCw className="w-6 h-6 text-teal-600 mr-3" />
                <h3 className="text-lg font-medium text-gray-900">
                  Batch Rename Parts
                </h3>
              </div>
              <button
                onClick={handleClose}
                disabled={isRenaming}
                className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="mb-6">
              <p className="text-sm text-gray-600">
                This will rename all parts in the <strong>{categoryName}</strong> category to use the new title template.
              </p>
            </div>

            {/* Error Display */}
            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-center">
                  <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            )}

            {/* Preview Section */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-medium text-gray-900">Preview Changes</h4>
                <button
                  onClick={() => setShowPreview(!showPreview)}
                  disabled={isLoadingPreview || isRenaming}
                  className="flex items-center text-sm text-teal-600 hover:text-teal-800 disabled:opacity-50"
                >
                  <Eye className="w-4 h-4 mr-1" />
                  {showPreview ? 'Hide Preview' : 'Show Preview'}
                </button>
              </div>

              {isLoadingPreview && (
                <div className="flex items-center justify-center py-4">
                  <LoadingSpinner size={20} />
                  <span className="ml-2 text-sm text-gray-600">Loading preview...</span>
                </div>
              )}

              <AnimatePresence>
                {showPreview && !isLoadingPreview && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="border border-gray-200 rounded-md overflow-hidden"
                  >
                    {preview.length > 0 ? (
                      <div className="max-h-60 overflow-y-auto">
                        {preview.map((item, index) => (
                          <div key={item.partId} className={`p-3 ${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}`}>
                            <div className="text-xs text-gray-500 mb-1">Part ID: {item.partId}</div>
                            <div className="text-sm">
                              <div className="text-gray-600 mb-1">
                                <span className="font-medium">Current:</span> {item.title}
                              </div>
                              <div className="text-gray-900">
                                <span className="font-medium">New:</span> {item.newTitle}
                              </div>
                            </div>
                          </div>
                        ))}
                        {preview.length === 5 && (
                          <div className="p-2 bg-gray-100 text-center text-xs text-gray-600">
                            Showing first 5 changes. More parts will be renamed.
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="p-4 text-center text-sm text-gray-500">
                        No parts need to be renamed. All parts already follow the current template.
                      </div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Warning */}
            <div className="mb-6 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <div className="flex items-start">
                <AlertTriangle className="w-5 h-5 text-yellow-500 mr-2 mt-0.5" />
                <div className="text-sm text-yellow-700">
                  <p className="font-medium mb-1">Important:</p>
                  <ul className="list-disc list-inside space-y-1">
                    <li>This action will rename ALL parts in this category</li>
                    <li>The changes cannot be easily undone</li>
                    <li>Parts will be processed in batches to avoid system overload</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              onClick={handleBatchRename}
              disabled={isRenaming || isLoadingPreview}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-teal-600 text-base font-medium text-white hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRenaming ? (
                <>
                  <LoadingSpinner size={16} />
                  <span className="ml-2">Renaming Parts...</span>
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Rename All Parts
                </>
              )}
            </button>
            <button
              type="button"
              onClick={handleClose}
              disabled={isRenaming}
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
            >
              Cancel
            </button>
          </div>
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>,
    document.body
  );
};

export default BatchRenameModal;
