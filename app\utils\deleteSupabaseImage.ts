import { createClient } from '@/app/libs/supabase/client';

/**
 * Helper function to list files in a directory and delete matches
 */
async function tryListAndDeleteFiles(
  supabase: any,
  bucketName: string,
  directory: string, 
  filename: string | undefined
) {
  try {
    const { data: fileList, error: listError } = await supabase.storage
      .from(bucketName)
      .list(directory);
      
    if (listError) {
      console.error(`Error listing files in ${directory || 'root'}:`, listError);
      return false;
    }
    
    if (!fileList || fileList.length === 0) {
      console.log(`No files found in ${directory || 'root'} directory`);
      return false;
    }
    
    console.log(`Found ${fileList.length} files in ${directory || 'root'} directory:`, 
      fileList.map((f: { name: string }) => f.name).join(', '));
    
    // Find files that match or contain the filename
    const matchingFiles = fileList.filter((file: { name: string }) => 
      filename && (
        file.name === filename || 
        file.name.includes(filename) || 
        filename.includes(file.name)
      )
    );
    
    console.log(`Found ${matchingFiles.length} matching files in ${directory || 'root'} directory`);
    
    for (const file of matchingFiles) {
      const filePath = directory ? `${directory}/${file.name}` : file.name;
      console.log(`Attempting to delete matching file: ${filePath}`);
      
      const { error: matchError } = await supabase.storage
        .from(bucketName)
        .remove([filePath]);
        
      if (!matchError) {
        console.log('Successfully deleted matching file:', filePath);
        
        // Try to invalidate cache for this file too
        try {
          // Upload a marker file to invalidate cache
          const dummyBlob = new Blob([''], { type: 'text/plain' });
          const dummyFile = new File([dummyBlob], 'deleted_marker.txt');
          
          await supabase.storage
            .from(bucketName)
            .upload(`${filePath}.deleted_marker`, dummyFile, {
              cacheControl: 'no-cache, no-store, must-revalidate',
              upsert: true
            });
            
          // Now remove the marker file
          await supabase.storage
            .from(bucketName)
            .remove([`${filePath}.deleted_marker`]);
        } catch (cacheError) {
          console.error('Error attempting to purge cache for matched file:', cacheError);
        }
        
        return true;
      } else {
        console.error(`Error deleting matching file ${filePath}:`, matchError);
      }
    }
    
    return false;
  } catch (error) {
    console.error(`Error in tryListAndDelete for ${directory}:`, error);
    return false;
  }
}

/**
 * Deletes an image from Supabase storage bucket given its URL
 * @param imageUrl - The full URL or storage path of the image to delete
 * @param userId - Optional user ID to help construct the path if needed
 * @param bucketName - The name of the storage bucket (defaults to 'car-part-images')
 * @returns An object containing success status and error information
 */
export async function deleteSupabaseImage(
  imageUrl: string, 
  userId?: string, 
  bucketName: string = 'car-part-images'
): Promise<{ success: boolean; error?: any; path?: string }> {
  if (!imageUrl) {
    return { success: false, error: 'No image URL provided' };
  }

  const supabase = createClient();
  let deletedPath: string | undefined;
  
  try {
    // Extract the file path from the full URL if needed
    let storagePath = imageUrl;
    
    // If it's a full URL from Supabase storage
    if (imageUrl.includes('supabase.co')) {
      // Parse the URL to extract the path
      const url = new URL(imageUrl);
      const pathSegments = url.pathname.split('/');
      
      // Find the index of the bucket name or storage indicators in the path
      const bucketIndex = pathSegments.findIndex(segment => 
        segment === bucketName || 
        segment === 'storage' || 
        segment === 'object'
      );
      
      if (bucketIndex !== -1 && bucketIndex + 1 < pathSegments.length) {
        // Extract everything after the bucket name
        storagePath = pathSegments.slice(bucketIndex + 1).join('/');
        console.log('Extracted storage path:', storagePath);
      }
    }
    
    // Try to extract just the filename
    const filename = imageUrl.split('/').pop();
    console.log('Extracted filename:', filename);
    
    // Get the current authenticated user if no userId is provided
    if (!userId) {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          userId = user.id;
          console.log('Using authenticated user ID:', userId);
        }
      } catch (authError) {
        console.error('Error getting authenticated user:', authError);
      }
    }
    
    // Create an array of paths to try
    const pathsToTry = [];
    
    // 1. Try with the extracted path
    pathsToTry.push(storagePath);
    
    // 2. Try with just the filename
    if (filename) {
      pathsToTry.push(filename);
    }
    
    // 3. Try with user ID prefixed path
    if (userId && filename) {
      pathsToTry.push(`${userId}/${filename}`);
    }
    
    // 4. Try with public/ prefix
    if (filename) {
      pathsToTry.push(`public/${filename}`);
    }
    
    // 5. Try with various date-based paths (common in auto-generated filenames)
    if (filename && filename.includes('T')) {
      // Extract date portion if it's a timestamp format
      const dateParts = filename.split('T')[0].split('_');
      if (dateParts.length > 1) {
        const datePrefix = dateParts[dateParts.length - 1];
        pathsToTry.push(`${datePrefix}/${filename}`);
        
        if (userId) {
          pathsToTry.push(`${userId}/${datePrefix}/${filename}`);
        }
      }
    }
    
    // 6. Try with the path format from the URL structure
    if (imageUrl.includes('/v1/object/public/')) {
      const objectPath = imageUrl.split('/v1/object/public/')[1];
      if (objectPath) {
        pathsToTry.push(objectPath);
      }
    }
    
    console.log('Paths to try:', pathsToTry);
    
    // Try each path in sequence
    for (const path of pathsToTry) {
      console.log(`Attempting to delete from path: ${path}`);
      const { error: pathError } = await supabase.storage
        .from(bucketName)
        .remove([path]);
        
      if (!pathError) {
        console.log('Successfully deleted image from path:', path);
        
        // Try to purge the CDN cache for this image
        try {
          // First approach: Try to update the file's cache control to force revalidation
          console.log('Attempting to purge CDN cache...');
          
          // Create a dummy blob instead of a File (which isn't available in Node.js environment)
          const dummyBlob = new Blob([''], { type: 'text/plain' });
          
          // Upload the blob directly instead of creating a File
          const { error: uploadError } = await supabase.storage
            .from(bucketName)
            .upload(`${path}_deleted_marker`, dummyBlob, {
              contentType: 'text/plain',
              upsert: true
            });
            
          if (!uploadError) {
            console.log('Uploaded cache invalidation marker');
            
            // Now remove the marker file
            await supabase.storage
              .from(bucketName)
              .remove([`${path}_deleted_marker`]);
          }
        } catch (cacheError) {
          console.error('Error attempting to purge cache:', cacheError);
        }
        
        return { success: true, path };
      } else {
        console.log(`Failed to delete from path ${path}:`, pathError.message);
      }
    }
    
    // If direct paths failed, try listing files and finding matches
    console.log('Direct path deletion failed, trying to list files...');
    
    // Try in root directory
    await tryListAndDeleteFiles(supabase, bucketName, '', filename);
    
    // Try in user directory if userId is available
    if (userId) {
      await tryListAndDeleteFiles(supabase, bucketName, userId, filename);
    }
    
    // Try in public directory
    await tryListAndDeleteFiles(supabase, bucketName, 'public', filename);
    
    // Last resort: try to find the file by listing all directories
    try {
      console.log('Last resort: Attempting to list all top-level directories...');
      const { data: rootList, error: rootError } = await supabase.storage
        .from(bucketName)
        .list('');
        
      if (!rootError && rootList) {
        // Get all directories
        const directories = rootList.filter(item => item.id === null);
        
        for (const dir of directories) {
          console.log(`Checking directory: ${dir.name}`);
          await tryListAndDeleteFiles(supabase, bucketName, dir.name, filename);
        }
      }
    } catch (lastError) {
      console.error('Error in last resort directory scan:', lastError);
    }
    
    // If all attempts failed
    return { 
      success: false, 
      error: 'Failed to delete image after multiple attempts',
      path: storagePath
    };
  } catch (error) {
    console.error('Error deleting image from Supabase:', error);
    return { success: false, error };
  }
}
