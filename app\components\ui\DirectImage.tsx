'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';

interface DirectImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  className?: string;
  sizes?: string;
  priority?: boolean;
  quality?: number;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * DirectImage component that can bypass Vercel's image optimization
 * for Supabase storage URLs to ensure they display correctly on mobile devices
 */
const DirectImage: React.FC<DirectImageProps> = ({
  src,
  alt,
  width,
  height,
  fill = false,
  className = '',
  sizes,
  priority = false,
  quality = 75,
  loading,
  onLoad,
  onError,
}) => {
  const [imgSrc, setImgSrc] = useState<string>(src);
  const [imgError, setImgError] = useState<boolean>(false);
  
  // Check if the image is from Supabase storage
  const isSupabaseImage = src.includes('supabase.co/storage') || 
                          src.includes('supabase.co/object');
  
  // Use a direct img tag for Supabase images to bypass Vercel's optimization
  const useDirectImg = isSupabaseImage;
  
  // Handle image error
  const handleError = () => {
    setImgError(true);
    if (onError) onError();
  };
  
  // Handle image load
  const handleLoad = () => {
    if (onLoad) onLoad();
  };
  
  // Update imgSrc when src prop changes
  useEffect(() => {
    setImgSrc(src);
    setImgError(false);
  }, [src]);
  
  // If image failed to load, show a placeholder
  if (imgError) {
    return (
      <div 
        className={`bg-gray-200 flex items-center justify-center ${className}`}
        style={fill ? {} : { width: width || 100, height: height || 100 }}
      >
        <span className="text-gray-400 text-sm">Image not available</span>
      </div>
    );
  }
  
  // Use a direct img tag for Supabase images
  if (useDirectImg) {
    if (fill) {
      return (
        <div className={`relative ${className}`} style={{ width: '100%', height: '100%' }}>
          <img
            src={imgSrc}
            alt={alt}
            className={`absolute inset-0 w-full h-full ${className}`}
            style={{ objectFit: className?.includes('object-contain') ? 'contain' : 'cover' }}
            onError={handleError}
            onLoad={handleLoad}
            loading={loading || (priority ? 'eager' : 'lazy')}
          />
        </div>
      );
    }
    
    return (
      <img
        src={imgSrc}
        alt={alt}
        width={width}
        height={height}
        className={className}
        onError={handleError}
        onLoad={handleLoad}
        loading={loading || (priority ? 'eager' : 'lazy')}
      />
    );
  }
  
  // Use Next.js Image component for non-Supabase images
  return fill ? (
    <Image
      src={imgSrc}
      alt={alt}
      fill={true}
      className={className}
      sizes={sizes || '100vw'}
      priority={priority}
      quality={quality}
      onError={handleError}
      onLoad={handleLoad}
      loading={loading}
    />
  ) : (
    <Image
      src={imgSrc}
      alt={alt}
      width={width || 100}
      height={height || 100}
      className={className}
      sizes={sizes}
      priority={priority}
      quality={quality}
      onError={handleError}
      onLoad={handleLoad}
      loading={loading}
    />
  );
};

export default DirectImage;
