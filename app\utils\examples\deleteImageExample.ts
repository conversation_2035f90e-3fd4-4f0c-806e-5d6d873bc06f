import { deleteSupabaseImage } from '../deleteSupabaseImage';

/**
 * Example function demonstrating how to use the deleteSupabaseImage utility
 */
async function deleteImageExample() {
  // Example 1: Delete an image using its full Supabase URL
  const imageUrl = 'https://excgraelqcvcdsnlvrtv.supabase.co/storage/v1/object/public/car-part-images/part_image_20250216T004340595Z.jpg';
  const userId = 'user123'; // The user ID associated with the image, if applicable
  
  console.log('Attempting to delete image:', imageUrl);
  
  const result = await deleteSupabaseImage(imageUrl, userId);
  
  if (result.success) {
    console.log('✅ Image deleted successfully from path:', result.path);
  } else {
    console.error('❌ Failed to delete image:', result.error);
  }
  
  // Example 2: Delete an image using just its storage path
  const storagePath = 'user123/engine_part.jpg';
  
  console.log('Attempting to delete image by path:', storagePath);
  
  const pathResult = await deleteSupabaseImage(storagePath, userId);
  
  if (pathResult.success) {
    console.log('✅ Image deleted successfully from path:', pathResult.path);
  } else {
    console.error('❌ Failed to delete image:', pathResult.error);
  }
  
  // Example 3: Delete an image from a different bucket
  const customBucketUrl = 'https://excgraelqcvcdsnlvrtv.supabase.co/storage/v1/object/public/user-avatars/profile.jpg';
  
  console.log('Attempting to delete image from custom bucket:', customBucketUrl);
  
  const customResult = await deleteSupabaseImage(customBucketUrl, userId, 'user-avatars');
  
  if (customResult.success) {
    console.log('✅ Image deleted successfully from custom bucket:', customResult.path);
  } else {
    console.error('❌ Failed to delete image from custom bucket:', customResult.error);
  }
}

// Example of how to call this function from an API route or component
export async function handleImageDeletion(imageUrl: string, userId?: string) {
  try {
    const result = await deleteSupabaseImage(imageUrl, userId);
    return { success: result.success, message: result.success ? 'Image deleted successfully' : 'Failed to delete image' };
  } catch (error) {
    console.error('Error in image deletion handler:', error);
    return { success: false, message: 'Error processing image deletion' };
  }
}
