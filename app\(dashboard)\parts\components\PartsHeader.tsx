'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Search, BellIcon, MenuIcon, User, LogOut } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { createClient } from '@/app/libs/supabase/client';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';

const PartsHeader: React.FC = () => {
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const supabase = createClient();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleLogout = async () => {
    try {
      // Clear Supabase session
      const { error } = await supabase.auth.signOut();
      if (error) {
        throw error;
      }

      // Clear custom auth cookies
      const { removeUserCookie } = await import('@/app/utils/cookies');
      await removeUserCookie();

      // Clear any other auth-related data
      localStorage.clear();
      sessionStorage.clear();

      toast.success('Logged out successfully');

      // Force a hard redirect to ensure clean state with logout parameter
      window.location.href = '/login?logout=true';
    } catch (error) {
      console.error('Error logging out:', error);
      toast.error('Failed to log out. Please try again.');

      // Force logout even if there's an error
      const { removeUserCookie } = await import('@/app/utils/cookies');
      await removeUserCookie();
      window.location.href = '/login?logout=true';
    }
  };

  return (
    <div className="mb-4">
      <div className="flex items-center justify-between mb-4">
        <button className="p-2 rounded-full bg-gray-100">
          <MenuIcon size={20} className="text-gray-700" />
        </button>
        <div className="flex items-center">
          <div className="flex items-center mr-2">
            <span className="text-sm font-medium mr-1">United State</span>
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="16" 
              height="16" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              className="text-red-500"
            >
              <path d="m6 9 6 6 6-6" />
            </svg>
          </div>
          <div className="flex items-center gap-2">
            <button className="p-2 rounded-full bg-gray-100 relative">
              <BellIcon size={20} className="text-gray-700" />
              <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
            </button>
            <div className="relative" ref={dropdownRef}>
              <button 
                className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                onClick={() => setShowDropdown(!showDropdown)}
              >
                <User size={20} className="text-gray-700" />
              </button>
              
              <AnimatePresence>
                {showDropdown && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                    className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-50"
                  >
                    <button 
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => {
                        setShowDropdown(false);
                        router.push('/profile');
                      }}
                    >
                      <User size={16} className="mr-2" />
                      Profile
                    </button>
                    <button 
                      className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                      onClick={() => {
                        setShowDropdown(false);
                        handleLogout();
                      }}
                    >
                      <LogOut size={16} className="mr-2" />
                      Logout
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </div>
      
      <div className="relative mb-4">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search size={16} className="text-gray-400" />
        </div>
        <input
          type="text"
          placeholder="Search Destinations"
          className="bg-gray-100 w-full pl-10 pr-4 py-2 rounded-lg text-sm border-none focus:ring-0 focus:outline-none"
        />
        <div className="absolute inset-y-0 right-4 flex items-center">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            width="20" 
            height="20" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            className="text-gray-400"
          >
            <rect width="18" height="18" x="3" y="3" rx="2" />
            <path d="M3 9h18" />
            <path d="M9 21V9" />
          </svg>
        </div>
      </div>
    </div>
  );
};

export default PartsHeader; 