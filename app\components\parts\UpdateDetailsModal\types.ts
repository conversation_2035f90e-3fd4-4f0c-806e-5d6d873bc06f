// /components/modals/UpdateDetailsModal/types.ts
import { Control, FieldErrors, UseFormRegister, UseFormSetValue, UseFormWatch, UseFormGetValues } from 'react-hook-form';

// Enums based on your Supabase schema (add others if needed)
export enum LocationSubType {
  OpenShelf = 'open_shelf',
  Crate = 'crate',
  Container = 'container',
  ShelfSection = 'shelf_section',
  CageSection = 'cage_section',
  HangingLine = 'hanging_line',
  EngineArea = 'engine_area',
  // Add other subtypes from your 'location_subtype_enum'
}

// Interfaces for fetched data
export interface StorageArea {
  area_id: number;
  name: string;
  location_type: string; // Consider using an enum if defined
  level: string;         // Consider using an enum if defined
  description?: string;
}

export interface StorageUnit {
  unit_id: number;
  area_id: number;
  unit_type: string; // Consider using an enum if defined
  identifier: string;
  description?: string;
}

export interface PartLocation {
  location_id?: number; // Optional because we might create a new one
  part_id: number;
  unit_id: number | null; // Use null for "not selected" state
  quantity: number;
  location_subtype: LocationSubType | '' ; // Use '' for "not selected"
  details?: {
    level?: string;
    crate_code?: string;
    container_code?: string;
    row?: string;
    col?: string;
    // Add other possible fields from the JSONB details
  } | null;
  notes?: string;
  // Include area_id for easier filtering/selection, needs join or separate fetch
  area_id?: number | null;
}

export interface CategoryAttribute {
  id: number;
  category_id: number;
  attribute: string;
  input_type: 'text' | 'number' | 'radio' | 'checkbox' | 'select'; // Add other types if needed
  is_required: boolean;
  // Add other fields from parts_category_attributes
}

export interface AttributeValue {
    id: number; // This is the value ID from parts_category_attribute_values
    part_id: number;
    attribute_id: number;
    value: string | null;
    selection_value: string | null; // For radio, checkbox, select
}


export interface AttributeOption {
  id: number;
  attribute_id: number;
  option_value: string;
  // Add other fields from parts_category_attribute_input_option
}

// Form Values Interface
export interface PartDetailsFormValues {
  title: string;
  partNumber: string;
  description?: string; // Add description field
  conditions: Array<{
    id: string; // Keep as string from original code, though likely number
    condition: string;
    stock: number;
    price: number;
    discountedPrice: number | null;
    reorderLevel: number;
    newCondition?: string; // To track changes
  }>;
  attributes: Array<{
    id: string; // Attribute ID (parts_category_attributes.id)
    name: string;
    value: string | string[] | null; // Can be string array for checkboxes
    inputType: string;
  }>;
  location: PartLocation; // Embed the location details directly
  carData: CarData; // Car-related data
  categoryData: CategoryData; // Category-related data
}

// Compatible Vehicle interface
export interface CompatibleVehicle {
  id: number;
  variation_trim_id: number;
  brand: string;
  model: string;
  generation: string;
  variation: string;
  trim: string;
  years: string;
}

// Props for Tab Components
export interface TabProps {
  register: UseFormRegister<PartDetailsFormValues>;
  control: Control<PartDetailsFormValues>;
  errors: FieldErrors<PartDetailsFormValues>;
  setValue: UseFormSetValue<PartDetailsFormValues>;
  watch: UseFormWatch<PartDetailsFormValues>; // Add watch
  getValues: UseFormGetValues<PartDetailsFormValues>; // Add getValues
  // Add any specific data needed by the tab
}

export interface PartInfoTabProps extends TabProps {
   conditions: any[]; // Use the original conditions prop structure for now
   onTabModified: (tabId: string | number) => void; // Add function to mark tab as modified
   partId: string | number; // Add partId for title updates
   compatibleVehicles?: CompatibleVehicle[]; // Add compatible vehicles from Cars tab
   onAiAlternativesGenerated?: (alternatives: string[]) => void; // Callback for AI-generated alternatives
}

export interface AttributesTabProps extends TabProps {
  categoryAttributes: CategoryAttribute[];
  attributeOptions: { [key: string]: AttributeOption[] };
  isLoadingAttributes: boolean;
  initialAttributeValues: AttributeValue[]; // Pass initial values
}

export interface LocationTabProps extends TabProps {
  storageAreas: StorageArea[];
  allStorageUnits: StorageUnit[]; // Pass all units initially
  initialLocation: PartLocation | null;
  partId: string;
  isLoadingLocation: boolean;
}

// Car-related types
export interface CarData {
  brandId: number | string;
  modelId: number | string;
  generationId: number | string;
  variationId: number | string;
  trimId: number | string;
}

export interface CarsTabProps extends TabProps {
  initialCarData: CarData | null;
  isLoadingCars: boolean;
  partId: string | number;
  compatibleVehicles?: CompatibleVehicle[];
  setCompatibleVehicles?: (vehicles: CompatibleVehicle[]) => void;
}

// Category-related types
export interface CategoryData {
  categoryId: number | string;
}

export interface CategoriesTabProps extends TabProps {
  initialCategoryId: number | string | null;
  isLoadingCategories: boolean;
}
