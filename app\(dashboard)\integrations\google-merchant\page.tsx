'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/app/components/ui/Card';
import { Button } from '@/app/components/ui/Button';
import { Alert, AlertDescription, AlertTitle } from '@/app/components/ui/alert';
import { Badge } from '@/app/components/ui/badge';
import { Progress } from '@/app/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs-shadcn';
import {
  AlertCircle,
  CheckCircle2,
  RefreshCw,
  ShoppingCart,
  XCircle,
  ExternalLink,
  Info
} from 'lucide-react';
import DashboardHeader from '@/app/components/dashboard/DashboardHeader';
import { SyncStatus } from '@/app/services/googleMerchant';
import { Skeleton } from '@/app/components/ui/skeleton';
import { Separator } from '@/app/components/ui/separator';
import { toast } from 'react-hot-toast';

interface GoogleMerchantStatus {
  authenticated: boolean;
  merchantId?: string;
  syncStatus?: SyncStatus;
}

export default function GoogleMerchantPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [authenticating, setAuthenticating] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [status, setStatus] = useState<GoogleMerchantStatus>({ authenticated: false });
  const [lastUpdateTime, setLastUpdateTime] = useState<Date>(new Date());
  const [progressPercent, setProgressPercent] = useState<number>(0);

  // Fetch status on mount and check for authentication
  useEffect(() => {
    // Check if we just completed authentication
    const justAuthenticated = localStorage.getItem('justAuthenticated') === 'true';
    if (justAuthenticated) {
      // Clear the flag
      localStorage.removeItem('justAuthenticated');

      // Show success message
      toast.success('Successfully authenticated with Google Merchant!');
    }

    fetchStatus();

    // Check if we have authentication data in localStorage
    const authSuccess = localStorage.getItem('googleAuthSuccess') === 'true';
    if (authSuccess) {
      // Automatically refresh status to ensure we have the latest data
      setTimeout(() => {
        refreshStatus();

        // Check if we should start syncing after authentication
        const startSync = localStorage.getItem('startSyncAfterAuth') === 'true';
        if (startSync) {
          // Clear the flag
          localStorage.removeItem('startSyncAfterAuth');

          // Start syncing after a short delay to ensure status is refreshed
          setTimeout(() => {
            toast.success('Starting product sync...');
            syncAllProducts();
          }, 1500);
        }
      }, 1000);
    }

    // Add event listener for messages from the auth popup window
    const handleAuthMessage = (event: MessageEvent) => {
      // Only process messages with our expected format
      if (event.data && typeof event.data === 'object') {
        if (event.data.type === 'GOOGLE_MERCHANT_AUTH_SUCCESS') {
          console.log('Received authentication success message from popup');
          toast.success('Google authentication successful!');
          setAuthenticating(false);

          // Store the merchant ID if provided
          if (event.data.merchantId) {
            localStorage.setItem('googleMerchantId', event.data.merchantId);
          }

          // Set flag to start syncing
          localStorage.setItem('startSyncAfterAuth', 'true');

          // Refresh the status to show the new authentication state
          fetchStatus().then(() => {
            // After fetching status, check if we should start syncing
            const startSync = localStorage.getItem('startSyncAfterAuth') === 'true';
            if (startSync) {
              // Clear the flag
              localStorage.removeItem('startSyncAfterAuth');

              // Start syncing after a short delay
              setTimeout(() => {
                toast.success('Starting product sync...');
                syncAllProducts();
              }, 1000);
            }
          });
        } else if (event.data.type === 'GOOGLE_MERCHANT_AUTH_ERROR') {
          console.error('Authentication error:', event.data.error);

          // Display a more user-friendly error message
          let errorMessage = 'Authentication failed';

          if (event.data.error) {
            errorMessage = `Authentication failed: ${event.data.error}`;
          }

          toast.error(errorMessage, { duration: 5000 });
          setAuthenticating(false);
          setSyncing(false); // Reset syncing state if auth failed
        }
      }
    };

    // Add the event listener
    window.addEventListener('message', handleAuthMessage);

    // Clean up the event listener when the component unmounts
    return () => {
      window.removeEventListener('message', handleAuthMessage);
    };
  }, []);

  // Fetch Google Merchant status
  const fetchStatus = async () => {
    try {
      setLoading(true);

      // Check localStorage first
      try {
        const storedTokens = localStorage.getItem('google_merchant_tokens');
        const forceCheck = localStorage.getItem('forceAuthCheck') === 'true';

        if (storedTokens && !forceCheck) {
          try {
            const tokenData = JSON.parse(storedTokens);
            if (tokenData && typeof tokenData === 'object' && tokenData.merchant_id) {
              console.log('Using authentication data from localStorage');

              // Convert merchant_id to string to ensure it's a valid React child
              const merchantId = String(tokenData.merchant_id || '');

              // Update status with data from localStorage
              setStatus(prevStatus => ({
                ...prevStatus,
                authenticated: true,
                merchantId
              }));

              // Update timestamp to force UI refresh
              setLastUpdateTime(new Date());

              setLoading(false);

              // If we're syncing, we still need to get the sync status from the API
              if (syncing) {
                // Continue to API call to get sync status
              } else {
                return;
              }
            }
          } catch (parseError) {
            console.error('Error parsing stored tokens:', parseError);
            // Clear invalid data
            localStorage.removeItem('google_merchant_tokens');
          }
        }
      } catch (localStorageError) {
        console.error('Error accessing localStorage:', localStorageError);
      }

      // If localStorage check fails or force check is enabled, try the API
      console.log('Fetching authentication status from API...');
      const response = await fetch('/api/google/merchant');
      const data = await response.json();

      console.log('API status response:', data);

      if (data.authenticated) {
        // Store in localStorage for future use
        try {
          localStorage.setItem('googleAuthSuccess', 'true');
          // Store merchant ID if available
          if (data.merchantId) {
            localStorage.setItem('googleMerchantId', String(data.merchantId));
          }
        } catch (storageError) {
          console.error('Error storing auth data in localStorage:', storageError);
        }

        // Update status
        setStatus(data);

        // Update timestamp to force UI refresh
        setLastUpdateTime(new Date());

        // Calculate progress percentage if syncing
        if (data.syncStatus && data.syncStatus.total > 0) {
          const percent = Math.round((data.syncStatus.processed / data.syncStatus.total) * 100);
          setProgressPercent(percent);

          // Update document title with progress
          if (syncing) {
            document.title = `Google Merchant (${percent}%) - Autoflow`;
          }
        }
      } else {
        // No fallback to hardcoded values - just use the API response
        setStatus(data);
      }

      return data;
    } catch (error) {
      console.error('Error fetching Google Merchant status:', error);
      toast.error('Failed to fetch Google Merchant status');

      // No fallback to hardcoded values
    } finally {
      setLoading(false);
    }
  };

  // Start Google authentication
  const startAuthentication = async () => {
    try {
      setAuthenticating(true);

      // Clear any previous authentication data
      localStorage.removeItem('googleAuthSuccess');
      localStorage.removeItem('googleMerchantId');
      localStorage.removeItem('google_merchant_tokens');
      localStorage.removeItem('justAuthenticated');

      // Set flag indicating authentication is in progress
      localStorage.setItem('authInProgress', 'true');

      const response = await fetch('/api/google/auth');
      const data = await response.json();

      if (data.error) {
        toast.error(`Authentication error: ${data.error}`);
        setAuthenticating(false);
        return;
      }

      if (data.url) {
        // Open Google auth URL in a new window
        const authWindow = window.open(data.url, 'googleAuth', 'width=600,height=600');

        if (!authWindow) {
          toast.error('Popup blocked! Please allow popups for this site and try again.');
          setAuthenticating(false);
          return;
        }

        // Store authentication start time in localStorage
        localStorage.setItem('googleAuthStartTime', Date.now().toString());

        // Show immediate instructions to the user
        toast.success(
          'Google authentication window opened. After completing authentication, click "Refresh Status" below.',
          { duration: 10000 }
        );

        // Set a timeout to check status after a reasonable time for auth completion
        setTimeout(async () => {
          try {
            console.log('Checking authentication status after timeout...');
            const success = await checkAuthStatus();

            if (success) {
              toast.success('Authentication successful!');
            } else {
              // Show refresh button toast
              toast.success(
                'If your status hasn\'t updated, click the "Refresh Status" button below.',
                { duration: 8000 }
              );
            }
          } catch (error) {
            console.error('Error checking auth status after timeout:', error);
            toast.error('Error checking authentication status. Please try refreshing manually.');
          }
        }, 8000); // Wait 8 seconds before first check
      }
    } catch (error) {
      console.error('Error starting Google authentication:', error);
      toast.error('Failed to start Google authentication');
      setAuthenticating(false);
    }
  };

  // Manual refresh button handler
  const refreshStatus = async () => {
    try {
      // Check if authentication is in progress
      const authInProgress = localStorage.getItem('authInProgress') === 'true';
      if (authInProgress) {
        toast('Authentication is in progress. Please complete the authentication in the popup window.', {
          icon: '🔄',
          duration: 5000
        });
        return;
      }

      setLoading(true);
      toast.success('Refreshing authentication status...');

      // Force localStorage check by setting a flag
      localStorage.setItem('forceAuthCheck', 'true');

      // Wait a moment to ensure any pending operations complete
      await new Promise(resolve => setTimeout(resolve, 500));

      // Check auth status
      await fetchStatus();

      // Clear the force check flag
      localStorage.removeItem('forceAuthCheck');

      setLoading(false);
    } catch (error) {
      console.error('Error refreshing status:', error);
      toast.error('Failed to refresh status');
      setLoading(false);
    }
  };

  // Helper function to check authentication status
  const checkAuthStatus = async (pollInterval?: NodeJS.Timeout) => {
    try {
      const statusResponse = await fetch('/api/google/merchant');
      const statusData = await statusResponse.json();

      console.log('Auth status check result:', statusData);

      if (statusData.authenticated) {
        console.log('Authentication successful!');

        // Clear interval if provided
        if (pollInterval) {
          clearInterval(pollInterval);
        }

        setStatus(statusData);
        setAuthenticating(false);
        toast.success('Successfully authenticated with Google Merchant');

        // Store success in localStorage
        localStorage.setItem('googleAuthSuccess', 'true');
        if (statusData.merchantId) {
          localStorage.setItem('googleMerchantId', statusData.merchantId);
        }

        return true;
      } else {
        // Check localStorage as a fallback
        const storedTokens = localStorage.getItem('google_merchant_tokens');
        if (storedTokens) {
          try {
            const tokenData = JSON.parse(storedTokens);
            if (tokenData && tokenData.merchant_id) {
              console.log('Found authentication data in localStorage');

              // Update status with data from localStorage
              setStatus({
                authenticated: true,
                merchantId: tokenData.merchant_id
              });

              setAuthenticating(false);
              toast.success('Successfully authenticated with Google Merchant (from localStorage)');

              return true;
            }
          } catch (parseError) {
            console.error('Error parsing stored tokens:', parseError);
          }
        }
      }

      return false;
    } catch (error) {
      console.error('Error checking auth status:', error);
      return false;
    }
  };

  // Sync all products
  const syncAllProducts = async () => {
    try {
      // First check if we're authenticated
      const authCheckResponse = await fetch('/api/google/merchant');
      const authCheckData = await authCheckResponse.json();

      // If not authenticated, prompt for authentication
      if (!authCheckData.authenticated) {
        toast.error('Google Merchant authentication required before syncing', {
          duration: 5000
        });

        // Show a more user-friendly dialog
        const shouldAuthenticate = window.confirm(
          'Authentication with Google Merchant is required before syncing products.\n\n' +
          'Would you like to authenticate with Google Merchant now?\n\n' +
          'Click OK to open the authentication window, or Cancel to abort the sync.'
        );

        if (shouldAuthenticate) {
          try {
            await startAuthentication();
            // Show a message to the user about what to do next
            toast.success(
              'After completing authentication, please click the "Sync Products" button again.',
              { duration: 8000 }
            );
          } catch (authError) {
            console.error('Error starting authentication:', authError);
            toast.error('Failed to start authentication. Please try again.');
          }
          return; // Exit the function - user will need to click sync again after auth
        } else {
          toast('Sync cancelled. Authentication is required to sync products.', {
            icon: 'ℹ️',
            duration: 5000
          });
          return; // Exit if user cancels
        }
      }

      // Check if we have a valid access token
      if (!authCheckData.hasAccessToken) {
        toast.error('No valid access token found. Please re-authenticate with Google Merchant.', {
          duration: 5000
        });

        // Show a more user-friendly dialog
        const shouldAuthenticate = window.confirm(
          'Your Google Merchant access token is missing or expired.\n\n' +
          'Would you like to authenticate with Google Merchant now?\n\n' +
          'Click OK to open the authentication window, or Cancel to abort the sync.'
        );

        if (shouldAuthenticate) {
          try {
            await startAuthentication();
            // Show a message to the user about what to do next
            toast.success(
              'After completing authentication, please click the "Sync Products" button again.',
              { duration: 8000 }
            );
          } catch (authError) {
            console.error('Error starting authentication:', authError);
            toast.error('Failed to start authentication. Please try again.');
          }
          return; // Exit the function - user will need to click sync again after auth
        } else {
          toast('Sync cancelled. Authentication is required to sync products.', {
            icon: 'ℹ️',
            duration: 5000
          });
          return; // Exit if user cancels
        }
      }

      setSyncing(true);

      // Show initial toast
      toast.success('Starting product sync with Google Merchant...', {
        duration: 3000
      });

      // Make the API request to start syncing
      const response = await fetch('/api/google/merchant', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'sync_all'
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Get the syncId from the response
        const syncId = data.syncStatus?.syncId;
        console.log(`Sync started with ID: ${syncId}`);

        // Show progress toast
        toast.success('Product sync initiated. Refreshing status...', {
          duration: 3000
        });

        // Track the last status to detect changes
        let lastProcessed = 0;
        let lastSucceeded = 0;
        let lastFailed = 0;
        let noProgressCount = 0;
        let syncComplete = false;

        // Set up periodic status refresh to show sync progress
        const refreshInterval = setInterval(async () => {
          try {
            // Fetch the latest status
            await fetchStatus();

            // Skip if no sync status yet
            if (!status.syncStatus) return;

            const currentStatus = status.syncStatus;

            // Check if there's been progress since last check
            const hasProgress =
              currentStatus.processed > lastProcessed ||
              currentStatus.succeeded > lastSucceeded ||
              currentStatus.failed > lastFailed;

            if (!hasProgress) {
              noProgressCount++;
            } else {
              noProgressCount = 0;

              // Show progress toast if significant progress has been made
              if (currentStatus.processed > lastProcessed + 5) {
                const percentComplete = Math.round((currentStatus.processed / currentStatus.total) * 100);
                toast(`Sync progress: ${percentComplete}% complete (${currentStatus.processed}/${currentStatus.total})`, {
                  duration: 2000
                });
              }
            }

            // Update last status
            lastProcessed = currentStatus.processed;
            lastSucceeded = currentStatus.succeeded;
            lastFailed = currentStatus.failed;

            // Check if sync is complete
            if (currentStatus.processed === currentStatus.total &&
                currentStatus.total > 0 &&
                !syncComplete) {
              syncComplete = true;

              // Show completion message
              if (currentStatus.failed === 0) {
                toast.success(`Sync completed successfully! ${currentStatus.succeeded} products synced.`, {
                  duration: 5000
                });
              } else {
                toast(`Sync completed with ${currentStatus.failed} errors. ${currentStatus.succeeded} products synced successfully.`, {
                  icon: '⚠️',
                  duration: 5000
                });
              }

              setSyncing(false);
              clearInterval(refreshInterval);
            }

            // If no progress for 5 checks but not complete, show a message
            if (noProgressCount >= 5 &&
                currentStatus.processed < currentStatus.total &&
                !syncComplete) {
              toast(`Sync in progress: ${currentStatus.processed}/${currentStatus.total} products processed. This may take a while...`, {
                duration: 3000
              });
              noProgressCount = 0; // Reset counter
            }
          } catch (refreshError) {
            console.error('Error refreshing sync status:', refreshError);
          }
        }, 2000); // Check every 2 seconds

        // Set a timeout to stop checking after 5 minutes to prevent infinite loop
        setTimeout(() => {
          clearInterval(refreshInterval);
          if (syncing) {
            setSyncing(false);

            // Final status check
            fetchStatus().then(() => {
              if (status.syncStatus) {
                const currentStatus = status.syncStatus;
                const percentComplete = Math.round((currentStatus.processed / currentStatus.total) * 100);

                if (currentStatus.processed === currentStatus.total) {
                  toast.success(`Sync completed! ${currentStatus.succeeded} products synced successfully.`, {
                    duration: 5000
                  });
                } else {
                  toast(`Sync timeout reached. Current progress: ${percentComplete}% (${currentStatus.processed}/${currentStatus.total})`, {
                    duration: 5000
                  });
                }
              } else {
                toast('Sync may still be in progress. Check the status for updates.', {
                  duration: 5000
                });
              }
            });
          }
        }, 300000); // 5 minutes

        // Initial status refresh
        setTimeout(fetchStatus, 2000);
      } else {
        toast.error(data.error || 'Failed to sync products');
        setSyncing(false);
      }
    } catch (error) {
      console.error('Error syncing products:', error);
      toast.error('Failed to sync products');
      setSyncing(false);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <DashboardHeader
        heading={syncing ? `Google Merchant Integration (Syncing ${progressPercent}%)` : "Google Merchant Integration"}
        text="Sync your products with Google Merchant Center for Google Shopping ads"
        icon={<ShoppingCart className={`h-6 w-6 ${syncing ? 'text-blue-500' : ''}`} />}
      />

      {/* Progress indicator at the top when syncing */}
      {syncing && status.syncStatus && (
        <div className="mt-4 bg-blue-50 border border-blue-100 rounded-md p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <RefreshCw className="h-4 w-4 mr-2 text-blue-500 animate-spin" />
              <h3 className="font-medium text-blue-700">Syncing Products with Google Merchant</h3>
            </div>
            <div className="text-sm font-medium text-blue-700">
              {progressPercent}% Complete
            </div>
          </div>

          <div className="w-full bg-blue-100 rounded-full h-2.5">
            <div
              className="bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-in-out"
              style={{ width: `${progressPercent}%` }}
            ></div>
          </div>

          <div className="flex justify-between mt-2 text-sm text-blue-600">
            <div>Processed: {status.syncStatus.processed} of {status.syncStatus.total}</div>
            <div>
              Success: {status.syncStatus.succeeded} |
              Failed: {status.syncStatus.failed}
            </div>
          </div>
        </div>
      )}

      <div className="grid gap-6 mt-8">
        {/* Authentication Status */}
        <Card>
          <CardHeader>
            <CardTitle>Connection Status</CardTitle>
            <CardDescription>
              Connect your Google Merchant Center account to sync products
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-2">
                <Skeleton className="h-4 w-[250px]" />
                <Skeleton className="h-4 w-[200px]" />
              </div>
            ) : status.authenticated ? (
              <Alert className="bg-green-50 border-green-200">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <AlertTitle className="text-green-800">Connected to Google Merchant Center</AlertTitle>
                <AlertDescription className="text-green-700">
                  Your account is connected to Google Merchant Center ID: {status.merchantId}
                </AlertDescription>
              </Alert>
            ) : (
              <Alert className="bg-amber-50 border-amber-200">
                <AlertCircle className="h-4 w-4 text-amber-600" />
                <AlertTitle className="text-amber-800">Not Connected</AlertTitle>
                <AlertDescription className="text-amber-700">
                  Connect your Google Merchant Center account to start syncing products
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
          <CardFooter>
            {status.authenticated ? (
              <Button
                variant="outline"
                onClick={refreshStatus}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh Status
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button
                  onClick={startAuthentication}
                  disabled={authenticating}
                >
                  {authenticating ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Connecting...
                    </>
                  ) : (
                    <>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Connect to Google Merchant
                    </>
                  )}
                </Button>

                {/* Add refresh button for after authentication */}
                {authenticating && (
                  <Button
                    variant="outline"
                    onClick={refreshStatus}
                    disabled={loading}
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                    Refresh Status
                  </Button>
                )}
              </div>
            )}
          </CardFooter>
        </Card>

        {/* Sync Status */}
        {status.authenticated && (
          <Card>
            <CardHeader>
              <CardTitle>Product Sync</CardTitle>
              <CardDescription>
                Sync your products with Google Merchant Center
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {status.syncStatus ? (
                  <>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Last Sync</p>
                        <p className="text-sm text-gray-500">
                          {new Date(status.syncStatus.lastSync).toLocaleString()}
                        </p>
                      </div>
                      <Badge variant={status.syncStatus.failed > 0 ? 'destructive' : 'default'}>
                        {status.syncStatus.succeeded}/{status.syncStatus.total} Synced
                      </Badge>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="font-medium">Sync Progress</span>
                          <span className="font-medium">
                            {progressPercent}%
                            {syncing && <span className="ml-1 text-blue-500 animate-pulse">●</span>}
                          </span>
                        </div>
                        <div className="relative pt-1">
                          <div className="overflow-hidden h-4 text-xs flex rounded bg-gray-200">
                            <div
                              style={{
                                width: `${status.syncStatus && status.syncStatus.total > 0
                                  ? (status.syncStatus.succeeded / status.syncStatus.total) * 100
                                  : 0}%`
                              }}
                              className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500 ${syncing ? 'transition-all duration-500' : ''}`}
                            ></div>
                            <div
                              style={{
                                width: `${status.syncStatus && status.syncStatus.total > 0
                                  ? (status.syncStatus.failed / status.syncStatus.total) * 100
                                  : 0}%`
                              }}
                              className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-red-500 ${syncing ? 'transition-all duration-500' : ''}`}
                            ></div>
                            {syncing && status.syncStatus.processed < status.syncStatus.total && (
                              <div className="absolute top-0 left-0 h-full w-full opacity-20">
                                <div className="h-full bg-blue-500 animate-pulse"></div>
                              </div>
                            )}
                          </div>
                        </div>
                        {syncing && (
                          <div className="text-xs text-blue-600 mt-1 text-right">
                            Updating in real-time...
                          </div>
                        )}
                      </div>

                      <div className="grid grid-cols-3 gap-2 text-center">
                        <div className="bg-green-50 p-2 rounded-md">
                          <div className="text-xs text-green-600 font-medium">Successful</div>
                          <div className="text-lg font-bold text-green-700">{status.syncStatus.succeeded}</div>
                        </div>
                        <div className="bg-red-50 p-2 rounded-md">
                          <div className="text-xs text-red-600 font-medium">Failed</div>
                          <div className="text-lg font-bold text-red-700">{status.syncStatus.failed}</div>
                        </div>
                        <div className="bg-blue-50 p-2 rounded-md">
                          <div className="text-xs text-blue-600 font-medium">Remaining</div>
                          <div className="text-lg font-bold text-blue-700">{status.syncStatus.total - status.syncStatus.processed}</div>
                        </div>
                      </div>

                      <div className={`p-3 rounded-md ${syncing ? 'bg-blue-50 border border-blue-100' : 'bg-gray-50'}`}>
                        <div className="flex justify-between text-sm">
                          <span className="font-medium">Last Updated:</span>
                          <span className="flex items-center">
                            {lastUpdateTime.toLocaleTimeString()}
                            {syncing && <span className="ml-2 h-2 w-2 bg-blue-500 rounded-full animate-ping"></span>}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm mt-1">
                          <span className="font-medium">Total Parts:</span>
                          <span>{status.syncStatus.total}</span>
                        </div>
                        <div className="flex justify-between text-sm mt-1">
                          <span className="font-medium">Processed:</span>
                          <span className={syncing ? 'font-bold text-blue-600' : ''}>
                            {status.syncStatus.processed}
                            {syncing && status.syncStatus.processed < status.syncStatus.total && (
                              <span className="ml-1 text-xs text-blue-500">
                                ({((status.syncStatus.processed / status.syncStatus.total) * 100).toFixed(1)}%)
                              </span>
                            )}
                          </span>
                        </div>
                        {syncing && (
                          <div className="flex justify-between text-sm mt-1">
                            <span className="font-medium">Remaining:</span>
                            <span className="text-blue-600">
                              {status.syncStatus.total - status.syncStatus.processed}
                            </span>
                          </div>
                        )}
                        {syncing && status.syncStatus.processed > 0 && (
                          <div className="mt-2 text-xs text-blue-600 text-center">
                            Sync in progress - Please wait...
                          </div>
                        )}
                      </div>
                    </div>

                    {status.syncStatus.errors.length > 0 && (
                      <div className="mt-6">
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-sm font-medium text-red-600">Sync Errors ({status.syncStatus.errors.length})</p>
                          {status.syncStatus.errors.length > 3 && (
                            <span className="text-xs text-gray-500">Showing latest 3 of {status.syncStatus.errors.length} errors</span>
                          )}
                        </div>
                        <div className="max-h-60 overflow-y-auto bg-red-50 p-3 rounded-md border border-red-100">
                          {status.syncStatus.errors.slice(-3).map((error, index) => (
                            <div key={index} className="mb-3 pb-3 border-b border-red-100 last:border-0 last:mb-0 last:pb-0">
                              <div className="flex items-center mb-1">
                                <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
                                <span className="font-semibold text-red-700">Part ID: {error.partId}</span>
                              </div>
                              <div className="ml-4 text-sm text-red-600 whitespace-normal break-words">
                                {error.error.includes('Invalid price value') ? (
                                  <>
                                    <span className="font-medium">Missing price information.</span> This part cannot be synced to Google Merchant without a valid price.
                                  </>
                                ) : error.error.includes('No merchant ID') ? (
                                  <>
                                    <span className="font-medium">Authentication issue.</span> Please reconnect your Google Merchant account.
                                  </>
                                ) : (
                                  error.error
                                )}
                              </div>
                            </div>
                          ))}

                          {status.syncStatus.errors.length > 3 && (
                            <div className="mt-2 pt-2 border-t border-red-100 text-center">
                              <span className="text-xs text-red-600">
                                {status.syncStatus.errors.length - 3} more errors not shown
                              </span>
                            </div>
                          )}
                        </div>

                        <div className="mt-3 text-xs text-gray-500">
                          Common issues:
                          <ul className="list-disc ml-4 mt-1">
                            <li>Missing price information</li>
                            <li>Missing or invalid product images</li>
                            <li>Product title too long (max 150 characters)</li>
                          </ul>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertTitle>No sync data</AlertTitle>
                    <AlertDescription>
                      You haven't synced any products yet. Click the button below to start syncing.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <div className="flex flex-col space-y-2">
                <Button
                  onClick={syncAllProducts}
                  disabled={syncing}
                  className="w-full"
                >
                  {syncing ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Syncing Products...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Sync All Products
                    </>
                  )}
                </Button>

                {syncing && (
                  <p className="text-xs text-gray-500 text-center">
                    Syncing products with Google Merchant Center. This may take a few minutes.
                  </p>
                )}
              </div>
            </CardFooter>
          </Card>
        )}
      </div>
    </div>
  );
}
