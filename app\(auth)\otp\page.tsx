// app/otp/page.tsx

'use client';

import { useState, useEffect, Suspense } from 'react';
import { createClient } from '@/app/libs/supabase/client'; // Assuming this uses createBrowserClient from @supabase/ssr
import Button from '@/app/components/ui/inputs/Button';
import { useSearchParams, useRouter } from 'next/navigation';
import toast, { Toaster } from 'react-hot-toast';
import OTPInput from '@/app/components/ui/inputs/OTPInput';
import { setUserCookie, getUserCookie } from '@/app/utils/cookies'; // Assuming these work correctly

// Create a separate component to use useSearchParams
function OtpPageComponent() {
  const [otp, setOtp] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams.get('email');
  const supabase = createClient(); // Initialize Supabase client

  useEffect(() => {
    // Redirect to login if email is missing in query params
    if (!email) {
      toast.error('Email missing, redirecting to login.');
      router.push('/login');
    }
  }, [email, router]);

  const handleVerify = async (e: React.FormEvent<HTMLFormElement>) => { // Use React.FormEvent
    e.preventDefault();
    setLoading(true);
    toast.dismiss(); // Clear previous toasts

    if (email) {
      // 1. Verify OTP
      const { error: otpError } = await supabase.auth.verifyOtp({
        email,
        token: otp,
        type: 'email', // Make sure this matches the OTP type sent (e.g., 'email' or 'sms')
      });

      if (otpError) {
        console.error('OTP Verification Error:', otpError);
        toast.error(`OTP Verification Failed: ${otpError.message}`);
        setLoading(false);
        return;
      }

      // OTP verification successful
      console.log('OTP Verified successfully for email:', email);

      // 2. Get User Profile Data (using email as fallback, ideally get from session)
      // It might be slightly better to get the user ID from the session after verifyOtp succeeds
      const { data: sessionData, error: sessionError } = await supabase.auth.getUser();

      if (sessionError || !sessionData.user) {
           console.error('Error fetching user session after OTP verification:', sessionError);
           toast.error('Could not fetch user session.');
           setLoading(false);
           return;
       }

      const userId = sessionData.user.id;
      console.log('User ID from session:', userId);


      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('id, full_name')
        .eq('id', userId) // Query profile using authenticated user ID
        .single();

      if (userError) {
        console.error('Error fetching user profile data:', userError);
        toast.error('Error fetching your profile data.');
        setLoading(false);
        return;
      }

      if (userData) {
        console.log('User profile data fetched:', userData);
        let roleId: string | null = null; // Use null for clarity if not found
        let roleName: string | null = null;

        // 3. Get User Role ID
        // Use maybeSingle here if a user might not have a role assigned yet
        const { data: userRoleData, error: userRoleError } = await supabase
          .from('user_roles')
          .select('role_id')
          .eq('user_id', userData.id)
          .maybeSingle(); // Use maybeSingle if role assignment is optional

        if (userRoleError) {
          console.error('Error fetching user role assignment:', userRoleError);
          // Don't necessarily block login, proceed without role if needed
          toast.error('Error fetching user role assignment.');
        } else if (userRoleData && userRoleData.role_id) {
          roleId = userRoleData.role_id;
          console.log('User role ID found:', roleId);

          // 4. Get Role Name using the PostgreSQL Function (WORKAROUND)
          console.log('Attempting to fetch role name via RPC for ID:', roleId);

          const { data: rpcRoleName, error: rpcError } = await supabase
            .rpc('get_role_name_by_id', { input_role_id: roleId });

          console.log('RPC function result:', rpcRoleName); // This logs the direct result (the name or null)

          if (rpcError) {
            console.error('Error fetching role name via RPC:', rpcError);
            toast.error('Error fetching role details.');
            // Decide if login should proceed without role name
          } else if (rpcRoleName) {
            // The function returns the name directly if found
            roleName = rpcRoleName;
            console.log('User role name found via RPC:', roleName);
          } else {
             // This means the function ran but didn't find the role (unexpected if DB is consistent)
             console.warn(`Role name not found via RPC for ID: ${roleId}. The role might be missing or the function has an issue.`);
             toast('Could not fully determine role details.');
          }
        } else {
             console.warn(`No role assignment found for user ID: ${userData.id}`);
             // User might not have a role assigned, which could be valid
        }

        // 5. Prepare and Set Cookie
        if (!roleId || !roleName) {
          console.warn('Could not determine complete role information (ID or Name missing).');
          // Decide if you want to proceed without full role info in cookie
        }

        const userDataForCookie = {
          id: userData.id,
          name: userData.full_name || '',
          roleId: roleId || '', // Ensure string format
          roleName: roleName || '', // Ensure string format
        };

        console.log('User data being saved to cookie:', userDataForCookie);

        try {
            await setUserCookie(userDataForCookie);
            console.log('Cookie potentially set.');

            // Optional: Verify cookie immediately after setting
            const verifiedCookie = await getUserCookie();
            console.log('Verified cookie after setting:', verifiedCookie);

        } catch(cookieError) {
             console.error("Error setting user cookie:", cookieError);
             toast.error("Failed to save session. Please try again.");
             setLoading(false);
             return; // Stop if cookie fails
        }


        // 6. Success and Redirect
        toast.success('You are now logged in!');
        router.push('/dashboard');
        router.refresh(); // Ensures server components re-render with new auth state

      } else {
           // This case should ideally not happen if user was fetched by ID after auth
           console.error('User profile data was unexpectedly null after fetch.');
           toast.error('Could not retrieve profile information.');
      }
    } // end if(email)

    setLoading(false);
  };

  // Render the OTP form
  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-2 bg-gray-50">
      <main className="flex w-full flex-1 flex-col items-center justify-center px-4 sm:px-20 text-center">
        <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
          <h1 className="text-2xl md:text-4xl font-bold mb-2">Enter OTP</h1>
          <p className="mb-6 text-gray-600">
            We sent a verification code to{' '}
            <span className="font-semibold text-gray-800 break-all">{email || 'your email'}</span>
          </p>

          <form onSubmit={handleVerify} className="w-full">
            <div className="flex justify-center mb-6">
              <OTPInput
                numInputs={6}
                value={otp}
                onChange={setOtp}
                disabled={loading}
              />
            </div>
            <Button type="submit" disabled={loading || otp.length !== 6} className="w-full">
              {loading ? 'Verifying...' : 'Verify'}
            </Button>
          </form>
        </div>
      </main>
      <Toaster position="top-center" />
    </div>
  );
}

// Wrap OtpPageComponent with Suspense as required by useSearchParams
export default function OtpPage() {
  return (
    <Suspense fallback={<div className="flex justify-center items-center min-h-screen">Loading...</div>}>
      <OtpPageComponent />
    </Suspense>
  );
}