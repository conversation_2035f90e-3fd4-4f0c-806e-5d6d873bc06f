// Types for the Storage Dashboard

export interface StorageArea {
  area_id: number;
  name: string;
  location_type: 'indoor' | 'outdoor';
  level: 'upstairs' | 'downstairs' | 'ground';
  description?: string;
  created_at?: string;
  updated_at?: string;
}

export interface StorageUnit {
  unit_id: number;
  area_id: number;
  unit_type: 'shelf' | 'cage' | 'hanging_line' | 'open_space' | 'engine_area';
  identifier: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
  // For UI display
  area_name?: string;
}

export interface PartLocation {
  location_id: number;
  part_id: number;
  unit_id: number;
  quantity: number;
  location_subtype: 'crate' | 'container' | 'shelf_section' | 'open_shelf' | 'cage_section' | 'hanging_point' | 'open_area_spot';
  details?: Record<string, any>;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface StorageAreaFormData {
  name: string;
  location_type: 'indoor' | 'outdoor';
  level: 'upstairs' | 'downstairs' | 'ground';
  description?: string;
}

export interface StorageUnitFormData {
  area_id: number;
  unit_type: 'shelf' | 'cage' | 'hanging_line' | 'open_space' | 'engine_area';
  identifier: string;
  description?: string;
}

export interface StorageStats {
  totalAreas: number;
  totalUnits: number;
  indoorAreas: number;
  outdoorAreas: number;
  shelfUnits: number;
  cageUnits: number;
  hangingLineUnits: number;
  openSpaceUnits: number;
  engineAreaUnits: number;
  partsStored: number;
}

export interface StorageAreaWithUnits extends StorageArea {
  units: StorageUnit[];
  isExpanded?: boolean;
}
