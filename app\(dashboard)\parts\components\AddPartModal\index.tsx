// app/(dashboard)/parts/add/components/AddPartModal/index.tsx
'use client';

import React from 'react';
import Modal from '@/app/components/ui/Modal';
import Icon from '@/app/components/ui/Icon';
import { PartsForm } from '../../PartsForm';
import { useForm, UseFormSetValue } from 'react-hook-form';
import { useCategories } from '../../hooks/useCategories';
import { PartFormValues } from '../../types';

interface AddPartModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess: () => void;
}

const AddPartModal: React.FC<AddPartModalProps> = ({ isOpen, onClose, onSuccess }) => {
    const {
        control,
        setValue,
        watch,
        getValues,
        formState,
        reset
    } = useForm<PartFormValues>({
        defaultValues: {
            stock: 0,
            price: 0,
            condition: 'Used',
            categoryId: '',
            partNumber: '',
            imageUrl: '',
            imageType: 'upload',
            images: [], // Add the images array
            trimId: '',
            generationId: '',
            variationId: '',
            brandId: '',
            modelId: '',
            attributes: {},
            requirePartNumber: false,
            isCheckingPartNumber: false,
            categoryAttributes: []
        }
    });

    const {
        flatCategories,
        isLoading: isLoadingCategories,
        error: categoryError
    } = useCategories({
        setValue: setValue as UseFormSetValue<PartFormValues>,
        watch,
        getValues
    });

    // Reset form when modal is closed or after success
    React.useEffect(() => {
        if (!isOpen) {
            // Complete form reset with a clean state
            reset({
                stock: 0,
                price: 0,
                condition: 'Used',
                categoryId: '',
                partNumber: '',
                imageUrl: '',
                imageType: 'upload',
                images: [], // Add the images array
                trimId: '',
                generationId: '',
                variationId: '',
                brandId: '',
                modelId: '',
                attributes: {},
                requirePartNumber: false,
                isCheckingPartNumber: false,
                categoryAttributes: [],
                showVehicleSelection: false
            });
        }
    }, [isOpen, reset]);

    // Added debugging
    React.useEffect(() => {
        console.log('AddPartModal: Categories data', {
            count: flatCategories?.length || 0,
            isLoading: isLoadingCategories,
            error: categoryError
        });
    }, [flatCategories, isLoadingCategories, categoryError]);

    // Debug isOpen prop changes
    React.useEffect(() => {
        console.log('AddPartModal: isOpen prop changed to', isOpen);
    }, [isOpen]);

    const handleSuccess = () => {
        reset();
        onSuccess();
    };

    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            width="w-full md:max-w-2xl"
            animationType="slide-in-bottom"
            header={
                <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200">
                    <h2 className="text-lg font-semibold">Add New Car Part</h2>
                    <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
                        <Icon name="x" size={20} />
                    </button>
                </div>
            }
        >
            <div className="p-6">
                {categoryError && (
                    <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
                        Category Error: {categoryError}
                    </div>
                )}

                <PartsForm
                    onClose={onClose}
                    onSuccess={handleSuccess}
                    flatCategories={flatCategories || []}
                    isLoadingCategories={isLoadingCategories}
                    initialData={{}}
                />
            </div>
        </Modal>
    );
};

export default AddPartModal;