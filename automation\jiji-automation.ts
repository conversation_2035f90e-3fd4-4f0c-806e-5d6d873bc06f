import { chrom<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>er<PERSON>ontext } from 'playwright';
import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

// Import the frontend pricing algorithm
import { getAdjustedPrice } from './priceAdjustment';

// Load environment variables from automation directory first, then parent
// When compiled, __dirname points to dist/, so we need to go up one level
const automationDir = __dirname.includes('dist') ? path.join(__dirname, '..') : __dirname;
dotenv.config({ path: path.join(automationDir, '.env') });
dotenv.config({ path: path.join(automationDir, '../.env.local') });

// Types
interface PartData {
  id: number;
  title: string;
  description: string;
  price: number;
  condition: string;
  category_id: number;
  category_name?: string;
  parent_category_name?: string;
  make?: string;
  images: PartImage[];
  partnumber_group?: number;
  createdAt?: string;
  updatedAt?: string;
}

interface PartImage {
  id: number;
  part_id: number;
  image_url: string;
  is_main_image: boolean;
}

interface JijiListingStatus {
  id?: number;
  part_id: number;
  jiji_listing_id?: string;
  listing_url?: string;
  status: 'pending' | 'listed' | 'failed' | 'updated';
  error_message?: string;
  listed_at?: string;
  updated_at?: string;
}

interface AutomationConfig {
  headless: boolean;
  batchSize: number;
  delayBetweenActions: number;
  maxRetries: number;
  skipExisting: boolean;
  testMode: boolean;
}

class JijiAutomation {
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private page: Page | null = null;
  private supabase: any;
  private config: AutomationConfig;
  private logFile: string;

  constructor(config: Partial<AutomationConfig> = {}) {
    this.config = {
      headless: false, // Set to false for debugging
      batchSize: 5,
      delayBetweenActions: 2000,
      maxRetries: 3,
      skipExisting: true,
      testMode: false,
      ...config
    };

    // Initialize Supabase client
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    // Setup logging
    this.logFile = path.join(__dirname, 'logs', `jiji-automation-${new Date().toISOString().split('T')[0]}.log`);
    this.ensureLogDirectory();
  }

  private ensureLogDirectory(): void {
    const logDir = path.dirname(this.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  private log(message: string, level: 'info' | 'error' | 'warn' = 'info'): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;

    console.log(logMessage);
    fs.appendFileSync(this.logFile, logMessage + '\n');
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async initialize(): Promise<void> {
    try {
      this.log('Initializing browser...');

      this.browser = await chromium.launch({
        headless: this.config.headless,
        slowMo: 100, // Slow down actions for better reliability
      });

      this.context = await this.browser.newContext({
        viewport: { width: 1280, height: 720 },
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      });

      this.page = await this.context.newPage();

      // Set up request interception for better performance
      await this.page.route('**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}', route => {
        if (this.config.headless) {
          route.abort(); // Skip images and CSS in headless mode for speed
        } else {
          route.continue();
        }
      });

      this.log('Browser initialized successfully');
    } catch (error) {
      this.log(`Failed to initialize browser: ${error}`, 'error');
      throw error;
    }
  }

  async login(email: string, password: string): Promise<boolean> {
    try {
      this.log('Navigating to Jiji.co.ke...');
      try {
        await this.page!.goto('https://jiji.co.ke', {
          waitUntil: 'domcontentloaded', // Less strict than networkidle
          timeout: 60000 // Increase timeout to 60 seconds
        });
      } catch (error) {
        this.log('Initial navigation failed, trying with load event...', 'warn');
        await this.page!.goto('https://jiji.co.ke', {
          waitUntil: 'load',
          timeout: 60000
        });
      }

      // Step 1: Look for "Sign in" link in top right
      this.log('Looking for Sign in link in top right...');

      // Optimized wait for page load
      await this.delay(1000);

      const signInSelectors = [
        'text="Sign in"',
        'a:has-text("Sign in")',
        'button:has-text("Sign in")',
        'text="Log in"',
        'text="Login"',
        'a[href*="signin"]',
        'a[href*="login"]'
      ];

      let signInClicked = false;
      for (const selector of signInSelectors) {
        try {
          await this.page!.waitForSelector(selector, { timeout: 2000 });
          await this.page!.click(selector);
          signInClicked = true;
          this.log(`Clicked Sign in using selector: ${selector}`);
          break;
        } catch (e) {
          continue;
        }
      }

      if (!signInClicked) {
        // Debug: Let's see what's actually on the page
        this.log('Could not find Sign in link. Debugging page content...', 'warn');

        try {
          // Look for any links or buttons that might be login-related
          const allLinks = await this.page!.$$eval('a', links =>
            links.map(link => ({ text: link.textContent?.trim(), href: link.href }))
              .filter(link => link.text && link.text.length > 0)
              .slice(0, 20) // First 20 links
          );

          this.log(`Found links: ${JSON.stringify(allLinks)}`, 'warn');

          const allButtons = await this.page!.$$eval('button', buttons =>
            buttons.map(btn => btn.textContent?.trim())
              .filter(text => text && text.length > 0)
              .slice(0, 10) // First 10 buttons
          );

          this.log(`Found buttons: ${JSON.stringify(allButtons)}`, 'warn');
        } catch (debugError) {
          this.log(`Debug error: ${debugError}`, 'warn');
        }

        throw new Error('Could not find Sign in link');
      }

      await this.delay(500);

      // Step 2: Look for "Email or phone" button in the popup
      this.log('Looking for "Email or phone" button in popup...');
      const emailPhoneSelectors = [
        'text="Email or phone"',
        'button:has-text("Email or phone")',
        'button:has-text("Phone")',
        'button:has-text("Email")'
      ];

      let emailPhoneClicked = false;
      for (const selector of emailPhoneSelectors) {
        try {
          await this.page!.waitForSelector(selector, { timeout: 3000 });
          await this.page!.click(selector);
          emailPhoneClicked = true;
          this.log(`Clicked "Email or phone" using selector: ${selector}`);
          break;
        } catch (e) {
          continue;
        }
      }

      if (!emailPhoneClicked) {
        this.log('Could not find "Email or phone" button, trying to proceed with login form...', 'warn');
      }

      await this.delay(500);

      // Step 3: Wait for login form and fill credentials
      this.log('Waiting for login form...');
      const emailInputSelectors = [
        '#emailOrPhone', // Specific ID for Jiji
        'input[type="email"]',
        'input[type="tel"]', // Phone input
        'input[name="email"]',
        'input[name="phone"]',
        'input[name="username"]',
        'input[placeholder*="email" i]',
        'input[placeholder*="Email" i]',
        'input[placeholder*="phone" i]',
        'input[placeholder*="Phone" i]',
        'input[placeholder*="number" i]',
        'input[id="email"]',
        'input[id="phone"]',
        'input[id="username"]',
        'input[type="text"]' // Generic text input as fallback
      ];

      let emailFilled = false;
      for (const selector of emailInputSelectors) {
        try {
          await this.page!.waitForSelector(selector, { timeout: 3000 });
          await this.page!.fill(selector, email);
          emailFilled = true;
          this.log(`Filled email using selector: ${selector}`);
          break;
        } catch (e) {
          continue;
        }
      }

      if (!emailFilled) {
        throw new Error('Could not find email input field');
      }

      await this.delay(300);

      // Fill password
      this.log('Filling password...');
      const passwordInputSelectors = [
        '#password', // Specific ID for Jiji
        'input[type="password"]',
        'input[name="password"]',
        'input[placeholder*="password" i]'
      ];

      let passwordFilled = false;
      for (const selector of passwordInputSelectors) {
        try {
          await this.page!.waitForSelector(selector, { timeout: 2000 });
          await this.page!.fill(selector, password);
          passwordFilled = true;
          this.log(`Filled password using selector: ${selector}`);
          break;
        } catch (e) {
          continue;
        }
      }

      if (!passwordFilled) {
        throw new Error('Could not find password input field');
      }

      await this.delay(300);

      // Step 4: Submit login form
      this.log('Submitting login form...');
      const submitSelectors = [
        'button[type="submit"]',
        'input[type="submit"]',
        'button:has-text("Sign in")',
        'button:has-text("Login")',
        'button:has-text("Log in")',
        '[data-testid="submit"]',
        '.submit-btn',
        '.login-btn'
      ];

      let submitted = false;
      for (const selector of submitSelectors) {
        try {
          await this.page!.waitForSelector(selector, { timeout: 3000 });
          await this.page!.click(selector);
          submitted = true;
          this.log(`Submitted form using selector: ${selector}`);
          break;
        } catch (e) {
          continue;
        }
      }

      if (!submitted) {
        // Try pressing Enter as fallback
        this.log('Could not find submit button, trying Enter key...');
        await this.page!.keyboard.press('Enter');
      }

      // Step 5: Wait for successful login (optimized)
      this.log('Waiting for login confirmation...');
      await this.delay(1000);

      // Check for successful login indicators
      const successSelectors = [
        'text="My Account"',
        'text="Profile"',
        'text="Logout"',
        'text="Sign out"',
        '.user-menu',
        '.profile-menu'
      ];

      let loginSuccess = false;
      for (const selector of successSelectors) {
        try {
          await this.page!.waitForSelector(selector, { timeout: 3000 });
          loginSuccess = true;
          this.log(`Login confirmed with selector: ${selector}`);
          break;
        } catch (e) {
          continue;
        }
      }

      // Alternative check: see if we're redirected away from login page
      if (!loginSuccess) {
        const currentUrl = this.page!.url();
        if (!currentUrl.includes('login') && !currentUrl.includes('signin') && currentUrl.includes('jiji.co.ke')) {
          loginSuccess = true;
          this.log('Login appears successful (redirected from login page)');
        }
      }

      if (loginSuccess) {
        this.log('Login successful!');
        return true;
      } else {
        throw new Error('Could not confirm successful login');
      }

    } catch (error) {
      this.log(`Login failed: ${error}`, 'error');

      // Take a screenshot for debugging if not in headless mode
      if (!this.config.headless) {
        try {
          await this.page!.screenshot({ path: 'automation/logs/login-error.png' });
          this.log('Screenshot saved to automation/logs/login-error.png for debugging');
        } catch (screenshotError) {
          this.log(`Could not save screenshot: ${screenshotError}`, 'warn');
        }
      }

      return false;
    }
  }

  async navigateToPostAd(): Promise<boolean> {
    try {
      this.log('🚀 STARTING navigateToPostAd() - This should include multiselect handling...');
      this.log('Looking for "Sell" button in top right corner...');

      // Optimized selectors for faster detection
      const sellSelectors = [
        'text="Sell"',
        'a:has-text("Sell")',
        'button:has-text("Sell")',
        'a[href*="sell"]'
      ];

      let clicked = false;
      for (const selector of sellSelectors) {
        try {
          // Reduced timeout for faster execution
          await this.page!.waitForSelector(selector, { timeout: 2000 });
          await this.page!.click(selector);
          clicked = true;
          this.log(`Clicked "Sell" button using selector: ${selector}`);
          break;
        } catch (e) {
          continue;
        }
      }

      if (!clicked) {
        // Debug: Let's see what's actually available
        this.log('Could not find "Sell" button. Debugging page content...', 'warn');

        try {
          // Look for any buttons or links that might be sell-related
          const allLinks = await this.page!.$$eval('a', links =>
            links.map(link => ({ text: link.textContent?.trim(), href: link.href }))
              .filter(link => link.text && link.text.length > 0)
              .slice(0, 20) // First 20 links
          );

          this.log(`Found links: ${JSON.stringify(allLinks)}`, 'warn');

          const allButtons = await this.page!.$$eval('button', buttons =>
            buttons.map(btn => btn.textContent?.trim())
              .filter(text => text && text.length > 0)
              .slice(0, 10) // First 10 buttons
          );

          this.log(`Found buttons: ${JSON.stringify(allButtons)}`, 'warn');
        } catch (debugError) {
          this.log(`Debug error: ${debugError}`, 'warn');
        }

        throw new Error('Could not find "Sell" button in top right corner');
      }

      await this.delay(500); // Optimized for faster execution

      // Click on multiselect dropdown after clicking sell button
      this.log('🎯 Looking for multiselect dropdown after clicking sell button...');
      try {
        // Wait for the multiselect component to be available
        await this.page!.waitForSelector('.multiselect.qa-multiselect', { timeout: 5000 });

        // Use robust targeting strategies to open the dropdown
        this.log('Using robust targeting strategies to open category dropdown...');
        let dropdownOpened = false;

        // Strategy 1: Using the 'qa-choose-category' data-qa attribute (most robust)
        try {
          this.log('Strategy 1: Clicking .qa-choose-category...');
          await this.page!.click('.qa-choose-category');
          this.log('Successfully clicked .qa-choose-category');
          await this.delay(1000);

          const isVisible = await this.page!.isVisible('.multiselect__content-wrapper');
          if (isVisible) {
            dropdownOpened = true;
            this.log('✅ Multiselect dropdown opened via .qa-choose-category');
          }
        } catch (e) {
          this.log(`Strategy 1 failed: ${e}`, 'warn');
        }

        // Strategy 2: Using the 'label' text content
        if (!dropdownOpened) {
          try {
            this.log('Strategy 2: Clicking label with "Category*"...');
            await this.page!.click('label:has-text("Category*")');
            this.log('Successfully clicked Category* label');
            await this.delay(1000);

            const isVisible = await this.page!.isVisible('.multiselect__content-wrapper');
            if (isVisible) {
              dropdownOpened = true;
              this.log('✅ Multiselect dropdown opened via Category* label');
            }
          } catch (e) {
            this.log(`Strategy 2 failed: ${e}`, 'warn');
          }
        }

        // Strategy 3: Using combination of class names
        if (!dropdownOpened) {
          try {
            this.log('Strategy 3: Clicking .fw-select.qa-fw-select.b-category-section__category-select...');
            await this.page!.click('.fw-select.qa-fw-select.b-category-section__category-select');
            this.log('Successfully clicked fw-select combination');
            await this.delay(1000);

            const isVisible = await this.page!.isVisible('.multiselect__content-wrapper');
            if (isVisible) {
              dropdownOpened = true;
              this.log('✅ Multiselect dropdown opened via fw-select combination');
            }
          } catch (e) {
            this.log(`Strategy 3 failed: ${e}`, 'warn');
          }
        }

        // Strategy 4: Original .multiselect__select approach as fallback
        if (!dropdownOpened) {
          try {
            this.log('Strategy 4: Clicking .multiselect__select (fallback)...');
            await this.page!.click('.multiselect__select');
            this.log('Successfully clicked .multiselect__select');
            await this.delay(1000);

            const isVisible = await this.page!.isVisible('.multiselect__content-wrapper');
            if (isVisible) {
              dropdownOpened = true;
              this.log('✅ Multiselect dropdown opened via .multiselect__select');
            }
          } catch (e) {
            this.log(`Strategy 4 failed: ${e}`, 'warn');
          }
        }

        if (!dropdownOpened) {
          // Try JavaScript approach using robust selectors
          this.log('Trying JavaScript approach with robust selectors...');
          await this.page!.evaluate(() => {
            // Try qa-choose-category first
            const qaCategory = document.querySelector('.qa-choose-category') as HTMLElement;
            if (qaCategory) {
              qaCategory.click();
              qaCategory.dispatchEvent(new Event('click', { bubbles: true }));
              return;
            }

            // Try fw-select combination
            const fwSelect = document.querySelector('.fw-select.qa-fw-select.b-category-section__category-select') as HTMLElement;
            if (fwSelect) {
              fwSelect.click();
              fwSelect.dispatchEvent(new Event('click', { bubbles: true }));
              return;
            }

            // Fallback to multiselect__select
            const selectDiv = document.querySelector('.multiselect__select') as HTMLElement;
            if (selectDiv) {
              selectDiv.click();
              selectDiv.dispatchEvent(new Event('click', { bubbles: true }));
            }
          });
          await this.delay(1500);
          this.log('Attempted to open multiselect dropdown via JavaScript with robust selectors');

          // Check if it opened after JavaScript attempt
          const isNowVisible = await this.page!.isVisible('.multiselect__content-wrapper');
          if (isNowVisible) {
            dropdownOpened = true;
            this.log('✅ Multiselect dropdown opened via JavaScript');
          }
        }

        // Now try to select "Vehicle Parts & Accessories" or "Vehicles" category
        this.log('🎯 About to call selectVehiclePartsFromMultiselect()...');
        try {
          const categorySelected = await this.selectVehiclePartsFromMultiselect();
          if (!categorySelected) {
            this.log('❌ Multiselect method returned false, trying old dropdown fallback method...', 'warn');
            // Try the old dropdown method as fallback
            const fallbackSuccess = await this.selectVehiclePartsCategory();
            if (!fallbackSuccess) {
              this.log('❌ Both multiselect and dropdown methods failed', 'error');
            }
          } else {
            this.log('✅ Successfully selected category via multiselect');
          }
        } catch (multiselectError) {
          this.log(`❌ Multiselect method threw error: ${multiselectError}`, 'error');
          this.log('🔄 Trying old dropdown fallback method...', 'warn');
          // Try the old dropdown method as fallback
          const fallbackSuccess = await this.selectVehiclePartsCategory();
          if (!fallbackSuccess) {
            this.log('❌ Both multiselect and dropdown methods failed', 'error');
          }
        }

      } catch (error) {
        this.log(`❌ Could not find or click multiselect dropdown: ${error}`, 'warn');
        this.log('🔄 Category selection will be handled by fallback methods above');
      }

      // Wait for the post ad form or category selection page to load
      this.log('Waiting for category selection or post form...');

      const pageLoadSelectors = [
        '.category-selection',
        '.post-form',
        '[data-testid="category-select"]',
        'text="Select a category"',
        'text="Choose category"',
        'text="Category"',
        '.categories',
        '.category-list'
      ];

      let pageLoaded = false;
      for (const selector of pageLoadSelectors) {
        try {
          await this.page!.waitForSelector(selector, { timeout: 5000 });
          pageLoaded = true;
          this.log(`Post ad page loaded, found: ${selector}`);
          break;
        } catch (e) {
          continue;
        }
      }

      if (!pageLoaded) {
        this.log('Could not confirm post ad page loaded, but continuing...', 'warn');
      }

      this.log('Successfully navigated to Post Ad section');
      return true;
    } catch (error) {
      this.log(`Failed to navigate to Post Ad: ${error}`, 'error');

      // Take a screenshot for debugging if not in headless mode
      if (!this.config.headless) {
        try {
          await this.page!.screenshot({ path: 'automation/logs/post-ad-error.png' });
          this.log('Screenshot saved to automation/logs/post-ad-error.png for debugging');
        } catch (screenshotError) {
          this.log(`Could not save screenshot: ${screenshotError}`, 'warn');
        }
      }

      return false;
    }
  }

  async selectVehiclePartsFromMultiselect(): Promise<boolean> {
    try {
      this.log('🎯 MULTISELECT METHOD CALLED: selectVehiclePartsFromMultiselect()');
      this.log('🔍 Starting multiselect category selection for "Vehicle Parts & Accessories"...');

      // First check if multiselect exists
      const multiselectExists = await this.page!.$('.multiselect.qa-multiselect');
      if (!multiselectExists) {
        this.log('❌ No multiselect found on page, returning false');
        return false;
      }
      this.log('✅ Multiselect component found on page');

      // Check if "Vehicle Parts & Accessories" is already selected
      const alreadySelected = await this.page!.evaluate(() => {
        const selectedSpan = document.querySelector('.multiselect__single');
        const selectedText = selectedSpan?.textContent?.trim();
        console.log('Current multiselect selection:', selectedText);
        return selectedText === 'Vehicle Parts & Accessories';
      });

      if (alreadySelected) {
        this.log('✅ "Vehicle Parts & Accessories" is already selected');
        return true;
      }

      this.log('🔄 Category not selected yet, proceeding to select it...');

      // First, check if the dropdown is open and has options
      const isDropdownOpen = await this.page!.isVisible('.multiselect__content-wrapper');
      this.log(`Multiselect dropdown open status: ${isDropdownOpen}`);

      if (!isDropdownOpen) {
        this.log('Multiselect dropdown is not open, trying robust strategies to open it...');

        let opened = false;

        // Strategy 1: qa-choose-category (most robust)
        try {
          await this.page!.click('.qa-choose-category');
          await this.delay(1000);
          if (await this.page!.isVisible('.multiselect__content-wrapper')) {
            opened = true;
            this.log('✅ Opened dropdown via .qa-choose-category');
          }
        } catch (e) {
          this.log('qa-choose-category strategy failed', 'warn');
        }

        // Strategy 2: fw-select combination
        if (!opened) {
          try {
            await this.page!.click('.fw-select.qa-fw-select.b-category-section__category-select');
            await this.delay(1000);
            if (await this.page!.isVisible('.multiselect__content-wrapper')) {
              opened = true;
              this.log('✅ Opened dropdown via fw-select combination');
            }
          } catch (e) {
            this.log('fw-select combination strategy failed', 'warn');
          }
        }

        // Strategy 3: multiselect__select fallback
        if (!opened) {
          try {
            await this.page!.click('.multiselect__select');
            await this.delay(1000);
            if (await this.page!.isVisible('.multiselect__content-wrapper')) {
              opened = true;
              this.log('✅ Opened dropdown via .multiselect__select');
            }
          } catch (e) {
            this.log('multiselect__select strategy failed', 'warn');
          }
        }

        if (!opened) {
          this.log('Could not open multiselect dropdown with any strategy', 'warn');
          return false;
        }
      }

      // Use the search functionality to find "Vehicle Parts & Accessories"
      this.log('Using search functionality to find "Vehicle Parts & Accessories"...');

      // Find and focus the search input
      const searchInput = await this.page!.waitForSelector('.multiselect__input', { timeout: 5000 });
      if (!searchInput) {
        this.log('Could not find multiselect search input', 'warn');
        return false;
      }

      // Clear any existing text and type the search term
      await searchInput.click();
      await searchInput.fill(''); // Clear existing text
      await this.delay(500);

      this.log('Typing "Vehicle Parts & Accessories" in search input...');
      await searchInput.type('Vehicle Parts & Accessories', { delay: 100 });
      await this.delay(1500); // Wait for search results to load

      // Look for the matching option in search results
      this.log('Looking for matching search result...');
      const categorySelected = await this.page!.evaluate(() => {
        // Look for the exact match in search results
        const options = Array.from(document.querySelectorAll('.multiselect__option .fw-select__option-label')) as Element[];
        for (const option of options) {
          const text = option.textContent?.trim();
          if (text === 'Vehicle Parts & Accessories') {
            const clickableElement = option.closest('.multiselect__option') as HTMLElement;
            if (clickableElement) {
              clickableElement.click();
              return { success: true, text: text };
            }
          }
        }

        // Fallback: look for partial matches
        for (const option of options) {
          const text = option.textContent?.trim();
          if (text && (text.includes('Vehicle Parts') || text.includes('Parts & Accessories'))) {
            const clickableElement = option.closest('.multiselect__option') as HTMLElement;
            if (clickableElement) {
              clickableElement.click();
              return { success: true, text: text };
            }
          }
        }

        return { success: false, text: null };
      });

      if (categorySelected.success) {
        this.log(`✅ Successfully selected category: ${categorySelected.text}`);
        await this.delay(1000);

        // Verify the final selection
        const finalSelection = await this.page!.evaluate(() => {
          const selectedSpan = document.querySelector('.multiselect__single');
          return selectedSpan?.textContent?.trim();
        });

        this.log(`Final category selection: ${finalSelection}`);
        return finalSelection === 'Vehicle Parts & Accessories' || (finalSelection?.includes('Vehicle Parts') ?? false);
      } else {
        this.log('Could not find "Vehicle Parts & Accessories" in search results', 'warn');

        // Try alternative search terms
        this.log('Trying alternative search term "Vehicles"...');
        await searchInput.fill('');
        await this.delay(300);
        await searchInput.type('Vehicles', { delay: 100 });
        await this.delay(1500);

        const fallbackSelected = await this.page!.evaluate(() => {
          const options = Array.from(document.querySelectorAll('.multiselect__option .fw-select__option-label')) as Element[];
          for (const option of options) {
            const text = option.textContent?.trim();
            if (text === 'Vehicles') {
              const clickableElement = option.closest('.multiselect__option') as HTMLElement;
              if (clickableElement) {
                clickableElement.click();
                return { success: true, text: text };
              }
            }
          }
          return { success: false, text: null };
        });

        if (fallbackSelected.success) {
          this.log(`✅ Selected fallback category: ${fallbackSelected.text}`);
          await this.delay(1000);
          return true;
        }

        return false;
      }

    } catch (error) {
      this.log(`Error selecting from multiselect dropdown: ${error}`, 'error');
      return false;
    }
  }

  async selectVehiclePartsCategory(part?: PartData): Promise<boolean> {
    try {
      this.log('🚨 OLD METHOD CALLED: selectVehiclePartsCategory() - This should NOT be the primary method!');
      this.log('🔍 Checking if modern multiselect exists...');

      // Check if we should skip the old dropdown method entirely
      const hasMultiselect = await this.page!.$('.multiselect.qa-multiselect');
      if (hasMultiselect) {
        this.log('✅ Modern multiselect detected, skipping old dropdown method and returning false');
        return false; // Let the multiselect method handle it
      }

      this.log('⚠️ No modern multiselect found, proceeding with old dropdown method...');

      // First, look for the parent_category dropdown (only if no multiselect)
      this.log('Looking for traditional parent_category dropdown...');
      const parentCategorySelectors = [
        'select[name="parent_category"]',
        '#parent_category',
        'select[id="parent_category"]',
        '[name="parent_category"]'
      ];

      let categoryDropdown = null;
      for (const selector of parentCategorySelectors) {
        try {
          categoryDropdown = await this.page!.waitForSelector(selector, { timeout: 3000 });
          if (categoryDropdown) {
            this.log(`Found parent_category dropdown using selector: ${selector}`);
            break;
          }
        } catch (e) {
          continue;
        }
      }

      if (categoryDropdown) {
        // Try multiple methods to trigger dropdown population
        this.log('Triggering dropdown to populate options...');

        try {
          // First, check if dropdown is enabled and visible
          const dropdownInfo = await this.page!.evaluate(() => {
            const dropdown = document.querySelector('select[name="parent_category"]') as HTMLSelectElement;
            if (dropdown) {
              return {
                disabled: dropdown.disabled,
                visible: dropdown.offsetParent !== null,
                optionCount: dropdown.options.length,
                style: window.getComputedStyle(dropdown).display
              };
            }
            return null;
          });

          this.log(`Dropdown info: ${JSON.stringify(dropdownInfo)}`);

          if (dropdownInfo?.disabled) {
            this.log('Dropdown is disabled, trying to enable it...', 'warn');
            await this.page!.evaluate(() => {
              const dropdown = document.querySelector('select[name="parent_category"]') as HTMLSelectElement;
              if (dropdown) dropdown.disabled = false;
            });
          }

          // Method 1: Try JavaScript events first (safer than clicking)
          await this.page!.evaluate(() => {
            const dropdown = document.querySelector('select[name="parent_category"]') as HTMLSelectElement;
            if (dropdown) {
              // Trigger various events that might populate the dropdown
              dropdown.dispatchEvent(new Event('focus', { bubbles: true }));
              dropdown.dispatchEvent(new Event('mousedown', { bubbles: true }));
              dropdown.dispatchEvent(new Event('click', { bubbles: true }));
              dropdown.dispatchEvent(new Event('change', { bubbles: true }));
            }
          });

          await this.delay(1500);

          // Method 2: Try focus (usually safer than click)
          try {
            await this.page!.focus('select[name="parent_category"]');
            await this.delay(1000);
          } catch (focusError) {
            this.log(`Could not focus dropdown: ${focusError}`, 'warn');
          }

          // Method 3: Try click with reduced timeout (only if other methods didn't work)
          try {
            await this.page!.click('select[name="parent_category"]', { timeout: 5000 });
            await this.delay(1000);
          } catch (clickError) {
            this.log(`Could not click dropdown (this is often normal): ${clickError}`, 'warn');
          }

          this.log('Dropdown trigger events completed');
        } catch (e) {
          this.log(`Could not trigger dropdown: ${e}`, 'warn');
        }

        // Check if options are now populated (retry mechanism)
        let optionsLoaded = false;
        let availableOptions: any[] = [];

        for (let attempt = 1; attempt <= 3; attempt++) {
          try {
            availableOptions = await this.page!.$$eval('select[name="parent_category"] option', options =>
              options.map(option => ({
                value: (option as HTMLOptionElement).value,
                text: option.textContent?.trim()
              }))
            );

            // Filter out empty options (usually the default "Select..." option)
            const validOptions = availableOptions.filter(opt => opt.text && opt.text.length > 0 && opt.text !== 'Select...');

            if (validOptions.length > 0) {
              optionsLoaded = true;
              this.log(`✅ Dropdown populated with ${validOptions.length} options: ${JSON.stringify(validOptions)}`);
              break;
            } else {
              this.log(`Attempt ${attempt}: Dropdown still empty, retrying...`);
              if (attempt < 3) {
                // Try triggering again with JavaScript (avoid timeout issues)
                await this.page!.evaluate(() => {
                  const dropdown = document.querySelector('select[name="parent_category"]') as HTMLSelectElement;
                  if (dropdown) {
                    dropdown.dispatchEvent(new Event('focus', { bubbles: true }));
                    dropdown.dispatchEvent(new Event('click', { bubbles: true }));
                  }
                });
                await this.delay(1500);
              }
            }
          } catch (debugError) {
            this.log(`Attempt ${attempt}: Could not check dropdown options: ${debugError}`, 'warn');
            if (attempt < 3) {
              await this.delay(1000);
            }
          }
        }

        if (!optionsLoaded) {
          this.log(`⚠️ Dropdown options still not loaded after 3 attempts`, 'warn');
        }

        // Try to select "Vehicle Parts & Accessories" from the dropdown
        this.log('Selecting "Vehicle Parts & Accessories" from dropdown...');
        const vehiclePartsOptions = [
          'Vehicle Parts & Accessories',
          'Vehicle Parts',
          'Vehicles',
          'Auto Parts',
          'Car Parts',
          'Automotive'
        ];

        let selected = false;
        for (const option of vehiclePartsOptions) {
          try {
            await this.page!.selectOption('select[name="parent_category"]', { label: option });
            selected = true;
            this.log(`Selected "${option}" from parent_category dropdown`);
            break;
          } catch (e) {
            // Try by value
            try {
              await this.page!.selectOption('select[name="parent_category"]', { value: option.toLowerCase().replace(/\s+/g, '_') });
              selected = true;
              this.log(`Selected "${option}" by value from parent_category dropdown`);
              break;
            } catch (e2) {
              // Try by index (Vehicle Parts is often the first or second option)
              try {
                if (option === 'Vehicle Parts & Accessories' || option === 'Vehicle Parts') {
                  await this.page!.selectOption('select[name="parent_category"]', { index: 1 }); // Try index 1 (skip default option)
                  selected = true;
                  this.log(`Selected "${option}" by index from parent_category dropdown`);
                  break;
                }
              } catch (e3) {
                continue;
              }
            }
          }
        }

        if (selected) {
          await this.delay(2000); // Wait for any subcategory dropdowns to load
          return true;
        }
      }

      // Fallback to clicking category elements if dropdown method fails
      this.log('Dropdown method failed, trying clickable category elements...');
      const categorySelectors = [
        'text="Vehicle Parts & Accessories"',
        'a:has-text("Vehicle Parts & Accessories")',
        'button:has-text("Vehicle Parts & Accessories")',
        '[title*="Vehicle Parts" i]',
        '[aria-label*="Vehicle Parts" i]',
        // Fallback options
        'text="Vehicles"',
        'text="Auto Parts"',
        'text="Car Parts"'
      ];

      let selected = false;
      for (const selector of categorySelectors) {
        try {
          await this.page!.waitForSelector(selector, { timeout: 3000 });
          await this.page!.click(selector);
          selected = true;
          this.log(`Selected category using selector: ${selector}`);
          break;
        } catch (e) {
          continue;
        }
      }

      if (!selected) {
        // Try to find any category that might work
        this.log('Primary category selectors failed, trying fallback options...');
        const fallbackSelectors = [
          'text="Parts"',
          'text="Spare Parts"',
          '[class*="category"]',
          '[data-testid*="category"]'
        ];

        for (const selector of fallbackSelectors) {
          try {
            await this.page!.waitForSelector(selector, { timeout: 2000 });
            await this.page!.click(selector);
            selected = true;
            this.log(`Selected fallback category using: ${selector}`);
            break;
          } catch (e) {
            continue;
          }
        }
      }

      if (!selected) {
        throw new Error('Could not find any suitable category');
      }

      await this.delay(2000);

      // If we have part category information, try to select more specific category
      if (part?.parent_category_name && part.parent_category_name !== 'Vehicle Parts') {
        await this.selectSpecificCategory(part.parent_category_name, part.category_name);
      }

      this.log('Vehicle Parts category selected');
      return true;
    } catch (error) {
      this.log(`Failed to select category: ${error}`, 'error');
      return false;
    }
  }

  async selectLocation(): Promise<boolean> {
    try {
      this.log('🌍 Setting location to Nairobi → Langata using .qa-choose-region...');

      // Step 1: Click the "Select Location" dropdown using its qa attribute
      this.log('🎯 Clicking location dropdown using .qa-choose-region...');
      await this.page!.click('.qa-choose-region .multiselect__select');

      // Wait for the dropdown options to become visible (reduced timeout)
      await this.page!.waitForSelector('.multiselect__content-wrapper', { state: 'visible', timeout: 5000 });
      this.log('✅ Location dropdown opened successfully');

      // Step 2: Select "Nairobi"
      this.log('🔍 Selecting "Nairobi" from dropdown options...');
      await this.page!.click('.multiselect__content-wrapper .fw-select__option-label:has-text("Nairobi")');
      this.log('✅ Successfully clicked Nairobi option');

      // Step 3: Wait for the sub-locations to appear or update (reduced timeout)
      this.log('⏳ Waiting for Langata sublocation to become available...');
      await this.page!.waitForSelector('.multiselect__content-wrapper .fw-select__option-label:has-text("Langata")', {
        state: 'visible',
        timeout: 3000
      });
      this.log('✅ Langata option is now visible');

      // Step 4: Select "Langata"
      this.log('🔍 Selecting "Langata" from sublocation options...');
      await this.page!.click('.multiselect__content-wrapper .fw-select__option-label:has-text("Langata")');
      this.log('✅ Successfully clicked Langata option');

      await this.delay(300); // Reduced delay

      // Verify the final selection
      const finalSelection = await this.page!.evaluate(() => {
        const selectedSpan = document.querySelector('.qa-choose-region .multiselect__single');
        return selectedSpan?.textContent?.trim();
      });

      this.log(`Final location selection: ${finalSelection}`);
      this.log('✅ Successfully set location to Nairobi → Langata');
      return true;

    } catch (error) {
      this.log(`❌ Failed to set location using .qa-choose-region: ${error}`, 'error');
      this.log('🔄 Trying fallback location selection methods...');
      return await this.selectLocationFallback();
    }
  }

  private async openLocationMultiselect(): Promise<boolean> {
    try {
      this.log('🎯 Looking for "Select Location*" dropdown...');

      // Strategy 1: Find the location input field by its label and click it to open dropdown
      const locationInputSelectors = [
        // Look for input field associated with "Select Location*" label
        'label:has-text("Select Location*") + div input',
        'label:has-text("Location*") + div input',
        'label:has-text("Select Location") + div input',

        // Alternative: look for input within the location multiselect container
        'label:has-text("Select Location*") + div .multiselect__input',
        'label:has-text("Location*") + div .multiselect__input',
        'label:has-text("Select Location") + div .multiselect__input'
      ];

      let dropdownOpened = false;

      for (const selector of locationInputSelectors) {
        try {
          this.log(`Trying location input selector: ${selector}`);
          await this.page!.click(selector);
          await this.delay(1000);

          // Check if location dropdown is now visible
          const isVisible = await this.page!.isVisible('.multiselect__content-wrapper');
          if (isVisible) {
            dropdownOpened = true;
            this.log(`✅ Location dropdown opened via input: ${selector}`);
            break;
          }
        } catch (e) {
          this.log(`Location input selector failed: ${selector}`, 'warn');
          continue;
        }
      }

      // Strategy 2: If input targeting fails, try clicking the multiselect container
      if (!dropdownOpened) {
        this.log('Input targeting failed, trying multiselect container...');
        const containerSelectors = [
          'label:has-text("Select Location*") + div .multiselect__select',
          'label:has-text("Location*") + div .multiselect__select',
          '.multiselect.qa-multiselect:not(.qa-choose-category) .multiselect__select'
        ];

        for (const selector of containerSelectors) {
          try {
            this.log(`Trying container selector: ${selector}`);
            await this.page!.click(selector);
            await this.delay(1000);

            const isVisible = await this.page!.isVisible('.multiselect__content-wrapper');
            if (isVisible) {
              dropdownOpened = true;
              this.log(`✅ Location dropdown opened via container: ${selector}`);
              break;
            }
          } catch (e) {
            continue;
          }
        }
      }

      // Strategy 3: JavaScript approach to find location input
      if (!dropdownOpened) {
        this.log('Trying JavaScript approach to find location input...');
        await this.page!.evaluate(() => {
          // Look for label containing "Location" and find its associated input
          const labels = Array.from(document.querySelectorAll('label'));
          for (const label of labels) {
            if (label.textContent?.includes('Location')) {
              // Look for input in the same container
              const container = label.parentElement;
              const input = container?.querySelector('input') as HTMLElement;
              if (input) {
                input.click();
                input.focus();
                return;
              }

              // Fallback: look for multiselect in the container
              const multiselect = container?.querySelector('.multiselect__select') as HTMLElement;
              if (multiselect) {
                multiselect.click();
                return;
              }
            }
          }
        });

        await this.delay(1500);
        const isNowVisible = await this.page!.isVisible('.multiselect__content-wrapper');
        if (isNowVisible) {
          dropdownOpened = true;
          this.log('✅ Location dropdown opened via JavaScript');
        }
      }

      return dropdownOpened;
    } catch (error) {
      this.log(`Error opening location multiselect: ${error}`, 'error');
      return false;
    }
  }

  private async searchAndSelectLocation(locationName: string): Promise<boolean> {
    try {
      this.log(`🔍 Searching and selecting location: ${locationName}`);

      // Find the specific location input field (not just any multiselect input)
      const locationInputSelectors = [
        // Look for input associated with "Select Location*" label
        'label:has-text("Select Location*") + div input',
        'label:has-text("Location*") + div input',
        'label:has-text("Select Location") + div input',

        // Alternative: look for multiselect input within location container
        'label:has-text("Select Location*") + div .multiselect__input',
        'label:has-text("Location*") + div .multiselect__input',
        'label:has-text("Select Location") + div .multiselect__input'
      ];

      let locationInput = null;
      for (const selector of locationInputSelectors) {
        try {
          locationInput = await this.page!.waitForSelector(selector, { timeout: 2000 });
          if (locationInput) {
            this.log(`Found location input using: ${selector}`);
            break;
          }
        } catch (e) {
          continue;
        }
      }

      // Fallback: if specific location input not found, try any multiselect input
      if (!locationInput) {
        this.log('Specific location input not found, trying any multiselect input...');
        locationInput = await this.page!.waitForSelector('.multiselect__input', { timeout: 3000 });
      }

      if (!locationInput) {
        this.log('❌ Could not find any location input field');
        return false;
      }

      // Clear any existing text and type the location name
      await locationInput.click();
      await locationInput.fill(''); // Clear existing text
      await this.delay(500);

      this.log(`Typing "${locationName}" in location input field...`);
      await locationInput.type(locationName, { delay: 100 });
      await this.delay(1500); // Wait for search results to load

      // Look for the matching location in search results
      this.log('Looking for matching location in search results...');
      const locationSelected = await this.page!.evaluate((location) => {
        const options = Array.from(document.querySelectorAll('.multiselect__option .fw-select__option-label')) as Element[];

        // First try exact match
        for (const option of options) {
          const text = option.textContent?.trim();
          if (text === location) {
            const clickableElement = option.closest('.multiselect__option') as HTMLElement;
            if (clickableElement) {
              clickableElement.click();
              return { success: true, text: text };
            }
          }
        }

        // Fallback: partial match (case insensitive)
        for (const option of options) {
          const text = option.textContent?.trim();
          if (text && text.toLowerCase().includes(location.toLowerCase())) {
            const clickableElement = option.closest('.multiselect__option') as HTMLElement;
            if (clickableElement) {
              clickableElement.click();
              return { success: true, text: text };
            }
          }
        }

        return { success: false, text: null };
      }, locationName);

      if (locationSelected.success) {
        this.log(`✅ Successfully selected location: ${locationSelected.text}`);
        await this.delay(1000);

        // Verify the selection by checking the multiselect display
        const finalSelection = await this.page!.evaluate(() => {
          const selectedSpan = document.querySelector('.multiselect__single');
          return selectedSpan?.textContent?.trim();
        });

        this.log(`Final location selection: ${finalSelection}`);
        return true;
      } else {
        this.log(`❌ Could not find "${locationName}" in search results`);

        // Debug: Log available options
        const availableOptions = await this.page!.evaluate(() => {
          const options = Array.from(document.querySelectorAll('.multiselect__option .fw-select__option-label'));
          return options.map(option => option.textContent?.trim()).filter(text => text && text.length > 0);
        });

        this.log(`Available location options: ${JSON.stringify(availableOptions.slice(0, 10))}`, 'warn'); // Show first 10 options
        return false;
      }

    } catch (error) {
      this.log(`Error searching and selecting location: ${error}`, 'error');
      return false;
    }
  }

  private async selectLocationFallback(): Promise<boolean> {
    try {
      this.log('🔄 Using fallback location selection methods...');

      // Step 1: Try traditional dropdown/input methods for Nairobi
      this.log('Trying fallback methods for Nairobi...');
      const nairobiSelectors = [
        'text="Nairobi"',
        'option:has-text("Nairobi")',
        'select option[value*="nairobi" i]',
        '[data-value*="nairobi" i]',
        'li:has-text("Nairobi")',
        'input[placeholder*="location" i]',
        'input[placeholder*="city" i]',
        'input[name*="location" i]',
        'input[id*="location" i]'
      ];

      let nairobiSelected = false;
      for (const selector of nairobiSelectors) {
        try {
          await this.page!.waitForSelector(selector, { timeout: 3000 });

          if (selector.includes('input')) {
            await this.page!.fill(selector, 'Nairobi');
          } else {
            await this.page!.click(selector);
          }

          nairobiSelected = true;
          this.log(`✅ Selected Nairobi using fallback: ${selector}`);
          break;
        } catch (e) {
          continue;
        }
      }

      if (!nairobiSelected) {
        this.log('❌ Could not select Nairobi with any fallback method');
        return false;
      }

      await this.delay(1000);

      // Step 2: Try traditional methods for Langata
      this.log('Trying fallback methods for Langata...');
      const langataSelectors = [
        'text="Langata"',
        'option:has-text("Langata")',
        'select option[value*="langata" i]',
        '[data-value*="langata" i]',
        'li:has-text("Langata")',
        'input[placeholder*="area" i]',
        'input[placeholder*="neighborhood" i]',
        'input[name*="area" i]',
        'input[id*="area" i]'
      ];

      let langataSelected = false;
      for (const selector of langataSelectors) {
        try {
          await this.page!.waitForSelector(selector, { timeout: 3000 });

          if (selector.includes('input')) {
            await this.page!.fill(selector, 'Langata');
          } else {
            await this.page!.click(selector);
          }

          langataSelected = true;
          this.log(`✅ Selected Langata using fallback: ${selector}`);
          break;
        } catch (e) {
          continue;
        }
      }

      if (langataSelected) {
        this.log('✅ Fallback location selection successful: Nairobi → Langata');
        return true;
      } else {
        this.log('⚠️ Could not select Langata with fallback, but Nairobi is selected');
        return true; // Continue with just Nairobi
      }

    } catch (error) {
      this.log(`❌ Fallback location selection failed: ${error}`, 'error');
      return false;
    }
  }

  private async selectSpecificCategory(parentCategory: string, category?: string): Promise<void> {
    try {
      this.log(`Attempting to select specific category: ${parentCategory} > ${category || 'N/A'}`);

      // Try to find and select the parent category
      const parentSelectors = [
        `text="${parentCategory}"`,
        `[title*="${parentCategory}" i]`,
        `[aria-label*="${parentCategory}" i]`
      ];

      for (const selector of parentSelectors) {
        try {
          if (await this.page!.$(selector)) {
            await this.page!.click(selector);
            await this.delay(1000);
            this.log(`Selected parent category: ${parentCategory}`);

            // If we have a specific category, try to select it too
            if (category && category !== parentCategory) {
              const categorySelectors = [
                `text="${category}"`,
                `[title*="${category}" i]`,
                `[aria-label*="${category}" i]`
              ];

              for (const catSelector of categorySelectors) {
                try {
                  if (await this.page!.$(catSelector)) {
                    await this.page!.click(catSelector);
                    await this.delay(1000);
                    this.log(`Selected specific category: ${category}`);
                    return;
                  }
                } catch (e) {
                  continue;
                }
              }
            }
            return;
          }
        } catch (e) {
          continue;
        }
      }

      this.log(`Could not find specific category: ${parentCategory}`, 'warn');
    } catch (error) {
      this.log(`Error selecting specific category: ${error}`, 'warn');
    }
  }

  async fillPartDetails(part: PartData): Promise<boolean> {
    try {
      this.log(`🖊️ Filling comprehensive part details for: ${part.title}`);

      // 1. Fill title (same as database)
      await this.fillTitle(part.title);

      // 2. Select type based on category
      await this.selectPartType(part);

      // 3. Select subtype (if type is lighting-related)
      await this.selectPartSubtype(part);

      // 4. Select make based on car brand
      await this.selectMake(part);

      // 4. Select condition from database
      await this.selectCondition(part.condition);

      // 5. Fill description (same as database)
      await this.fillDescription(part.description);

      // 6. Fill price (marked up shop price)
      await this.fillPrice(part.price);

      // 7. Select "Yes" for negotiation
      await this.selectNegotiation(true);

      this.log('✅ Part details filled successfully');
      return true;
    } catch (error) {
      this.log(`❌ Failed to fill part details: ${error}`, 'error');
      return false;
    }
  }

  private async fillTitle(title: string): Promise<void> {
    this.log(`📝 Filling title: ${title}`);
    const titleSelectors = [
      'input[name="title"]',
      'input[placeholder*="title" i]',
      '#title',
      '.title-input',
      'input[id*="title"]'
    ];

    for (const selector of titleSelectors) {
      try {
        await this.page!.waitForSelector(selector, { timeout: 3000 });
        await this.page!.fill(selector, title);
        this.log(`✅ Filled title: ${title}`);
        await this.delay(500);
        return;
      } catch (e) {
        continue;
      }
    }
    throw new Error('Could not find title input field');
  }

  private async selectPartType(part: PartData): Promise<void> {
    this.log(`🔧 Selecting part type based on category: ${part.category_name}`);

    let typeToSelect = 'Other'; // Default

    // Determine type based on category
    if (part.category_name?.toLowerCase().includes('body') ||
        part.parent_category_name?.toLowerCase().includes('body')) {
      typeToSelect = 'Exterior Accessories';
      this.log(`📦 Body parts category detected → selecting "Exterior Accessories"`);
    } else if (part.category_name?.toLowerCase().includes('lighting') ||
               part.category_name?.toLowerCase().includes('headlight') ||
               part.parent_category_name?.toLowerCase().includes('lighting')) {
      typeToSelect = 'Headlights and lighting';
      this.log(`💡 Lighting category detected → selecting "Headlights and lighting"`);
    } else {
      this.log(`🔧 Other category detected → selecting "Other"`);
    }

    try {
      // First, try to click the dropdown to open it
      this.log(`🎯 Clicking Type dropdown using label selector...`);
      await this.page!.locator('label:has-text("Type*") + div .multiselect__select').click();
      await this.delay(500);

      this.log(`🎯 Looking for "${typeToSelect}" option in Type dropdown...`);

      // Try to find and click the option directly
      const optionSelectors = [
        `.multiselect__option-label:has-text("${typeToSelect}")`,
        `.fw-select__option-label:has-text("${typeToSelect}")`,
        `.multiselect__element:has-text("${typeToSelect}")`,
        // Try partial matches for common variations
        `.multiselect__option-label:has-text("Headlights")`,
        `.fw-select__option-label:has-text("Headlights")`,
        `.multiselect__option-label:has-text("Exterior")`,
        `.fw-select__option-label:has-text("Exterior")`,
        `.multiselect__option-label:has-text("Other")`,
        `.fw-select__option-label:has-text("Other")`
      ];

      let optionSelected = false;
      for (const optionSelector of optionSelectors) {
        try {
          const option = await this.page!.locator(optionSelector).first();
          if (await option.isVisible()) {
            await option.click({ timeout: 3000 });
            this.log(`✅ Selected type option using selector: ${optionSelector}`);
            optionSelected = true;
            break;
          }
        } catch (e) {
          continue;
        }
      }

      if (!optionSelected) {
        // Try to find options by keyword matching
        this.log(`🔍 Trying keyword-based selection...`);
        const keywords = ['headlight', 'lighting', 'exterior', 'accessories', 'other'];

        for (const keyword of keywords) {
          if (typeToSelect.toLowerCase().includes(keyword)) {
            try {
              const keywordOption = await this.page!.locator(`.fw-select__option-label:text-matches(".*${keyword}.*", "i")`).first();
              if (await keywordOption.isVisible()) {
                await keywordOption.click({ timeout: 3000 });
                this.log(`✅ Selected type by keyword: "${keyword}"`);
                optionSelected = true;
                break;
              }
            } catch (e) {
              continue;
            }
          }
        }
      }

      if (!optionSelected) {
        this.log(`⚠️ Could not find matching option, trying to select first available option...`);
        try {
          const firstOption = await this.page!.locator('.fw-select__option-label').first();
          if (await firstOption.isVisible()) {
            const optionText = await firstOption.textContent();
            await firstOption.click({ timeout: 3000 });
            this.log(`✅ Selected first available type option: "${optionText}"`);
            optionSelected = true;
          }
        } catch (e) {
          this.log(`❌ Could not select any type option`, 'error');
        }
      }

      this.log(`✅ Type selection completed`);
      await this.delay(300);
    } catch (error) {
      this.log(`⚠️ Could not select type "${typeToSelect}": ${error}`, 'warn');
    }
  }

  private async selectPartSubtype(part: PartData): Promise<void> {
    this.log(`🔧 Selecting part subtype based on category: ${part.category_name}`);

    let subtypeToSelect = ''; // Default - don't select if not lighting

    // Only select subtype for lighting categories
    if (part.category_name?.toLowerCase().includes('lighting') ||
        part.category_name?.toLowerCase().includes('headlight') ||
        part.parent_category_name?.toLowerCase().includes('lighting')) {
      subtypeToSelect = 'Headlights, Components & Accessories';
      this.log(`💡 Lighting category detected → selecting "Headlights, Components & Accessories"`);
    } else {
      this.log(`🔧 Non-lighting category detected → skipping subtype selection`);
      return; // Skip subtype selection for non-lighting parts
    }

    try {
      // Look for subtype dropdown (it might appear after type selection)
      const subtypeSelectors = [
        'label:has-text("Subtype*") + div .multiselect__select',
        'label:has-text("Sub-type*") + div .multiselect__select',
        'label:has-text("Category*") + div .multiselect__select',
        '.subtype-select .multiselect__select'
      ];

      let subtypeDropdownFound = false;
      for (const selector of subtypeSelectors) {
        try {
          const dropdown = await this.page!.locator(selector);
          if (await dropdown.isVisible()) {
            this.log(`🎯 Clicking Subtype dropdown using selector: ${selector}`);
            await dropdown.click();
            await this.delay(500);
            subtypeDropdownFound = true;
            break;
          }
        } catch (e) {
          continue;
        }
      }

      if (!subtypeDropdownFound) {
        this.log(`⚠️ Subtype dropdown not found, might not be required for this type`, 'warn');
        return;
      }

      this.log(`🎯 Looking for "${subtypeToSelect}" option in Subtype dropdown...`);

      // Try to find and click the subtype option
      const optionSelectors = [
        `.multiselect__option-label:has-text("${subtypeToSelect}")`,
        `.fw-select__option-label:has-text("${subtypeToSelect}")`,
        `.multiselect__element:has-text("${subtypeToSelect}")`,
        // Try partial matches
        `.fw-select__option-label:has-text("Headlights")`,
        `.multiselect__option-label:has-text("Headlights")`,
        `.fw-select__option-label:has-text("Components")`,
        `.multiselect__option-label:has-text("Components")`
      ];

      let optionSelected = false;
      for (const optionSelector of optionSelectors) {
        try {
          const option = await this.page!.locator(optionSelector).first();
          if (await option.isVisible()) {
            await option.click({ timeout: 3000 });
            this.log(`✅ Selected subtype option using selector: ${optionSelector}`);
            optionSelected = true;
            break;
          }
        } catch (e) {
          continue;
        }
      }

      if (!optionSelected) {
        this.log(`⚠️ Could not find matching subtype option, trying first available...`);
        try {
          const firstOption = await this.page!.locator('.fw-select__option-label').first();
          if (await firstOption.isVisible()) {
            const optionText = await firstOption.textContent();
            await firstOption.click({ timeout: 3000 });
            this.log(`✅ Selected first available subtype option: "${optionText}"`);
            optionSelected = true;
          }
        } catch (e) {
          this.log(`❌ Could not select any subtype option`, 'error');
        }
      }

      this.log(`✅ Subtype selection completed`);
      await this.delay(300);
    } catch (error) {
      this.log(`⚠️ Could not select subtype "${subtypeToSelect}": ${error}`, 'warn');
    }
  }

  private async selectMake(part: PartData): Promise<void> {
    this.log(`🚗 Selecting make based on part data`);

    // Extract brand from part title or use a default mapping
    let makeToSelect = '';
    const title = part.title.toLowerCase();

    if (title.includes('vw') || title.includes('volkswagen')) {
      makeToSelect = 'Volkswagen';
      this.log(`🚗 VW detected → selecting "Volkswagen"`);
    } else if (title.includes('audi')) {
      makeToSelect = 'Audi';
      this.log(`🚗 Audi detected → selecting "Audi"`);
    } else if (title.includes('bmw')) {
      makeToSelect = 'BMW';
      this.log(`🚗 BMW detected → selecting "BMW"`);
    } else if (title.includes('mercedes')) {
      makeToSelect = 'Mercedes-Benz';
      this.log(`🚗 Mercedes detected → selecting "Mercedes-Benz"`);
    } else if (title.includes('toyota')) {
      makeToSelect = 'Toyota';
      this.log(`🚗 Toyota detected → selecting "Toyota"`);
    } else if (title.includes('nissan')) {
      makeToSelect = 'Nissan';
      this.log(`🚗 Nissan detected → selecting "Nissan"`);
    } else if (title.includes('honda')) {
      makeToSelect = 'Honda';
      this.log(`🚗 Honda detected → selecting "Honda"`);
    } else {
      this.log(`🚗 No specific make detected, will try to extract from title`);
      // Try to extract the first word as make
      const words = part.title.split(' ');
      if (words.length > 0) {
        makeToSelect = words[0];
      }
    }

    if (!makeToSelect) {
      this.log(`⚠️ Could not determine make, skipping...`, 'warn');
      return;
    }

    try {
      // First, try to click the dropdown to open it
      this.log(`🎯 Clicking Make dropdown using label selector...`);
      await this.page!.locator('label:has-text("Make*") + div .multiselect__select').click();
      await this.delay(500);

      this.log(`🎯 Looking for "${makeToSelect}" option in Make dropdown...`);

      // Try different variations of the make name
      const makeVariations = [
        makeToSelect,
        makeToSelect === 'Volkswagen' ? 'VW' : makeToSelect,
        makeToSelect === 'VW' ? 'Volkswagen' : makeToSelect,
        makeToSelect === 'Mercedes-Benz' ? 'Mercedes' : makeToSelect,
        makeToSelect === 'Mercedes' ? 'Mercedes-Benz' : makeToSelect
      ];

      let optionSelected = false;
      for (const makeVariation of makeVariations) {
        try {
          const optionSelectors = [
            `.multiselect__option-label:has-text("${makeVariation}")`,
            `.fw-select__option-label:has-text("${makeVariation}")`,
            `.multiselect__element:has-text("${makeVariation}")`,
            // Try case-insensitive partial matches
            `.fw-select__option-label:text-matches(".*${makeVariation}.*", "i")`
          ];

          for (const optionSelector of optionSelectors) {
            try {
              const option = await this.page!.locator(optionSelector).first();
              if (await option.isVisible()) {
                await option.click({ timeout: 3000 });
                this.log(`✅ Selected make option: "${makeVariation}" using selector: ${optionSelector}`);
                optionSelected = true;
                break;
              }
            } catch (e) {
              continue;
            }
          }

          if (optionSelected) break;
        } catch (e) {
          continue;
        }
      }

      if (!optionSelected) {
        this.log(`⚠️ Could not find matching option, trying to select first available option...`);
        try {
          const firstOption = await this.page!.locator('.fw-select__option-label').first();
          if (await firstOption.isVisible()) {
            const optionText = await firstOption.textContent();
            await firstOption.click({ timeout: 3000 });
            this.log(`✅ Selected first available make option: "${optionText}"`);
            optionSelected = true;
          }
        } catch (e) {
          this.log(`❌ Could not select any make option`, 'error');
        }
      }

      this.log(`✅ Make selection completed`);
      await this.delay(300);
    } catch (error) {
      this.log(`⚠️ Could not select make "${makeToSelect}": ${error}`, 'warn');
    }
  }

  private async selectCondition(condition: string): Promise<void> {
    this.log(`📊 Selecting condition: ${condition}`);

    let conditionToSelect = '';
    const conditionLower = condition.toLowerCase();

    if (conditionLower.includes('new') || conditionLower.includes('brand new')) {
      conditionToSelect = 'Brand New';
    } else {
      conditionToSelect = 'Used';
    }

    this.log(`📊 Condition "${condition}" → selecting "${conditionToSelect}"`);

    try {
      // First, try to click the dropdown to open it
      this.log(`🎯 Clicking Condition dropdown using label selector...`);
      await this.page!.locator('label:has-text("Condition*") + div .multiselect__select').click();
      await this.delay(500);

      this.log(`🎯 Looking for "${conditionToSelect}" option in Condition dropdown...`);

      // Try different variations of the condition
      const conditionVariations = [
        conditionToSelect,
        conditionToSelect === 'Brand New' ? 'New' : conditionToSelect,
        conditionToSelect === 'New' ? 'Brand New' : conditionToSelect,
        conditionToSelect === 'Used' ? 'Second Hand' : conditionToSelect,
        conditionToSelect === 'Second Hand' ? 'Used' : conditionToSelect
      ];

      let optionSelected = false;
      for (const conditionVariation of conditionVariations) {
        try {
          const optionSelectors = [
            `.multiselect__option-label:has-text("${conditionVariation}")`,
            `.fw-select__option-label:has-text("${conditionVariation}")`,
            `.multiselect__element:has-text("${conditionVariation}")`,
            // Try case-insensitive partial matches
            `.fw-select__option-label:text-matches(".*${conditionVariation}.*", "i")`
          ];

          for (const optionSelector of optionSelectors) {
            try {
              const option = await this.page!.locator(optionSelector).first();
              if (await option.isVisible()) {
                await option.click({ timeout: 3000 });
                this.log(`✅ Selected condition option: "${conditionVariation}" using selector: ${optionSelector}`);
                optionSelected = true;
                break;
              }
            } catch (e) {
              continue;
            }
          }

          if (optionSelected) break;
        } catch (e) {
          continue;
        }
      }

      if (!optionSelected) {
        this.log(`⚠️ Could not find matching option, trying to select first available option...`);
        try {
          const firstOption = await this.page!.locator('.fw-select__option-label').first();
          if (await firstOption.isVisible()) {
            const optionText = await firstOption.textContent();
            await firstOption.click({ timeout: 3000 });
            this.log(`✅ Selected first available condition option: "${optionText}"`);
            optionSelected = true;
          }
        } catch (e) {
          this.log(`❌ Could not select any condition option`, 'error');
        }
      }

      this.log(`✅ Condition selection completed`);
      await this.delay(300);
    } catch (error) {
      this.log(`⚠️ Could not select condition "${conditionToSelect}": ${error}`, 'warn');
    }
  }

  private async fillDescription(description?: string): Promise<void> {
    const desc = description || 'Quality auto part in good condition';
    this.log(`📝 Filling description: ${desc.substring(0, 50)}...`);

    const descriptionSelectors = [
      'textarea[name="description"]',
      'textarea[placeholder*="description" i]',
      '#description',
      '.description-input',
      'textarea[id*="description"]'
    ];

    for (const selector of descriptionSelectors) {
      try {
        await this.page!.waitForSelector(selector, { timeout: 3000 });
        await this.page!.fill(selector, desc);
        this.log(`✅ Filled description`);
        await this.delay(500);
        return;
      } catch (e) {
        continue;
      }
    }

    this.log(`⚠️ Could not find description field, continuing...`, 'warn');
  }

  private async fillPrice(price: number): Promise<void> {
    this.log(`💰 Filling price: ${price}`);

    const priceSelectors = [
      'input[name="price"]',
      'input[placeholder*="price" i]',
      '#price',
      '.price-input',
      'input[id*="price"]'
    ];

    for (const selector of priceSelectors) {
      try {
        await this.page!.waitForSelector(selector, { timeout: 3000 });
        await this.page!.fill(selector, price.toString());
        this.log(`✅ Filled price: ${price}`);
        await this.delay(500);
        return;
      } catch (e) {
        continue;
      }
    }

    throw new Error('Could not find price input field');
  }

  private async selectNegotiation(negotiable: boolean): Promise<void> {
    this.log(`🤝 Setting negotiation to: ${negotiable ? 'Yes' : 'No'}`);

    try {
      // Use the correct selector pattern for Jiji radio buttons
      const radioValue = negotiable ? 'yes' : 'no';

      this.log(`🎯 Selecting negotiation radio button for "${negotiable ? 'Yes' : 'No'}"...`);

      // Try the label-based approach first
      try {
        await this.page!.locator(`label[for="negotiable"]:has-text("${negotiable ? 'Yes' : 'No'}")`).click();
        this.log(`✅ Selected negotiation via label: ${negotiable ? 'Yes' : 'No'}`);
      } catch (e) {
        // Fallback to direct radio input
        await this.page!.locator(`input[type="radio"][name="negotiable"][value="${radioValue}"]`).check();
        this.log(`✅ Selected negotiation via radio input: ${negotiable ? 'Yes' : 'No'}`);
      }

      await this.delay(300);
    } catch (error) {
      this.log(`⚠️ Could not select negotiation "${negotiable ? 'Yes' : 'No'}": ${error}`, 'warn');
    }
  }

  private generateEnhancedDescription(part: PartData): string {
    let description = part.description || '';

    // Add part number if available
    if (part.partnumber_group) {
      description += `\n\nPart Number: ${part.partnumber_group}`;
    }

    // Add condition information
    description += `\n\nCondition: ${part.condition}`;

    // Add make information if available
    if (part.make) {
      description += `\nCompatible with: ${part.make}`;
    }

    // Add contact information
    description += '\n\nContact us for more information about this part.';
    description += '\nWe have a wide selection of automotive parts available.';

    return description.trim();
  }



  async uploadImagesToJiji(part: PartData): Promise<boolean> {
    try {
      this.log(`📤 Uploading ${part.images.length} images for part: ${part.title}`);

      // Create temp directory if it doesn't exist
      const tempDir = path.join(__dirname, 'temp');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // Download images to temp folder
      const downloadedImages: string[] = [];
      for (let i = 0; i < part.images.length && i < 10; i++) { // Max 10 images
        const image = part.images[i];

        // Extract original extension from URL
        const urlPath = new URL(image.image_url).pathname;
        const originalExt = path.extname(urlPath).toLowerCase();
        this.log(`🔍 Debug: Original extension from URL: ${originalExt}`);

        // Use original extension, but we'll convert it in downloadImageToTemp if needed
        const filename = `part_${part.id}_image_${i + 1}${originalExt || '.jpg'}`;

        try {
          this.log(`⬇️ Downloading image ${i + 1}/${part.images.length} from: ${image.image_url}`);
          this.log(`🔍 Debug: Generated filename: ${filename}`);
          const localPath = await this.downloadImageToTemp(image.image_url, filename);
          downloadedImages.push(localPath);
          this.log(`✅ Downloaded image ${i + 1}/${part.images.length}: ${path.basename(localPath)}`);
        } catch (error) {
          this.log(`❌ Failed to download image ${i + 1}: ${error}`, 'warn');
        }
      }

      if (downloadedImages.length === 0) {
        this.log('❌ No images were downloaded successfully', 'error');
        return false;
      }

      // Find and prepare the file input element using robust selectors
      this.log('🔍 Looking for image upload input...');
      const uploadSelectors = [
        'input[type="file"].qa-messenger-send-image',
        'input[type="file"][accept*="image"]',
        'input[type="file"][name="image"]',
        'input[type="file"][multiple]',
        'input[type="file"]',
        '.qa-messenger-send-image',
        '[data-testid="image-upload"]',
        '.image-upload input[type="file"]',
        '.photo-upload input[type="file"]'
      ];

      let fileInput = null;
      for (const selector of uploadSelectors) {
        try {
          fileInput = await this.page!.waitForSelector(selector, { timeout: 3000 });
          if (fileInput) {
            this.log(`✅ Found file input using selector: ${selector}`);
            break;
          }
        } catch (e) {
          this.log(`Selector failed: ${selector}`, 'warn');
          continue;
        }
      }

      if (!fileInput) {
        this.log('❌ Could not find file input element', 'error');
        return false;
      }

      // Remove disabled attribute and ensure visibility (as mentioned in your example)
      this.log('🔧 Preparing file input for upload...');
      await fileInput.evaluate((node: HTMLInputElement) => {
        node.removeAttribute('disabled');
        node.style.display = 'block';
        node.style.opacity = '1';
        node.style.visibility = 'visible';
        node.style.position = 'static';
      });

      // Check if input supports multiple files
      const isMultiple = await fileInput.evaluate((node: HTMLInputElement) => node.hasAttribute('multiple'));
      this.log(`File input supports multiple files: ${isMultiple}`);

      // Check for existing image thumbnails before uploading
      const initialImageCount = await this.getExistingImageCount();
      this.log(`📊 Initial image thumbnail count: ${initialImageCount}`);

      if (initialImageCount > 0) {
        this.log(`⚠️ Found ${initialImageCount} existing image thumbnails. Checking if we should skip upload...`);

        // If there are already image thumbnails, skip upload to avoid duplicates
        this.log(`🔄 Skipping image upload to avoid duplicates (${initialImageCount} existing thumbnails)`);

        // Still proceed to next button
        this.log('🔄 Attempting to click "Next" button after skipping image upload...');
        try {
          const nextSuccess = await this.clickNextButton();
          if (nextSuccess) {
            this.log('✅ Successfully clicked "Next" button after skipping upload');
          }
        } catch (nextError) {
          this.log(`⚠️ Error clicking "Next" button: ${nextError}`, 'warn');
        }

        return true;
      } else {
        this.log(`📤 No existing image thumbnails found, proceeding with upload...`);
      }

      if (isMultiple && downloadedImages.length > 1) {
        // Upload all images at once if multiple attribute is present
        this.log(`📤 Uploading all ${downloadedImages.length} images at once...`);
        try {
          await fileInput.setInputFiles(downloadedImages);
          await this.delay(3000); // Wait for upload to process

          // Check for Jiji error messages
          const uploadError = await this.checkForUploadErrors();
          if (uploadError) {
            this.log(`❌ Jiji upload error: ${uploadError}`, 'error');
            throw new Error(`Jiji upload error: ${uploadError}`);
          }

          // Verify the image count increased
          const newImageCount = await this.getExistingImageCount();
          if (newImageCount > initialImageCount) {
            this.log(`✅ Uploaded all ${downloadedImages.length} images successfully (${initialImageCount} → ${newImageCount})`);
          } else {
            this.log(`⚠️ Image count didn't increase as expected (${initialImageCount} → ${newImageCount})`, 'warn');
          }
        } catch (error) {
          this.log(`❌ Failed to upload multiple images: ${error}`, 'error');
          // Fall back to individual uploads
          this.log('🔄 Falling back to individual image uploads...');
          return await this.uploadImagesIndividually(downloadedImages, fileInput, initialImageCount);
        }
      } else {
        // Upload images one by one
        return await this.uploadImagesIndividually(downloadedImages, fileInput, initialImageCount);
      }

      // Verify upload success by looking for image thumbnails
      const uploadVerified = await this.verifyImageUpload(downloadedImages.length);

      // Clean up temp files
      this.cleanupTempFiles(downloadedImages);

      if (uploadVerified) {
        this.log(`✅ Image upload completed and verified. Processed ${downloadedImages.length} images with thumbnails visible`);

        // After successful upload, automatically click next button
        this.log('🔄 Attempting to click "Next" button after successful image upload...');
        try {
          const nextSuccess = await this.clickNextButton();
          if (nextSuccess) {
            this.log('✅ Successfully clicked "Next" button after image upload');
          } else {
            this.log('⚠️ Could not click "Next" button, but image upload was successful', 'warn');
          }
        } catch (nextError) {
          this.log(`⚠️ Error clicking "Next" button: ${nextError}`, 'warn');
        }

        return true;
      } else {
        this.log(`⚠️ Image upload completed but thumbnails not verified. Processed ${downloadedImages.length} images`);
        return true; // Still return true as upload process completed without errors
      }

    } catch (error) {
      this.log(`❌ Image upload failed: ${error}`, 'error');
      return false;
    }
  }

  private async uploadImagesIndividually(downloadedImages: string[], fileInput: any, initialImageCount: number = 0): Promise<boolean> {
    this.log(`📤 Uploading ${downloadedImages.length} images individually...`);

    for (let i = 0; i < downloadedImages.length; i++) {
      const imagePath = downloadedImages[i];
      this.log(`Uploading image ${i + 1}/${downloadedImages.length}: ${path.basename(imagePath)}`);

      try {
        await fileInput.setInputFiles([imagePath]);
        await this.delay(3000); // Wait for upload to process

        // Check for Jiji error messages after each upload
        const uploadError = await this.checkForUploadErrors();
        if (uploadError) {
          this.log(`❌ Jiji upload error for image ${i + 1}: ${uploadError}`, 'error');
          throw new Error(`Jiji upload error: ${uploadError}`);
        }

        // Verify the image count increased
        const currentImageCount = await this.getExistingImageCount();
        if (currentImageCount > initialImageCount + i) {
          this.log(`✅ Uploaded image ${i + 1} (count: ${initialImageCount + i} → ${currentImageCount})`);
        } else {
          this.log(`⚠️ Image ${i + 1} upload unclear (count: ${currentImageCount})`, 'warn');
        }

        // If not the last image, we might need to find the input again
        if (i < downloadedImages.length - 1) {
          await this.delay(1000);

          // Re-find the input element in case the DOM changed
          try {
            fileInput = await this.page!.waitForSelector('input[type="file"]', { timeout: 3000 });
            if (fileInput) {
              await fileInput.evaluate((node: HTMLInputElement) => {
                node.removeAttribute('disabled');
                node.style.display = 'block';
                node.style.opacity = '1';
              });
            }
          } catch (e) {
            this.log('Could not re-find file input, continuing...', 'warn');
          }
        }
      } catch (error) {
        this.log(`❌ Failed to upload image ${i + 1}: ${error}`, 'error');
        // Continue with next image instead of stopping completely
      }
    }

    // Verify final upload count
    const finalImageCount = await this.getExistingImageCount();
    const uploadedCount = finalImageCount - initialImageCount;

    if (uploadedCount > 0) {
      this.log(`✅ Individual image uploads completed: ${uploadedCount} new images (${initialImageCount} → ${finalImageCount})`);

      // After successful upload, automatically click next button
      this.log('🔄 Attempting to click "Next" button after individual image uploads...');
      try {
        const nextSuccess = await this.clickNextButton();
        if (nextSuccess) {
          this.log('✅ Successfully clicked "Next" button after individual uploads');
        } else {
          this.log('⚠️ Could not click "Next" button, but uploads were successful', 'warn');
        }
      } catch (nextError) {
        this.log(`⚠️ Error clicking "Next" button: ${nextError}`, 'warn');
      }
    } else {
      this.log('⚠️ Individual uploads completed but no new images detected');
    }

    return true;
  }

  private async verifyImageUpload(expectedCount: number): Promise<boolean> {
    this.log('🔍 Verifying image upload success and looking for thumbnails...');
    try {
      // Wait a bit for thumbnails to appear
      await this.delay(2000);

      const imagePreviewSelectors = [
        '.b-add-image-section__photos-wrapper img',
        '.uploaded-image img',
        '.image-preview img',
        'img[src*="blob:"]',
        '.file-preview img',
        '.photo-preview img',
        '[data-testid="uploaded-image"]',
        '.thumbnail img',
        '.image-thumbnail',
        '.upload-preview img',
        // More specific Jiji selectors
        '.b-add-image-section img',
        '.photo-upload-preview img',
        '.image-container img'
      ];

      let uploadedCount = 0;
      let foundSelector = '';

      for (const selector of imagePreviewSelectors) {
        try {
          const previews = await this.page!.locator(selector).count();
          if (previews > 0) {
            uploadedCount = previews;
            foundSelector = selector;
            this.log(`✅ Found ${previews} image thumbnail(s) using selector: ${selector}`);
            break;
          }
        } catch (e) {
          continue;
        }
      }

      if (uploadedCount > 0) {
        this.log(`✅ Image upload verification successful: ${uploadedCount} thumbnails visible`);

        // Take a screenshot to show the thumbnails (if not headless)
        if (!this.config.headless) {
          try {
            await this.page!.screenshot({ path: 'automation/logs/image-upload-success.png' });
            this.log('📸 Screenshot saved showing uploaded image thumbnails');
          } catch (e) {
            // Ignore screenshot errors
          }
        }

        return true;
      } else {
        this.log('⚠️ Could not find image thumbnails, checking for any upload indicators...', 'warn');

        // Check for other upload success indicators
        const uploadIndicators = [
          '.upload-success',
          '.file-uploaded',
          '.image-uploaded',
          '[data-upload-status="success"]',
          '.upload-complete'
        ];

        for (const indicator of uploadIndicators) {
          try {
            const found = await this.page!.locator(indicator).count();
            if (found > 0) {
              this.log(`✅ Found upload success indicator: ${indicator}`);
              return true;
            }
          } catch (e) {
            continue;
          }
        }

        this.log('❌ No image thumbnails or upload indicators found', 'warn');
        return false;
      }
    } catch (error) {
      this.log(`⚠️ Could not verify image upload: ${error}`, 'warn');
      return false;
    }
  }

  private async checkForUploadErrors(): Promise<string | null> {
    try {
      // Wait a moment for error messages to appear
      await this.delay(1000);

      // Common error message selectors for Jiji
      const errorSelectors = [
        '.error-message',
        '.alert-danger',
        '.validation-error',
        '.upload-error',
        '[class*="error"]',
        '[class*="alert"]',
        '.text-danger',
        '.text-red',
        // Look for specific Jiji error messages
        'text="Supported formats are *.jpg and *.png"',
        'text="Image width must be at least 600 px"',
        ':has-text("Supported formats")',
        ':has-text("Image width must be")',
        ':has-text("file size")',
        ':has-text("format")'
      ];

      for (const selector of errorSelectors) {
        try {
          const errorElement = await this.page!.waitForSelector(selector, { timeout: 2000 });
          if (errorElement) {
            const errorText = await errorElement.textContent();
            if (errorText && errorText.trim().length > 0) {
              return errorText.trim();
            }
          }
        } catch (e) {
          // Continue to next selector
          continue;
        }
      }

      return null; // No errors found
    } catch (error) {
      this.log(`Error checking for upload errors: ${error}`, 'warn');
      return null;
    }
  }

  private async getExistingImageCount(): Promise<number> {
    try {
      // Selectors for existing image thumbnails on Jiji (prioritize most specific)
      const imageThumbnailSelectors = [
        '.b-add-image-section__draggable .b-image-item__wrapper',
        '.b-add-image-section__photos-wrapper .b-image-item',
        '.b-add-image-section .b-image-item',
        '.image-thumbnail',
        '.uploaded-image',
        '.photo-preview',
        '.image-preview',
        '[data-testid="uploaded-image"]'
      ];

      for (const selector of imageThumbnailSelectors) {
        try {
          const elements = await this.page!.locator(selector);
          const count = await elements.count();

          if (count > 0) {
            // Verify these are actual image thumbnails by checking if they contain images
            let validImageCount = 0;
            for (let i = 0; i < count; i++) {
              try {
                const element = elements.nth(i);
                const hasImage = await element.locator('img, [style*="background-image"]').count() > 0;
                if (hasImage) {
                  validImageCount++;
                }
              } catch (e) {
                // If we can't verify, assume it's a valid image
                validImageCount++;
              }
            }

            if (validImageCount > 0) {
              this.log(`📊 Found ${validImageCount} existing image thumbnails using selector: ${selector}`);
              return validImageCount;
            }
          }
        } catch (e) {
          continue;
        }
      }

      this.log(`📊 No existing image thumbnails found`);
      return 0;
    } catch (error) {
      this.log(`⚠️ Error checking existing image thumbnail count: ${error}`, 'warn');
      return 0;
    }
  }

  private async clearExistingImages(): Promise<void> {
    try {
      this.log(`🗑️ Clearing existing image thumbnails for testing...`);

      // Selectors for delete/remove buttons on existing images
      const deleteButtonSelectors = [
        '.b-image-item__wrapper .b-image-item__delete',
        '.b-image-item__wrapper .delete-button',
        '.b-image-item__wrapper [title*="delete" i]',
        '.b-image-item__wrapper [title*="remove" i]',
        '.b-image-item__wrapper .fa-trash',
        '.b-image-item__wrapper .fa-times',
        '.b-image-item__wrapper .fa-close',
        '.uploaded-image .delete-btn',
        '.image-preview .remove-btn'
      ];

      let deletedCount = 0;
      for (const selector of deleteButtonSelectors) {
        try {
          const deleteButtons = await this.page!.locator(selector);
          const count = await deleteButtons.count();

          for (let i = 0; i < count; i++) {
            try {
              await deleteButtons.nth(i).click();
              await this.delay(500);
              deletedCount++;
              this.log(`🗑️ Deleted image ${deletedCount}`);
            } catch (e) {
              continue;
            }
          }

          if (deletedCount > 0) {
            this.log(`✅ Cleared ${deletedCount} existing images using selector: ${selector}`);
            return;
          }
        } catch (e) {
          continue;
        }
      }

      this.log(`📊 No existing images to clear or no delete buttons found`);
    } catch (error) {
      this.log(`⚠️ Error clearing existing images: ${error}`, 'warn');
    }
  }

  private cleanupTempFiles(downloadedImages: string[]): void {
    for (const imagePath of downloadedImages) {
      try {
        fs.unlinkSync(imagePath);
      } catch (e) {
        // Ignore cleanup errors
      }
    }
  }

  async clickNextButton(): Promise<boolean> {
    try {
      this.log('Looking for "Next" button...');

      const nextSelectors = [
        'button:has-text("Next")',
        'input[type="submit"][value*="Next" i]',
        'button[type="submit"]:has-text("Next")',
        'a:has-text("Next")',
        '[data-testid="next"]',
        '.next-btn',
        'button:has-text("Continue")',
        'button:has-text("Proceed")'
      ];

      for (const selector of nextSelectors) {
        try {
          await this.page!.waitForSelector(selector, { timeout: 3000 });
          await this.page!.click(selector);
          this.log(`Clicked "Next" button using selector: ${selector}`);

          // Wait for next page to load
          this.log('⏳ Waiting for next page to load...');
          await this.waitForNextPageLoad();

          return true;
        } catch (e) {
          continue;
        }
      }

      throw new Error('Could not find "Next" button');

    } catch (error) {
      this.log(`Failed to click "Next" button: ${error}`, 'error');
      return false;
    }
  }

  private async waitForNextPageLoad(): Promise<void> {
    try {
      // Wait for common next page indicators
      const nextPageSelectors = [
        // Form fields that might appear on next page
        'input[name="title"]',
        'input[name="price"]',
        'textarea[name="description"]',
        'input[placeholder*="title" i]',
        'input[placeholder*="price" i]',
        'textarea[placeholder*="description" i]',

        // Generic form indicators
        '.form-section',
        '.form-group',
        '.input-group',
        'form',

        // Loading indicators (wait for them to disappear)
        '.loading:not([style*="display: none"])',
        '.spinner:not([style*="display: none"])'
      ];

      let pageLoaded = false;

      // First, wait a short time for navigation
      await this.delay(1000);

      // Then check for next page elements
      for (const selector of nextPageSelectors) {
        try {
          if (selector.includes('loading') || selector.includes('spinner')) {
            // For loading indicators, wait for them to disappear
            await this.page!.waitForSelector(selector, { state: 'hidden', timeout: 5000 });
          } else {
            // For form elements, wait for them to appear
            await this.page!.waitForSelector(selector, { timeout: 3000 });
          }
          pageLoaded = true;
          this.log(`✅ Next page loaded, found: ${selector}`);
          break;
        } catch (e) {
          continue;
        }
      }

      if (!pageLoaded) {
        this.log('⚠️ Could not confirm next page loaded, but continuing...', 'warn');
        // Additional wait as fallback
        await this.delay(2000);
      }

    } catch (error) {
      this.log(`Error waiting for next page: ${error}`, 'warn');
      await this.delay(2000); // Fallback delay
    }
  }

  private async downloadImageToTemp(imageUrl: string, filename: string): Promise<string> {
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.statusText}`);
    }

    const buffer = await response.arrayBuffer();
    const tempDir = path.join(__dirname, 'temp');

    // Ensure temp directory exists
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Ensure filename has proper extension for Jiji requirements (.jpg or .png)
    let finalFilename = filename;
    const ext = path.extname(filename).toLowerCase();

    if (ext === '.jpeg') {
      // Convert .jpeg to .jpg for Jiji compatibility
      finalFilename = filename.replace(/\.jpeg$/i, '.jpg');
      this.log(`🔄 Converting .jpeg to .jpg for Jiji compatibility: ${filename} → ${finalFilename}`);
    } else if (ext !== '.jpg' && ext !== '.png') {
      // Default to .jpg if no proper extension
      finalFilename = filename.replace(/\.[^/.]+$/, '') + '.jpg';
      this.log(`⚠️ Converting image extension to .jpg for Jiji compatibility: ${filename} → ${finalFilename}`);
    }

    const tempPath = path.join(tempDir, finalFilename);
    fs.writeFileSync(tempPath, Buffer.from(buffer));

    // Verify image meets Jiji requirements
    await this.verifyImageRequirements(tempPath, imageUrl);

    return tempPath;
  }

  private async verifyImageRequirements(filePath: string, originalUrl: string): Promise<void> {
    try {
      // Check file extension (after conversion)
      const ext = path.extname(filePath).toLowerCase();
      if (ext !== '.jpg' && ext !== '.png') {
        this.log(`❌ Image format error: ${ext} (Jiji requires .jpg or .png, .jpeg should be converted to .jpg)`, 'error');
        throw new Error(`Unsupported image format: ${ext}. Jiji requires .jpg or .png`);
      }

      // Check file size
      const stats = fs.statSync(filePath);
      if (stats.size < 5000) { // Less than 5KB might be too small
        this.log(`⚠️ Image size warning: ${stats.size} bytes (might be too small for Jiji)`, 'warn');
      }

      if (stats.size > 10 * 1024 * 1024) { // Larger than 10MB might be too big
        this.log(`⚠️ Image size warning: ${stats.size} bytes (might be too large for Jiji)`, 'warn');
      }

      this.log(`✅ Image verified: ${path.basename(filePath)} (${Math.round(stats.size/1024)}KB, ${ext})`);
      this.log(`📏 Note: Jiji requires images to be at least 600px wide and in .jpg/.png format`);
      this.log(`🔍 Debug: Original URL: ${originalUrl}`);
      this.log(`🔍 Debug: Final file path: ${filePath}`);
      this.log(`🔍 Debug: File exists: ${fs.existsSync(filePath)}`);
    } catch (error) {
      this.log(`⚠️ Could not verify image requirements: ${error}`, 'warn');
      throw error;
    }
  }

  async uploadImages(images: PartImage[]): Promise<boolean> {
    try {
      this.log(`Uploading ${images.length} images...`);

      // Find image upload input
      const imageInputSelector = 'input[type="file"][accept*="image"], input[type="file"][name*="image"], input[type="file"][name*="photo"]';
      await this.page!.waitForSelector(imageInputSelector, { timeout: 10000 });

      // Download and upload each image
      for (let i = 0; i < Math.min(images.length, 10); i++) { // Limit to 10 images
        const image = images[i];
        try {
          this.log(`Uploading image ${i + 1}/${images.length}: ${image.image_url}`);

          // Download image to temp file
          const tempImagePath = await this.downloadImage(image.image_url, i);

          // Upload the image
          await this.page!.setInputFiles(imageInputSelector, tempImagePath);
          await this.delay(2000); // Wait for upload

          // Clean up temp file
          if (fs.existsSync(tempImagePath)) {
            fs.unlinkSync(tempImagePath);
          }
        } catch (error) {
          this.log(`Failed to upload image ${i + 1}: ${error}`, 'warn');
        }
      }

      this.log('Images uploaded successfully');
      return true;
    } catch (error) {
      this.log(`Failed to upload images: ${error}`, 'error');
      return false;
    }
  }

  private async downloadImage(imageUrl: string, index: number): Promise<string> {
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.statusText}`);
    }

    const buffer = await response.arrayBuffer();
    const extension = path.extname(new URL(imageUrl).pathname) || '.jpg';
    const tempPath = path.join(__dirname, 'temp', `image_${index}_${Date.now()}${extension}`);

    // Ensure temp directory exists
    const tempDir = path.dirname(tempPath);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    fs.writeFileSync(tempPath, Buffer.from(buffer));
    return tempPath;
  }

  async submitListing(): Promise<{ success: boolean; listingUrl?: string; listingId?: string }> {
    try {
      this.log('📤 Submitting listing by clicking "Post ad"...');

      // Find and click "Post ad" button with priority order
      const submitSelectors = [
        'button:has-text("Post ad")',
        'input[type="submit"][value*="Post ad" i]',
        'button[type="submit"]:has-text("Post ad")',
        'button:has-text("Post")',
        'button:has-text("Submit")',
        'button:has-text("Publish")',
        'button[type="submit"]',
        'input[type="submit"]',
        '.submit-btn',
        '.post-btn',
        '.publish-btn'
      ];

      let submitted = false;
      for (const selector of submitSelectors) {
        try {
          await this.page!.waitForSelector(selector, { timeout: 3000 });
          await this.page!.click(selector);
          this.log(`✅ Clicked "Post ad" button using selector: ${selector}`);
          submitted = true;
          break;
        } catch (e) {
          continue;
        }
      }

      if (!submitted) {
        throw new Error('Could not find "Post ad" button');
      }

      // Wait for submission to process
      this.log('⏳ Waiting for listing submission to process...');
      await this.delay(5000);

      // Try to get listing URL and ID from the confirmation page
      const currentUrl = this.page!.url();
      let listingId: string | undefined;
      let listingUrl: string | undefined;

      // Extract listing ID from URL if possible
      const idMatch = currentUrl.match(/\/(\d+)(?:\/|$)/);
      if (idMatch) {
        listingId = idMatch[1];
        listingUrl = currentUrl;
      }

      // Check for success indicators with comprehensive selectors
      const successSelectors = [
        '.success-message',
        '.alert-success',
        '.confirmation',
        '[data-testid="success"]',
        'text="successfully"',
        'text="posted"',
        'text="published"',
        'text="Your ad has been posted"',
        'text="Ad posted successfully"',
        '.listing-success',
        '.post-success'
      ];

      // Look for success message
      for (const selector of successSelectors) {
        try {
          await this.page!.waitForSelector(selector, { timeout: 5000 });
          this.log('✅ Listing submitted successfully - success message found');
          return {
            success: true,
            listingUrl: listingUrl || currentUrl,
            listingId
          };
        } catch (e) {
          continue;
        }
      }

      // Check for error messages
      const errorSelectors = [
        '.error-message',
        '.alert-danger',
        '.alert-error',
        'text="error"',
        'text="failed"',
        '[class*="error"]'
      ];

      for (const selector of errorSelectors) {
        try {
          const errorElement = await this.page!.waitForSelector(selector, { timeout: 2000 });
          if (errorElement) {
            const errorText = await errorElement.textContent();
            this.log(`❌ Submission error detected: ${errorText}`, 'error');
            return { success: false };
          }
        } catch (e) {
          continue;
        }
      }

      // Check if we're on a listing page or success page by URL
      if (currentUrl.includes('success') ||
          currentUrl.includes('posted') ||
          currentUrl.includes('confirmation') ||
          (currentUrl.includes('jiji.co.ke') && !currentUrl.includes('post') && currentUrl !== 'https://jiji.co.ke')) {
        this.log('✅ Listing appears to be submitted successfully (URL indicates success)');
        return {
          success: true,
          listingUrl: currentUrl,
          listingId
        };
      }

      // Default to success if no errors detected and we're not on the post page anymore
      this.log('⚠️ No explicit success message found, but no errors detected - assuming success');
      return {
        success: true,
        listingUrl: currentUrl,
        listingId
      };

    } catch (error) {
      this.log(`❌ Failed to submit listing: ${error}`, 'error');
      return { success: false };
    }
  }

  async getPartsFromDatabase(limit: number = 50): Promise<PartData[]> {
    try {
      this.log(`Fetching ${limit} parts from database...`);

      // Use the database function to get parts ready for listing
      const { data: partsData, error } = await this.supabase
        .rpc('get_parts_ready_for_jiji_listing', { limit_count: limit });

      if (error) {
        throw error;
      }

      if (!partsData || partsData.length === 0) {
        this.log('No parts found ready for listing');
        return [];
      }

      // Get part IDs for efficient querying
      const partIds = partsData.map((part: any) => part.part_id);

      // Fetch images for all parts
      const { data: allImages } = await this.supabase
        .from('part_images')
        .select('part_id, image_url, is_main_image, alt_text')
        .in('part_id', partIds);

      // Fetch conditions and pricing for all parts
      const { data: allConditions } = await this.supabase
        .from('parts_condition')
        .select(`
          id,
          part_id,
          condition,
          stock,
          part_price(price, discounted_price)
        `)
        .in('part_id', partIds)
        .gt('stock', 0);

      // Group images by part_id
      const imagesByPartId: { [key: number]: PartImage[] } = {};
      if (allImages) {
        for (const image of allImages) {
          if (!imagesByPartId[image.part_id]) {
            imagesByPartId[image.part_id] = [];
          }
          imagesByPartId[image.part_id].push({
            id: 0, // Not needed for automation
            part_id: image.part_id,
            image_url: image.image_url,
            is_main_image: image.is_main_image || false
          });
        }
      }

      // Group conditions by part_id
      const conditionsByPartId: { [key: number]: any[] } = {};
      if (allConditions) {
        for (const condition of allConditions) {
          if (!conditionsByPartId[condition.part_id]) {
            conditionsByPartId[condition.part_id] = [];
          }
          conditionsByPartId[condition.part_id].push(condition);
        }
      }

      // Build enriched parts array
      const enrichedParts: PartData[] = [];

      for (const part of partsData) {
        const conditions = conditionsByPartId[part.part_id] || [];
        const images = imagesByPartId[part.part_id] || [];

        // Find the best condition (with stock > 0)
        const availableCondition = conditions.find(c => c.stock > 0) || conditions[0];

        if (availableCondition) {
          // Get the actual price from the database
          const actualPrice = availableCondition.part_price?.[0]?.discounted_price ||
                             availableCondition.part_price?.[0]?.price || 1000;

          // Apply the frontend markup algorithm for Jiji listing
          const jijiPrice = getAdjustedPrice(actualPrice);

          enrichedParts.push({
            id: part.part_id,
            title: part.title,
            description: part.description,
            price: jijiPrice, // Use the marked-up price for Jiji
            condition: availableCondition.condition || 'used',
            category_id: part.category_id,
            category_name: part.category_name || 'Auto Parts',
            parent_category_name: part.parent_category_name || 'Vehicle Parts',
            images: images,
            partnumber_group: part.partnumber_group,
            createdAt: part.created_at,
            updatedAt: part.updated_at
          });
        }
      }

      this.log(`Retrieved ${enrichedParts.length} parts with images and pricing`);
      this.log(`Sample part: ${enrichedParts[0]?.title} - Price: ${enrichedParts[0]?.price} - Images: ${enrichedParts[0]?.images?.length || 0}`);

      return enrichedParts;
    } catch (error) {
      this.log(`Failed to fetch parts from database: ${error}`, 'error');
      return [];
    }
  }

  async getExistingListings(): Promise<Set<number>> {
    try {
      const { data: listings, error } = await this.supabase
        .from('jiji_listings')
        .select('part_id')
        .eq('status', 'listed');

      if (error) {
        throw error;
      }

      return new Set(listings.map((l: any) => l.part_id));
    } catch (error) {
      this.log(`Failed to fetch existing listings: ${error}`, 'error');
      return new Set();
    }
  }

  async saveListingStatus(status: JijiListingStatus): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('jiji_listings')
        .upsert({
          part_id: status.part_id,
          jiji_listing_id: status.jiji_listing_id,
          listing_url: status.listing_url,
          status: status.status,
          error_message: status.error_message,
          listed_at: status.listed_at || new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (error) {
        throw error;
      }
    } catch (error) {
      this.log(`Failed to save listing status: ${error}`, 'error');
    }
  }

  async processPartListing(part: PartData): Promise<boolean> {
    let retries = 0;

    while (retries < this.config.maxRetries) {
      try {
        this.log(`🔄 Processing part ${part.id}: ${part.title} (Attempt ${retries + 1})`);

        const currentUrl = this.page!.url();
        this.log(`📍 Current URL: ${currentUrl}`);

        // Navigate to post ad if not already there
        if (!currentUrl.includes('post') && !currentUrl.includes('sell')) {
          this.log('🚀 Not on post/sell page, calling navigateToPostAd()...');
          const navSuccess = await this.navigateToPostAd(); // This now includes multiselect dropdown interaction
          if (!navSuccess) {
            throw new Error('Failed to navigate to post ad section');
          }
        } else {
          this.log('📍 Already on post/sell page, ensuring category is selected...');
          // If already on post page, we still need to ensure category is selected
          const categorySelected = await this.selectVehiclePartsFromMultiselect();
          if (!categorySelected) {
            this.log('❌ Multiselect category selection failed, trying fallback...', 'warn');
            const fallbackSuccess = await this.selectVehiclePartsCategory();
            if (!fallbackSuccess) {
              this.log('❌ Both category selection methods failed', 'error');
            }
          }
        }

        // Always ensure location is set
        this.log('📍 Setting location...');
        await this.selectLocation();

        // Upload images first (before filling details)
        if (part.images && part.images.length > 0) {
          await this.uploadImagesToJiji(part);
        }

        // Click next to proceed to details form
        await this.clickNextButton();

        // Fill part details
        await this.fillPartDetails(part);

        // Submit listing
        const result = await this.submitListing();

        if (result.success) {
          // Save successful listing
          await this.saveListingStatus({
            part_id: part.id,
            jiji_listing_id: result.listingId,
            listing_url: result.listingUrl,
            status: 'listed',
            listed_at: new Date().toISOString()
          });

          this.log(`Successfully listed part ${part.id}`);
          return true;
        } else {
          throw new Error('Listing submission failed');
        }
      } catch (error) {
        retries++;
        this.log(`Attempt ${retries} failed for part ${part.id}: ${error}`, 'warn');

        if (retries >= this.config.maxRetries) {
          // Save failed listing
          await this.saveListingStatus({
            part_id: part.id,
            status: 'failed',
            error_message: String(error)
          });

          this.log(`Failed to list part ${part.id} after ${this.config.maxRetries} attempts`, 'error');
          return false;
        }

        // Wait before retry
        await this.delay(this.config.delayBetweenActions * 2);
      }
    }

    return false;
  }

  async runBatchProcess(): Promise<void> {
    try {
      this.log('Starting batch processing...');

      // Get parts to process
      const parts = await this.getPartsFromDatabase(this.config.batchSize);

      if (parts.length === 0) {
        this.log('No parts found to process');
        return;
      }

      // Get existing listings to skip
      const existingListings = this.config.skipExisting ? await this.getExistingListings() : new Set();

      // Filter out already listed parts
      const partsToProcess = parts.filter(part => !existingListings.has(part.id));

      this.log(`Processing ${partsToProcess.length} parts (${parts.length - partsToProcess.length} already listed)`);

      let successCount = 0;
      let failureCount = 0;

      for (const part of partsToProcess) {
        if (this.config.testMode) {
          this.log(`[TEST MODE] Testing complete flow for part: ${part.title}`);

          // Create test part with the specific image FIRST
          this.log(`[TEST MODE] 🧪 Testing with specific image: https://excgraelqcvcdsnlvrtv.supabase.co/storage/v1/object/public/car-part-images/8d1c6770-9261-44e7-829b-fa47463a5b44/part_image_1745318131270_nkvlcfcy.jpeg`);

          const testPart = {
            ...part,
            images: [{
              id: 1,
              part_id: part.id,
              image_url: 'https://excgraelqcvcdsnlvrtv.supabase.co/storage/v1/object/public/car-part-images/8d1c6770-9261-44e7-829b-fa47463a5b44/part_image_1745318131270_nkvlcfcy.jpeg',
              is_main_image: true
            }]
          };

          // Test navigation to post ad section (now includes multiselect category selection)
          const navSuccess = await this.navigateToPostAd();
          if (navSuccess) {
            this.log(`[TEST MODE] ✅ Successfully navigated to Post Ad section`);

            // Test location selection
            const locationSuccess = await this.selectLocation();
            if (locationSuccess) {
              this.log(`[TEST MODE] ✅ Successfully set location to Nairobi → Langata`);

              // Test image upload with specific test image
              // First, clear any existing images to test upload functionality
              await this.clearExistingImages();

              const imageSuccess = await this.uploadImagesToJiji(testPart);
                if (imageSuccess) {
                  this.log(`[TEST MODE] ✅ Successfully uploaded test image to Jiji`);

                  // The uploadImagesToJiji method already handles the next button click
                  // So we can proceed directly to form filling
                  this.log(`[TEST MODE] 🖊️ Testing form filling with part details...`);
                  const formSuccess = await this.fillPartDetails(testPart);
                  if (formSuccess) {
                    this.log(`[TEST MODE] ✅ Successfully filled part details form`);

                    // Test submission
                    this.log(`[TEST MODE] 📤 Testing form submission...`);
                    const submitSuccess = await this.submitListing();
                    if (submitSuccess.success) {
                      this.log(`[TEST MODE] ✅ Successfully submitted listing!`);
                      if (submitSuccess.listingUrl) {
                        this.log(`[TEST MODE] 🔗 Listing URL: ${submitSuccess.listingUrl}`);
                      }
                      this.log(`[TEST MODE] 🎉 COMPLETE END-TO-END FLOW TEST SUCCESSFUL!`);
                    } else {
                      this.log(`[TEST MODE] ⚠️ Form submission failed`);
                    }
                  } else {
                    this.log(`[TEST MODE] ⚠️ Form filling failed`);
                  }
                } else {
                  this.log(`[TEST MODE] ⚠️ Test image upload failed`);
                }
              } else {
                this.log(`[TEST MODE] ⚠️ Location selection failed`);
              }
            } else {
              this.log(`[TEST MODE] ❌ Navigation to Post Ad failed`);
            }

          continue;
        }

        const success = await this.processPartListing(part);

        if (success) {
          successCount++;
        } else {
          failureCount++;
        }

        // Delay between parts to avoid being blocked
        await this.delay(this.config.delayBetweenActions);
      }

      this.log(`Batch processing completed. Success: ${successCount}, Failed: ${failureCount}`);
    } catch (error) {
      this.log(`Batch processing failed: ${error}`, 'error');
    }
  }

  async cleanup(): Promise<void> {
    try {
      // Clean up temp files
      const tempDir = path.join(__dirname, 'temp');
      if (fs.existsSync(tempDir)) {
        const files = fs.readdirSync(tempDir);
        for (const file of files) {
          fs.unlinkSync(path.join(tempDir, file));
        }
      }

      // Close browser
      if (this.browser) {
        await this.browser.close();
        this.log('Browser closed');
      }
    } catch (error) {
      this.log(`Cleanup failed: ${error}`, 'error');
    }
  }
}

export { JijiAutomation };
export type { PartData, JijiListingStatus, AutomationConfig };
