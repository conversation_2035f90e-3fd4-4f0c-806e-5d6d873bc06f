// components/FormSections/FormButtons.tsx
import React from 'react';

interface FormButtonsProps {
  isSubmitting: boolean;
  onCancel: () => void;
}

export default function FormButtons({ isSubmitting, onCancel }: FormButtonsProps) {
  return (
    <div className="flex justify-end space-x-4">
      <button
        type="button"
        className="btn-secondary"
        onClick={onCancel}
      >
        Cancel
      </button>
      <button
        type="submit"
        className="btn-primary"
        disabled={isSubmitting}
      >
        {isSubmitting ? 'Submitting...' : 'Submit'}
      </button>
    </div>
  );
}