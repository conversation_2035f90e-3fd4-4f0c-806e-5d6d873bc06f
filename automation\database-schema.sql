-- Create table to track Jiji listings
CREATE TABLE IF NOT EXISTS jiji_listings (
  id SERIAL PRIMARY KEY,
  part_id INTEGER NOT NULL REFERENCES parts(part_id) ON DELETE CASCADE,
  jiji_listing_id VARCHAR(255),
  listing_url TEXT,
  status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'listed', 'failed', 'updated', 'removed')),
  error_message TEXT,
  listed_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one listing per part
  UNIQUE(part_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_jiji_listings_part_id ON jiji_listings(part_id);
CREATE INDEX IF NOT EXISTS idx_jiji_listings_status ON jiji_listings(status);
CREATE INDEX IF NOT EXISTS idx_jiji_listings_listed_at ON jiji_listings(listed_at);

-- <PERSON>reate trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_jiji_listings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_jiji_listings_updated_at
  BEFORE UPDATE ON jiji_listings
  FOR EACH ROW
  EXECUTE FUNCTION update_jiji_listings_updated_at();

-- Create view for listing statistics
CREATE OR REPLACE VIEW jiji_listing_stats AS
SELECT 
  status,
  COUNT(*) as count,
  COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() as percentage
FROM jiji_listings 
GROUP BY status;

-- Function to get parts ready for listing (with images and pricing)
CREATE OR REPLACE FUNCTION get_parts_ready_for_jiji_listing(limit_count INTEGER DEFAULT 50)
RETURNS TABLE (
  part_id INTEGER,
  title TEXT,
  description TEXT,
  price DECIMAL,
  condition TEXT,
  category_id INTEGER,
  category_name TEXT,
  image_count INTEGER,
  has_main_image BOOLEAN,
  partnumber_group TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.part_id as part_id,
    p.title,
    p.description,
    COALESCE(pp.discounted_price, pp.price, 0) as price,
    COALESCE(pc.condition, 'used') as condition,
    p.category_id,
    c.name as category_name,
    COALESCE(img_count.count, 0) as image_count,
    COALESCE(img_count.has_main, false) as has_main_image,
    p.partnumber_group,
    p."createdAt" as created_at,
    p."updatedAt" as updated_at
  FROM parts p
  LEFT JOIN categories c ON p.category_id = c.id
  LEFT JOIN parts_condition pc ON p.id = pc.part_id AND pc.stock > 0
  LEFT JOIN part_price pp ON pc.id = pp.condition_id
  LEFT JOIN (
    SELECT 
      part_id,
      COUNT(*) as count,
      BOOL_OR(is_main_image) as has_main
    FROM part_images 
    GROUP BY part_id
  ) img_count ON p.id = img_count.part_id
  LEFT JOIN jiji_listings jl ON p.id = jl.part_id
  WHERE 
    p.title IS NOT NULL 
    AND p.title != ''
    AND pc.stock > 0
    AND (jl.id IS NULL OR jl.status = 'failed') -- Not listed or failed listings
  ORDER BY 
    img_count.count DESC, -- Prioritize parts with more images
    pp.price DESC, -- Higher priced items first
    p."updatedAt" DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get listing performance metrics
CREATE OR REPLACE FUNCTION get_jiji_listing_metrics()
RETURNS TABLE (
  total_parts INTEGER,
  listed_parts INTEGER,
  failed_parts INTEGER,
  pending_parts INTEGER,
  success_rate DECIMAL,
  avg_listing_time INTERVAL,
  last_listing_date TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT COUNT(*)::INTEGER FROM parts WHERE title IS NOT NULL AND title != '') as total_parts,
    (SELECT COUNT(*)::INTEGER FROM jiji_listings WHERE status = 'listed') as listed_parts,
    (SELECT COUNT(*)::INTEGER FROM jiji_listings WHERE status = 'failed') as failed_parts,
    (SELECT COUNT(*)::INTEGER FROM jiji_listings WHERE status = 'pending') as pending_parts,
    CASE 
      WHEN (SELECT COUNT(*) FROM jiji_listings WHERE status IN ('listed', 'failed')) > 0 
      THEN (SELECT COUNT(*)::DECIMAL FROM jiji_listings WHERE status = 'listed') * 100.0 / 
           (SELECT COUNT(*) FROM jiji_listings WHERE status IN ('listed', 'failed'))
      ELSE 0
    END as success_rate,
    (SELECT AVG(listed_at - created_at) FROM jiji_listings WHERE status = 'listed' AND listed_at IS NOT NULL) as avg_listing_time,
    (SELECT MAX(listed_at) FROM jiji_listings WHERE status = 'listed') as last_listing_date;
END;
$$ LANGUAGE plpgsql;

-- Create automation log table
CREATE TABLE IF NOT EXISTS jiji_automation_logs (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  level VARCHAR(10) NOT NULL CHECK (level IN ('info', 'warn', 'error')),
  message TEXT NOT NULL,
  part_id INTEGER REFERENCES parts(id),
  error_details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for automation logs
CREATE INDEX IF NOT EXISTS idx_jiji_automation_logs_session_id ON jiji_automation_logs(session_id);
CREATE INDEX IF NOT EXISTS idx_jiji_automation_logs_level ON jiji_automation_logs(level);
CREATE INDEX IF NOT EXISTS idx_jiji_automation_logs_created_at ON jiji_automation_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_jiji_automation_logs_part_id ON jiji_automation_logs(part_id);

-- Function to clean up old logs (keep last 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_jiji_logs()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM jiji_automation_logs 
  WHERE created_at < NOW() - INTERVAL '30 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to clean up logs (if pg_cron is available)
-- SELECT cron.schedule('cleanup-jiji-logs', '0 2 * * *', 'SELECT cleanup_old_jiji_logs();');
