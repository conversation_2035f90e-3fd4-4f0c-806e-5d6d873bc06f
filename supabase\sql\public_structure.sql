/*
 Navicat Premium Data Transfer

 Source Server         : autoflow
 Source Server Type    : PostgreSQL
 Source Server Version : 150008 (150008)
 Source Host           : aws-0-eu-central-1.pooler.supabase.com:6543
 Source Catalog        : postgres
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 150008 (150008)
 File Encoding         : 65001

 Date: 26/04/2025 05:54:09
*/


-- ----------------------------
-- Type structure for condition_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."condition_enum";
CREATE TYPE "public"."condition_enum" AS ENUM (
  'new',
  'used'
);
ALTER TYPE "public"."condition_enum" OWNER TO "postgres";

-- ----------------------------
-- Type structure for level_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."level_type_enum";
CREATE TYPE "public"."level_type_enum" AS ENUM (
  'upstairs',
  'downstairs',
  'ground'
);
ALTER TYPE "public"."level_type_enum" OWNER TO "postgres";

-- ----------------------------
-- Type structure for location_subtype_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."location_subtype_enum";
CREATE TYPE "public"."location_subtype_enum" AS ENUM (
  'crate',
  'container',
  'shelf_section',
  'open_shelf',
  'cage_section',
  'hanging_point',
  'open_area_spot'
);
ALTER TYPE "public"."location_subtype_enum" OWNER TO "postgres";

-- ----------------------------
-- Type structure for location_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."location_type_enum";
CREATE TYPE "public"."location_type_enum" AS ENUM (
  'indoor',
  'outdoor'
);
ALTER TYPE "public"."location_type_enum" OWNER TO "postgres";

-- ----------------------------
-- Type structure for storage_unit_type_enum
-- ----------------------------
DROP TYPE IF EXISTS "public"."storage_unit_type_enum";
CREATE TYPE "public"."storage_unit_type_enum" AS ENUM (
  'shelf',
  'cage',
  'hanging_line',
  'open_space',
  'engine_area'
);
ALTER TYPE "public"."storage_unit_type_enum" OWNER TO "postgres";

-- ----------------------------
-- Sequence structure for car_part_categories_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."car_part_categories_id_seq";
CREATE SEQUENCE "public"."car_part_categories_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for engine_families_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."engine_families_id_seq";
CREATE SEQUENCE "public"."engine_families_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for engines_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."engines_id_seq";
CREATE SEQUENCE "public"."engines_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for part_compatibility_groups_group_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."part_compatibility_groups_group_id_seq";
CREATE SEQUENCE "public"."part_compatibility_groups_group_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for part_images_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."part_images_id_seq";
CREATE SEQUENCE "public"."part_images_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for part_locations_location_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."part_locations_location_id_seq";
CREATE SEQUENCE "public"."part_locations_location_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for part_price_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."part_price_id_seq";
CREATE SEQUENCE "public"."part_price_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for parts_car_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."parts_car_id_seq";
CREATE SEQUENCE "public"."parts_car_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for parts_category_attribute_input_option_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."parts_category_attribute_input_option_id_seq";
CREATE SEQUENCE "public"."parts_category_attribute_input_option_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for parts_category_attributes_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."parts_category_attributes_id_seq";
CREATE SEQUENCE "public"."parts_category_attributes_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for parts_condition_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."parts_condition_id_seq";
CREATE SEQUENCE "public"."parts_condition_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for parts_part_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."parts_part_id_seq";
CREATE SEQUENCE "public"."parts_part_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for storage_areas_area_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."storage_areas_area_id_seq";
CREATE SEQUENCE "public"."storage_areas_area_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for storage_units_unit_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."storage_units_unit_id_seq";
CREATE SEQUENCE "public"."storage_units_unit_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for variation_trim_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."variation_trim_id_seq";
CREATE SEQUENCE "public"."variation_trim_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Table structure for audit_log
-- ----------------------------
DROP TABLE IF EXISTS "public"."audit_log";
CREATE TABLE "public"."audit_log" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "timestamp" timestamptz(6) NOT NULL DEFAULT now(),
  "user_id" uuid,
  "action_type" text COLLATE "pg_catalog"."default" NOT NULL,
  "target_table" text COLLATE "pg_catalog"."default",
  "target_record_id" text COLLATE "pg_catalog"."default",
  "change_details" jsonb,
  "ip_address" inet
)
;
COMMENT ON COLUMN "public"."audit_log"."user_id" IS 'The user (from profiles table) who performed the action. NULL for system actions.';
COMMENT ON COLUMN "public"."audit_log"."change_details" IS 'JSONB field to store specifics about the change, like old and new values.';
COMMENT ON TABLE "public"."audit_log" IS 'Logs changes to permissions, roles, and assignments for auditing purposes.';

-- ----------------------------
-- Table structure for car_brands
-- ----------------------------
DROP TABLE IF EXISTS "public"."car_brands";
CREATE TABLE "public"."car_brands" (
  "brand_id" int4 NOT NULL,
  "brand_name" varchar(50) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for car_generation
-- ----------------------------
DROP TABLE IF EXISTS "public"."car_generation";
CREATE TABLE "public"."car_generation" (
  "id" int4 NOT NULL,
  "model_id" int4,
  "name" text COLLATE "pg_catalog"."default",
  "start_production_year" int4,
  "end_production_year" int4
)
;

-- ----------------------------
-- Table structure for car_models
-- ----------------------------
DROP TABLE IF EXISTS "public"."car_models";
CREATE TABLE "public"."car_models" (
  "id" int4 NOT NULL,
  "brand_id" int4,
  "model_name" varchar(100) COLLATE "pg_catalog"."default",
  "model_image" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for car_part_categories
-- ----------------------------
DROP TABLE IF EXISTS "public"."car_part_categories";
CREATE TABLE "public"."car_part_categories" (
  "id" int4 NOT NULL DEFAULT nextval('car_part_categories_id_seq'::regclass),
  "label" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "href" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "icon" varchar(255) COLLATE "pg_catalog"."default",
  "library" varchar(255) COLLATE "pg_catalog"."default",
  "parent_category_id" int4,
  "isActive" bool,
  "requirePartNumber" bool DEFAULT true,
  "isEnginePart" bool DEFAULT false
)
;

-- ----------------------------
-- Table structure for car_variation
-- ----------------------------
DROP TABLE IF EXISTS "public"."car_variation";
CREATE TABLE "public"."car_variation" (
  "id" int4 NOT NULL,
  "generation_id" int4,
  "variation" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for category_fields
-- ----------------------------
DROP TABLE IF EXISTS "public"."category_fields";
CREATE TABLE "public"."category_fields" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "category_id" uuid NOT NULL,
  "field_name" text COLLATE "pg_catalog"."default" NOT NULL,
  "field_label" text COLLATE "pg_catalog"."default" NOT NULL,
  "field_type" text COLLATE "pg_catalog"."default" NOT NULL DEFAULT 'text'::text,
  "is_required" bool NOT NULL DEFAULT false,
  "description" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON COLUMN "public"."category_fields"."category_id" IS 'Links to the client category this field belongs to.';
COMMENT ON COLUMN "public"."category_fields"."field_name" IS 'Internal name/key for the field (use snake_case).';
COMMENT ON COLUMN "public"."category_fields"."field_label" IS 'User-friendly label for the field.';
COMMENT ON COLUMN "public"."category_fields"."field_type" IS 'Suggested data type for the field value (e.g., text, number, date, boolean). Application layer should handle validation/casting.';
COMMENT ON COLUMN "public"."category_fields"."is_required" IS 'Indicates if this field must have a value for clients in this category.';
COMMENT ON TABLE "public"."category_fields" IS 'Defines the specific data fields required for each client category.';

-- ----------------------------
-- Table structure for client_categories
-- ----------------------------
DROP TABLE IF EXISTS "public"."client_categories";
CREATE TABLE "public"."client_categories" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "name" text COLLATE "pg_catalog"."default" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON COLUMN "public"."client_categories"."name" IS 'Unique name of the category (e.g., Personal, Garage, Shop).';
COMMENT ON COLUMN "public"."client_categories"."description" IS 'Optional description of the client category.';
COMMENT ON TABLE "public"."client_categories" IS 'Stores the different categories a client can belong to.';

-- ----------------------------
-- Table structure for client_data
-- ----------------------------
DROP TABLE IF EXISTS "public"."client_data";
CREATE TABLE "public"."client_data" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "client_id" uuid NOT NULL,
  "field_id" uuid NOT NULL,
  "value" text COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON COLUMN "public"."client_data"."client_id" IS 'Links to the client this data belongs to.';
COMMENT ON COLUMN "public"."client_data"."field_id" IS 'Links to the specific field definition in category_fields.';
COMMENT ON COLUMN "public"."client_data"."value" IS 'The actual value for the field, stored as text.';
COMMENT ON TABLE "public"."client_data" IS 'Stores the specific data values for each client based on their category fields (EAV model).';

-- ----------------------------
-- Table structure for client_profiles
-- ----------------------------
DROP TABLE IF EXISTS "public"."client_profiles";
CREATE TABLE "public"."client_profiles" (
  "client_id" uuid NOT NULL,
  "profile_id" uuid NOT NULL,
  "linked_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON COLUMN "public"."client_profiles"."client_id" IS 'Reference to the client record.';
COMMENT ON COLUMN "public"."client_profiles"."profile_id" IS 'Reference to the user profile record.';
COMMENT ON COLUMN "public"."client_profiles"."linked_at" IS 'Timestamp when the client was linked to the profile.';
COMMENT ON TABLE "public"."client_profiles" IS 'Junction table linking clients to their user profiles for login purposes.';

-- ----------------------------
-- Table structure for clients
-- ----------------------------
DROP TABLE IF EXISTS "public"."clients";
CREATE TABLE "public"."clients" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "category_id" uuid NOT NULL,
  "name" text COLLATE "pg_catalog"."default",
  "phone_number" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "can_receive_credit" bool NOT NULL DEFAULT false,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON COLUMN "public"."clients"."category_id" IS 'The category this client belongs to.';
COMMENT ON COLUMN "public"."clients"."name" IS 'Optional name of the client (individual or company).';
COMMENT ON COLUMN "public"."clients"."phone_number" IS 'Primary phone number for the client, assumed unique.';
COMMENT ON COLUMN "public"."clients"."can_receive_credit" IS 'Flag indicating if the client is eligible for credit (default: false).';
COMMENT ON TABLE "public"."clients" IS 'Stores core information for all clients.';

-- ----------------------------
-- Table structure for engine_families
-- ----------------------------
DROP TABLE IF EXISTS "public"."engine_families";
CREATE TABLE "public"."engine_families" (
  "engine_id" int4 NOT NULL,
  "engine_family_code_id" int4 NOT NULL
)
;

-- ----------------------------
-- Table structure for engine_family_codes
-- ----------------------------
DROP TABLE IF EXISTS "public"."engine_family_codes";
CREATE TABLE "public"."engine_family_codes" (
  "id" int4 NOT NULL DEFAULT nextval('engine_families_id_seq'::regclass),
  "code" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "generation" varchar(50) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for engines
-- ----------------------------
DROP TABLE IF EXISTS "public"."engines";
CREATE TABLE "public"."engines" (
  "id" int4 NOT NULL DEFAULT nextval('engines_id_seq'::regclass),
  "capacity" varchar(10) COLLATE "pg_catalog"."default",
  "engine_code" varchar(10) COLLATE "pg_catalog"."default",
  "fuel_type" varchar(10) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for part_compatibility_groups
-- ----------------------------
DROP TABLE IF EXISTS "public"."part_compatibility_groups";
CREATE TABLE "public"."part_compatibility_groups" (
  "id" int4 NOT NULL DEFAULT nextval('part_compatibility_groups_group_id_seq'::regclass),
  "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
  "part_number" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for part_images
-- ----------------------------
DROP TABLE IF EXISTS "public"."part_images";
CREATE TABLE "public"."part_images" (
  "id" int4 NOT NULL DEFAULT nextval('part_images_id_seq'::regclass),
  "part_id" int4 NOT NULL,
  "image_url" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "alt_text" varchar(255) COLLATE "pg_catalog"."default",
  "is_main_image" bool DEFAULT false
)
;

-- ----------------------------
-- Table structure for part_locations
-- ----------------------------
DROP TABLE IF EXISTS "public"."part_locations";
CREATE TABLE "public"."part_locations" (
  "location_id" int4 NOT NULL DEFAULT nextval('part_locations_location_id_seq'::regclass),
  "part_id" int4 NOT NULL,
  "unit_id" int4 NOT NULL,
  "quantity" int4 NOT NULL DEFAULT 1,
  "location_subtype" "public"."location_subtype_enum" NOT NULL,
  "details" jsonb,
  "notes" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
)
;

-- ----------------------------
-- Table structure for part_price
-- ----------------------------
DROP TABLE IF EXISTS "public"."part_price";
CREATE TABLE "public"."part_price" (
  "id" int4 NOT NULL DEFAULT nextval('part_price_id_seq'::regclass),
  "condition_id" int4,
  "price" numeric,
  "discounted_price" numeric
)
;

-- ----------------------------
-- Table structure for part_to_group
-- ----------------------------
DROP TABLE IF EXISTS "public"."part_to_group";
CREATE TABLE "public"."part_to_group" (
  "partnumber" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "group_id" int4,
  "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP
)
;

-- ----------------------------
-- Table structure for parts
-- ----------------------------
DROP TABLE IF EXISTS "public"."parts";
CREATE TABLE "public"."parts" (
  "part_id" int4 NOT NULL DEFAULT nextval('parts_part_id_seq'::regclass),
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "sku" varchar(100) COLLATE "pg_catalog"."default",
  "category" varchar(100) COLLATE "pg_catalog"."default",
  "description" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
)
;

-- ----------------------------
-- Table structure for parts_car
-- ----------------------------
DROP TABLE IF EXISTS "public"."parts_car";
CREATE TABLE "public"."parts_car" (
  "id" int4 NOT NULL GENERATED BY DEFAULT AS IDENTITY (
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1
),
  "variation_trim_id" int4,
  "part_id" int4
)
;

-- ----------------------------
-- Table structure for parts_category_attribute_input_option
-- ----------------------------
DROP TABLE IF EXISTS "public"."parts_category_attribute_input_option";
CREATE TABLE "public"."parts_category_attribute_input_option" (
  "id" int4 NOT NULL DEFAULT nextval('parts_category_attribute_input_option_id_seq'::regclass),
  "attribute_id" int4,
  "option_value" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
)
;

-- ----------------------------
-- Table structure for parts_category_attribute_values
-- ----------------------------
DROP TABLE IF EXISTS "public"."parts_category_attribute_values";
CREATE TABLE "public"."parts_category_attribute_values" (
  "part_id" int4 NOT NULL,
  "attribute_id" int4 NOT NULL,
  "value" varchar(255) COLLATE "pg_catalog"."default",
  "selection_value" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for parts_category_attributes
-- ----------------------------
DROP TABLE IF EXISTS "public"."parts_category_attributes";
CREATE TABLE "public"."parts_category_attributes" (
  "id" int4 NOT NULL DEFAULT nextval('parts_category_attributes_id_seq'::regclass),
  "category_id" int4,
  "attribute" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "input_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "depends_on_attribute_id" int4,
  "depends_on_option_id" int4
)
;
COMMENT ON COLUMN "public"."parts_category_attributes"."depends_on_attribute_id" IS 'The ID of the attribute this attribute depends on (within the same category). NULL if independent.';
COMMENT ON COLUMN "public"."parts_category_attributes"."depends_on_option_id" IS 'The ID of the option (from parts_category_attribute_input_option) of the parent attribute that must be selected for this attribute to be active. NULL if independent.';

-- ----------------------------
-- Table structure for parts_condition
-- ----------------------------
DROP TABLE IF EXISTS "public"."parts_condition";
CREATE TABLE "public"."parts_condition" (
  "id" int4 NOT NULL DEFAULT nextval('parts_condition_id_seq'::regclass),
  "part_id" int4,
  "condition" varchar(255) COLLATE "pg_catalog"."default",
  "stock" int4 DEFAULT 0
)
;

-- ----------------------------
-- Table structure for parts_engines
-- ----------------------------
DROP TABLE IF EXISTS "public"."parts_engines";
CREATE TABLE "public"."parts_engines" (
  "part_id" int4 NOT NULL,
  "engine_id" int4 NOT NULL
)
;

-- ----------------------------
-- Table structure for permissions
-- ----------------------------
DROP TABLE IF EXISTS "public"."permissions";
CREATE TABLE "public"."permissions" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "name" text COLLATE "pg_catalog"."default" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "category" text COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON COLUMN "public"."permissions"."name" IS 'Unique permission identifier, recommended format: category:action (e.g., parts:create)';
COMMENT ON COLUMN "public"."permissions"."category" IS 'Logical grouping for UI presentation (e.g., Parts Management)';
COMMENT ON TABLE "public"."permissions" IS 'Stores the granular permissions available in the system (e.g., parts:create, users:read).';

-- ----------------------------
-- Table structure for profiles
-- ----------------------------
DROP TABLE IF EXISTS "public"."profiles";
CREATE TABLE "public"."profiles" (
  "id" uuid NOT NULL,
  "full_name" text COLLATE "pg_catalog"."default",
  "avatar_url" text COLLATE "pg_catalog"."default",
  "email" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now(),
  "idle_timeout" bool DEFAULT false,
  "logout_only" bool DEFAULT false,
  "phone" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for role_permissions
-- ----------------------------
DROP TABLE IF EXISTS "public"."role_permissions";
CREATE TABLE "public"."role_permissions" (
  "role_id" uuid NOT NULL,
  "permission_id" uuid NOT NULL,
  "assigned_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON TABLE "public"."role_permissions" IS 'Links roles to the permissions granted by that role.';

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS "public"."roles";
CREATE TABLE "public"."roles" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "name" text COLLATE "pg_catalog"."default" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON TABLE "public"."roles" IS 'Stores the different roles users can have (e.g., Employee, Supplier, Super Admin).';

-- ----------------------------
-- Table structure for storage_areas
-- ----------------------------
DROP TABLE IF EXISTS "public"."storage_areas";
CREATE TABLE "public"."storage_areas" (
  "area_id" int4 NOT NULL DEFAULT nextval('storage_areas_area_id_seq'::regclass),
  "name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "location_type" "public"."location_type_enum" NOT NULL,
  "level" "public"."level_type_enum" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
)
;

-- ----------------------------
-- Table structure for storage_units
-- ----------------------------
DROP TABLE IF EXISTS "public"."storage_units";
CREATE TABLE "public"."storage_units" (
  "unit_id" int4 NOT NULL DEFAULT nextval('storage_units_unit_id_seq'::regclass),
  "area_id" int4 NOT NULL,
  "unit_type" "public"."storage_unit_type_enum" NOT NULL,
  "identifier" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "description" text COLLATE "pg_catalog"."default",
  "created_at" timestamptz(6) DEFAULT now(),
  "updated_at" timestamptz(6) DEFAULT now()
)
;

-- ----------------------------
-- Table structure for trim_engines
-- ----------------------------
DROP TABLE IF EXISTS "public"."trim_engines";
CREATE TABLE "public"."trim_engines" (
  "trim_id" int4 NOT NULL,
  "engine_id" int4 NOT NULL
)
;

-- ----------------------------
-- Table structure for user_permissions
-- ----------------------------
DROP TABLE IF EXISTS "public"."user_permissions";
CREATE TABLE "public"."user_permissions" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "permission_id" uuid NOT NULL,
  "has_permission" bool NOT NULL,
  "created_at" timestamptz(6) NOT NULL DEFAULT now(),
  "updated_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON COLUMN "public"."user_permissions"."has_permission" IS 'TRUE indicates an explicit grant, FALSE indicates an explicit denial, overriding role permissions.';
COMMENT ON TABLE "public"."user_permissions" IS 'Stores user-specific permission overrides (explicit grants or denies).';

-- ----------------------------
-- Table structure for user_roles
-- ----------------------------
DROP TABLE IF EXISTS "public"."user_roles";
CREATE TABLE "public"."user_roles" (
  "user_id" uuid NOT NULL,
  "role_id" uuid NOT NULL,
  "assigned_at" timestamptz(6) NOT NULL DEFAULT now()
)
;
COMMENT ON TABLE "public"."user_roles" IS 'Links users (from profiles table) to their assigned roles.';

-- ----------------------------
-- Table structure for variation_trim
-- ----------------------------
DROP TABLE IF EXISTS "public"."variation_trim";
CREATE TABLE "public"."variation_trim" (
  "id" int4 NOT NULL GENERATED BY DEFAULT AS IDENTITY (
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1
),
  "variation_id" int4,
  "trim" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Function structure for add_part_with_compatibles
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."add_part_with_compatibles"("new_partnumber" varchar, "compatible_partnumbers" _varchar);
CREATE OR REPLACE FUNCTION "public"."add_part_with_compatibles"("new_partnumber" varchar, "compatible_partnumbers" _varchar)
  RETURNS "pg_catalog"."void" AS $BODY$
DECLARE
    existing_group_id INTEGER;
    new_group_id INTEGER;
    compatible_partnumber VARCHAR(20);
BEGIN
    -- Find if any of the part numbers already belong to a group
    SELECT group_id INTO existing_group_id 
    FROM part_to_group 
    WHERE partnumber = new_partnumber 
       OR partnumber = ANY(compatible_partnumbers)
    LIMIT 1;
    
    -- If no existing group found, create a new group
    IF existing_group_id IS NULL THEN
        INSERT INTO part_compatibility_groups DEFAULT VALUES
        RETURNING group_id INTO new_group_id;
        existing_group_id := new_group_id;
    END IF;
    
    -- Add the new part to the group
    INSERT INTO part_to_group (partnumber, group_id)
    VALUES (new_partnumber, existing_group_id)
    ON CONFLICT (partnumber) 
    DO UPDATE SET group_id = existing_group_id;
    
    -- Add all compatible parts to the same group
    FOREACH compatible_partnumber IN ARRAY compatible_partnumbers
    LOOP
        INSERT INTO part_to_group (partnumber, group_id)
        VALUES (compatible_partnumber, existing_group_id)
        ON CONFLICT (partnumber) 
        DO UPDATE SET group_id = existing_group_id;
    END LOOP;
    
    -- Merge any other groups that now conflict
    WITH groups_to_merge AS (
        SELECT DISTINCT p2g.group_id
        FROM part_to_group p2g
        WHERE p2g.partnumber IN (SELECT unnest(ARRAY[new_partnumber] || compatible_partnumbers))
          AND p2g.group_id != existing_group_id
    )
    UPDATE part_to_group
    SET group_id = existing_group_id
    WHERE group_id IN (SELECT group_id FROM groups_to_merge);
    
    -- Clean up empty groups (optional)
    DELETE FROM part_compatibility_groups
    WHERE group_id NOT IN (SELECT DISTINCT group_id FROM part_to_group);
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- Function structure for check_user_permission
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."check_user_permission"("p_user_id" uuid, "p_permission_name" text);
CREATE OR REPLACE FUNCTION "public"."check_user_permission"("p_user_id" uuid, "p_permission_name" text)
  RETURNS "pg_catalog"."bool" AS $BODY$
DECLARE
    v_permission_id uuid;
    v_has_override boolean;
    v_override_value boolean;
    v_has_role_permission boolean;
BEGIN
    -- Get the permission ID from the name
    SELECT id INTO v_permission_id
    FROM public.permissions
    WHERE name = p_permission_name;
    
    -- If permission doesn't exist, return false
    IF v_permission_id IS NULL THEN
        RETURN false;
    END IF;
    
    -- Check for user-specific override
    SELECT EXISTS (
        SELECT 1
        FROM public.user_permissions
        WHERE user_id = p_user_id AND permission_id = v_permission_id
    ) INTO v_has_override;
    
    -- If override exists, return its value
    IF v_has_override THEN
        SELECT has_permission INTO v_override_value
        FROM public.user_permissions
        WHERE user_id = p_user_id AND permission_id = v_permission_id;
        
        RETURN v_override_value;
    END IF;
    
    -- Check if any of the user's roles grant this permission
    SELECT EXISTS (
        SELECT 1
        FROM public.user_roles ur
        JOIN public.role_permissions rp ON ur.role_id = rp.role_id
        WHERE ur.user_id = p_user_id AND rp.permission_id = v_permission_id
    ) INTO v_has_role_permission;
    
    RETURN v_has_role_permission;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER
  COST 100;

-- ----------------------------
-- Function structure for delete_part_and_references
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."delete_part_and_references"("p_part_id" int4);
CREATE OR REPLACE FUNCTION "public"."delete_part_and_references"("p_part_id" int4)
  RETURNS "pg_catalog"."void" AS $BODY$
DECLARE
    v_condition_ids INTEGER[]; -- To hold condition IDs related to the part
    v_image_urls TEXT[]; -- To hold image URLs to be deleted from storage
    v_image_url TEXT; -- Loop variable for image URLs
    v_object_path TEXT; -- Extracted object path from URL
    v_bucket_name TEXT := 'car-part-images'; -- Your bucket name
    v_project_ref TEXT := 'excgraelqcvcdsnlvrtv'; -- Your Supabase project reference
    v_supabase_url TEXT := 'https://' || v_project_ref || '.supabase.co';
    v_storage_api_url TEXT;
    v_service_role_key TEXT;
    v_request_id BIGINT; -- To store the request ID from pg_net
BEGIN
    -- Check if the part exists
    IF NOT EXISTS (SELECT 1 FROM public.parts WHERE id = p_part_id) THEN
        RAISE NOTICE 'Part with ID % does not exist.', p_part_id;
        RETURN;
    END IF;

    RAISE NOTICE 'Starting deletion process for part ID %...', p_part_id;

    -- 0. Get the Service Role Key from Supabase Vault
    BEGIN
        SELECT decrypted_secret INTO v_service_role_key
        FROM vault.decrypted_secrets
        WHERE name = 'supabase_service_role_key'; -- Ensure this matches the name in Vault

        IF v_service_role_key IS NULL THEN
            RAISE EXCEPTION 'Supabase Service Role Key named "supabase_service_role_key" not found in Vault.';
        END IF;
    EXCEPTION
        WHEN insufficient_privilege THEN
            RAISE EXCEPTION 'The executing role lacks permission to access Vault secrets. Grant usage on schema `vault` and select on `vault.decrypted_secrets`, or use SECURITY DEFINER with an owner that has these privileges.';
        WHEN others THEN
            RAISE EXCEPTION 'Error retrieving Service Role Key from Vault: %', SQLERRM;
    END;


    -- 1a. Get image URLs before deleting records from part_images table
    RAISE NOTICE 'Fetching image URLs for part ID %...', p_part_id;
    SELECT array_agg(image_url) INTO v_image_urls
    FROM public.part_images
    WHERE part_id = p_part_id;

    -- 1b. Delete images from Supabase Storage using pg_net
    IF v_image_urls IS NOT NULL THEN
        RAISE NOTICE 'Attempting to delete % associated images from Supabase Storage bucket "%"...', array_length(v_image_urls, 1), v_bucket_name;
        FOREACH v_image_url IN ARRAY v_image_urls LOOP
            -- Extract the object path after the bucket name from the URL
            BEGIN
                 -- This regex extracts the part of the path *after* '/public/bucket_name/' or '/bucket_name/'
                 v_object_path := substring(v_image_url from '/(?:public/)?' || v_bucket_name || '/(.*)');

                IF v_object_path IS NULL OR v_object_path = '' THEN
                    RAISE WARNING 'Could not parse object path from URL: %', v_image_url;
                    CONTINUE; -- Skip to the next image URL
                END IF;

                -- Construct the Storage API URL for deletion
                v_storage_api_url := v_supabase_url || '/storage/v1/object/' || v_bucket_name || '/' || v_object_path;

                RAISE NOTICE 'Attempting Storage DELETE via http_delete: %', v_storage_api_url;

                -- Corrected Call: Assign the direct result of net.http_delete
                SELECT net.http_delete(
                    url := v_storage_api_url,
                    headers := jsonb_build_object(
                        'Authorization', 'Bearer ' || v_service_role_key,
                        'apikey', v_service_role_key -- Service key works as apikey for backend calls
                    ),
                    timeout_milliseconds := 5000 -- 5 seconds timeout
                ) INTO v_request_id; -- Assign the returned bigint directly

                RAISE NOTICE 'Storage deletion request sent via http_delete for object: % (Request ID: %)', v_object_path, v_request_id;

            EXCEPTION
                WHEN insufficient_privilege THEN
                    RAISE EXCEPTION 'The executing role lacks permission to use the pg_net extension. Grant usage on schema `net` or use SECURITY DEFINER.';
                WHEN others THEN
                    -- Log the error but continue the function execution
                    RAISE WARNING 'Failed to delete storage object % (URL: %). Error: %', v_object_path, v_image_url, SQLERRM;
            END;
        END LOOP;
    ELSE
         RAISE NOTICE 'No images found in part_images table to delete from storage.';
    END IF;

    -- 1c. Now delete from part_images table
    RAISE NOTICE 'Deleting from part_images table...';
    DELETE FROM public.part_images WHERE part_id = p_part_id;
    RAISE NOTICE 'Deleted % rows from part_images table.', FOUND;

    -- 2. Delete from parts_engines
    RAISE NOTICE 'Deleting from parts_engines...';
     BEGIN
         DELETE FROM public.parts_engines WHERE part_id = p_part_id;
         RAISE NOTICE 'Deleted % rows from parts_engines.', FOUND;
     EXCEPTION
         WHEN undefined_table THEN RAISE NOTICE 'Table parts_engines not found, skipping.';
         WHEN others THEN RAISE WARNING 'Error deleting from parts_engines: %', SQLERRM;
     END;


    -- 3. Delete from parts_car
    RAISE NOTICE 'Deleting from parts_car...';
     BEGIN
         DELETE FROM public.parts_car WHERE part_id = p_part_id;
         RAISE NOTICE 'Deleted % rows from parts_car.', FOUND;
     EXCEPTION
         WHEN undefined_table THEN RAISE NOTICE 'Table parts_car not found, skipping.';
         WHEN others THEN RAISE WARNING 'Error deleting from parts_car: %', SQLERRM;
     END;


    -- 4. Delete from parts_category_attribute_values
    RAISE NOTICE 'Deleting from parts_category_attribute_values...';
     BEGIN
         DELETE FROM public.parts_category_attribute_values WHERE part_id = p_part_id;
         RAISE NOTICE 'Deleted % rows from parts_category_attribute_values.', FOUND;
     EXCEPTION
         WHEN undefined_table THEN RAISE NOTICE 'Table parts_category_attribute_values not found, skipping.';
         WHEN others THEN RAISE WARNING 'Error deleting from parts_category_attribute_values: %', SQLERRM;
     END;


    -- 5. Identify related condition IDs first
    -- Assuming parts_condition table exists as per original function structure
    BEGIN
        SELECT array_agg(id) INTO v_condition_ids FROM public.parts_condition WHERE part_id = p_part_id;
    EXCEPTION
        WHEN undefined_table THEN
             RAISE NOTICE 'Table parts_condition not found, skipping related deletions.';
             v_condition_ids := NULL;
        WHEN others THEN
             RAISE WARNING 'Error checking parts_condition: %', SQLERRM;
             v_condition_ids := NULL;
    END;


    -- 6. Delete from part_price (referencing parts_condition)
    IF v_condition_ids IS NOT NULL AND array_length(v_condition_ids, 1) > 0 THEN
        RAISE NOTICE 'Deleting from part_price for related condition IDs %...', v_condition_ids;
         BEGIN
            DELETE FROM public.part_price WHERE condition_id = ANY(v_condition_ids);
             RAISE NOTICE 'Deleted % rows from part_price.', FOUND;
         EXCEPTION
             WHEN undefined_table THEN
                  RAISE NOTICE 'Table part_price not found, skipping deletion.';
             WHEN others THEN
                  RAISE WARNING 'Error deleting from part_price: %', SQLERRM;
         END;

    ELSE
        RAISE NOTICE 'No related rows found or accessible in parts_condition to check in part_price.';
    END IF;

    -- 7. Delete from parts_condition
    IF v_condition_ids IS NOT NULL THEN -- Check if we potentially looked for condition IDs
         RAISE NOTICE 'Deleting from parts_condition...';
         BEGIN
             DELETE FROM public.parts_condition WHERE part_id = p_part_id;
             RAISE NOTICE 'Deleted % rows from parts_condition.', FOUND;
         EXCEPTION
             WHEN undefined_table THEN
                  -- Already logged finding it missing, no need to log again
                  NULL; -- Do nothing
             WHEN others THEN
                  RAISE WARNING 'Error deleting from parts_condition: %', SQLERRM;
         END;
    END IF;

    -- 8. Finally, delete the part itself
    RAISE NOTICE 'Deleting from parts...';
    DELETE FROM public.parts WHERE id = p_part_id;
    RAISE NOTICE 'Deleted % rows from parts.', FOUND;

    RAISE NOTICE 'Deletion process completed for part ID %.', p_part_id;

EXCEPTION
    WHEN others THEN
        -- Catch any unexpected error during the *entire* process
        RAISE EXCEPTION 'Error during deletion process for part ID %: %', p_part_id, SQLERRM;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- Function structure for get_all_roles
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."get_all_roles"();
CREATE OR REPLACE FUNCTION "public"."get_all_roles"()
  RETURNS TABLE("id" uuid, "name" text, "description" text) AS $BODY$
BEGIN
  RETURN QUERY
  SELECT r.id, r.name, r.description
  FROM roles r
  ORDER BY r.name;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER
  COST 100
  ROWS 1000;

-- ----------------------------
-- Function structure for get_car_string
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."get_car_string"("p_trim_id" int4);
CREATE OR REPLACE FUNCTION "public"."get_car_string"("p_trim_id" int4)
  RETURNS "pg_catalog"."text" AS $BODY$
DECLARE
    v_brand_name TEXT;
    v_model_name TEXT;
    v_generation_name TEXT;
    v_start_year TEXT;
    v_end_year TEXT;
    v_variation_name TEXT;
    v_trim_name TEXT;
    v_car_string TEXT;
BEGIN
    -- Retrieve information from the tables based on trim_id
   SELECT
        b.brand_name,
        m.model_name,
        g.name,
        g.start_production_year,
        g.end_production_year,
        v.variation,
        t.trim
    INTO
        v_brand_name,
        v_model_name,
        v_generation_name,
        v_start_year,
        v_end_year,
        v_variation_name,
        v_trim_name
    FROM
        trims t
    JOIN
        variations v ON t.variation_id = v.id
    JOIN
        generations g ON v.generation_id = g.id
    JOIN
        models m ON g.model_id = m.id
    JOIN
        brands b ON m.brand_id = b.brand_id
    WHERE
        t.id = p_trim_id;

    -- Construct the car string
    v_car_string := v_brand_name || ' ' || v_model_name || ' ' || v_generation_name || ' ' || v_start_year || '-' || v_end_year ||' '|| v_variation_name || ' ' || v_trim_name;

    RETURN v_car_string;

EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Error retrieving car string for trim_id: %', p_trim_id;
        RAISE NOTICE 'Error: %', SQLERRM;
        RETURN NULL;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- Function structure for get_compatible_parts
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."get_compatible_parts"("search_partnumber" varchar);
CREATE OR REPLACE FUNCTION "public"."get_compatible_parts"("search_partnumber" varchar)
  RETURNS TABLE("partnumber" varchar) AS $BODY$
DECLARE
    part_group_id INT;
BEGIN
    -- Find the group ID for the part number
    SELECT group_id INTO part_group_id
    FROM part_to_group
    WHERE partnumber = search_partnumber;
    
    -- If found, return all part numbers in that group except the search part number
    IF part_group_id IS NOT NULL THEN
        RETURN QUERY
        SELECT p2g.partnumber
        FROM part_to_group p2g
        WHERE p2g.group_id = part_group_id
          AND p2g.partnumber != search_partnumber;
    END IF;
    
    -- If not found, return empty set
    RETURN;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000;

-- ----------------------------
-- Function structure for get_role_name_by_id
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."get_role_name_by_id"("input_role_id" uuid);
CREATE OR REPLACE FUNCTION "public"."get_role_name_by_id"("input_role_id" uuid)
  RETURNS "pg_catalog"."text"
  LANGUAGE sql VOLATILE SECURITY DEFINER
  COST 100
  SET "search_path"="public"

  SELECT name
  FROM public.roles
  WHERE id = input_role_id;

;

-- ----------------------------
-- Function structure for get_trim_id
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."get_trim_id"("car_string" text);
CREATE OR REPLACE FUNCTION "public"."get_trim_id"("car_string" text)
  RETURNS "pg_catalog"."int4" AS $BODY$
DECLARE
    brand_name TEXT;
    model_name TEXT;
    generation_name TEXT;
    start_year TEXT;
    end_year TEXT;
    variation_name TEXT;
    trim_name TEXT;
    brand_id INTEGER;
    model_id INTEGER;
    generation_id INTEGER;
    variation_id INTEGER;
    trim_id INTEGER;
BEGIN
    -- Extract components from the input string using regular expressions.  This is the most complex and potentially fragile part.
    --  We're making assumptions about the format and using regex to split.
    SELECT
        split_part(car_string, ' ', 1),  -- Brand Name
        split_part(car_string, ' ', 2), --model name
        substring(car_string from 'MK\d+'),   -- Generation
        substring(car_string from '\d{4}-\d{4}'), -- year
        substring(car_string from '\d{4}-\d{4}') ,--year
        regexp_replace(substring(car_string from '(Hatchback|Wagon/Variant|Sedan|SUV|Coupe|Convertible|Minivan|Wagon/Avant|Sportback|Citycarver)'), '^ ', ''), -- Variation
        regexp_replace(car_string, '.*(Hatchback|Wagon/Variant|Sedan|SUV|Coupe|Convertible|Minivan|Wagon/Avant|Sportback|Citycarver) ', '') -- Trim

    INTO brand_name, model_name, generation_name, start_year,end_year, variation_name, trim_name;
  
    -- Get brand_id
    SELECT b.brand_id INTO brand_id
    FROM brands b
    WHERE b.brand_name = brand_name;

    -- Get model_id
    SELECT m.id INTO model_id
    FROM models m
    WHERE m.brand_id = brand_id AND m.model_name = model_name;

    -- Get generation_id
    SELECT g.id INTO generation_id
    FROM generations g
    WHERE g.model_id = model_id AND g.name = generation_name AND g.start_production_year = substring(start_year from 1 for 4) AND g.end_production_year= substring(end_year from 6 for 9) ;
    
    -- Get variation_id
     SELECT v.id INTO variation_id
     FROM variations v
     WHERE v.generation_id = generation_id AND v.variation = variation_name;

    -- Get trim_id
    SELECT t.id INTO trim_id
    FROM trims t
    WHERE t.variation_id = variation_id AND t.trim = trim_name;

    RETURN trim_id;

EXCEPTION
    WHEN others THEN
        -- Log the error and the input string for debugging.  In a production system, use a proper logging mechanism.
        RAISE NOTICE 'Error processing string: %', car_string;
        RAISE NOTICE 'Error: %', SQLERRM;
        RETURN NULL;  -- Or perhaps -1 or another sentinel value to indicate failure.

END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- Function structure for get_user_permissions
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."get_user_permissions"("user_id_param" uuid);
CREATE OR REPLACE FUNCTION "public"."get_user_permissions"("user_id_param" uuid)
  RETURNS SETOF "pg_catalog"."text"
  LANGUAGE sql VOLATILE
  COST 100
  ROWS 1000

  SELECT p.name
  FROM user_permissions up
  JOIN permissions p ON up.permission_id = p.id
  WHERE up.user_id = user_id_param;

;

-- ----------------------------
-- Function structure for get_user_role
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."get_user_role"("user_id_param" uuid);
CREATE OR REPLACE FUNCTION "public"."get_user_role"("user_id_param" uuid)
  RETURNS "pg_catalog"."text"
  LANGUAGE sql VOLATILE
  COST 100

  SELECT r.name
  FROM user_roles ur
  JOIN roles r ON ur.role_id = r.id
  WHERE ur.user_id = user_id_param;

;

-- ----------------------------
-- Function structure for get_variation_trim_id
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."get_variation_trim_id"("p_car_string" text);
CREATE OR REPLACE FUNCTION "public"."get_variation_trim_id"("p_car_string" text)
  RETURNS "pg_catalog"."int4" AS $BODY$
DECLARE
    v_result_id INT;         -- Variable to hold the resulting ID
    v_normalized_input TEXT; -- Variable for the cleaned input string
BEGIN
    -- 1. Normalize the input string:
    --    - Convert to lowercase for case-insensitive matching.
    --    - Replace multiple spaces with a single space.
    --    - Trim leading/trailing whitespace.
    v_normalized_input := TRIM(LOWER(regexp_replace(p_car_string, '\s+', ' ', 'g')));

    -- 2. Query the database by joining all relevant tables
    --    and comparing the concatenated fields (also normalized)
    --    with the normalized input string.
    SELECT vt.id
    INTO v_result_id -- Store the found ID here
    FROM public.variation_trim vt
    JOIN public.car_variation cv ON vt.variation_id = cv.id
    JOIN public.car_generation cg ON cv.generation_id = cg.id
    JOIN public.car_models cm ON cg.model_id = cm.id
    JOIN public.car_brands cb ON cm.brand_id = cb.brand_id
    WHERE
        -- Concatenate the relevant fields, normalize them (lowercase),
        -- and compare with the normalized input string.
        LOWER(cb.brand_name || ' ' || cm.model_name || ' ' || cg.name || ' ' || cv.variation || ' ' || vt.trim) = v_normalized_input;

    -- 3. Return the result.
    --    If the SELECT INTO query found a matching row, v_result_id will contain the ID.
    --    If no row was found, v_result_id will be NULL by default.
    RETURN v_result_id;

END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- Function structure for handle_new_user
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."handle_new_user"();
CREATE OR REPLACE FUNCTION "public"."handle_new_user"()
  RETURNS "pg_catalog"."trigger" AS $BODY$
BEGIN
  INSERT INTO public.profiles (id, email, phone, created_at, updated_at)
  VALUES (
    NEW.id,
    NEW.email,
    NEW.raw_user_meta_data->>'phone',  -- Extract phone from metadata
    NEW.created_at,
    NEW.updated_at
  );
  RETURN NEW;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER
  COST 100;

-- ----------------------------
-- Function structure for handle_update_user
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."handle_update_user"();
CREATE OR REPLACE FUNCTION "public"."handle_update_user"()
  RETURNS "pg_catalog"."trigger" AS $BODY$
BEGIN
  IF (OLD.raw_user_meta_data->>'phone' IS DISTINCT FROM NEW.raw_user_meta_data->>'phone') THEN
    UPDATE public.profiles
    SET 
      phone = NEW.raw_user_meta_data->>'phone',
      updated_at = NOW()
    WHERE id = NEW.id;
  END IF;
  RETURN NEW;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- Function structure for is_super_admin
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."is_super_admin"("p_user_id" uuid);
CREATE OR REPLACE FUNCTION "public"."is_super_admin"("p_user_id" uuid)
  RETURNS "pg_catalog"."bool" AS $BODY$
DECLARE
    v_super_admin_role_id uuid;
BEGIN
    -- Get the super admin role ID
    SELECT id INTO v_super_admin_role_id
    FROM public.roles
    WHERE name = 'Super Admin';
    
    -- If super admin role doesn't exist, return false
    IF v_super_admin_role_id IS NULL THEN
        RETURN false;
    END IF;
    
    -- Check if the user has the super admin role
    RETURN EXISTS (
        SELECT 1
        FROM public.user_roles
        WHERE user_id = p_user_id AND role_id = v_super_admin_role_id
    );
END;
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER
  COST 100;

-- ----------------------------
-- Function structure for log_rbac_changes
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."log_rbac_changes"();
CREATE OR REPLACE FUNCTION "public"."log_rbac_changes"()
  RETURNS "pg_catalog"."trigger" AS $BODY$
DECLARE
    v_action_type text;
    v_target_table text;
    v_target_record_id text;
    v_change_details jsonb;
BEGIN
    v_target_table := TG_TABLE_NAME;
    
    IF TG_OP = 'INSERT' THEN
        v_action_type := 'CREATE';
        v_change_details := jsonb_build_object('new_values', row_to_json(NEW));
        
        IF v_target_table = 'roles' THEN
            v_target_record_id := NEW.id::text;
        ELSIF v_target_table = 'permissions' THEN
            v_target_record_id := NEW.id::text;
        ELSIF v_target_table = 'user_roles' THEN
            v_target_record_id := NEW.user_id::text || '/' || NEW.role_id::text;
        ELSIF v_target_table = 'role_permissions' THEN
            v_target_record_id := NEW.role_id::text || '/' || NEW.permission_id::text;
        ELSIF v_target_table = 'user_permissions' THEN
            v_target_record_id := NEW.id::text;
        END IF;
    ELSIF TG_OP = 'UPDATE' THEN
        v_action_type := 'UPDATE';
        v_change_details := jsonb_build_object(
            'old_values', row_to_json(OLD),
            'new_values', row_to_json(NEW)
        );
        
        IF v_target_table = 'roles' THEN
            v_target_record_id := NEW.id::text;
        ELSIF v_target_table = 'permissions' THEN
            v_target_record_id := NEW.id::text;
        ELSIF v_target_table = 'user_roles' THEN
            v_target_record_id := NEW.user_id::text || '/' || NEW.role_id::text;
        ELSIF v_target_table = 'role_permissions' THEN
            v_target_record_id := NEW.role_id::text || '/' || NEW.permission_id::text;
        ELSIF v_target_table = 'user_permissions' THEN
            v_target_record_id := NEW.id::text;
        END IF;
    ELSIF TG_OP = 'DELETE' THEN
        v_action_type := 'DELETE';
        v_change_details := jsonb_build_object('old_values', row_to_json(OLD));
        
        IF v_target_table = 'roles' THEN
            v_target_record_id := OLD.id::text;
        ELSIF v_target_table = 'permissions' THEN
            v_target_record_id := OLD.id::text;
        ELSIF v_target_table = 'user_roles' THEN
            v_target_record_id := OLD.user_id::text || '/' || OLD.role_id::text;
        ELSIF v_target_table = 'role_permissions' THEN
            v_target_record_id := OLD.role_id::text || '/' || OLD.permission_id::text;
        ELSIF v_target_table = 'user_permissions' THEN
            v_target_record_id := OLD.id::text;
        END IF;
    END IF;
    
    INSERT INTO public.audit_log (
        user_id,
        action_type,
        target_table,
        target_record_id,
        change_details,
        ip_address
    ) VALUES (
        auth.uid(),
        v_action_type || '_' || upper(v_target_table),
        v_target_table,
        v_target_record_id,
        v_change_details,
        inet_client_addr()
    );
    
    RETURN NULL;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE SECURITY DEFINER
  COST 100;

-- ----------------------------
-- Function structure for set_createdat_timestamp
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."set_createdat_timestamp"();
CREATE OR REPLACE FUNCTION "public"."set_createdat_timestamp"()
  RETURNS "pg_catalog"."trigger" AS $BODY$
BEGIN
  IF NEW."createdAt" IS NULL THEN
    NEW."createdAt" = now();
  END IF;
  RETURN NEW;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- Function structure for set_updatedat_timestamp
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."set_updatedat_timestamp"();
CREATE OR REPLACE FUNCTION "public"."set_updatedat_timestamp"()
  RETURNS "pg_catalog"."trigger" AS $BODY$
BEGIN
  NEW."updatedAt" = now();
  RETURN NEW;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- Function structure for trigger_set_timestamp
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."trigger_set_timestamp"();
CREATE OR REPLACE FUNCTION "public"."trigger_set_timestamp"()
  RETURNS "pg_catalog"."trigger" AS $BODY$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- Function structure for update_parent_category_is_active
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."update_parent_category_is_active"();
CREATE OR REPLACE FUNCTION "public"."update_parent_category_is_active"()
  RETURNS "pg_catalog"."trigger" AS $BODY$
BEGIN
    IF NEW."isActive" = TRUE THEN
        -- Recursively update parent categories to active
        WITH RECURSIVE parent_categories AS (
            SELECT id, parent_category_id
            FROM car_part_categories
            WHERE id = NEW.parent_category_id
            UNION ALL
            SELECT cp.id, cp.parent_category_id
            FROM car_part_categories cp
            INNER JOIN parent_categories pc ON cp.id = pc.parent_category_id
        )
        UPDATE car_part_categories
        SET "isActive" = TRUE
        WHERE id IN (SELECT id FROM parent_categories) AND "isActive" IS FALSE;
    END IF;
    RETURN NEW;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- Function structure for update_updated_at_column
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."update_updated_at_column"();
CREATE OR REPLACE FUNCTION "public"."update_updated_at_column"()
  RETURNS "pg_catalog"."trigger" AS $BODY$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- Function structure for validate_part_location_details
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."validate_part_location_details"();
CREATE OR REPLACE FUNCTION "public"."validate_part_location_details"()
  RETURNS "pg_catalog"."trigger" AS $BODY$
BEGIN
    IF NEW.location_subtype = 'crate' THEN
        IF NOT (NEW.details ? 'level' AND NEW.details ? 'crate_code') THEN
            RAISE EXCEPTION 'Crate location details must include "level" and "crate_code" keys.';
        END IF;
    ELSIF NEW.location_subtype = 'container' THEN
         IF NOT (NEW.details ? 'level' AND NEW.details ? 'container_code') THEN
             RAISE EXCEPTION 'Container location details must include "level" and "container_code" keys.';
         END IF;
         IF (NEW.details ->> 'container_code') NOT LIKE 'C-%' THEN
             RAISE EXCEPTION 'Container code must start with "C-".';
         END IF;
    -- Add ELSIF for other subtypes...
    END IF;
    RETURN NEW;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- View structure for full_part_location_details
-- ----------------------------
DROP VIEW IF EXISTS "public"."full_part_location_details";
CREATE VIEW "public"."full_part_location_details" AS  SELECT pl.location_id,
    p.part_id,
    p.name AS part_name,
    p.sku AS part_sku,
    p.category AS part_category,
    sa.name AS area_name,
    sa.location_type AS area_location_type,
    sa.level AS area_level,
    su.unit_type,
    su.identifier AS unit_identifier,
    pl.quantity,
    pl.location_subtype,
        CASE pl.location_subtype
            WHEN 'crate'::location_subtype_enum THEN jsonb_pretty(pl.details)
            WHEN 'container'::location_subtype_enum THEN jsonb_pretty(pl.details)
            WHEN 'shelf_section'::location_subtype_enum THEN jsonb_pretty(pl.details)
            WHEN 'open_shelf'::location_subtype_enum THEN jsonb_pretty(pl.details)
            WHEN 'cage_section'::location_subtype_enum THEN jsonb_pretty(pl.details)
            WHEN 'hanging_point'::location_subtype_enum THEN pl.details ->> 'point_identifier'::text
            WHEN 'open_area_spot'::location_subtype_enum THEN pl.details ->> 'spot_description'::text
            ELSE pl.details::text
        END AS location_specifics,
    pl.details AS raw_details,
    pl.notes,
    pl.created_at,
    pl.updated_at
   FROM part_locations pl
     JOIN parts p ON pl.part_id = p.part_id
     JOIN storage_units su ON pl.unit_id = su.unit_id
     JOIN storage_areas sa ON su.area_id = sa.area_id;

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."car_part_categories_id_seq"
OWNED BY "public"."car_part_categories"."id";
SELECT setval('"public"."car_part_categories_id_seq"', 166, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."engine_families_id_seq"
OWNED BY "public"."engine_family_codes"."id";
SELECT setval('"public"."engine_families_id_seq"', 40, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."engines_id_seq"
OWNED BY "public"."engines"."id";
SELECT setval('"public"."engines_id_seq"', 1625, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."part_compatibility_groups_group_id_seq"
OWNED BY "public"."part_compatibility_groups"."id";
SELECT setval('"public"."part_compatibility_groups_group_id_seq"', 160, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."part_images_id_seq"
OWNED BY "public"."part_images"."id";
SELECT setval('"public"."part_images_id_seq"', 209, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."part_locations_location_id_seq"
OWNED BY "public"."part_locations"."location_id";
SELECT setval('"public"."part_locations_location_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."part_price_id_seq"
OWNED BY "public"."part_price"."id";
SELECT setval('"public"."part_price_id_seq"', 169, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."parts_car_id_seq"
OWNED BY "public"."parts_car"."id";
SELECT setval('"public"."parts_car_id_seq"', 18, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."parts_category_attribute_input_option_id_seq"
OWNED BY "public"."parts_category_attribute_input_option"."id";
SELECT setval('"public"."parts_category_attribute_input_option_id_seq"', 8, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."parts_category_attributes_id_seq"
OWNED BY "public"."parts_category_attributes"."id";
SELECT setval('"public"."parts_category_attributes_id_seq"', 3, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."parts_condition_id_seq"
OWNED BY "public"."parts_condition"."id";
SELECT setval('"public"."parts_condition_id_seq"', 191, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."parts_part_id_seq"
OWNED BY "public"."parts"."part_id";
SELECT setval('"public"."parts_part_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."storage_areas_area_id_seq"
OWNED BY "public"."storage_areas"."area_id";
SELECT setval('"public"."storage_areas_area_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."storage_units_unit_id_seq"
OWNED BY "public"."storage_units"."unit_id";
SELECT setval('"public"."storage_units_unit_id_seq"', 1, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."variation_trim_id_seq"
OWNED BY "public"."variation_trim"."id";
SELECT setval('"public"."variation_trim_id_seq"', 242, true);

-- ----------------------------
-- Indexes structure for table audit_log
-- ----------------------------
CREATE INDEX "idx_audit_log_action_type" ON "public"."audit_log" USING btree (
  "action_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_audit_log_target" ON "public"."audit_log" USING btree (
  "target_table" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "target_record_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_audit_log_timestamp" ON "public"."audit_log" USING btree (
  "timestamp" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_audit_log_user_id" ON "public"."audit_log" USING btree (
  "user_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table audit_log
-- ----------------------------
ALTER TABLE "public"."audit_log" ADD CONSTRAINT "audit_log_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table car_brands
-- ----------------------------
ALTER TABLE "public"."car_brands" ADD CONSTRAINT "car_brands_pkey" PRIMARY KEY ("brand_id");

-- ----------------------------
-- Primary Key structure for table car_generation
-- ----------------------------
ALTER TABLE "public"."car_generation" ADD CONSTRAINT "car_generation_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table car_models
-- ----------------------------
ALTER TABLE "public"."car_models" ADD CONSTRAINT "car_models_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Triggers structure for table car_part_categories
-- ----------------------------
CREATE TRIGGER "car_part_categories_update_parent_trigger" AFTER UPDATE OF "isActive" ON "public"."car_part_categories"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_parent_category_is_active"();

-- ----------------------------
-- Primary Key structure for table car_part_categories
-- ----------------------------
ALTER TABLE "public"."car_part_categories" ADD CONSTRAINT "car_part_categories_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table car_variation
-- ----------------------------
ALTER TABLE "public"."car_variation" ADD CONSTRAINT "car_variation_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table category_fields
-- ----------------------------
ALTER TABLE "public"."category_fields" ADD CONSTRAINT "category_fields_category_id_field_name_key" UNIQUE ("category_id", "field_name");

-- ----------------------------
-- Primary Key structure for table category_fields
-- ----------------------------
ALTER TABLE "public"."category_fields" ADD CONSTRAINT "category_fields_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Uniques structure for table client_categories
-- ----------------------------
ALTER TABLE "public"."client_categories" ADD CONSTRAINT "client_categories_name_key" UNIQUE ("name");

-- ----------------------------
-- Primary Key structure for table client_categories
-- ----------------------------
ALTER TABLE "public"."client_categories" ADD CONSTRAINT "client_categories_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table client_data
-- ----------------------------
CREATE INDEX "idx_client_data_client_id" ON "public"."client_data" USING btree (
  "client_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_client_data_field_id" ON "public"."client_data" USING btree (
  "field_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table client_data
-- ----------------------------
ALTER TABLE "public"."client_data" ADD CONSTRAINT "client_data_client_id_field_id_key" UNIQUE ("client_id", "field_id");

-- ----------------------------
-- Primary Key structure for table client_data
-- ----------------------------
ALTER TABLE "public"."client_data" ADD CONSTRAINT "client_data_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table client_profiles
-- ----------------------------
CREATE INDEX "idx_client_profiles_profile_id" ON "public"."client_profiles" USING btree (
  "profile_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table client_profiles
-- ----------------------------
ALTER TABLE "public"."client_profiles" ADD CONSTRAINT "client_profiles_pkey" PRIMARY KEY ("client_id", "profile_id");

-- ----------------------------
-- Indexes structure for table clients
-- ----------------------------
CREATE INDEX "idx_clients_category_id" ON "public"."clients" USING btree (
  "category_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table clients
-- ----------------------------
ALTER TABLE "public"."clients" ADD CONSTRAINT "clients_phone_number_key" UNIQUE ("phone_number");

-- ----------------------------
-- Primary Key structure for table clients
-- ----------------------------
ALTER TABLE "public"."clients" ADD CONSTRAINT "clients_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table engine_families
-- ----------------------------
ALTER TABLE "public"."engine_families" ADD CONSTRAINT "engine_families_pkey1" PRIMARY KEY ("engine_id", "engine_family_code_id");

-- ----------------------------
-- Indexes structure for table engine_family_codes
-- ----------------------------
CREATE INDEX "idx_engine_families_code" ON "public"."engine_family_codes" USING btree (
  "code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table engine_family_codes
-- ----------------------------
ALTER TABLE "public"."engine_family_codes" ADD CONSTRAINT "engine_families_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table engines
-- ----------------------------
ALTER TABLE "public"."engines" ADD CONSTRAINT "engines_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table part_compatibility_groups
-- ----------------------------
ALTER TABLE "public"."part_compatibility_groups" ADD CONSTRAINT "part_compatibility_groups_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table part_images
-- ----------------------------
ALTER TABLE "public"."part_images" ADD CONSTRAINT "part_images_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table part_locations
-- ----------------------------
CREATE INDEX "idx_part_locations_details_gin" ON "public"."part_locations" USING gin (
  "details" "pg_catalog"."jsonb_ops"
);
CREATE INDEX "idx_part_locations_location_subtype" ON "public"."part_locations" USING btree (
  "location_subtype" "pg_catalog"."enum_ops" ASC NULLS LAST
);
CREATE INDEX "idx_part_locations_part_id" ON "public"."part_locations" USING btree (
  "part_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_part_locations_unit_id" ON "public"."part_locations" USING btree (
  "unit_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

-- ----------------------------
-- Triggers structure for table part_locations
-- ----------------------------
CREATE TRIGGER "update_part_locations_updated_at" BEFORE UPDATE ON "public"."part_locations"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_updated_at_column"();
CREATE TRIGGER "validate_part_location_details_trigger" BEFORE INSERT OR UPDATE ON "public"."part_locations"
FOR EACH ROW
EXECUTE PROCEDURE "public"."validate_part_location_details"();

-- ----------------------------
-- Checks structure for table part_locations
-- ----------------------------
ALTER TABLE "public"."part_locations" ADD CONSTRAINT "chk_details_container_code_format" CHECK (location_subtype <> 'container'::location_subtype_enum OR (details ->> 'container_code'::text) IS NULL OR (details ->> 'container_code'::text) ~~ 'C-%'::text);
ALTER TABLE "public"."part_locations" ADD CONSTRAINT "chk_details_crate_code_exists" CHECK (location_subtype <> 'crate'::location_subtype_enum OR (details ->> 'crate_code'::text) IS NOT NULL);
ALTER TABLE "public"."part_locations" ADD CONSTRAINT "chk_details_shelf_level_exists" CHECK ((location_subtype <> ALL (ARRAY['crate'::location_subtype_enum, 'container'::location_subtype_enum, 'shelf_section'::location_subtype_enum, 'open_shelf'::location_subtype_enum])) OR (details ->> 'level'::text) IS NOT NULL);
ALTER TABLE "public"."part_locations" ADD CONSTRAINT "part_locations_quantity_check" CHECK (quantity >= 0);
ALTER TABLE "public"."part_locations" ADD CONSTRAINT "chk_details_cage_row_col_exists" CHECK (location_subtype <> 'cage_section'::location_subtype_enum OR (details ->> 'row'::text) IS NOT NULL AND (details ->> 'col'::text) IS NOT NULL);

-- ----------------------------
-- Primary Key structure for table part_locations
-- ----------------------------
ALTER TABLE "public"."part_locations" ADD CONSTRAINT "part_locations_pkey" PRIMARY KEY ("location_id");

-- ----------------------------
-- Primary Key structure for table part_price
-- ----------------------------
ALTER TABLE "public"."part_price" ADD CONSTRAINT "part_price_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table part_to_group
-- ----------------------------
CREATE INDEX "idx_part_to_group_group_id" ON "public"."part_to_group" USING btree (
  "group_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table part_to_group
-- ----------------------------
ALTER TABLE "public"."part_to_group" ADD CONSTRAINT "part_to_group_pkey" PRIMARY KEY ("partnumber");

-- ----------------------------
-- Indexes structure for table parts
-- ----------------------------
CREATE INDEX "idx_parts_category" ON "public"."parts" USING btree (
  "category" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_parts_name" ON "public"."parts" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_parts_sku" ON "public"."parts" USING btree (
  "sku" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Triggers structure for table parts
-- ----------------------------
CREATE TRIGGER "update_parts_updated_at" BEFORE UPDATE ON "public"."parts"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_updated_at_column"();

-- ----------------------------
-- Uniques structure for table parts
-- ----------------------------
ALTER TABLE "public"."parts" ADD CONSTRAINT "parts_sku_key" UNIQUE ("sku");

-- ----------------------------
-- Primary Key structure for table parts
-- ----------------------------
ALTER TABLE "public"."parts" ADD CONSTRAINT "parts_pkey" PRIMARY KEY ("part_id");

-- ----------------------------
-- Primary Key structure for table parts_car
-- ----------------------------
ALTER TABLE "public"."parts_car" ADD CONSTRAINT "parts_car_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table parts_category_attribute_input_option
-- ----------------------------
ALTER TABLE "public"."parts_category_attribute_input_option" ADD CONSTRAINT "parts_category_attribute_input_option_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table parts_category_attribute_values
-- ----------------------------
ALTER TABLE "public"."parts_category_attribute_values" ADD CONSTRAINT "parts_category_attribute_values_pkey" PRIMARY KEY ("part_id", "attribute_id");

-- ----------------------------
-- Checks structure for table parts_category_attributes
-- ----------------------------
ALTER TABLE "public"."parts_category_attributes" ADD CONSTRAINT "chk_pca_dependency_logic" CHECK (depends_on_attribute_id IS NULL AND depends_on_option_id IS NULL OR depends_on_attribute_id IS NOT NULL AND depends_on_option_id IS NOT NULL);
ALTER TABLE "public"."parts_category_attributes" ADD CONSTRAINT "parts_category_attributes_input_type_check" CHECK (input_type::text = ANY (ARRAY['text'::character varying, 'radio'::character varying, 'dropdown'::character varying, 'checkbox'::character varying, 'number'::character varying, 'date'::character varying]::text[]));

-- ----------------------------
-- Primary Key structure for table parts_category_attributes
-- ----------------------------
ALTER TABLE "public"."parts_category_attributes" ADD CONSTRAINT "parts_category_attributes_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table parts_condition
-- ----------------------------
ALTER TABLE "public"."parts_condition" ADD CONSTRAINT "parts_condition_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table parts_engines
-- ----------------------------
ALTER TABLE "public"."parts_engines" ADD CONSTRAINT "parts_engines_pkey" PRIMARY KEY ("part_id", "engine_id");

-- ----------------------------
-- Indexes structure for table permissions
-- ----------------------------
CREATE INDEX "idx_permissions_category" ON "public"."permissions" USING btree (
  "category" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_permissions_name" ON "public"."permissions" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Triggers structure for table permissions
-- ----------------------------
CREATE TRIGGER "permissions_audit_trigger" AFTER INSERT OR UPDATE OR DELETE ON "public"."permissions"
FOR EACH ROW
EXECUTE PROCEDURE "public"."log_rbac_changes"();

-- ----------------------------
-- Uniques structure for table permissions
-- ----------------------------
ALTER TABLE "public"."permissions" ADD CONSTRAINT "permissions_name_key" UNIQUE ("name");

-- ----------------------------
-- Primary Key structure for table permissions
-- ----------------------------
ALTER TABLE "public"."permissions" ADD CONSTRAINT "permissions_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table profiles
-- ----------------------------
ALTER TABLE "public"."profiles" ADD CONSTRAINT "profiles_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table role_permissions
-- ----------------------------
CREATE INDEX "idx_role_permissions_permission_id" ON "public"."role_permissions" USING btree (
  "permission_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_role_permissions_role_id" ON "public"."role_permissions" USING btree (
  "role_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

-- ----------------------------
-- Triggers structure for table role_permissions
-- ----------------------------
CREATE TRIGGER "role_permissions_audit_trigger" AFTER INSERT OR DELETE ON "public"."role_permissions"
FOR EACH ROW
EXECUTE PROCEDURE "public"."log_rbac_changes"();

-- ----------------------------
-- Primary Key structure for table role_permissions
-- ----------------------------
ALTER TABLE "public"."role_permissions" ADD CONSTRAINT "role_permissions_pkey" PRIMARY KEY ("role_id", "permission_id");

-- ----------------------------
-- Indexes structure for table roles
-- ----------------------------
CREATE INDEX "idx_roles_name" ON "public"."roles" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Triggers structure for table roles
-- ----------------------------
CREATE TRIGGER "roles_audit_trigger" AFTER INSERT OR UPDATE OR DELETE ON "public"."roles"
FOR EACH ROW
EXECUTE PROCEDURE "public"."log_rbac_changes"();
CREATE TRIGGER "set_timestamp_roles" BEFORE UPDATE ON "public"."roles"
FOR EACH ROW
EXECUTE PROCEDURE "public"."trigger_set_timestamp"();

-- ----------------------------
-- Uniques structure for table roles
-- ----------------------------
ALTER TABLE "public"."roles" ADD CONSTRAINT "roles_name_key" UNIQUE ("name");

-- ----------------------------
-- Primary Key structure for table roles
-- ----------------------------
ALTER TABLE "public"."roles" ADD CONSTRAINT "roles_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table storage_areas
-- ----------------------------
CREATE INDEX "idx_storage_areas_name" ON "public"."storage_areas" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Triggers structure for table storage_areas
-- ----------------------------
CREATE TRIGGER "update_storage_areas_updated_at" BEFORE UPDATE ON "public"."storage_areas"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_updated_at_column"();

-- ----------------------------
-- Uniques structure for table storage_areas
-- ----------------------------
ALTER TABLE "public"."storage_areas" ADD CONSTRAINT "storage_areas_name_key" UNIQUE ("name");

-- ----------------------------
-- Primary Key structure for table storage_areas
-- ----------------------------
ALTER TABLE "public"."storage_areas" ADD CONSTRAINT "storage_areas_pkey" PRIMARY KEY ("area_id");

-- ----------------------------
-- Indexes structure for table storage_units
-- ----------------------------
CREATE INDEX "idx_storage_units_area_id" ON "public"."storage_units" USING btree (
  "area_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "idx_storage_units_identifier" ON "public"."storage_units" USING btree (
  "identifier" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_storage_units_type" ON "public"."storage_units" USING btree (
  "unit_type" "pg_catalog"."enum_ops" ASC NULLS LAST
);

-- ----------------------------
-- Triggers structure for table storage_units
-- ----------------------------
CREATE TRIGGER "update_storage_units_updated_at" BEFORE UPDATE ON "public"."storage_units"
FOR EACH ROW
EXECUTE PROCEDURE "public"."update_updated_at_column"();

-- ----------------------------
-- Uniques structure for table storage_units
-- ----------------------------
ALTER TABLE "public"."storage_units" ADD CONSTRAINT "storage_units_area_id_unit_type_identifier_key" UNIQUE ("area_id", "unit_type", "identifier");

-- ----------------------------
-- Checks structure for table storage_units
-- ----------------------------
ALTER TABLE "public"."storage_units" ADD CONSTRAINT "chk_cage_identifier_prefix" CHECK (unit_type <> 'cage'::storage_unit_type_enum OR identifier::text ~~ 'CG%'::text);

-- ----------------------------
-- Primary Key structure for table storage_units
-- ----------------------------
ALTER TABLE "public"."storage_units" ADD CONSTRAINT "storage_units_pkey" PRIMARY KEY ("unit_id");

-- ----------------------------
-- Primary Key structure for table trim_engines
-- ----------------------------
ALTER TABLE "public"."trim_engines" ADD CONSTRAINT "trim_engines_pkey" PRIMARY KEY ("trim_id", "engine_id");

-- ----------------------------
-- Indexes structure for table user_permissions
-- ----------------------------
CREATE INDEX "idx_user_permissions_permission_id" ON "public"."user_permissions" USING btree (
  "permission_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_user_permissions_user_id" ON "public"."user_permissions" USING btree (
  "user_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

-- ----------------------------
-- Triggers structure for table user_permissions
-- ----------------------------
CREATE TRIGGER "set_timestamp_user_permissions" BEFORE UPDATE ON "public"."user_permissions"
FOR EACH ROW
EXECUTE PROCEDURE "public"."trigger_set_timestamp"();
CREATE TRIGGER "user_permissions_audit_trigger" AFTER INSERT OR UPDATE OR DELETE ON "public"."user_permissions"
FOR EACH ROW
EXECUTE PROCEDURE "public"."log_rbac_changes"();

-- ----------------------------
-- Uniques structure for table user_permissions
-- ----------------------------
ALTER TABLE "public"."user_permissions" ADD CONSTRAINT "user_permissions_user_id_permission_id_key" UNIQUE ("user_id", "permission_id");

-- ----------------------------
-- Primary Key structure for table user_permissions
-- ----------------------------
ALTER TABLE "public"."user_permissions" ADD CONSTRAINT "user_permissions_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table user_roles
-- ----------------------------
CREATE INDEX "idx_user_roles_role_id" ON "public"."user_roles" USING btree (
  "role_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);
CREATE INDEX "idx_user_roles_user_id" ON "public"."user_roles" USING btree (
  "user_id" "pg_catalog"."uuid_ops" ASC NULLS LAST
);

-- ----------------------------
-- Triggers structure for table user_roles
-- ----------------------------
CREATE TRIGGER "user_roles_audit_trigger" AFTER INSERT OR DELETE ON "public"."user_roles"
FOR EACH ROW
EXECUTE PROCEDURE "public"."log_rbac_changes"();

-- ----------------------------
-- Primary Key structure for table user_roles
-- ----------------------------
ALTER TABLE "public"."user_roles" ADD CONSTRAINT "user_roles_pkey" PRIMARY KEY ("user_id", "role_id");

-- ----------------------------
-- Primary Key structure for table variation_trim
-- ----------------------------
ALTER TABLE "public"."variation_trim" ADD CONSTRAINT "variation_trim_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Keys structure for table audit_log
-- ----------------------------
ALTER TABLE "public"."audit_log" ADD CONSTRAINT "audit_log_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles" ("id") ON DELETE SET NULL ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table car_models
-- ----------------------------
ALTER TABLE "public"."car_models" ADD CONSTRAINT "car_models_brand_id_fkey" FOREIGN KEY ("brand_id") REFERENCES "public"."car_brands" ("brand_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table car_part_categories
-- ----------------------------
ALTER TABLE "public"."car_part_categories" ADD CONSTRAINT "car_part_categories_parent_category_id_fkey" FOREIGN KEY ("parent_category_id") REFERENCES "public"."car_part_categories" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table category_fields
-- ----------------------------
ALTER TABLE "public"."category_fields" ADD CONSTRAINT "category_fields_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."client_categories" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table client_data
-- ----------------------------
ALTER TABLE "public"."client_data" ADD CONSTRAINT "client_data_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "public"."clients" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "public"."client_data" ADD CONSTRAINT "client_data_field_id_fkey" FOREIGN KEY ("field_id") REFERENCES "public"."category_fields" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table client_profiles
-- ----------------------------
ALTER TABLE "public"."client_profiles" ADD CONSTRAINT "client_profiles_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "public"."clients" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "public"."client_profiles" ADD CONSTRAINT "client_profiles_profile_id_fkey" FOREIGN KEY ("profile_id") REFERENCES "public"."profiles" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table clients
-- ----------------------------
ALTER TABLE "public"."clients" ADD CONSTRAINT "clients_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."client_categories" ("id") ON DELETE RESTRICT ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table engine_families
-- ----------------------------
ALTER TABLE "public"."engine_families" ADD CONSTRAINT "engine_families_engine_family_code_id_fkey" FOREIGN KEY ("engine_family_code_id") REFERENCES "public"."engine_family_codes" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."engine_families" ADD CONSTRAINT "engine_families_engine_id_fkey" FOREIGN KEY ("engine_id") REFERENCES "public"."engines" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table part_locations
-- ----------------------------
ALTER TABLE "public"."part_locations" ADD CONSTRAINT "part_locations_part_id_fkey" FOREIGN KEY ("part_id") REFERENCES "public"."parts" ("part_id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "public"."part_locations" ADD CONSTRAINT "part_locations_unit_id_fkey" FOREIGN KEY ("unit_id") REFERENCES "public"."storage_units" ("unit_id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table part_price
-- ----------------------------
ALTER TABLE "public"."part_price" ADD CONSTRAINT "part_price_condition_id_fkey" FOREIGN KEY ("condition_id") REFERENCES "public"."parts_condition" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table part_to_group
-- ----------------------------
ALTER TABLE "public"."part_to_group" ADD CONSTRAINT "part_to_group_group_id_fkey" FOREIGN KEY ("group_id") REFERENCES "public"."part_compatibility_groups" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table parts_car
-- ----------------------------
ALTER TABLE "public"."parts_car" ADD CONSTRAINT "parts_car_variation_trim_id_fkey" FOREIGN KEY ("variation_trim_id") REFERENCES "public"."variation_trim" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table parts_category_attribute_input_option
-- ----------------------------
ALTER TABLE "public"."parts_category_attribute_input_option" ADD CONSTRAINT "parts_category_attribute_input_option_attribute_id_fkey" FOREIGN KEY ("attribute_id") REFERENCES "public"."parts_category_attributes" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table parts_category_attribute_values
-- ----------------------------
ALTER TABLE "public"."parts_category_attribute_values" ADD CONSTRAINT "parts_category_attribute_values_attribute_id_fkey" FOREIGN KEY ("attribute_id") REFERENCES "public"."parts_category_attributes" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table parts_category_attributes
-- ----------------------------
ALTER TABLE "public"."parts_category_attributes" ADD CONSTRAINT "fk_pca_depends_on_attribute" FOREIGN KEY ("depends_on_attribute_id") REFERENCES "public"."parts_category_attributes" ("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "public"."parts_category_attributes" ADD CONSTRAINT "fk_pca_depends_on_option" FOREIGN KEY ("depends_on_option_id") REFERENCES "public"."parts_category_attribute_input_option" ("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table parts_engines
-- ----------------------------
ALTER TABLE "public"."parts_engines" ADD CONSTRAINT "fk_parts_engines_engine_id" FOREIGN KEY ("engine_id") REFERENCES "public"."engines" ("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- ----------------------------
-- Foreign Keys structure for table profiles
-- ----------------------------
ALTER TABLE "public"."profiles" ADD CONSTRAINT "profiles_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table role_permissions
-- ----------------------------
ALTER TABLE "public"."role_permissions" ADD CONSTRAINT "role_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "public"."permissions" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "public"."role_permissions" ADD CONSTRAINT "role_permissions_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."roles" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table storage_units
-- ----------------------------
ALTER TABLE "public"."storage_units" ADD CONSTRAINT "storage_units_area_id_fkey" FOREIGN KEY ("area_id") REFERENCES "public"."storage_areas" ("area_id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table user_permissions
-- ----------------------------
ALTER TABLE "public"."user_permissions" ADD CONSTRAINT "user_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "public"."permissions" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "public"."user_permissions" ADD CONSTRAINT "user_permissions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table user_roles
-- ----------------------------
ALTER TABLE "public"."user_roles" ADD CONSTRAINT "user_roles_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "public"."roles" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "public"."user_roles" ADD CONSTRAINT "user_roles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."profiles" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
