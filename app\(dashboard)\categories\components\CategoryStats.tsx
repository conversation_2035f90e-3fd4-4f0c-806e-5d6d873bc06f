'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { FolderTree, CheckCircle, Layers, GitBranch, Network, Cog } from 'lucide-react';
import { CategoryStats as CategoryStatsType } from '../types';

interface CategoryStatsProps {
  stats: CategoryStatsType;
}

const CategoryStats: React.FC<CategoryStatsProps> = ({ stats }) => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  const statCards = [
    {
      title: 'Total Categories',
      value: stats.totalCategories,
      icon: <FolderTree size={24} />,
      color: 'bg-gray-100 text-gray-600',
      valueColor: 'text-gray-800'
    },
    {
      title: 'Active Categories',
      value: stats.activeCategories,
      icon: <CheckCircle size={24} />,
      color: 'bg-teal-100 text-teal-600',
      valueColor: 'text-teal-700'
    },
    {
      title: 'Parent Categories',
      value: stats.parentCategories,
      icon: <Layers size={24} />,
      color: 'bg-orange-100 text-orange-600',
      valueColor: 'text-orange-700'
    },
    {
      title: 'Child Categories',
      value: stats.childCategories,
      icon: <GitBranch size={24} />,
      color: 'bg-blue-100 text-blue-600',
      valueColor: 'text-blue-700'
    },
    {
      title: 'Nested Depth',
      value: stats.maxDepth || 0,
      icon: <Network size={24} />,
      color: 'bg-purple-100 text-purple-600',
      valueColor: 'text-purple-700'
    },
    {
      title: 'Engine Parts',
      value: stats.engineParts || 0,
      icon: <Cog size={24} />,
      color: 'bg-red-100 text-red-600',
      valueColor: 'text-red-700'
    }
  ];

  return (
    <motion.div
      className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {statCards.map((card, index) => (
        <motion.div
          key={index}
          className="bg-white rounded-lg shadow-md p-6 flex items-center"
          variants={itemVariants}
          whileHover={{ y: -5, transition: { duration: 0.2 } }}
        >
          <div className={`p-3 rounded-lg mr-4 ${card.color}`}>
            {card.icon}
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">{card.title}</h3>
            <p className={`text-2xl font-bold ${card.valueColor}`}>{card.value}</p>
          </div>
        </motion.div>
      ))}
    </motion.div>
  );
};

export default CategoryStats;
