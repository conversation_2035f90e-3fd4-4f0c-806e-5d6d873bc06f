'use client';

import { useState } from 'react';
import { deleteSupabaseImage } from '@/app/utils/deleteSupabaseImage';
import { directImageDelete } from '@/app/utils/directImageDelete';
import { Button } from '@/app/components/ui/Button';
import { Input } from '@/app/components/ui/Input';
import Icon from '@/app/components/ui/Icon';
import Notification from '@/app/components/ui/Notification';
import { createClient } from '@/app/libs/supabase/client';


export default function ImageDeletionTestPage() {
  const [imageUrl, setImageUrl] = useState('');
  const [userId, setUserId] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [result, setResult] = useState<{
    success?: boolean;
    message?: string;
    path?: string;
    error?: string;
  } | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [showNotification, setShowNotification] = useState(false);
  const [notificationHeader, setNotificationHeader] = useState('');
  const [notificationBody, setNotificationBody] = useState('');
  const [notificationType, setNotificationType] = useState<'info' | 'error' | 'success' | 'hint'>('info');

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] ${message}`]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!imageUrl) {
      setNotificationHeader('Missing URL');
      setNotificationBody('Please enter an image URL');
      setNotificationType('error');
      setShowNotification(true);
      return;
    }
    
    setIsDeleting(true);
    setResult(null);
    setLogs([]);
    
    addLog(`Starting deletion of image: ${imageUrl}`);
    
    try {
      // First check if the image exists (without cache-busting)
      try {
        addLog('Checking if image exists...');
        const checkResponse = await fetch(imageUrl, { method: 'HEAD' });
        if (checkResponse.ok) {
          addLog('✅ Image exists and is accessible');
        } else {
          addLog(`⚠️ Image returned status ${checkResponse.status} - might not exist or be accessible`);
        }
      } catch (checkError) {
        addLog(`⚠️ Error checking if image exists: ${checkError}`);
      }
      
      // First attempt: Try the standard approach
      addLog('Attempting to delete image using standard approach...');
      const deleteResult = await deleteSupabaseImage(imageUrl, userId || undefined);
      
      if (deleteResult.success) {
        addLog(`✅ Standard approach: Successfully deleted image from path: ${deleteResult.path}`);
      } else {
        addLog(`❌ Standard approach failed: ${deleteResult.error}`);
      }
      
      // Second attempt: Try the direct approach regardless of first result
      addLog('Attempting direct deletion as a backup method...');
      const directResult = await directImageDelete(imageUrl);
      
      if (directResult.success) {
        addLog(`✅ Direct approach: Successfully deleted image`);
        if (directResult.details?.path) {
          addLog(`  Path: ${directResult.details.path}`);
        }
      } else {
        addLog(`❌ Direct approach failed: ${directResult.error}`);
      }
      
      // Determine overall success
      const overallSuccess = deleteResult.success || directResult.success;
      
      if (overallSuccess) {
        setResult({
          success: true,
          message: 'Image deleted successfully using at least one method',
          path: deleteResult.path || directResult.details?.path
        });
        setNotificationHeader('Success');
        setNotificationBody('Image deleted successfully');
        setNotificationType('success');
        
        // Add a delay to allow CDN cache to update
        addLog('Waiting 3 seconds for CDN cache to update...');
        await new Promise(resolve => setTimeout(resolve, 3000));
      } else {
        setResult({
          success: false,
          message: 'Failed to delete image using both methods',
          error: `Standard: ${deleteResult.error}, Direct: ${directResult.error}`
        });
        setNotificationHeader('Error');
        setNotificationBody('Failed to delete image using both methods');
        setNotificationType('error');
      }
      
      // Check if the image still exists with cache-busting
      try {
        const cacheBustUrl = `${imageUrl}?t=${Date.now()}`;
        addLog('Checking if image still exists (with cache-busting)...');
        const afterCheckResponse = await fetch(cacheBustUrl, { method: 'HEAD' });
        
        if (afterCheckResponse.ok) {
          addLog('⚠️ Image still appears to exist and is accessible after deletion attempt');
          addLog('Note: This might be due to CDN caching. The image may have been deleted from storage but still be cached.');
          
          // Add instructions for the user
          addLog('');
          addLog('TROUBLESHOOTING STEPS:');
          addLog('1. Try accessing the image in an incognito window');
          addLog('2. Wait a few minutes for the cache to expire');
          addLog('3. Check the Supabase dashboard directly to confirm deletion');
          addLog('4. If the image is still in Supabase storage, try deleting it directly from the dashboard');
        } else if (afterCheckResponse.status === 404) {
          addLog('✅ Image confirmed deleted (returned 404 Not Found)');
        } else {
          addLog(`ℹ️ Image returned status ${afterCheckResponse.status} after deletion attempt`);
        }
        
        // Try a direct check with the Supabase client
        try {
          const supabase = createClient();
          addLog('Checking directly with Supabase storage API...');
          
          // Extract the path from the success result
          let path = '';
          if (deleteResult.success && deleteResult.path) {
            path = deleteResult.path;
          } else if (directResult.success && directResult.details?.path) {
            path = directResult.details.path;
          } else if (imageUrl.includes('/v1/object/public/')) {
            path = imageUrl.split('/v1/object/public/')[1];
          }
          
          if (path) {
            addLog(`Checking if file exists at path: ${path}`);
            try {
              const { data: fileExists } = await supabase.storage
                .from('car-part-images')
                .download(path);
                
              if (fileExists) {
                addLog('⚠️ File still exists in Supabase storage according to direct API check');
                
                // Try one more direct deletion
                addLog('Attempting one final direct deletion...');
                const { error: finalDeleteError } = await supabase.storage
                  .from('car-part-images')
                  .remove([path]);
                  
                if (!finalDeleteError) {
                  addLog('✅ Final direct deletion successful');
                } else {
                  addLog(`❌ Final direct deletion failed: ${finalDeleteError.message}`);
                }
              } else {
                addLog('✅ File confirmed deleted via direct Supabase storage API check');
              }
            } catch (downloadError) {
              // If we get an error, it likely means the file doesn't exist
              addLog('✅ File confirmed deleted via direct Supabase storage API (error on download attempt)');
            }
          } else {
            addLog('⚠️ Could not determine path for direct API check');
          }
        } catch (supabaseError) {
          addLog(`❌ Error checking with Supabase API: ${supabaseError}`);
        }
      } catch (afterCheckError) {
        addLog(`ℹ️ Error checking if image exists after deletion: ${afterCheckError}`);
      }
      
      setShowNotification(true);
    } catch (error) {
      addLog(`❌ Error during deletion process: ${error}`);
      setResult({
        success: false,
        message: 'Error during deletion process',
        error: String(error)
      });
      setNotificationHeader('Error');
      setNotificationBody(`Error during deletion process: ${error}`);
      setNotificationType('error');
      setShowNotification(true);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClear = () => {
    setImageUrl('');
    setUserId('');
    setResult(null);
    setLogs([]);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="bg-white rounded-lg shadow-md p-6 max-w-2xl mx-auto">
        <div className="mb-6">
          <div className="flex items-center gap-2 mb-2">
            <Icon name="image" size={24} />
            <h1 className="text-2xl font-bold">Image Deletion Test Tool</h1>
          </div>
          <p className="text-gray-600">
            Test deleting images from Supabase storage by entering a URL
          </p>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="imageUrl" className="block text-sm font-medium mb-1">
              Image URL
            </label>
            <Input
              id="imageUrl"
              type="text"
              placeholder="https://excgraelqcvcdsnlvrtv.supabase.co/storage/v1/object/public/car-part-images/example.jpg"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              className="w-full"
              disabled={isDeleting}
            />
          </div>
          
          <div>
            <label htmlFor="userId" className="block text-sm font-medium mb-1">
              User ID (Optional)
            </label>
            <Input
              id="userId"
              type="text"
              placeholder="User ID associated with the image (optional)"
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              className="w-full"
              disabled={isDeleting}
            />
            <p className="text-xs text-gray-500 mt-1">
              If not provided, the authenticated user's ID will be used automatically
            </p>
          </div>
          
          <div className="flex gap-2">
            <Button 
              type="submit" 
              disabled={isDeleting}
              className="flex-1"
              variant="default"
            >
              {isDeleting ? (
                <>
                  <div className="w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin mr-2"></div>
                  Deleting...
                </>
              ) : (
                'Delete Image'
              )}
            </Button>
            <Button 
              type="button" 
              onClick={handleClear} 
              disabled={isDeleting}
              variant="outline"
            >
              Clear
            </Button>
          </div>
        </form>
        
        {result && (
          <div className={`mt-4 p-4 rounded-md ${result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
            <div className="flex items-center gap-2">
              <Icon 
                name={result.success ? 'check-circle' : 'x-circle'} 
                size={20} 
                className={result.success ? 'text-green-600' : 'text-red-600'} 
              />
              <h3 className="font-medium">{result.success ? 'Success' : 'Error'}</h3>
            </div>
            <div className="mt-1 ml-6">
              <p>{result.message}</p>
              {result.path && (
                <p className="text-sm mt-1">Path: {result.path}</p>
              )}
              {result.error && (
                <p className="text-sm mt-1 text-red-600">{result.error}</p>
              )}
            </div>
          </div>
        )}
        
        {logs.length > 0 && (
          <div className="mt-6">
            <h3 className="text-sm font-medium mb-2">Logs</h3>
            <div className="bg-gray-50 p-3 rounded-md text-xs font-mono h-64 overflow-y-auto border border-gray-200">
              {logs.map((log, index) => (
                <div key={index} className="pb-1">
                  {log}
                </div>
              ))}
            </div>
          </div>
        )}
        
        <div className="mt-6 pt-4 border-t border-gray-200 text-xs text-gray-500">
          This tool uses the deleteSupabaseImage utility to remove images from Supabase storage.
        </div>
      </div>
      
      {showNotification && (
        <Notification
          header={notificationHeader}
          body={notificationBody}
          type={notificationType}
          onClose={() => setShowNotification(false)}
        />
      )}
    </div>
  );
}
