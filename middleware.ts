// middleware.ts

import { type NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from './app/libs/supabase/server';
import { decrypt } from './app/utils/encryption';

// Cookie name (must match the one in utils/cookies.ts)
const COOKIE_NAME = 'watu';

// Helper function to check if user has Super Admin role from cookies
async function hasSuperAdminRole(req: NextRequest): Promise<boolean> {
  try {
    // This function extracts the role information from encrypted cookies
    const cookieHeader = req.headers.get('cookie') || '';
    if (!cookieHeader) return false;

    // Parse cookies from the header
    const cookiePairs = cookieHeader.split(';').map(pair => pair.trim());

    // Check for direct watu cookie first (unencrypted fallback)
    const directWatuCookie = cookiePairs.find(pair => pair.startsWith(`${COOKIE_NAME}=`));
    if (directWatuCookie) {
      try {
        const value = directWatuCookie.split('=')[1];
        const userData = JSON.parse(decodeURIComponent(value));

        // Check if user has Super Admin role
        if (userData && userData.roleName === 'Super Admin') {
          // console.log('Middleware: Found Super Admin in direct watu cookie');
          return true;
        }
      } catch (e) {
        // console.error('Error parsing direct watu cookie:', e);
        // Continue to try encrypted cookies
      }
    }

    // Find our encrypted cookie
    for (const cookiePair of cookiePairs) {
      const [name, value] = cookiePair.split('=');
      if (!name || !value) continue;

      // Skip carFilter cookie
      if (name === 'carFilter') {
        continue;
      }

      try {
        // Try to decrypt the cookie name
        const decryptedName = await decrypt(decodeURIComponent(name));

        // If this is our cookie, decrypt the value and check for Super Admin role
        if (decryptedName === COOKIE_NAME) {
          try {
            const decryptedValue = await decrypt(decodeURIComponent(value));
            const userData = JSON.parse(decryptedValue);

            // Check if user has Super Admin role
            return userData.roleName === 'Super Admin';
          } catch (decryptError) {
            // console.error('Error decrypting cookie value:', decryptError);
            // Continue checking other cookies
          }
        }
      } catch (e) {
        // Skip cookies that can't be decrypted
        continue;
      }
    }

    return false;
  } catch (error) {
    // console.error('Error checking for Super Admin role:', error);
    return false;
  }
}

export async function middleware(req: NextRequest) {
  const res = NextResponse.next();
  const requestUrl = new URL(req.url);

  // HANDLE SUPABASE AUTH REDIRECTS - This fixes the localhost issue in confirmation URLs
  // Check if this is a request coming from Supabase Auth verification
  if (requestUrl.searchParams.has('token') &&
      requestUrl.searchParams.has('type') &&
      requestUrl.searchParams.has('redirect_to')) {

    // Get the redirect_to parameter (we don't need token and type for this operation)
    const redirectTo = requestUrl.searchParams.get('redirect_to');

    // Check if redirectTo contains localhost but we're on production
    if (redirectTo && redirectTo.includes('localhost') &&
        !requestUrl.hostname.includes('localhost')) {
      // console.log('Fixing Supabase redirect URL:', {
      //   from: redirectTo,
      //   hostname: requestUrl.hostname
      // });

      // Create a production URL based on the current hostname
      const productionUrl = redirectTo.replace(
        /https?:\/\/localhost(:\d+)?/,
        `${requestUrl.protocol}//${requestUrl.hostname}`
      );

      // Create a new URL with the fixed redirect_to parameter
      const fixedUrl = new URL(requestUrl.href);
      fixedUrl.searchParams.set('redirect_to', productionUrl);

      // console.log('Redirecting to fixed URL:', fixedUrl.toString());
      return NextResponse.redirect(fixedUrl);
    }
  }

  const supabase = createRouteHandlerClient({ request: req, response: res });

  // Get the current user (more secure than getSession)
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError) {
    // console.error('Middleware: Error getting user:', userError);
  }

  // Additional check: if user exists but user is accessing logout page,
  // allow it to proceed (this helps with logout flow)
  if (user && requestUrl.pathname === '/logout') {
    return res;
  }

  // Check if any users exist (for registration redirect)
  const { data: users } = await supabase.from('profiles').select('*');

  // Debug logging to check what's happening with the profile query
  // console.log('Middleware: Users query result:', {
  //   hasUsers: users && users.length > 0,
  //   usersCount: users?.length || 0,
  //   hasError: !!usersError,
  //   errorMessage: usersError?.message || 'No error'
  // });

  const hasUsers = users && users.length > 0;

  // Redirect to /register if no users exist and not already on /register
  if (!hasUsers && requestUrl.pathname !== '/register') {
    return NextResponse.redirect(new URL('/register', req.url));
  }

  // Check if this is an invitation registration
  const isInvitation = requestUrl.searchParams.get('invitation') === 'true';

  // Define public paths that don't require authentication
  const publicPaths = [
    '/', // Add root path to public paths
    '/login',
    '/otp',
    '/register',
    '/forgot-password',
    '/reset-password',
    '/unauthorized',
    '/shop', // Allow public access to shop
    '/images', // Allow public access to images
    '/sitemap.xml', // Allow public access to main sitemap
    '/sitemap-index.xml', // Allow public access to sitemap index
    '/sitemap-parts.xml', // Allow public access to parts sitemap
    '/sitemap-categories.xml', // Allow public access to categories sitemap
    '/robots.txt', // Allow public access to robots.txt
    '/api/parts', // Allow public access to parts API for public listing
    '/api/parts/search', // Allow public access to parts search API
    '/api/parts/filter-by-car', // Allow public access to car filter API
    '/api/car/brands', // Allow public access to car brands API
    '/api/car/models', // Allow public access to car models API
    '/api/car/generations', // Allow public access to car generations API
    '/api/car/variations', // Allow public access to car variations API
    '/api/car/trims' // Allow public access to car trims API
  ];

  // Handle unauthenticated users or invalid user
  const isValidUser = user && !userError;

  if (!isValidUser) {
    // Allow access to public paths
    if (publicPaths.some(path => requestUrl.pathname === path || requestUrl.pathname.startsWith(path + '/'))) {
      return res;
    }

    // Special case for shop item details pages - using the product/[slug] route
    if (requestUrl.pathname.match(/^\/shop\/product\/[\w-]+$/)) {
      return res;
    }

    // Special case for shop item details pages - using the p/[id] route (redirect)
    if (requestUrl.pathname.match(/^\/shop\/p\/\d+$/)) {
      return res;
    }

    // Redirect to login for all other paths
    return NextResponse.redirect(new URL('/login', req.url));
  } else {
    // User has a valid session

    // Redirect away from auth pages and base URL
    // But allow access to registration page if it's an invitation
    // Also check if this is a forced logout (allow login page access)
    const isForcedLogout = requestUrl.searchParams.get('logout') === 'true';

    if (
      (requestUrl.pathname === '/register' && !isInvitation) ||
      (requestUrl.pathname === '/login' && !isForcedLogout) ||
      requestUrl.pathname === '/otp' ||
      requestUrl.pathname === '/forgot-password' ||
      requestUrl.pathname === '/reset-password'
      // Removed root path redirection to allow landing page for all users
    ) {
      return NextResponse.redirect(new URL('/dashboard', req.url));
    }

    // For admin routes, check if user has Super Admin role
    if (requestUrl.pathname.startsWith('/admin')) {
      const isSuperAdmin = await hasSuperAdminRole(req);

      // console.log('Middleware: Super Admin check from cookies:', isSuperAdmin);

      if (isSuperAdmin) {
        // console.log('Middleware: User is Super Admin, granting access to admin route');
        return res;
      } else {
        // console.log('Middleware: User is NOT Super Admin, redirecting to unauthorized');
        return NextResponse.redirect(new URL('/unauthorized', req.url));
      }
    }

    // Protect dashboard routes
    if (requestUrl.pathname.startsWith('/dashboard') ||
        requestUrl.pathname.startsWith('/parts') ||
        requestUrl.pathname.startsWith('/categories') ||
        requestUrl.pathname.startsWith('/cars') ||
        requestUrl.pathname.startsWith('/clients') ||
        requestUrl.pathname.startsWith('/sales') ||
        requestUrl.pathname.startsWith('/storage') ||
        requestUrl.pathname.startsWith('/settings') ||
        requestUrl.pathname.startsWith('/profile')) {
      // User is already authenticated at this point, so just allow access
      // console.log('Middleware: User is authenticated, granting access to dashboard route');
      return res;
    }
  }

  return res;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images (public image files)
     * - sitemap files (sitemap.xml, sitemap-*.xml)
     * - robots.txt
     */
    '/((?!_next/static|_next/image|favicon.ico|images|sitemap.*\\.xml|robots\\.txt).*)',

    // Include specific API routes that need protection
    '/api/parts/add',
    '/api/parts/update',
    '/api/parts/delete',
    '/api/parts/upload',
    '/api/parts/manage',
    '/api/admin/:path*',
    '/api/dashboard/:path*',
    '/api/rbac/:path*',
    '/api/users/:path*',
    '/api/storage/:path*',
    '/api/categories/:path*',
    '/api/cars/:path*',
    '/api/sales/:path*',
    '/api/clients/:path*',
    '/api/mpesa/:path*',
  ],
};