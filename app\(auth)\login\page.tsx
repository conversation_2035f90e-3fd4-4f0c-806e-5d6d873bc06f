// app/login/page.tsx
import AuthLayout from '@/app/layouts/AuthLayout';
import { createClient } from '@supabase/supabase-js';
import LoginForm from './components/LoginForm';
import AdminRegistrationForm from '../register/components/adminRegistrationForm';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

export default async function LoginPage() {
  // Create a Supabase client using cookies
  const cookieStore = await cookies();

  // Use service role key for admin operations
  const supabaseAdmin = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  );

  // Check if user is already logged in
  const supabaseClient = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
      },
    }
  );

  // Use getUser() instead of getSession() for security
  const { data: { user } } = await supabaseClient.auth.getUser();

  // If user is already logged in, redirect to profile
  if (user) {
    redirect('/profile');
  }

  // Check for existing auth users
  const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers();

  if (error) {
    console.error('Error checking for users:', error);
    return (
      <AuthLayout>
        <div className="text-red-500">Error loading authentication page</div>
      </AuthLayout>
    );
  }

  const hasUsers = users && users.length > 0;

  return (
    <AuthLayout>
      <div className="w-full max-w-md space-y-4">
        {hasUsers ? (
          <>
            <h2 className="text-2xl font-bold">Welcome Back</h2>
            <LoginForm />
          </>
        ) : (
          <>
            <h2 className="text-2xl font-bold">Create Admin Account</h2>
            <p className="text-sm text-muted-foreground">
              No users found. Please create the initial admin account
            </p>
            <AdminRegistrationForm />
          </>
        )}
      </div>
    </AuthLayout>
  );
}