import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import AnimatedEllipsisLoader from '../AnimatedEllipsisLoader';

interface StatusMessage {
  id: string;
  message: string;
  completed?: boolean;
}

interface StatusLabelProps {
  currentTask?: string;
  isProcessing: boolean;
  className?: string;
  ellipsisProps?: {
    textColor?: string;
    bgColor?: string;
    numDots?: number;
    animationDuration?: number;
  };
}

const StatusLabel: React.FC<StatusLabelProps> = ({
  currentTask = '',
  isProcessing,
  className = '',
  ellipsisProps = {},
}) => {
  const [messages, setMessages] = useState<StatusMessage[]>([]);
  const messagesRef = useRef<StatusMessage[]>([]);
  const [showLabel, setShowLabel] = useState(false);
  
  // Updates the messages array whenever currentTask changes
  useEffect(() => {
    if (currentTask && isProcessing) {
      const newMessage: StatusMessage = {
        id: `msg-${Date.now()}`,
        message: currentTask,
        completed: false,
      };
      
      setMessages(prev => [...prev, newMessage]);
      messagesRef.current = [...messagesRef.current, newMessage];
      setShowLabel(true);
    } else if (messagesRef.current.length > 0 && !isProcessing) {
      // Mark the last message as completed
      const updatedMessages = [...messagesRef.current];
      const lastIndex = updatedMessages.length - 1;
      
      if (lastIndex >= 0 && !updatedMessages[lastIndex].completed) {
        updatedMessages[lastIndex] = {
          ...updatedMessages[lastIndex],
          completed: true,
        };
        
        setMessages(updatedMessages);
        messagesRef.current = updatedMessages;
        
        // Hide label after all processes are complete
        const timeout = setTimeout(() => {
          setShowLabel(false);
        }, 2000);
        
        return () => clearTimeout(timeout);
      }
    }
  }, [currentTask, isProcessing]);
  
  // Clear messages when all processes are done and label is hidden
  useEffect(() => {
    if (!showLabel && !isProcessing) {
      const timeout = setTimeout(() => {
        setMessages([]);
        messagesRef.current = [];
      }, 500);
      
      return () => clearTimeout(timeout);
    }
  }, [showLabel, isProcessing]);
  
  return (
    <AnimatePresence>
      {showLabel && (
        <motion.div
          className={`fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-white dark:bg-gray-800 shadow-lg rounded-md px-4 py-3 max-w-md w-full overflow-hidden ${className}`}
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          transition={{ duration: 0.5, ease: "easeInOut" }}
        >
          <div className="relative overflow-hidden max-h-32">
            <AnimatePresence>
              {messages.map((msg, index) => {
                const isLatest = index === messages.length - 1;
                
                return (
                  <motion.div
                    key={msg.id}
                    className={`text-center text-gray-700 dark:text-gray-300 font-medium ${isLatest ? 'font-bold' : 'text-opacity-60 dark:text-opacity-60'}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    {msg.message}
                    {isLatest && !msg.completed && (
                      <AnimatedEllipsisLoader
                        textColor={ellipsisProps.textColor}
                        bgColor={ellipsisProps.bgColor}
                        numDots={ellipsisProps.numDots}
                        animationDuration={ellipsisProps.animationDuration}
                      />
                    )}
                  </motion.div>
                );
              })}
            </AnimatePresence>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default StatusLabel;