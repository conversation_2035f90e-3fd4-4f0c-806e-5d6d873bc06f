'use client';

import React from 'react';
import Link from 'next/link';
import { AlertTriangle, ArrowLeft } from 'lucide-react';
import { useClientAuth } from '../../components/ClientAuthContext';

const UnauthorizedPage: React.FC = () => {
  const { user, isLoading } = useClientAuth();
  
  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] p-4">
      <div className="p-4 rounded-full bg-amber-100 text-amber-600 mb-4">
        <AlertTriangle className="w-12 h-12" />
      </div>
      
      <h1 className="text-2xl font-bold text-gray-900 mb-2 text-center">
        Access Denied
      </h1>
      
      <p className="text-gray-600 mb-6 text-center max-w-md">
        {isLoading ? (
          'Checking your access...'
        ) : user ? (
          'Your account does not have client access. Please contact support if you believe this is an error.'
        ) : (
          'You need to be logged in as a client to access this area.'
        )}
      </p>
      
      <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
        <Link
          href="/"
          className="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 flex items-center justify-center"
        >
          <ArrowLeft className="w-4 h-4 mr-2" /> Return to Home
        </Link>
        
        {!user && (
          <Link
            href="/login?redirect=/client"
            className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 flex items-center justify-center"
          >
            Sign In
          </Link>
        )}
      </div>
    </div>
  );
};

export default UnauthorizedPage;
