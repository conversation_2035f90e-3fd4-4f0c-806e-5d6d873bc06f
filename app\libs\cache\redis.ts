import { Redis } from '@upstash/redis';

// Initialize Redis client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL || '',
  token: process.env.UPSTASH_REDIS_REST_TOKEN || '',
});

// Cache configuration
const CACHE_TTL = 60 * 60; // 1 hour in seconds

export interface CacheOptions {
  ttl?: number;
  prefix?: string;
}

export class CacheService {
  private prefix: string;
  private ttl: number;

  constructor(options: CacheOptions = {}) {
    this.prefix = options.prefix || 'cache:';
    this.ttl = options.ttl || CACHE_TTL;
  }

  private getKey(key: string): string {
    return `${this.prefix}${key}`;
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const data = await redis.get<T>(this.getKey(key));
      return data;
    } catch (error) {
      console.error('Redis get error:', error);
      return null;
    }
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    try {
      await redis.set(this.getKey(key), value, {
        ex: ttl || this.ttl,
      });
    } catch (error) {
      console.error('Redis set error:', error);
    }
  }

  async delete(key: string): Promise<void> {
    try {
      await redis.del(this.getKey(key));
    } catch (error) {
      console.error('Redis delete error:', error);
    }
  }

  async invalidatePattern(pattern: string): Promise<void> {
    try {
      const keys = await redis.keys(`${this.prefix}${pattern}`);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
    } catch (error) {
      console.error('Redis invalidate pattern error:', error);
    }
  }
}

// Create instances for different types of data
export const productCache = new CacheService({ prefix: 'product:', ttl: 60 * 60 }); // 1 hour
export const categoryCache = new CacheService({ prefix: 'category:', ttl: 60 * 60 * 24 }); // 24 hours
export const searchCache = new CacheService({ prefix: 'search:', ttl: 60 * 15 }); // 15 minutes 