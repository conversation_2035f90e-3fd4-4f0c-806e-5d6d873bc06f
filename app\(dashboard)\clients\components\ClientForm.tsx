'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/app/libs/supabase/client';
import { motion } from 'framer-motion';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { User, Wrench, Store, Briefcase, Check, X, Plus, Trash2 } from 'lucide-react';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import PhoneInput from '@/app/components/ui/inputs/PhoneInput';
import ContactPersonsManager from './ContactPersonsManager';

// Types
interface ClientCategory {
  id: string;
  name: string;
  description: string | null;
}

interface CategoryField {
  id: string;
  category_id: string;
  field_name: string;
  field_label: string;
  field_type: string;
  is_required: boolean;
  description: string | null;
}

interface ContactPerson {
  id?: string;
  name: string;
  phone_number: string;
  email: string;
  position: string;
  is_primary: boolean;
  is_active: boolean;
  notes: string;
}

interface ClientData {
  id?: string;
  name: string;
  phone_number: string;
  email: string;
  client_type: 'credit' | 'cash';
  category_id: string;
  can_receive_credit: boolean;
  is_active: boolean;
  custom_fields?: Record<string, string>;
}

// Form schema
const clientSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  phone_number: z.string().min(1, 'Phone number is required'),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  client_type: z.enum(['credit', 'cash']),
  category_id: z.string().min(1, 'Category is required'),
  can_receive_credit: z.boolean().default(false),
  is_active: z.boolean().default(true),
  custom_fields: z.record(z.string(), z.string()).optional(),
});

type ClientFormValues = z.infer<typeof clientSchema>;

// Props
interface ClientFormProps {
  clientId?: string; // If provided, we're editing an existing client
  onSuccess?: () => void;
  isModal?: boolean; // If true, the form is being rendered in a modal
}

// Category Icon Component
interface CategoryIconProps {
  categoryName: string;
  selected: boolean;
}

const CategoryIcon: React.FC<CategoryIconProps> = ({ categoryName, selected }) => {
  const baseClasses = "w-12 h-12 p-3 rounded-full flex items-center justify-center";
  const selectedClasses = selected
    ? "bg-teal-100 text-teal-600 border-2 border-teal-500"
    : "bg-gray-100 text-gray-500 border border-gray-300 hover:bg-gray-200";

  const getIcon = () => {
    switch(categoryName) {
      case 'Individual':
        return <User className="w-6 h-6" />;
      case 'Garage':
        return <Wrench className="w-6 h-6" />;
      case 'Shop':
        return <Store className="w-6 h-6" />;
      case 'Broker':
        return <Briefcase className="w-6 h-6" />;
      default:
        return <User className="w-6 h-6" />;
    }
  };

  return (
    <div className={`${baseClasses} ${selectedClasses}`}>
      {getIcon()}
    </div>
  );
};

// Main Form Component
const ClientForm: React.FC<ClientFormProps> = ({ clientId, onSuccess, isModal = false }) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<ClientCategory[]>([]);
  const [categoryFields, setCategoryFields] = useState<CategoryField[]>([]);
  const [clientData, setClientData] = useState<Record<string, any> | null>(null);
  const [contactPersons, setContactPersons] = useState<ContactPerson[]>([]);

  // Form setup
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors }
  } = useForm<ClientFormValues>({
    resolver: zodResolver(clientSchema),
    defaultValues: {
      name: '',
      phone_number: '',
      email: '',
      client_type: 'cash',
      category_id: '',
      can_receive_credit: false,
      is_active: true,
      custom_fields: {},
    }
  });

  // Watch for category changes
  const selectedCategoryId = watch('category_id');
  const selectedClientType = watch('client_type');

  // Fetch categories and client data (if editing)
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const supabase = createClient();

        // Fetch categories
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('client_categories')
          .select('*')
          .order('name');

        if (categoriesError) throw new Error(categoriesError.message);
        setCategories(categoriesData || []);

        // If we have a clientId, fetch the client data
        if (clientId) {
          const { data: clientData, error: clientError } = await supabase
            .from('clients')
            .select('*')
            .eq('id', clientId)
            .single();

          if (clientError) throw new Error(clientError.message);
          setClientData(clientData);

          // Fetch custom fields data
          const { data: customFieldsData, error: customFieldsError } = await supabase
            .from('client_data')
            .select('field_id, value, category_fields(field_name)')
            .eq('client_id', clientId);

          if (customFieldsError) throw new Error(customFieldsError.message);

          // Transform custom fields data
          const customFields: Record<string, string> = {};
          customFieldsData?.forEach(field => {
            if (field.category_fields?.field_name) {
              customFields[field.category_fields.field_name] = field.value;
            }
          });

          // Set form values
          reset({
            name: clientData.name || '',
            phone_number: clientData.phone_number || '',
            email: clientData.email || '',
            client_type: clientData.client_type || 'cash',
            category_id: clientData.category_id || '',
            can_receive_credit: clientData.can_receive_credit || false,
            is_active: clientData.is_active !== undefined ? clientData.is_active : true,
            custom_fields: customFields,
          });

          // Fetch category fields for the selected category
          if (clientData.category_id) {
            const { data: fieldsData, error: fieldsError } = await supabase
              .from('category_fields')
              .select('*')
              .eq('category_id', clientData.category_id)
              .order('field_label');

            if (fieldsError) throw new Error(fieldsError.message);
            setCategoryFields(fieldsData || []);
          }

          // Fetch contact persons for existing client
          const { data: contactsData, error: contactsError } = await supabase
            .from('client_contacts')
            .select('*')
            .eq('client_id', clientId)
            .order('is_primary', { ascending: false });

          if (contactsError) throw new Error(contactsError.message);

          const contacts: ContactPerson[] = contactsData?.map(contact => ({
            id: contact.id,
            name: contact.name,
            phone_number: contact.phone_number,
            email: contact.email || '',
            position: contact.position || '',
            is_primary: contact.is_primary,
            is_active: contact.is_active,
            notes: contact.notes || ''
          })) || [];

          setContactPersons(contacts);
        } else if (categoriesData && categoriesData.length > 0) {
          // For new clients, set the first category as default
          setValue('category_id', categoriesData[0].id);
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [clientId, reset, setValue]);

  // Fetch category fields when category changes
  useEffect(() => {
    const fetchCategoryFields = async () => {
      if (!selectedCategoryId) return;

      try {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('category_fields')
          .select('*')
          .eq('category_id', selectedCategoryId)
          .order('field_label');

        if (error) throw error;
        setCategoryFields(data || []);
      } catch (err) {
        console.error('Error fetching category fields:', err);
      }
    };

    fetchCategoryFields();
  }, [selectedCategoryId]);

  // Auto-populate contact person fields when primary contact changes
  useEffect(() => {
    const primaryContact = contactPersons.find(contact => contact.is_primary);

    if (primaryContact && categoryFields.length > 0) {
      // Find contact person fields and update them
      categoryFields.forEach(field => {
        const isContactPersonField = field.field_label?.toLowerCase().includes('contact person') ||
                                   field.field_name?.toLowerCase().includes('contact_person');

        if (isContactPersonField) {
          setValue(`custom_fields.${field.field_name}`, primaryContact.name);
        }
      });
    }
  }, [contactPersons, categoryFields, setValue]);

  // Handle form submission
  const onSubmit = async (data: ClientFormValues) => {
    setIsSaving(true);
    setError(null);

    try {
      const supabase = createClient();

      // Prepare client data
      const clientData = {
        name: data.name,
        phone_number: data.phone_number,
        email: data.email || null,
        client_type: data.client_type,
        category_id: data.category_id,
        can_receive_credit: data.client_type === 'credit' ? data.can_receive_credit : false,
        is_active: data.is_active,
      };

      let finalClientId = '';

      if (clientId) {
        // Update existing client
        const { data: updatedClient, error } = await supabase
          .from('clients')
          .update(clientData)
          .eq('id', clientId)
          .select()
          .single();

        if (error) throw error;
        finalClientId = updatedClient.id;
      } else {
        // Create new client
        const { data: newClient, error } = await supabase
          .from('clients')
          .insert(clientData)
          .select()
          .single();

        if (error) throw error;
        finalClientId = newClient.id;
      }

      // Handle custom fields if any
      if (data.custom_fields && Object.keys(data.custom_fields).length > 0 && categoryFields.length > 0) {
        // First, get existing custom field data if updating
        let existingFields: Record<string, string> = {};

        if (finalClientId) {
          const { data: existingData } = await supabase
            .from('client_data')
            .select('id, field_id, category_fields(field_name)')
            .eq('client_id', finalClientId);

          if (existingData) {
            existingData.forEach(item => {
              if (item.category_fields?.field_name) {
                existingFields[item.category_fields.field_name] = item.id;
              }
            });
          }
        }

        // Prepare upserts for custom fields
        const fieldUpserts = [];

        for (const field of categoryFields) {
          const value = data.custom_fields[field.field_name] || '';

          if (existingFields[field.field_name]) {
            // Update existing field
            fieldUpserts.push({
              id: existingFields[field.field_name],
              client_id: finalClientId,
              field_id: field.id,
              value,
            });
          } else {
            // Insert new field
            fieldUpserts.push({
              client_id: finalClientId,
              field_id: field.id,
              value,
            });
          }
        }

        if (fieldUpserts.length > 0) {
          const { error: fieldsError } = await supabase
            .from('client_data')
            .upsert(fieldUpserts);

          if (fieldsError) throw fieldsError;
        }
      }

      // Save contact persons if any
      const selectedCategory = categories.find(c => c.id === data.category_id);
      const shouldSaveContacts = selectedCategory?.name === 'Garage' || selectedCategory?.name === 'Shop' || selectedCategory?.name === 'Broker';

      if (shouldSaveContacts && contactPersons.length > 0) {
        // Delete existing contacts first
        await supabase
          .from('client_contacts')
          .delete()
          .eq('client_id', finalClientId);

        // Insert new contacts
        const contactsToInsert = contactPersons.map(contact => ({
          client_id: finalClientId,
          name: contact.name,
          phone_number: contact.phone_number,
          email: contact.email || null,
          position: contact.position,
          is_primary: contact.is_primary,
          is_active: contact.is_active,
          notes: contact.notes || null
        }));

        const { error: contactsError } = await supabase
          .from('client_contacts')
          .insert(contactsToInsert);

        if (contactsError) throw contactsError;
      }

      // Success!
      if (onSuccess) {
        onSuccess();
      } else if (!isModal) {
        router.push(`/clients/${finalClientId}`);
      }
    } catch (err) {
      console.error('Error saving client:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setIsSaving(false);
    }
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p>{error}</p>
        </div>
      )}

      {/* Client Type Selection */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-lg font-medium text-gray-800 mb-4">Client Type</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Controller
            name="client_type"
            control={control}
            render={({ field }) => (
              <div
                className={`p-4 rounded-lg border cursor-pointer ${
                  field.value === 'cash'
                    ? 'border-teal-500 bg-teal-50'
                    : 'border-gray-300 hover:bg-gray-50'
                }`}
                onClick={() => field.onChange('cash')}
              >
                <div className="flex items-center">
                  <div className={`w-5 h-5 rounded-full border flex items-center justify-center ${
                    field.value === 'cash' ? 'border-teal-500' : 'border-gray-400'
                  }`}>
                    {field.value === 'cash' && <Check className="w-3 h-3 text-teal-500" />}
                  </div>
                  <span className="ml-2 font-medium">Cash Client</span>
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  Client pays for parts at the time of purchase.
                </p>
              </div>
            )}
          />

          <Controller
            name="client_type"
            control={control}
            render={({ field }) => (
              <div
                className={`p-4 rounded-lg border cursor-pointer ${
                  field.value === 'credit'
                    ? 'border-teal-500 bg-teal-50'
                    : 'border-gray-300 hover:bg-gray-50'
                }`}
                onClick={() => field.onChange('credit')}
              >
                <div className="flex items-center">
                  <div className={`w-5 h-5 rounded-full border flex items-center justify-center ${
                    field.value === 'credit' ? 'border-teal-500' : 'border-gray-400'
                  }`}>
                    {field.value === 'credit' && <Check className="w-3 h-3 text-teal-500" />}
                  </div>
                  <span className="ml-2 font-medium">Credit Client</span>
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  Client can purchase parts on credit and pay later.
                </p>
              </div>
            )}
          />
        </div>

        {/* Credit Options */}
        {selectedClientType === 'credit' && (
          <div className="mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Credit Options</h3>

            <Controller
              name="can_receive_credit"
              control={control}
              render={({ field }) => (
                <label className="flex items-center space-x-2 cursor-pointer">
                  <div className={`w-5 h-5 rounded border flex items-center justify-center ${
                    field.value ? 'border-teal-500 bg-teal-500' : 'border-gray-400'
                  }`}>
                    {field.value && <Check className="w-3 h-3 text-white" />}
                  </div>
                  <input
                    type="checkbox"
                    className="hidden"
                    checked={field.value}
                    onChange={(e) => field.onChange(e.target.checked)}
                  />
                  <span>Allow this client to receive credit</span>
                </label>
              )}
            />
          </div>
        )}
      </div>

      {/* Client Category Selection */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-lg font-medium text-gray-800 mb-4">Client Category</h2>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {categories.map((category) => (
            <Controller
              key={category.id}
              name="category_id"
              control={control}
              render={({ field }) => (
                <div
                  className={`flex flex-col items-center p-4 rounded-lg border cursor-pointer ${
                    field.value === category.id
                      ? 'border-teal-500 bg-teal-50'
                      : 'border-gray-300 hover:bg-gray-50'
                  }`}
                  onClick={() => field.onChange(category.id)}
                >
                  <CategoryIcon
                    categoryName={category.name}
                    selected={field.value === category.id}
                  />
                  <span className="mt-2 font-medium text-center">{category.name}</span>
                  {category.description && (
                    <p className="text-xs text-gray-500 mt-1 text-center">{category.description}</p>
                  )}
                </div>
              )}
            />
          ))}
        </div>

        {errors.category_id && (
          <p className="mt-2 text-sm text-red-600">{errors.category_id.message}</p>
        )}
      </div>

      {/* Basic Information */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-lg font-medium text-gray-800 mb-4">Basic Information</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Client Name <span className="text-red-500">*</span>
            </label>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <input
                  {...field}
                  type="text"
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 ${
                    errors.name ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter client name"
                />
              )}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </label>
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <input
                  {...field}
                  type="email"
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 ${
                    errors.email ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter email address"
                />
              )}
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Phone Number <span className="text-red-500">*</span>
            </label>
            <Controller
              name="phone_number"
              control={control}
              render={({ field }) => (
                <PhoneInput
                  id="phone_number"
                  value={field.value}
                  onChange={field.onChange}
                  initialCountryCode="+254"
                  errorMessage={errors.phone_number?.message}
                />
              )}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <Controller
              name="is_active"
              control={control}
              render={({ field }) => (
                <div className="flex items-center space-x-4">
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <div className={`w-5 h-5 rounded-full border flex items-center justify-center ${
                      field.value ? 'border-teal-500' : 'border-gray-400'
                    }`}>
                      {field.value && <Check className="w-3 h-3 text-teal-500" />}
                    </div>
                    <input
                      type="radio"
                      className="hidden"
                      checked={field.value}
                      onChange={() => field.onChange(true)}
                    />
                    <span>Active</span>
                  </label>

                  <label className="flex items-center space-x-2 cursor-pointer">
                    <div className={`w-5 h-5 rounded-full border flex items-center justify-center ${
                      !field.value ? 'border-red-500' : 'border-gray-400'
                    }`}>
                      {!field.value && <X className="w-3 h-3 text-red-500" />}
                    </div>
                    <input
                      type="radio"
                      className="hidden"
                      checked={!field.value}
                      onChange={() => field.onChange(false)}
                    />
                    <span>Inactive</span>
                  </label>
                </div>
              )}
            />
          </div>
        </div>
      </div>

      {/* Category-specific Fields */}
      {categoryFields.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-medium text-gray-800 mb-4">
            {categories.find(c => c.id === selectedCategoryId)?.name} Details
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {categoryFields.map((field) => (
              <div key={field.id}>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {field.field_label}
                  {field.is_required && <span className="text-red-500">*</span>}
                </label>

                <Controller
                  name={`custom_fields.${field.field_name}`}
                  control={control}
                  defaultValue=""
                  render={({ field: formField }) => {
                    // Check if this is a contact person field and get primary contact
                    const isContactPersonField = field.field_label?.toLowerCase().includes('contact person') ||
                                               field.field_name?.toLowerCase().includes('contact_person');
                    const primaryContact = contactPersons.find(contact => contact.is_primary);

                    // Auto-populate contact person field with primary contact name
                    const displayValue = isContactPersonField && primaryContact
                      ? primaryContact.name
                      : (formField.value || '');

                    switch (field.field_type) {
                      case 'text':
                      case 'number':
                      case 'email':
                        return (
                          <div className="relative">
                            <input
                              type={field.field_type}
                              value={displayValue}
                              onChange={isContactPersonField ? () => {} : formField.onChange}
                              className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 ${
                                isContactPersonField && primaryContact ? 'bg-gray-50' : ''
                              }`}
                              placeholder={`Enter ${field.field_label.toLowerCase()}`}
                              readOnly={isContactPersonField && primaryContact}
                            />
                            {isContactPersonField && primaryContact && (
                              <div className="mt-1 text-xs text-teal-600 flex items-center">
                                <User className="w-3 h-3 mr-1" />
                                Auto-filled from primary contact person
                              </div>
                            )}
                          </div>
                        );
                      case 'textarea':
                        return (
                          <textarea
                            value={formField.value || ''}
                            onChange={formField.onChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                            placeholder={`Enter ${field.field_label.toLowerCase()}`}
                            rows={3}
                          />
                        );
                      case 'boolean':
                        return (
                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={formField.value === 'true'}
                              onChange={(e) => formField.onChange(e.target.checked ? 'true' : 'false')}
                              className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                            />
                            <span>Yes</span>
                          </div>
                        );
                      default:
                        return (
                          <input
                            type="text"
                            value={formField.value || ''}
                            onChange={formField.onChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                            placeholder={`Enter ${field.field_label.toLowerCase()}`}
                          />
                        );
                    }
                  }}
                />

                {field.description && (
                  <p className="mt-1 text-xs text-gray-500">{field.description}</p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Contact Persons Manager */}
      <ContactPersonsManager
        clientId={clientId}
        categoryName={categories.find(c => c.id === selectedCategoryId)?.name}
        contacts={contactPersons}
        onChange={setContactPersons}
        isEditing={true}
        showSaveButton={false}
      />

      {/* Form Actions */}
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={isModal ? onSuccess : () => router.back()}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          disabled={isSaving}
        >
          Cancel
        </button>

        <button
          type="submit"
          className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 flex items-center"
          disabled={isSaving}
        >
          {isSaving ? (
            <>
              <LoadingSpinner size="sm" className="mr-2" />
              Saving...
            </>
          ) : (
            <>
              <Check className="w-4 h-4 mr-2" />
              {clientId ? 'Update Client' : 'Create Client'}
            </>
          )}
        </button>
      </div>
    </form>
  );
};

export default ClientForm;
