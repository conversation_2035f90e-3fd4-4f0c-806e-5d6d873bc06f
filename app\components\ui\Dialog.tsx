import React, { ReactNode, useState, forwardRef, ForwardedRef, PropsWithChildren } from 'react';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import { cn } from '@/app/utils/cn';

// --- Types ---

interface DialogProps extends DialogPrimitive.DialogProps {
}

interface DialogTriggerProps extends React.PropsWithChildren {
}

interface DialogContentProps extends DialogPrimitive.DialogContentProps {
  children?: ReactNode;
  className?: string;
}

// --- Components ---

const Dialog = ({ children, ...props }: PropsWithChildren<DialogProps>) => {
  return (
    <DialogPrimitive.Root {...props}>
      {children}
    </DialogPrimitive.Root>
  );
};

const DialogTrigger = forwardRef<React.ElementRef<typeof DialogPrimitive.Trigger>, DialogTriggerProps>(
  ({ children, ...props }, ref) => {
    return (
      <DialogPrimitive.Trigger ref={ref} asChild {...props}>
        {children}
      </DialogPrimitive.Trigger>
    );
  }
);
DialogTrigger.displayName = DialogPrimitive.Trigger.displayName;

const DialogPortal = ({ children, ...props }: DialogPrimitive.DialogPortalProps) => {
    return (
        <DialogPrimitive.Portal {...props}>
            {children}
        </DialogPrimitive.Portal>
    )
}
DialogPortal.displayName = DialogPrimitive.Portal.displayName

const DialogOverlay = forwardRef<React.ElementRef<typeof DialogPrimitive.Overlay>, DialogPrimitive.DialogOverlayProps>(
    ({ className, ...props }, ref) => {
        return (
            <DialogPrimitive.Overlay
                ref={ref}
                className={cn(
                    "fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
                    className
                )}
                {...props}
            />
        )
    }
)

DialogOverlay.displayName = DialogPrimitive.Overlay.displayName

const DialogContent = forwardRef<React.ElementRef<typeof DialogPrimitive.Content>, DialogContentProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <DialogPortal>
          <DialogOverlay />
          <DialogPrimitive.Content
              ref={ref}
              className={cn(
                  "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full",
                  className
              )}
              {...props}
          >
              {children}
          </DialogPrimitive.Content>
      </DialogPortal>

    );
  }
);
DialogContent.displayName = DialogPrimitive.Content.displayName;

const DialogHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col space-y-1.5 text-center sm:text-left",
      className
    )}
    {...props}
  />
)
DialogHeader.displayName = "DialogHeader"

const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
      className
    )}
    {...props}
  />
)
DialogFooter.displayName = "DialogFooter"

const DialogTitle = forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  DialogPrimitive.DialogTitleProps
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn(
      "text-lg font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
DialogTitle.displayName = DialogPrimitive.Title.displayName

const DialogDescription = forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  DialogPrimitive.DialogDescriptionProps
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
DialogDescription.displayName = DialogPrimitive.Description.displayName

// --- Exports ---

export {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
};