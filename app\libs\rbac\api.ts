import { createClient } from '@/app/libs/supabase/client';
import { Database } from '@/app/types/database';

type Role = Database['public']['Tables']['roles']['Row'];
type Permission = Database['public']['Tables']['permissions']['Row'];
type UserRole = Database['public']['Tables']['user_roles']['Row'];
type RolePermission = Database['public']['Tables']['role_permissions']['Row'];
type UserPermission = Database['public']['Tables']['user_permissions']['Row'];
type Profile = Database['public']['Tables']['profiles']['Row'];
type AuditLog = Database['public']['Tables']['audit_log']['Row'];

// Role Management
export const getRoles = async (): Promise<{ data: Role[] | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('roles')
      .select('*')
      .order('name');

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching roles:', error);
    return { data: null, error: error as Error };
  }
};

export const getRole = async (id: string): Promise<{ data: Role | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('roles')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error(`Error fetching role with ID ${id}:`, error);
    return { data: null, error: error as Error };
  }
};

export const createRole = async (
  name: string,
  description?: string
): Promise<{ data: Role | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('roles')
      .insert([{ name, description }])
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error creating role:', error);
    return { data: null, error: error as Error };
  }
};

export const updateRole = async (
  id: string,
  updates: { name?: string; description?: string }
): Promise<{ data: Role | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('roles')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error(`Error updating role with ID ${id}:`, error);
    return { data: null, error: error as Error };
  }
};

export const deleteRole = async (id: string): Promise<{ error: Error | null }> => {
  try {
    const supabase = createClient();
    const { error } = await supabase
      .from('roles')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error(`Error deleting role with ID ${id}:`, error);
    return { error: error as Error };
  }
};

// Permission Management
export const getPermissions = async (
  category?: string
): Promise<{ data: Permission[] | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    let query = supabase.from('permissions').select('*');

    if (category) {
      query = query.eq('category', category);
    }

    const { data, error } = await query.order('category').order('name');

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching permissions:', error);
    return { data: null, error: error as Error };
  }
};

export const getPermissionCategories = async (): Promise<{ data: string[] | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('permissions')
      .select('category')
      .order('category');

    if (error) throw error;

    // Extract unique categories
    const categoryMap: Record<string, boolean> = {};
    const categories: string[] = [];

    data.forEach(item => {
      if (!categoryMap[item.category]) {
        categoryMap[item.category] = true;
        categories.push(item.category);
      }
    });

    return { data: categories, error: null };
  } catch (error) {
    console.error('Error fetching permission categories:', error);
    return { data: null, error: error as Error };
  }
};

export const getPermission = async (id: string): Promise<{ data: Permission | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('permissions')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error(`Error fetching permission with ID ${id}:`, error);
    return { data: null, error: error as Error };
  }
};

export const createPermission = async (
  name: string,
  category: string,
  description?: string
): Promise<{ data: Permission | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('permissions')
      .insert([{ name, category, description }])
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error creating permission:', error);
    return { data: null, error: error as Error };
  }
};

export const updatePermission = async (
  id: string,
  updates: { name?: string; category?: string; description?: string }
): Promise<{ data: Permission | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('permissions')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error(`Error updating permission with ID ${id}:`, error);
    return { data: null, error: error as Error };
  }
};

export const deletePermission = async (id: string): Promise<{ error: Error | null }> => {
  try {
    const supabase = createClient();
    const { error } = await supabase
      .from('permissions')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error(`Error deleting permission with ID ${id}:`, error);
    return { error: error as Error };
  }
};

// User Role Management
export const getUserRoles = async (userId: string): Promise<{ data: any[] | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('user_roles')
      .select('role_id, roles(*)')
      .eq('user_id', userId);

    if (error) throw error;

    // Extract role objects from the joined query
    const roles = data.map(item => item.roles);
    return { data: roles, error: null };
  } catch (error) {
    console.error(`Error fetching roles for user ${userId}:`, error);
    return { data: null, error: error as Error };
  }
};

export const getRoleUsers = async (roleId: string): Promise<{ data: any[] | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('user_roles')
      .select('user_id, profiles(*)')
      .eq('role_id', roleId);

    if (error) throw error;

    // Extract profile objects from the joined query
    const profiles = data.map(item => item.profiles);
    return { data: profiles, error: null };
  } catch (error) {
    console.error(`Error fetching users for role ${roleId}:`, error);
    return { data: null, error: error as Error };
  }
};

export const assignRoleToUser = async (
  userId: string,
  roleId: string
): Promise<{ data: UserRole | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('user_roles')
      .insert([{ user_id: userId, role_id: roleId }])
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error(`Error assigning role ${roleId} to user ${userId}:`, error);
    return { data: null, error: error as Error };
  }
};

export const removeRoleFromUser = async (
  userId: string,
  roleId: string
): Promise<{ error: Error | null }> => {
  try {
    const supabase = createClient();
    const { error } = await supabase
      .from('user_roles')
      .delete()
      .eq('user_id', userId)
      .eq('role_id', roleId);

    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error(`Error removing role ${roleId} from user ${userId}:`, error);
    return { error: error as Error };
  }
};

// Role Permission Management
export const getRolePermissions = async (roleId: string): Promise<{ data: any[] | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('role_permissions')
      .select('permission_id, permissions(*)')
      .eq('role_id', roleId);

    if (error) throw error;

    // Extract permission objects from the joined query
    const permissions = data.map(item => item.permissions);
    return { data: permissions, error: null };
  } catch (error) {
    console.error(`Error fetching permissions for role ${roleId}:`, error);
    return { data: null, error: error as Error };
  }
};

export const assignPermissionToRole = async (
  roleId: string,
  permissionId: string
): Promise<{ data: RolePermission | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('role_permissions')
      .insert([{ role_id: roleId, permission_id: permissionId }])
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error(`Error assigning permission ${permissionId} to role ${roleId}:`, error);
    return { data: null, error: error as Error };
  }
};

export const removePermissionFromRole = async (
  roleId: string,
  permissionId: string
): Promise<{ error: Error | null }> => {
  try {
    const supabase = createClient();
    const { error } = await supabase
      .from('role_permissions')
      .delete()
      .eq('role_id', roleId)
      .eq('permission_id', permissionId);

    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error(`Error removing permission ${permissionId} from role ${roleId}:`, error);
    return { error: error as Error };
  }
};

// User Permission Overrides
export const getUserPermissionOverrides = async (
  userId: string
): Promise<{ data: UserPermission[] | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('user_permissions')
      .select('*, permissions(*)')
      .eq('user_id', userId);

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error(`Error fetching permission overrides for user ${userId}:`, error);
    return { data: null, error: error as Error };
  }
};

export const getUserPermissionOverride = async (
  userId: string,
  permissionId: string
): Promise<{ data: UserPermission | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('user_permissions')
      .select('*')
      .eq('user_id', userId)
      .eq('permission_id', permissionId)
      .maybeSingle();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error(`Error fetching permission override for user ${userId} and permission ${permissionId}:`, error);
    return { data: null, error: error as Error };
  }
};

export const setUserPermissionOverride = async (
  userId: string,
  permissionId: string,
  hasPermission: boolean
): Promise<{ data: UserPermission | null; error: Error | null }> => {
  try {
    const supabase = createClient();

    // Check if an override already exists
    const { data: existingOverride } = await supabase
      .from('user_permissions')
      .select('id')
      .eq('user_id', userId)
      .eq('permission_id', permissionId)
      .maybeSingle();

    let result;

    if (existingOverride) {
      // Update existing override
      result = await supabase
        .from('user_permissions')
        .update({ has_permission: hasPermission })
        .eq('id', existingOverride.id)
        .select()
        .single();
    } else {
      // Create new override
      result = await supabase
        .from('user_permissions')
        .insert([{ user_id: userId, permission_id: permissionId, has_permission: hasPermission }])
        .select()
        .single();
    }

    const { data, error } = result;

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error(`Error setting permission override for user ${userId} and permission ${permissionId}:`, error);
    return { data: null, error: error as Error };
  }
};

export const removeUserPermissionOverride = async (
  userId: string,
  permissionId: string
): Promise<{ error: Error | null }> => {
  try {
    const supabase = createClient();
    const { error } = await supabase
      .from('user_permissions')
      .delete()
      .eq('user_id', userId)
      .eq('permission_id', permissionId);

    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error(`Error removing permission override for user ${userId} and permission ${permissionId}:`, error);
    return { error: error as Error };
  }
};

// User Management
export const getUsers = async (): Promise<{ data: Profile[] | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .order('full_name');

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching users:', error);
    return { data: null, error: error as Error };
  }
};

export const getUser = async (id: string): Promise<{ data: Profile | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error(`Error fetching user with ID ${id}:`, error);
    return { data: null, error: error as Error };
  }
};

// Audit Log
export const getAuditLogs = async (
  limit = 50,
  offset = 0
): Promise<{ data: AuditLog[] | null; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('audit_log')
      .select('*, profiles(*)')
      .order('timestamp', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    return { data: null, error: error as Error };
  }
};

// Permission Check
export const checkPermission = async (
  userId: string,
  permissionName: string
): Promise<{ data: boolean; error: Error | null }> => {
  try {
    const supabase = createClient();
    const { data, error } = await supabase.rpc('check_user_permission', {
      p_user_id: userId,
      p_permission_name: permissionName,
    });

    if (error) throw error;
    return { data: !!data, error: null };
  } catch (error) {
    console.error(`Error checking permission ${permissionName} for user ${userId}:`, error);
    return { data: false, error: error as Error };
  }
};
