'use client';

import React from 'react';

interface PartPageLayoutProps {
  mainContent: React.ReactNode;
  sidebarContent?: React.ReactNode; // Sidebar is optional
  similarProducts?: React.ReactNode; // Similar products section
}

const PartPageLayout: React.FC<PartPageLayoutProps> = ({ mainContent, sidebarContent, similarProducts }) => {
  const hasSidebar = Boolean(sidebarContent);
  const hasSimilarProducts = Boolean(similarProducts);

  return (
    <div className="bg-white">
      <div className="container mx-auto px-2 sm:px-4 py-4 max-w-full xl:max-w-screen-2xl">
        {/* Main Content and Sidebar Area */}
        <div className="flex flex-col lg:flex-row gap-4 mb-4">
          {/* Main Content Area */}
          <div className="bg-white rounded-lg shadow-sm overflow-hidden lg:flex-grow lg:w-3/4">
            <div className="p-4 md:p-6">
              <main className="w-full">
                {mainContent}
              </main>
            </div>
          </div>

          {/* Sidebar Area - Rendered below main content on mobile, side by side on desktop */}
          {hasSidebar && (
            <aside className="bg-white rounded-lg shadow-sm p-4 lg:w-1/4">
              {sidebarContent}
            </aside>
          )}
        </div>

        {/* Similar Products Section */}
        {hasSimilarProducts && (
          <div className="bg-white rounded-lg shadow-sm p-4 md:p-6">
            {similarProducts}
          </div>
        )}
      </div>
    </div>
  );
};

export default PartPageLayout;
