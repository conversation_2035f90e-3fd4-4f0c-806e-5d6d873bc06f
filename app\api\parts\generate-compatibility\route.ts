import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';
import { GoogleGenerativeAI } from '@google/generative-ai';

export async function POST(request: NextRequest) {
  try {
    const { partId, regenerate } = await request.json();

    if (!partId) {
      return NextResponse.json({ error: 'Part ID is required' }, { status: 400 });
    }

    const supabase = createClient();

    // Get the part details first
    const { data: partData, error: partError } = await supabase
      .from('parts')
      .select(`
        id,
        title,
        category_id,
        partnumber_group,
        part_compatibility_groups:partnumber_group(id, part_number)
      `)
      .eq('id', partId)
      .single();

    if (partError || !partData) {
      return NextResponse.json({ error: 'Part not found' }, { status: 404 });
    }

    // Get the category name
    const { data: categoryData, error: categoryError } = await supabase
      .from('parts_categories')
      .select('name')
      .eq('id', partData.category_id)
      .single();

    const categoryName = categoryData?.name || 'Unknown';
    const partNumber = (partData.part_compatibility_groups as any)?.part_number || '';

    // Check if we have a part number for accurate analysis
    if (!partNumber) {
      return NextResponse.json({
        success: false,
        requiresManualEntry: true,
        message: 'This part does not have a part number assigned. AI compatibility generation requires a part number for accurate analysis. Please use the manual vehicle selection form below to add compatible vehicles.',
        partTitle: partData.title,
        category: categoryName
      }, { status: 200 });
    }

    // If regenerate is true, delete existing compatibility data
    if (regenerate) {
      const { error: deleteError } = await supabase
        .from('parts_car')
        .delete()
        .eq('part_id', partId);

      if (deleteError) {
        console.error('Error deleting existing compatibility:', deleteError);
        // Continue anyway, don't fail the whole operation
      } else {
        console.log('🗑️ Cleared existing compatibility data for regeneration');
      }
    }

    // Check if Gemini API key is configured
    if (!process.env.NEXT_PUBLIC_GEMINI_API_KEY) {
      return NextResponse.json({
        error: 'Gemini API key is not configured. Please set NEXT_PUBLIC_GEMINI_API_KEY in your environment variables.'
      }, { status: 500 });
    }

    // Initialize Gemini AI
    const genAI = new GoogleGenerativeAI(process.env.NEXT_PUBLIC_GEMINI_API_KEY);
    const modelName = process.env.NEXT_PUBLIC_GEMINI_MODEL || 'gemini-2.0-flash';
    const model = genAI.getGenerativeModel({ model: `models/${modelName}` });

    // Get valid car combinations from database
    console.log('🚗 Fetching valid car combinations...');

    // Try using the RPC function first, fallback to manual query if it fails
    let flatCombinations: any[] = [];

    try {
      const { data: validCombinations, error: combinationsError } = await supabase
        .rpc('get_all_car_combinations');

      if (combinationsError) {
        console.warn('⚠️ RPC function failed, trying manual query:', combinationsError.message);
        throw new Error('RPC failed');
      }

      console.log(`🚗 Fetched ${validCombinations?.length || 0} total car combinations via RPC`);

      // Filter for VW and Audi only and format for AI prompt
      flatCombinations = validCombinations
        ?.filter((combo: any) => ['VW', 'Audi'].includes(combo.brand))
        ?.map((combo: any) => ({
          brand: combo.brand,
          model: combo.model,
          generation: combo.generation,
          variation: combo.variation,
          trim: combo.trim_name
        })) || [];

    } catch (rpcError) {
      console.log('🔄 Using manual query approach...');

      // Manual query approach - get all VW/Audi combinations
      const { data: manualCombinations, error: manualError } = await supabase
        .from('variation_trim')
        .select(`
          id,
          trim,
          car_variation (
            variation,
            car_generation (
              name,
              car_models (
                model_name,
                car_brands (
                  brand_name
                )
              )
            )
          )
        `)
        .eq('car_variation.car_generation.car_models.car_brands.brand_name', 'VW')
        .limit(100);

      if (manualError) {
        console.error('❌ Manual query also failed:', manualError);
        return NextResponse.json({
          error: 'Failed to fetch car combinations: ' + manualError.message
        }, { status: 500 });
      }

      // Process manual query results
      flatCombinations = manualCombinations?.map((trimData: any) => {
        const variation = trimData.car_variation;
        const generation = variation?.car_generation;
        const model = generation?.car_models;
        const brand = model?.car_brands;

        return {
          brand: brand?.brand_name || '',
          model: model?.model_name || '',
          generation: generation?.name || '',
          variation: variation?.variation || '',
          trim: trimData.trim || ''
        };
      }).filter(combo => combo.brand && combo.model) || [];

      // Also get Audi combinations
      const { data: audiCombinations, error: audiError } = await supabase
        .from('variation_trim')
        .select(`
          id,
          trim,
          car_variation (
            variation,
            car_generation (
              name,
              car_models (
                model_name,
                car_brands (
                  brand_name
                )
              )
            )
          )
        `)
        .eq('car_variation.car_generation.car_models.car_brands.brand_name', 'Audi')
        .limit(100);

      if (!audiError && audiCombinations) {
        const audiFlat = audiCombinations.map((trimData: any) => {
          const variation = trimData.car_variation;
          const generation = variation?.car_generation;
          const model = generation?.car_models;
          const brand = model?.car_brands;

          return {
            brand: brand?.brand_name || '',
            model: model?.model_name || '',
            generation: generation?.name || '',
            variation: variation?.variation || '',
            trim: trimData.trim || ''
          };
        }).filter(combo => combo.brand && combo.model);

        flatCombinations = [...flatCombinations, ...audiFlat];
      }

      console.log(`🚗 Manual query returned ${flatCombinations.length} combinations`);
    }

    console.log(`🚗 Final filtered combinations: ${flatCombinations.length} VW/AUDI vehicles`);

    // Log some sample combinations for debugging
    if (flatCombinations.length > 0) {
      console.log('🚗 Sample combinations available:');
      flatCombinations.slice(0, 10).forEach((combo, index) => {
        console.log(`  ${index + 1}. ${combo.brand} ${combo.model} ${combo.generation} ${combo.variation} ${combo.trim}`);
      });

      // Look for Touareg and Q7 specifically
      const touaregs = flatCombinations.filter(c => c.model.toLowerCase().includes('touareg'));
      const q7s = flatCombinations.filter(c => c.model.toLowerCase().includes('q7'));
      console.log(`🚗 Found ${touaregs.length} Touareg combinations and ${q7s.length} Q7 combinations`);

      if (touaregs.length > 0) {
        console.log('🚗 ALL Touareg combinations:', touaregs);
      }
      if (q7s.length > 0) {
        console.log('🚗 ALL Q7 combinations:', q7s);
      }

      // Show what will be sent to AI for 7L6199207
      if (partNumber === '7L6199207') {
        console.log('🤖 For part 7L6199207, sending these combinations to AI:');
        console.log('🚗 Touaregs:', touaregs);
        console.log('🚗 Q7s:', q7s);
        console.log('🚗 Total combinations being sent:', flatCombinations.length);
      }
    }

    if (flatCombinations.length === 0) {
      // Fallback to some sample data if database is empty
      console.log('⚠️ No combinations found, using sample data');
      flatCombinations = [
        { brand: 'VW', model: 'Golf', generation: 'Mk7', variation: '2.0 TDI', trim: 'GTI' },
        { brand: 'VW', model: 'Golf', generation: 'Mk7', variation: '1.4 TSI', trim: 'Comfortline' },
        { brand: 'VW', model: 'Passat', generation: 'B8', variation: '2.0 TDI', trim: 'Highline' },
        { brand: 'VW', model: 'Touareg', generation: '7L', variation: 'SUV', trim: 'Base' },
        { brand: 'VW', model: 'Touareg', generation: '7L', variation: 'SUV', trim: 'R-Line' },
        { brand: 'Audi', model: 'A3', generation: '8V', variation: '2.0 TDI', trim: 'S line' },
        { brand: 'Audi', model: 'A4', generation: 'B9', variation: '2.0 TFSI', trim: 'S line' },
        { brand: 'Audi', model: 'Q5', generation: 'FY', variation: '2.0 TFSI', trim: 'S line' },
        { brand: 'Audi', model: 'Q7', generation: 'Mk1', variation: 'SUV', trim: 'Base' },
        { brand: 'Audi', model: 'Q7', generation: 'Mk1', variation: 'SUV', trim: 'S line' }
      ];
    }

    // Create AI prompt focused on part number analysis
    const prompt = `
You are an automotive parts compatibility expert with extensive knowledge of OEM part numbers and their vehicle applications.

PART TO ANALYZE:
Part Number: "${partNumber}"
Part Title: "${partData.title}"
Category: "${categoryName}"

TASK:
1. First, analyze the part number "${partNumber}" using your automotive knowledge to identify:
   - What type of component this is
   - Which specific vehicle models and years this part number is designed for
   - The engine variants or trim levels it's compatible with

2. Based on your part number analysis, match the compatible vehicles to the valid database combinations below.

VALID DATABASE COMBINATIONS (you MUST only return vehicles from this exact list):
${flatCombinations.map(combo =>
  `${combo.brand} ${combo.model} ${combo.generation} ${combo.variation} ${combo.trim}`
).join('\n')}

INSTRUCTIONS:
- Use your automotive parts database knowledge to identify the ACTUAL compatible vehicles for part number "${partNumber}"
- You MUST only return vehicles that exist EXACTLY in the valid combinations list above
- Each returned vehicle must be a perfect match (brand, model, generation, variation, trim) from the list
- CRITICAL: For part number 7L6199207 and similar shared platform parts, you MUST return vehicles from BOTH VW and Audi brands
- Part 7L6199207 is compatible with BOTH VW Touareg AND Audi Q7 - return ALL available combinations from BOTH brands
- Look through the valid combinations list and find ALL Touareg entries AND ALL Q7 entries
- Do NOT return only one brand - shared platform parts require both VW and Audi vehicles in the response
- For 7L6199207 specifically: Include ALL VW Touareg variants AND ALL Audi Q7 variants from the valid combinations list
- Platform sharing examples: VW Touareg (7L) = Audi Q7 (4L), VW Phaeton = Audi A8, VW Golf = Audi A3
- When you identify a compatible model from one brand, always check for the platform-shared equivalent from the other brand
- Return a comprehensive list that includes vehicles from both brands when platform sharing applies

RESPONSE FORMAT:
{
  "partAnalysis": "Brief description of what this part number is and its primary applications",
  "compatibleVehicles": [
    {
      "brand": "string",
      "model": "string",
      "generation": "string",
      "variation": "string",
      "trim": "string"
    }
  ]
}

EXAMPLE FOR 7L6199207:
If analyzing part 7L6199207, your response should include vehicles like:
- VW Touareg 7L SUV Base
- VW Touareg 7L SUV R-Line
- Audi Q7 Mk1 SUV Base
- Audi Q7 Mk1 SUV S line
(Include ALL available combinations from BOTH brands)

CRITICAL: Base your analysis on the specific part number "${partNumber}" and your automotive knowledge, then match to the valid combinations list. For shared platform parts, always include vehicles from both VW and Audi brands.`;

    try {
      console.log('🤖 Generating AI response for part:', partData.title);

      // Generate AI response
      const result = await model.generateContent(prompt);
      const text = result.response.text().replace(/```json/g, '').replace(/```/g, '').trim();

      console.log('🤖 AI Response received:', text);

      const aiData = JSON.parse(text);

      if (!aiData.compatibleVehicles || !Array.isArray(aiData.compatibleVehicles)) {
        throw new Error('Invalid AI response format');
      }

      console.log(`🤖 Part Analysis: ${aiData.partAnalysis || 'No analysis provided'}`);
      console.log(`🤖 AI generated ${aiData.compatibleVehicles.length} compatible vehicles`);
      console.log('🤖 Generated vehicles:', aiData.compatibleVehicles);

      // Convert AI-generated vehicles to variation_trim_ids and save to database
      const savedVehicles = [];

      console.log('💾 Starting to save vehicles to database...');

      for (const vehicle of aiData.compatibleVehicles) {
        try {
          console.log(`💾 Processing vehicle: ${vehicle.brand} ${vehicle.model} ${vehicle.generation} ${vehicle.variation} ${vehicle.trim}`);

          // Find the variation_trim_id for this vehicle combination using a step-by-step approach
          let trimData = null;
          let trimError = null;

          try {
            // Step 1: Find the brand
            const { data: brandData, error: brandErr } = await supabase
              .from('car_brands')
              .select('brand_id')
              .eq('brand_name', vehicle.brand)
              .single();

            if (brandErr || !brandData) {
              console.warn(`❌ Brand not found: ${vehicle.brand}`);
              continue;
            }

            // Step 2: Find the model
            const { data: modelData, error: modelErr } = await supabase
              .from('car_models')
              .select('id')
              .eq('model_name', vehicle.model)
              .eq('brand_id', brandData.brand_id)
              .single();

            if (modelErr || !modelData) {
              console.warn(`❌ Model not found: ${vehicle.model} for brand ${vehicle.brand}`);
              continue;
            }

            // Step 3: Find the generation
            const { data: generationData, error: generationErr } = await supabase
              .from('car_generation')
              .select('id')
              .eq('name', vehicle.generation)
              .eq('model_id', modelData.id)
              .single();

            if (generationErr || !generationData) {
              console.warn(`❌ Generation not found: ${vehicle.generation} for model ${vehicle.model}`);
              continue;
            }

            // Step 4: Find the variation
            const { data: variationData, error: variationErr } = await supabase
              .from('car_variation')
              .select('id')
              .eq('variation', vehicle.variation)
              .eq('generation_id', generationData.id)
              .single();

            if (variationErr || !variationData) {
              console.warn(`❌ Variation not found: ${vehicle.variation} for generation ${vehicle.generation}`);
              continue;
            }

            // Step 5: Find the trim
            const { data: trimResult, error: trimErr } = await supabase
              .from('variation_trim')
              .select('id')
              .eq('trim', vehicle.trim)
              .eq('variation_id', variationData.id)
              .single();

            if (trimErr || !trimResult) {
              console.warn(`❌ Trim not found: ${vehicle.trim} for variation ${vehicle.variation}`);

              // Try to find any trim for this variation as fallback
              const { data: fallbackTrim, error: fallbackErr } = await supabase
                .from('variation_trim')
                .select('id')
                .eq('variation_id', variationData.id)
                .limit(1)
                .single();

              if (fallbackErr || !fallbackTrim) {
                console.warn(`❌ No trims found for variation ${vehicle.variation}`);
                continue;
              }

              trimData = fallbackTrim;
              console.log(`✅ Using fallback trim for variation ${vehicle.variation}`);
            } else {
              trimData = trimResult;
              console.log(`✅ Found exact trim match: ${vehicle.trim}`);
            }

          } catch (error) {
            console.error(`❌ Error in step-by-step lookup:`, error);
            continue;
          }

          if (!trimData) {
            console.warn(`❌ Could not find any trim data for vehicle:`, vehicle);
            continue;
          }

          console.log(`✅ Found variation_trim_id ${trimData.id} for vehicle`);

          // Check if this combination already exists
          const { data: existingRecord, error: checkError } = await supabase
            .from('parts_car')
            .select('id')
            .eq('part_id', partId)
            .eq('variation_trim_id', trimData.id)
            .maybeSingle();

          if (checkError) {
            console.error('❌ Error checking existing record:', checkError);
            continue;
          }

          if (existingRecord) {
            console.log(`⚠️ Vehicle combination already exists, skipping`);
            savedVehicles.push({
              ...vehicle,
              variation_trim_id: trimData.id
            });
            continue;
          }

          // Insert into parts_car table using auto-increment (sequence is now synced)
          const { error: insertError } = await supabase
            .from('parts_car')
            .insert({
              part_id: partId,
              variation_trim_id: trimData.id
            });

          if (insertError) {
            // Check if it's a duplicate constraint violation
            if (insertError.code === '23505' && insertError.message.includes('parts_car_part_trim_unique')) {
              console.log(`⚠️ Vehicle combination already exists (duplicate), skipping`);
              savedVehicles.push({
                ...vehicle,
                variation_trim_id: trimData.id
              });
              continue;
            }
            console.error('❌ Error inserting parts_car record:', insertError);
            continue;
          }

          console.log(`✅ Successfully saved vehicle to database`);

          savedVehicles.push({
            ...vehicle,
            variation_trim_id: trimData.id
          });

        } catch (error) {
          console.error('Error processing vehicle:', vehicle, error);
          continue;
        }
      }

      return NextResponse.json({
        success: true,
        message: `Successfully generated and saved ${savedVehicles.length} compatible vehicles using AI.`,
        partTitle: partData.title,
        partNumber: partNumber,
        category: categoryName,
        partAnalysis: aiData.partAnalysis || 'No analysis provided',
        generatedVehicles: savedVehicles.length,
        vehicles: savedVehicles
      });

    } catch (aiError: any) {
      console.error('AI generation error:', aiError);
      return NextResponse.json({
        error: 'Failed to generate compatibility with AI: ' + aiError.message
      }, { status: 500 });
    }

  } catch (error: any) {
    console.error('Error in generate-compatibility API:', error);
    return NextResponse.json(
      { error: 'Internal server error: ' + error.message },
      { status: 500 }
    );
  }
}
