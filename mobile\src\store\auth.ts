import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, UserProfile } from '@/types';
import { authService } from '@/services/auth';
import { CONFIG } from '@/constants/config';

interface AuthState {
  // State
  user: User | null;
  userProfile: UserProfile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (email: string, password: string) => Promise<{ requiresOTP: boolean }>;
  completeLogin: (email: string, otp: string) => Promise<void>;
  register: (email: string, password: string, fullName: string, phone: string) => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  loadUserProfile: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      userProfile: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null });
        try {
          const result = await authService.login(email, password);
          set({ isLoading: false });
          return result;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Login failed';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      completeLogin: async (email: string, otp: string) => {
        set({ isLoading: true, error: null });
        try {
          const user = await authService.completeLogin(email, otp);
          const userProfile = await authService.getUserProfile();
          
          set({
            user,
            userProfile,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'OTP verification failed';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      register: async (email: string, password: string, fullName: string, phone: string) => {
        set({ isLoading: true, error: null });
        try {
          const user = await authService.register(email, password, fullName, phone);
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Registration failed';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      logout: async () => {
        set({ isLoading: true });
        try {
          await authService.signOut();
          set({
            user: null,
            userProfile: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          console.error('Logout error:', error);
          // Force logout even if API call fails
          set({
            user: null,
            userProfile: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      },

      resetPassword: async (email: string) => {
        set({ isLoading: true, error: null });
        try {
          await authService.resetPassword(email);
          set({ isLoading: false });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Password reset failed';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      updateProfile: async (updates: Partial<User>) => {
        set({ isLoading: true, error: null });
        try {
          const updatedUser = await authService.updateProfile(updates);
          set({
            user: updatedUser,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Profile update failed';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      loadUserProfile: async () => {
        const { user } = get();
        if (!user) return;

        set({ isLoading: true });
        try {
          const userProfile = await authService.getUserProfile();
          set({
            userProfile,
            isLoading: false,
          });
        } catch (error) {
          console.error('Load user profile error:', error);
          set({ isLoading: false });
        }
      },

      clearError: () => set({ error: null }),

      setLoading: (loading: boolean) => set({ isLoading: loading }),
    }),
    {
      name: CONFIG.STORAGE_KEYS.USER_PROFILE,
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        userProfile: state.userProfile,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Helper hooks
export const useAuth = () => {
  const store = useAuthStore();
  return {
    user: store.user,
    userProfile: store.userProfile,
    isAuthenticated: store.isAuthenticated,
    isLoading: store.isLoading,
    error: store.error,
    login: store.login,
    completeLogin: store.completeLogin,
    register: store.register,
    logout: store.logout,
    resetPassword: store.resetPassword,
    updateProfile: store.updateProfile,
    loadUserProfile: store.loadUserProfile,
    clearError: store.clearError,
  };
};

export const useUser = () => {
  const user = useAuthStore((state) => state.user);
  const userProfile = useAuthStore((state) => state.userProfile);
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
  
  return {
    user,
    userProfile,
    isAuthenticated,
    isEmployee: userProfile?.user_roles?.some(ur => ur.roles.name === 'Employee') ?? false,
    isSuperAdmin: userProfile?.user_roles?.some(ur => ur.roles.name === 'Super Admin') ?? false,
  };
};
