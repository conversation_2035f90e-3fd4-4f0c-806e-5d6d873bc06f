'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/app/libs/supabase/client';
import Button from '@/app/components/ui/inputs/Button';
import Input from '@/app/components/ui/inputs/Input';
import Password from '@/app/components/ui/inputs/Password';
import Notification from '@/app/components/ui/Notification';

export default function LoginForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [errorDescription, setErrorDescription] = useState('');
  const [notificationMessage, setNotificationMessage] = useState('');
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.hash.substring(1));
    const error = urlParams.get('error');
    const error_description = urlParams.get('error_description');
    const queryParams = new URLSearchParams(window.location.search);
    const queryError = queryParams.get('error');

    if (error || queryError) {
      setErrorMessage(queryError || error || '');
      setErrorDescription(error_description || '');
    }
  }, []);

  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    setErrorMessage('');

    try {
      if (password) {
        // Phase 1: Validate credentials
        const { error: signInError } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (signInError) throw signInError;

        // Immediately sign out to prevent session creation
        const { error: signOutError } = await supabase.auth.signOut();
        if (signOutError) throw signOutError;

        // Phase 2: Initiate OTP flow
        const { error: otpError } = await supabase.auth.signInWithOtp({
          email,
          options: {
            shouldCreateUser: false,
          },
        });

        if (otpError) throw otpError;

        setNotificationMessage('OTP sent to your email!');
        router.push('/otp?email=' + email);
      }
    } catch (error: any) {
      setErrorMessage(error.message);
      setErrorDescription(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md space-y-6">
      {errorMessage && (
        <Notification
          type="error"
          header="Authentication Failed"
          body={errorDescription}
        />
      )}

      {notificationMessage && (
        <Notification
          type="success"
          header="Check Your Email"
          body={notificationMessage}
        />
      )}

      <form onSubmit={handleLogin} className="space-y-4">
        <Input
          type="email"
          label="Email Address"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          disabled={loading}
        />

        <Password
          label="Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          disabled={loading}
          // helperText="Enter your password or leave blank for magic link"
        />

        <Button
          type="submit"
          disabled={loading}
          className="w-full"
          variant="primary"
        >
          {loading ? 'Processing...' : 'Continue'}
        </Button>
      </form>
      <div className="text-center text-sm text-muted-foreground">
        <a href="/forgot-password" className="text-primary underline text-sm">
          Forgot Password?
        </a>
      </div>

      {/* <div className="text-center text-sm text-muted-foreground">
        <p>Don't have an account? </p>
        <Button
          variant="link"
          className="text-primary underline"
          onClick={() => router.push('/register')}
        >
          Create account
        </Button>
      </div> */}
    </div>
  );
}