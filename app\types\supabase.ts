export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      parts: {
        Row: {
          id: number
          part_id: number
          title: string | null
          description: string | null
          category_id: number | null
          partnumber_group: string | null
          createdAt: string | null
          updatedAt: string | null
        }
        Insert: {
          id?: number
          part_id?: number
          title?: string | null
          description?: string | null
          category_id?: number | null
          partnumber_group?: string | null
          createdAt?: string | null
          updatedAt?: string | null
        }
        Update: {
          id?: number
          part_id?: number
          title?: string | null
          description?: string | null
          category_id?: number | null
          partnumber_group?: string | null
          createdAt?: string | null
          updatedAt?: string | null
        }
        Relationships: []
      }
      parts_car: {
        Row: {
          id: number
          variation_trim_id: number | null
          part_id: number | null
        }
        Insert: {
          id?: number
          variation_trim_id?: number | null
          part_id?: number | null
        }
        Update: {
          id?: number
          variation_trim_id?: number | null
          part_id?: number | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_all_car_combinations: {
        Args: {}
        Returns: {
          trim_id: number
          brand: string
          model: string
          generation: string
          variation: string
          trim_name: string
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never
