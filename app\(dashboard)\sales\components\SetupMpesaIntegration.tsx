'use client';

import React, { useState } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

const SetupMpesaIntegration: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const setupMpesaTable = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const supabase = createClient();
      
      // Read the SQL file
      const response = await fetch('/app/sql/mpesa_transactions.sql');
      const sqlScript = await response.text();
      
      // Execute the SQL script
      const { error } = await supabase.rpc('exec_sql', { sql: sqlScript });
      
      if (error) throw error;
      
      setSuccess(true);
    } catch (error) {
      console.error('Error setting up M-PESA integration:', error);
      setError('Failed to set up M-PESA integration. Please check the console for details.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h2 className="text-lg font-medium text-gray-900 mb-4">Set Up M-PESA Integration</h2>
      
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
          M-PESA integration set up successfully!
        </div>
      )}
      
      <p className="text-gray-600 mb-4">
        This will create the necessary database tables for M-PESA integration.
        Make sure you have added the required environment variables for M-PESA API.
      </p>
      
      <button
        onClick={setupMpesaTable}
        disabled={isLoading}
        className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 disabled:opacity-50 flex items-center"
      >
        {isLoading ? (
          <>
            <LoadingSpinner size={16} />
            <span className="ml-2">Setting up...</span>
          </>
        ) : (
          'Set Up M-PESA Integration'
        )}
      </button>
    </div>
  );
};

export default SetupMpesaIntegration;
