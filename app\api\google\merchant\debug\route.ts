import { NextRequest, NextResponse } from 'next/server';
import { googleMerchantService } from '@/app/services/googleMerchant';
import { createClient } from '@/app/libs/supabase/client';

export async function POST(request: NextRequest) {
  try {
    console.log('=== Starting Debug Sync Process ===');

    // Check authentication
    console.log('Checking authentication...');
    const isAuthenticated = await googleMerchantService.isAuthenticated();
    if (!isAuthenticated) {
      console.error('Authentication failed');
      return NextResponse.json(
        { error: 'Not authenticated with Google Merchant API' },
        { status: 401 }
      );
    }
    console.log('Authentication successful');

    // Get merchant ID
    const merchantId = googleMerchantService.getMerchantId();
    console.log('Using merchant ID:', merchantId);

    // Parse request body
    const body = await request.json();
    const { partId } = body;

    if (!partId) {
      console.error('No part ID provided');
      return NextResponse.json(
        { error: 'Part ID is required' },
        { status: 400 }
      );
    }

    // Get part data from database
    console.log('Fetching part data from database...');
    const supabase = createClient();
    const { data: part, error: partError } = await supabase
      .from('parts')
      .select('*')
      .eq('id', partId)
      .single();

    if (partError || !part) {
      console.error('Error fetching part:', partError);
      return NextResponse.json(
        { error: 'Part not found', details: partError?.message },
        { status: 404 }
      );
    }
    console.log('Part data retrieved:', JSON.stringify(part, null, 2));

    // Get part images
    console.log('Fetching part images...');
    const { data: images, error: imageError } = await supabase
      .from('part_images')
      .select('image_url, is_main_image')
      .eq('part_id', part.id);

    if (imageError) {
      console.error('Error fetching images:', imageError);
    } else {
      console.log('Images retrieved:', JSON.stringify(images, null, 2));
    }

    // Add images to part object
    part.images = images || [];

    // Sync the product
    console.log('Starting product sync...');
    const result = await googleMerchantService.syncProduct(part);
    console.log('Sync result:', JSON.stringify(result, null, 2));

    return NextResponse.json({
      success: true,
      message: 'Product sync completed',
      result
    });
  } catch (error) {
    console.error('=== Debug Sync Error ===');
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      status: error.status,
      response: error.response?.data
    });

    return NextResponse.json(
      {
        error: 'Failed to sync product',
        details: {
          message: error.message,
          code: error.code,
          status: error.status,
          response: error.response?.data
        }
      },
      { status: 500 }
    );
  }
} 