import React, { useState, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { MapPin, Car } from 'lucide-react';
import LoadingSpinner from '../ui/LoadingSpinner';

interface CompatibleCarsCardProps {
  partId: string | number;
}

interface CarData {
  brand: string;
  model: string;
  generation: string;
  generationYears: string;
  variation: string;
  trim: string;
  totalCompatibleCars?: number;
}

const CompatibleCarsCard: React.FC<CompatibleCarsCardProps> = ({ partId }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [carData, setCarData] = useState<CarData | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCarData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const supabase = createClient();

        // First, check if the part has car data (get all compatible cars)
        const { data: partCars, error: partCarError } = await supabase
          .from('parts_car')
          .select('variation_trim_id')
          .eq('part_id', partId);

        if (partCarError) {
          throw new Error(`Error fetching part car data: ${partCarError.message}`);
        }

        if (!partCars || partCars.length === 0) {
          setCarData(null);
          setIsLoading(false);
          return;
        }

        // For now, just use the first compatible car
        // TODO: In the future, we might want to show all compatible cars
        const partCar = partCars[0];

        // Get the variation_trim data
        const { data: variationTrim, error: trimError } = await supabase
          .from('variation_trim')
          .select('*')
          .eq('id', partCar.variation_trim_id)
          .single();

        if (trimError) {
          throw new Error(`Error fetching trim data: ${trimError.message}`);
        }

        // Get the variation data
        const { data: variation, error: variationError } = await supabase
          .from('car_variation')
          .select('*')
          .eq('id', variationTrim.variation_id)
          .single();

        if (variationError) {
          throw new Error(`Error fetching variation data: ${variationError.message}`);
        }

        // Get the generation data
        const { data: generation, error: generationError } = await supabase
          .from('car_generation')
          .select('*')
          .eq('id', variation.generation_id)
          .single();

        if (generationError) {
          throw new Error(`Error fetching generation data: ${generationError.message}`);
        }

        // Get the model data
        const { data: model, error: modelError } = await supabase
          .from('car_models')
          .select('*')
          .eq('id', generation.model_id)
          .single();

        if (modelError) {
          throw new Error(`Error fetching model data: ${modelError.message}`);
        }

        // Get the brand data
        const { data: brand, error: brandError } = await supabase
          .from('car_brands')
          .select('*')
          .eq('brand_id', model.brand_id)
          .single();

        if (brandError) {
          throw new Error(`Error fetching brand data: ${brandError.message}`);
        }

        // Format the generation years
        const startYear = generation.start_production_year;
        const endYear = generation.end_production_year;
        const generationYears = endYear ? `${startYear}-${endYear}` : `${startYear}+`;

        // Construct the car data
        const carInfo: CarData = {
          brand: brand.brand_name,
          model: model.model_name,
          generation: generation.name,
          generationYears,
          variation: variation.variation,
          trim: variationTrim.trim,
          totalCompatibleCars: partCars.length // Add count of total compatible cars
        };

        setCarData(carInfo);
      } catch (err: any) {
        console.error('Error fetching car data:', err);
        setError(err.message || 'Failed to load car data');
      } finally {
        setIsLoading(false);
      }
    };

    if (partId) {
      fetchCarData();
    }
  }, [partId]);

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-4 h-full">
        <div className="flex items-center mb-2">
          <Car className="w-5 h-5 text-blue-600 mr-2" />
          <h3 className="text-md font-semibold text-gray-700">Compatible Car</h3>
        </div>
        <div className="bg-blue-50 p-3 rounded-md text-blue-800 flex justify-center py-4">
          <div className="flex items-center">
            <div className="animate-spin mr-2 h-4 w-4 border-2 border-blue-500 rounded-full border-t-transparent"></div>
            <span>Loading car information...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-4 h-full">
        <div className="flex items-center mb-2">
          <Car className="w-5 h-5 text-blue-600 mr-2" />
          <h3 className="text-md font-semibold text-gray-700">Compatible Car</h3>
        </div>
        <div className="bg-red-50 p-3 rounded-md text-red-800">
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }

  if (!carData) {
    return (
      <div className="bg-white rounded-lg shadow p-4 h-full">
        <div className="flex items-center mb-2">
          <Car className="w-5 h-5 text-blue-600 mr-2" />
          <h3 className="text-md font-semibold text-gray-700">Compatible Car</h3>
        </div>
        <div className="bg-blue-50 p-3 rounded-md text-blue-800">
          <p className="text-sm italic">No car compatibility information available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-4 h-full">
      <div className="flex items-center mb-2">
        <Car className="w-5 h-5 text-blue-600 mr-2" />
        <h3 className="text-md font-semibold text-gray-700">
          Compatible Car{carData.totalCompatibleCars && carData.totalCompatibleCars > 1 ? 's' : ''}
        </h3>
      </div>
      <div className="bg-blue-50 p-3 rounded-md text-blue-800">
        <p className="text-sm">
          {carData.brand} {carData.model} {carData.generation} {carData.generationYears}
          <br />
          <span className="text-blue-600">{carData.variation} {carData.trim}</span>
          {carData.totalCompatibleCars && carData.totalCompatibleCars > 1 && (
            <>
              <br />
              <span className="text-xs text-blue-500 italic">
                +{carData.totalCompatibleCars - 1} more compatible car{carData.totalCompatibleCars - 1 > 1 ? 's' : ''}
              </span>
            </>
          )}
        </p>
      </div>
    </div>
  );
};

export default CompatibleCarsCard;
