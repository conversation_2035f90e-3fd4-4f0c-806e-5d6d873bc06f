// <PERSON>ript to check for existing images in the Supabase bucket
const { createClient } = require('@supabase/supabase-js');

// Get Supabase credentials from environment variables
require('dotenv').config();

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL or key not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkExistingImages() {
  console.log('=== CHECKING FOR EXISTING IMAGES IN SUPABASE ===');
  
  try {
    // First, try to get images from the part_images table
    console.log('\n1. Checking part_images table...');
    const { data: partImages, error: partImagesError } = await supabase
      .from('part_images')
      .select('*')
      .limit(5);
    
    if (partImagesError) {
      console.error('Error fetching part_images:', partImagesError.message);
    } else if (partImages && partImages.length > 0) {
      console.log(`Found ${partImages.length} images in part_images table:`);
      partImages.forEach((img, index) => {
        console.log(`[${index + 1}] Part ID: ${img.part_id}, Image Path: ${img.image_path}`);
      });
    } else {
      console.log('No images found in part_images table');
    }
    
    // Next, check parts table for image paths
    console.log('\n2. Checking parts table for image paths...');
    const { data: parts, error: partsError } = await supabase
      .from('parts')
      .select('*')
      .not('image_path', 'is', null)
      .limit(5);
    
    if (partsError) {
      console.error('Error fetching parts:', partsError.message);
    } else if (parts && parts.length > 0) {
      console.log(`Found ${parts.length} parts with image paths:`);
      parts.forEach((part, index) => {
        console.log(`[${index + 1}] Part ID: ${part.id}, Image Path: ${part.image_path}`);
      });
    } else {
      console.log('No parts found with image paths');
    }
    
    // Finally, list files in the storage bucket
    console.log('\n3. Listing files in storage bucket...');
    const { data: bucketFiles, error: bucketError } = await supabase.storage
      .from('car-part-images')
      .list('');
    
    if (bucketError) {
      console.error('Error listing bucket files:', bucketError.message);
    } else if (bucketFiles && bucketFiles.length > 0) {
      console.log(`Found ${bucketFiles.length} files in bucket root:`);
      bucketFiles.forEach((file, index) => {
        console.log(`[${index + 1}] ${file.name} (${file.metadata?.size || 'unknown'} bytes)`);
      });
      
      // Check if any of these files are actually accessible
      if (bucketFiles.length > 0) {
        console.log('\n4. Checking if files are accessible...');
        for (let i = 0; i < Math.min(3, bucketFiles.length); i++) {
          const file = bucketFiles[i];
          try {
            const { data: publicUrl } = supabase.storage
              .from('car-part-images')
              .getPublicUrl(file.name);
            
            console.log(`[${i + 1}] Public URL for ${file.name}: ${publicUrl.publicUrl}`);
            
            // Check if the URL is accessible
            try {
              const response = await fetch(publicUrl.publicUrl, { method: 'HEAD' });
              console.log(`    Status: ${response.status} (${response.ok ? 'Accessible' : 'Not accessible'})`);
            } catch (fetchError) {
              console.log(`    Error accessing URL: ${fetchError.message}`);
            }
          } catch (urlError) {
            console.log(`    Error getting public URL: ${urlError.message}`);
          }
        }
      }
    } else {
      console.log('No files found in bucket root');
      
      // Try to check if there are any subdirectories
      try {
        const { data: directories } = await supabase.storage
          .from('car-part-images')
          .list('', { sortBy: { column: 'name', order: 'asc' } });
        
        const potentialDirs = directories?.filter(item => !item.name.includes('.')) || [];
        
        if (potentialDirs.length > 0) {
          console.log(`\nFound ${potentialDirs.length} potential subdirectories:`);
          
          for (const dir of potentialDirs) {
            console.log(`\nChecking directory: ${dir.name}`);
            
            const { data: dirFiles, error: dirError } = await supabase.storage
              .from('car-part-images')
              .list(dir.name);
              
            if (dirError) {
              console.log(`  Error listing files in ${dir.name}: ${dirError.message}`);
            } else if (dirFiles && dirFiles.length > 0) {
              console.log(`  Found ${dirFiles.length} files in ${dir.name}:`);
              dirFiles.forEach((file, index) => {
                console.log(`  [${index + 1}] ${file.name}`);
              });
              
              // Check if the first file is accessible
              if (dirFiles.length > 0) {
                const filePath = `${dir.name}/${dirFiles[0].name}`;
                const { data: publicUrl } = supabase.storage
                  .from('car-part-images')
                  .getPublicUrl(filePath);
                
                console.log(`  Public URL for ${filePath}: ${publicUrl.publicUrl}`);
                
                try {
                  const response = await fetch(publicUrl.publicUrl, { method: 'HEAD' });
                  console.log(`  Status: ${response.status} (${response.ok ? 'Accessible' : 'Not accessible'})`);
                } catch (fetchError) {
                  console.log(`  Error accessing URL: ${fetchError.message}`);
                }
              }
            } else {
              console.log(`  No files found in ${dir.name}`);
            }
          }
        }
      } catch (dirError) {
        console.log(`Error checking for subdirectories: ${dirError.message}`);
      }
    }
    
  } catch (error) {
    console.error('Error during check:', error);
  }
  
  console.log('\n=== CHECK COMPLETED ===');
}

// Run the check
checkExistingImages();
