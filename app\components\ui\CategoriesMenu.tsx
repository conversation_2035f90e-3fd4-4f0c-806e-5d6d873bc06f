'use client';

import React, { useEffect, useState } from 'react';
import { ChevronRight } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { cn } from '@/app/utils/cn';

interface Category {
  id: number;
  label: string;
  href: string;
  icon?: string;
  library?: string;
  parent_category_id: number | null;
  isActive: boolean;
  children?: Category[];
}

const CategoriesMenu: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<Set<number>>(new Set());

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('car_part_categories')
          .select('*')
          .eq('isActive', true);

        if (error) throw error;

        // Build the category tree
        const categoryMap = new Map<number, Category>();
        const rootCategories: Category[] = [];

        // First pass: create all map entries
        data.forEach(category => {
          categoryMap.set(category.id, {
            ...category,
            children: []
          });
        });

        // Second pass: assign children to parents
        data.forEach(category => {
          if (category.parent_category_id !== null) {
            const parent = categoryMap.get(category.parent_category_id);
            if (parent) {
              parent.children?.push(categoryMap.get(category.id)!);
            }
          } else {
            rootCategories.push(categoryMap.get(category.id)!);
          }
        });

        setCategories(rootCategories);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch categories');
        console.error('Error fetching categories:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const toggleCategory = (categoryId: number) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  };

  const renderCategory = (category: Category, level: number = 0) => {
    const hasChildren = category.children && category.children.length > 0;
    const isExpanded = expandedCategories.has(category.id);

    return (
      <div key={category.id} className={cn(
        "border-b border-gray-100",
        level > 0 && "pl-4"
      )}>
        <div 
          className={cn(
            "flex items-center justify-between px-4 py-3 hover:bg-gray-50 cursor-pointer",
            isExpanded && "bg-gray-50"
          )}
          onClick={() => hasChildren && toggleCategory(category.id)}
        >
          <a 
            href={category.href} 
            className={cn(
              "text-gray-800 hover:text-cyan-500 transition-colors",
              !hasChildren && "w-full"
            )}
            onClick={(e) => {
              if (hasChildren) {
                e.preventDefault();
              }
            }}
          >
            {category.label}
          </a>
          {hasChildren && (
            <ChevronRight 
              size={20} 
              className={cn(
                "text-gray-400 transition-transform",
                isExpanded && "transform rotate-90"
              )} 
            />
          )}
        </div>
        {hasChildren && isExpanded && (
          <div className="pl-4">
            {category.children?.map(child => renderCategory(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="p-4">
        <div className="animate-pulse space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-8 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-red-500">
        Error loading categories: {error}
      </div>
    );
  }

  return (
    <div className="flex flex-col">
      {categories.map(category => renderCategory(category))}
    </div>
  );
};

export default CategoriesMenu; 