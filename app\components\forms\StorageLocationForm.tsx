'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { createClient } from '@/app/libs/supabase/client';
import { CheckCircle2, AlertCircle, MapPin, Loader2 } from 'lucide-react';

// UI Components
import Input from '@/app/components/ui/inputs/Input';
import { Select, SelectContent, SelectItem, SelectTrigger } from '@/app/components/ui/inputs/Select';
import Button from '@/app/components/ui/inputs/Button';
import NumberInput from '@/app/components/ui/inputs/Number';

// Types
import {
  StorageLocationFormData,
  StorageArea,
  StorageUnit,
  LocationSubtype
} from './types';

// Animation variants
const formVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut",
      staggerChildren: 0.1
    }
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: { duration: 0.3 }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 10 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.3 }
  }
};

// Form validation schema
const storageLocationSchema = z.object({
  areaId: z.string().min(1, "Storage area is required"),
  unitId: z.string().min(1, "Storage unit is required"),
  locationSubtype: z.enum([
    'crate', 'container', 'shelf_section', 'open_shelf',
    'cage_section', 'hanging_point', 'open_area_spot'
  ]),
  quantity: z.number().min(1, "Quantity must be at least 1"),
  notes: z.string().optional(),
  // Dynamic fields will be validated conditionally
  details: z.record(z.string().optional()).optional(),
});

interface StorageLocationFormProps {
  partId: number;
  partName: string;
  onSuccess: () => void;
  onCancel: () => void;
}

const StorageLocationForm: React.FC<StorageLocationFormProps> = ({
  partId,
  partName,
  onSuccess,
  onCancel
}) => {
  // State
  const [areas, setAreas] = useState<StorageArea[]>([]);
  const [units, setUnits] = useState<StorageUnit[]>([]);
  const [filteredUnits, setFilteredUnits] = useState<StorageUnit[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  // Form setup
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    reset
  } = useForm<StorageLocationFormData>({
    resolver: zodResolver(storageLocationSchema),
    defaultValues: {
      areaId: '',
      unitId: '',
      locationSubtype: 'open_shelf',
      quantity: 1,
      notes: '',
      details: {}
    }
  });

  // Watch form values for conditional rendering
  const selectedAreaId = watch('areaId');
  const selectedUnitId = watch('unitId');
  const selectedLocationType = watch('locationSubtype');

  // Fetch storage areas on component mount
  useEffect(() => {
    const fetchStorageAreas = async () => {
      setIsLoading(true);
      try {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('storage_areas')
          .select('*')
          .order('name');

        if (error) throw error;
        setAreas(data || []);
      } catch (error) {
        console.error('Error fetching storage areas:', error);
        setErrorMessage('Failed to load storage areas');
        // Add some sample data for testing if we can't fetch from the database
        setAreas([
          { area_id: 1, name: 'Room 1', location_type: 'indoor', level: 'downstairs', description: 'Main storage room' },
          { area_id: 2, name: 'Room 3', location_type: 'indoor', level: 'upstairs', description: 'Upstairs storage' },
          { area_id: 3, name: 'Outdoor Area 1', location_type: 'outdoor', level: 'ground', description: 'Outdoor storage area' },
          { area_id: 4, name: 'Room 11', location_type: 'indoor', level: 'downstairs', description: 'Section storage room' },
          { area_id: 5, name: 'Room 13', location_type: 'indoor', level: 'downstairs', description: 'Cage storage room' }
        ]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStorageAreas();
  }, []);

  // Fetch storage units on component mount
  useEffect(() => {
    const fetchStorageUnits = async () => {
      setIsLoading(true);
      try {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('storage_units')
          .select('*')
          .order('identifier');

        if (error) throw error;
        setUnits(data || []);
      } catch (error) {
        console.error('Error fetching storage units:', error);
        setErrorMessage('Failed to load storage units');
        // Add some sample data for testing if we can't fetch from the database
        setUnits([
          { unit_id: 1, area_id: 1, unit_type: 'shelf', identifier: 'Shelf 1', description: '1st shelf in Room 1' },
          { unit_id: 2, area_id: 1, unit_type: 'shelf', identifier: 'Shelf 2', description: '2nd shelf in Room 1' },
          { unit_id: 3, area_id: 1, unit_type: 'shelf', identifier: 'Shelf 4', description: '4th shelf in Room 1' },
          { unit_id: 4, area_id: 2, unit_type: 'shelf', identifier: 'Shelf 2', description: '2nd shelf in Room 3' },
          { unit_id: 5, area_id: 3, unit_type: 'open_space', identifier: 'Open Area 1', description: 'Open storage area' },
          { unit_id: 6, area_id: 4, unit_type: 'shelf', identifier: 'Section Shelf', description: 'Section shelf in Room 11' },
          { unit_id: 7, area_id: 5, unit_type: 'cage', identifier: 'CG10', description: 'Cage 10 in Room 13' },
          { unit_id: 8, area_id: 3, unit_type: 'hanging_line', identifier: 'Hanging Line 1', description: 'Hanging line in outdoor area' }
        ]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStorageUnits();
  }, []);

  // Filter units when area changes
  useEffect(() => {
    if (selectedAreaId) {
      const filtered = units.filter(unit => unit.area_id.toString() === selectedAreaId);
      setFilteredUnits(filtered);

      // Reset unit selection if current selection is not in filtered list
      if (selectedUnitId && !filtered.some(unit => unit.unit_id.toString() === selectedUnitId)) {
        setValue('unitId', '');
      }
    } else {
      setFilteredUnits([]);
      setValue('unitId', '');
    }
  }, [selectedAreaId, units, selectedUnitId, setValue]);

  // Handle form submission
  const onSubmit = async (data: StorageLocationFormData) => {
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      const supabase = createClient();

      // Prepare the data for insertion
      const locationData = {
        part_id: partId,
        unit_id: parseInt(data.unitId),
        quantity: data.quantity,
        location_subtype: data.locationSubtype,
        details: data.details || {},
        notes: data.notes || null
      };

      // Insert the data
      const { error } = await supabase
        .from('part_locations')
        .insert(locationData);

      if (error) {
        console.error('Error inserting location data:', error);
        throw error;
      }

      // Success!
      setSubmitStatus('success');
      reset(); // Reset form

      // Call onSuccess after a short delay to show success animation
      setTimeout(() => {
        onSuccess();
      }, 1500);

    } catch (error: any) {
      console.error('Error saving storage location:', error);
      setErrorMessage(error.message || 'Failed to save storage location');
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render location subtype specific fields
  const renderLocationSubtypeFields = () => {
    switch (selectedLocationType) {
      case 'crate':
        return (
          <motion.div
            variants={itemVariants}
            className="space-y-4"
          >
            <Controller
              name="details.level"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <div>
                  <label className="block text-sm font-medium mb-1">Shelf Level</label>
                  <Input
                    {...field}
                    placeholder="Enter shelf level (e.g., 3)"
                    className="w-full"
                  />
                </div>
              )}
            />

            <Controller
              name="details.crate_code"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <div>
                  <label className="block text-sm font-medium mb-1">Crate Code</label>
                  <Input
                    {...field}
                    placeholder="Enter crate code (e.g., A23)"
                    className="w-full"
                  />
                </div>
              )}
            />
          </motion.div>
        );

      case 'container':
        return (
          <motion.div
            variants={itemVariants}
            className="space-y-4"
          >
            <Controller
              name="details.level"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <div>
                  <label className="block text-sm font-medium mb-1">Shelf Level</label>
                  <Input
                    {...field}
                    placeholder="Enter shelf level (e.g., 3)"
                    className="w-full"
                  />
                </div>
              )}
            />

            <Controller
              name="details.container_code"
              control={control}
              defaultValue="C-"
              render={({ field }) => (
                <div>
                  <label className="block text-sm font-medium mb-1">Container Code</label>
                  <Input
                    {...field}
                    placeholder="Enter container code (e.g., C-A15)"
                    className="w-full"
                  />
                  <p className="text-xs text-gray-500 mt-1">Container codes must start with C-</p>
                </div>
              )}
            />
          </motion.div>
        );

      case 'shelf_section':
        return (
          <motion.div
            variants={itemVariants}
            className="space-y-4"
          >
            <Controller
              name="details.level"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <div>
                  <label className="block text-sm font-medium mb-1">Shelf Level</label>
                  <Input
                    {...field}
                    placeholder="Enter shelf level (e.g., 2)"
                    className="w-full"
                  />
                </div>
              )}
            />

            <Controller
              name="details.section"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <div>
                  <label className="block text-sm font-medium mb-1">Section</label>
                  <Input
                    {...field}
                    placeholder="Enter section (e.g., 5)"
                    className="w-full"
                  />
                </div>
              )}
            />
          </motion.div>
        );

      case 'open_shelf':
        return (
          <motion.div
            variants={itemVariants}
            className="space-y-4"
          >
            <Controller
              name="details.level"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <div>
                  <label className="block text-sm font-medium mb-1">Shelf Level</label>
                  <Input
                    {...field}
                    placeholder="Enter shelf level (e.g., 5)"
                    className="w-full"
                  />
                </div>
              )}
            />
          </motion.div>
        );

      case 'cage_section':
        return (
          <motion.div
            variants={itemVariants}
            className="space-y-4"
          >
            <Controller
              name="details.row"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <div>
                  <label className="block text-sm font-medium mb-1">Row</label>
                  <Input
                    {...field}
                    placeholder="Enter row (e.g., 1)"
                    className="w-full"
                  />
                </div>
              )}
            />

            <Controller
              name="details.col"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <div>
                  <label className="block text-sm font-medium mb-1">Column</label>
                  <Input
                    {...field}
                    placeholder="Enter column (e.g., 3)"
                    className="w-full"
                  />
                </div>
              )}
            />
          </motion.div>
        );

      case 'hanging_point':
        return (
          <motion.div
            variants={itemVariants}
            className="space-y-4"
          >
            <Controller
              name="details.point_identifier"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <div>
                  <label className="block text-sm font-medium mb-1">Hanging Point Identifier</label>
                  <Input
                    {...field}
                    placeholder="Enter hanging point identifier"
                    className="w-full"
                  />
                </div>
              )}
            />
          </motion.div>
        );

      case 'open_area_spot':
        return (
          <motion.div
            variants={itemVariants}
            className="space-y-4"
          >
            <Controller
              name="details.spot_description"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <div>
                  <label className="block text-sm font-medium mb-1">Spot Description</label>
                  <Input
                    {...field}
                    placeholder="Enter spot description"
                    className="w-full"
                  />
                </div>
              )}
            />
          </motion.div>
        );

      default:
        return null;
    }
  };

  // Render success message
  const renderSuccessMessage = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className="bg-green-50 border border-green-200 rounded-lg p-6 text-center"
    >
      <CheckCircle2 className="w-12 h-12 text-green-500 mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-green-800 mb-2">Location Added Successfully!</h3>
      <p className="text-green-600 mb-4">
        The part has been successfully assigned to its storage location.
      </p>
    </motion.div>
  );

  // Render error message
  const renderErrorMessage = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className="bg-red-50 border border-red-200 rounded-lg p-6 text-center"
    >
      <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-red-800 mb-2">Error Adding Location</h3>
      <p className="text-red-600 mb-4">
        {errorMessage || "There was an error adding the storage location. Please try again."}
      </p>
      <Button
        variant="outline"
        onClick={() => setSubmitStatus('idle')}
        className="mx-auto"
      >
        Try Again
      </Button>
    </motion.div>
  );

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      <div className="bg-gradient-to-r from-teal-500 to-blue-500 p-6">
        <div className="flex items-center">
          <MapPin className="w-6 h-6 text-white mr-3" />
          <h2 className="text-xl font-semibold text-white">Add Storage Location</h2>
        </div>
        <p className="text-teal-50 mt-1">
          Specify where this part will be stored in your inventory
        </p>
      </div>

      <div className="p-6">
        <AnimatePresence mode="wait">
          {submitStatus === 'success' ? (
            renderSuccessMessage()
          ) : submitStatus === 'error' ? (
            renderErrorMessage()
          ) : (
            <motion.form
              variants={formVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              onSubmit={handleSubmit(onSubmit)}
              className="space-y-6"
            >
              {/* Part Information */}
              <motion.div
                variants={itemVariants}
                className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6"
              >
                <h3 className="text-sm font-medium text-gray-500 mb-1">Part</h3>
                <p className="text-gray-900 font-medium">{partName}</p>
              </motion.div>

              {/* Storage Area Selection */}
              <motion.div variants={itemVariants}>
                <label className="block text-sm font-medium mb-1">Storage Area</label>
                <Controller
                  name="areaId"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={isLoading}
                    >
                      <SelectTrigger className="w-full" placeholder="Select warehouse">
                        {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                        {field.value ?
                          areas.find(area => area.area_id.toString() === field.value)?.name || "Select area"
                          : "Select storage area"
                        }
                      </SelectTrigger>
                      <SelectContent>
                        {areas.map((area) => (
                          <SelectItem
                            key={area.area_id}
                            value={area.area_id.toString()}
                          >
                            {area.name} ({area.location_type}, {area.level})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.areaId && (
                  <p className="text-red-500 text-sm mt-1">{errors.areaId.message}</p>
                )}
              </motion.div>

              {/* Storage Unit Selection */}
              <motion.div variants={itemVariants}>
                <label className="block text-sm font-medium mb-1">Storage Unit</label>
                <Controller
                  name="unitId"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={!selectedAreaId || isLoading}
                    >
                      <SelectTrigger className="w-full" placeholder="Select area">
                        {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                        {field.value ?
                          units.find(unit => unit.unit_id.toString() === field.value)?.identifier || "Select unit"
                          : "Select storage unit"
                        }
                      </SelectTrigger>
                      <SelectContent>
                        {filteredUnits.map((unit) => (
                          <SelectItem
                            key={unit.unit_id}
                            value={unit.unit_id.toString()}
                          >
                            {unit.identifier} ({unit.unit_type})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.unitId && (
                  <p className="text-red-500 text-sm mt-1">{errors.unitId.message}</p>
                )}
              </motion.div>

              {/* Location Subtype Selection */}
              <motion.div variants={itemVariants}>
                <label className="block text-sm font-medium mb-1">Location Type</label>
                <Controller
                  name="locationSubtype"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <SelectTrigger className="w-full" placeholder="Select unit">
                        {field.value === 'crate' ? 'Crate' :
                         field.value === 'container' ? 'Container' :
                         field.value === 'shelf_section' ? 'Shelf Section' :
                         field.value === 'open_shelf' ? 'Open Shelf' :
                         field.value === 'cage_section' ? 'Cage Section' :
                         field.value === 'hanging_point' ? 'Hanging Point' :
                         field.value === 'open_area_spot' ? 'Open Area Spot' :
                         'Select location type'}
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="crate">Crate</SelectItem>
                        <SelectItem value="container">Container</SelectItem>
                        <SelectItem value="shelf_section">Shelf Section</SelectItem>
                        <SelectItem value="open_shelf">Open Shelf</SelectItem>
                        <SelectItem value="cage_section">Cage Section</SelectItem>
                        <SelectItem value="hanging_point">Hanging Point</SelectItem>
                        <SelectItem value="open_area_spot">Open Area Spot</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.locationSubtype && (
                  <p className="text-red-500 text-sm mt-1">{errors.locationSubtype.message}</p>
                )}
              </motion.div>

              {/* Render location subtype specific fields */}
              {renderLocationSubtypeFields()}

              {/* Quantity */}
              <motion.div variants={itemVariants}>
                <Controller
                  name="quantity"
                  control={control}
                  render={({ field }) => (
                    <NumberInput
                      name="quantity"
                      label="Quantity"
                      control={control}
                      errorMessage={errors.quantity?.message}
                      placeholder="Enter quantity"
                    />
                  )}
                />
              </motion.div>

              {/* Notes */}
              <motion.div variants={itemVariants}>
                <label className="block text-sm font-medium mb-1">Notes (Optional)</label>
                <Controller
                  name="notes"
                  control={control}
                  render={({ field }) => (
                    <textarea
                      {...field}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                      placeholder="Add any additional notes about this location"
                      rows={3}
                    />
                  )}
                />
              </motion.div>

              {/* Form Actions */}
              <motion.div
                variants={itemVariants}
                className="flex justify-end space-x-3 pt-4 border-t border-gray-200"
              >
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  loading={isSubmitting}
                  loadingText="Saving..."
                >
                  Save Location
                </Button>
              </motion.div>
            </motion.form>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default StorageLocationForm;
