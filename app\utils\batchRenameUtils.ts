/**
 * Batch Rename Utility
 * 
 * This utility provides functions to batch rename all parts in a category
 * when the category's title template is updated.
 */

import { createClient } from '@/app/libs/supabase/client';
import { generatePartTitle } from './titleGenerator';

interface PartData {
  id: number;
  title: string;
  category_id: number;
  partnumber_group?: number;
  part_number?: string;
  condition?: string;
  attributes?: Record<string, any>;
  cars?: any[];
}

interface RenameResult {
  success: boolean;
  totalParts: number;
  renamedParts: number;
  errors: string[];
}

/**
 * Batch rename all parts in a category using the new title template
 */
export async function batchRenamePartsInCategory(categoryId: number): Promise<RenameResult> {
  const supabase = createClient();
  const result: RenameResult = {
    success: false,
    totalParts: 0,
    renamedParts: 0,
    errors: []
  };

  try {
    console.log(`Starting batch rename for category ${categoryId}`);

    // First, get all parts in this category with enhanced data
    const { data: parts, error: fetchError } = await supabase
      .from('parts')
      .select(`
        id,
        title,
        category_id,
        partnumber_group,
        description
      `)
      .eq('category_id', categoryId);

    if (fetchError) {
      result.errors.push(`Failed to fetch parts: ${fetchError.message}`);
      return result;
    }

    if (!parts || parts.length === 0) {
      console.log(`No parts found in category ${categoryId}`);
      result.success = true;
      return result;
    }

    result.totalParts = parts.length;
    console.log(`Found ${parts.length} parts to rename in category ${categoryId}`);

    // Process parts in batches to avoid overwhelming the system
    const batchSize = 10;
    const batches = [];
    for (let i = 0; i < parts.length; i += batchSize) {
      batches.push(parts.slice(i, i + batchSize));
    }

    for (const batch of batches) {
      await Promise.all(batch.map(async (part: any) => {
        try {
          // Get additional part data
          const partNumber = await getPartNumber(supabase, part.partnumber_group);
          const compatibleVehicles = await getCompatibleVehicles(supabase, part.id);
          const compatiblePartNumbers = await getCompatiblePartNumbers(supabase, part.partnumber_group);

          // Prepare part data for title generation
          const partData: PartData = {
            id: part.id,
            title: part.title,
            category_id: part.category_id,
            partnumber_group: part.partnumber_group,
            part_number: partNumber,
            condition: 'Used', // Default condition
            attributes: {},
            cars: compatibleVehicles.length > 0 ? compatibleVehicles : undefined
          };

          // Add compatible part numbers to the part data
          (partData as any).compatible_part_numbers = compatiblePartNumbers;

          // Generate new title using the template
          const newTitle = await generatePartTitle(partData);

          // Only update if the title has actually changed
          if (newTitle && newTitle !== part.title) {
            const { error: updateError } = await supabase
              .from('parts')
              .update({ title: newTitle })
              .eq('id', part.id);

            if (updateError) {
              result.errors.push(`Failed to update part ${part.id}: ${updateError.message}`);
            } else {
              result.renamedParts++;
              console.log(`Renamed part ${part.id}: "${part.title}" → "${newTitle}"`);
            }
          }
        } catch (error: any) {
          result.errors.push(`Error processing part ${part.id}: ${error.message}`);
        }
      }));

      // Small delay between batches to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    result.success = result.errors.length === 0;
    console.log(`Batch rename completed. Renamed ${result.renamedParts}/${result.totalParts} parts`);

    return result;
  } catch (error: any) {
    console.error('Error in batch rename:', error);
    result.errors.push(`Batch rename failed: ${error.message}`);
    return result;
  }
}

/**
 * Get a preview of how parts will be renamed (without actually renaming them)
 */
export async function previewBatchRename(categoryId: number): Promise<{title: string, newTitle: string, partId: number}[]> {
  const supabase = createClient();
  const preview: {title: string, newTitle: string, partId: number}[] = [];

  try {
    // Get first 5 parts as a preview
    const { data: parts, error: fetchError } = await supabase
      .from('parts')
      .select(`
        id,
        title,
        category_id,
        partnumber_group,
        description
      `)
      .eq('category_id', categoryId)
      .limit(5);

    if (fetchError || !parts) {
      return preview;
    }

    for (const part of parts) {
      try {
        // Get additional part data for preview
        const partNumber = await getPartNumber(supabase, part.partnumber_group);
        const compatibleVehicles = await getCompatibleVehicles(supabase, part.id);
        const compatiblePartNumbers = await getCompatiblePartNumbers(supabase, part.partnumber_group);

        const partData: PartData = {
          id: part.id,
          title: part.title,
          category_id: part.category_id,
          partnumber_group: part.partnumber_group,
          part_number: partNumber,
          condition: 'Used',
          attributes: {},
          cars: compatibleVehicles.length > 0 ? compatibleVehicles : undefined
        };

        // Add compatible part numbers to the part data
        (partData as any).compatible_part_numbers = compatiblePartNumbers;

        const newTitle = await generatePartTitle(partData);
        
        if (newTitle && newTitle !== part.title) {
          preview.push({
            title: part.title,
            newTitle: newTitle,
            partId: part.id
          });
        }
      } catch (error) {
        console.error(`Error generating preview for part ${part.id}:`, error);
      }
    }

    return preview;
  } catch (error) {
    console.error('Error generating preview:', error);
    return preview;
  }
}

/**
 * Helper function to get part number from part_to_group table
 */
async function getPartNumber(supabase: any, partnumberGroup?: number): Promise<string> {
  if (!partnumberGroup) return '';

  try {
    const { data, error } = await supabase
      .from('part_to_group')
      .select('partnumber')
      .eq('group_id', partnumberGroup)
      .limit(1)
      .single();

    if (error || !data) return '';
    return data.partnumber || '';
  } catch (error) {
    console.error('Error fetching part number:', error);
    return '';
  }
}

/**
 * Helper function to get compatible vehicles from parts_car table
 */
async function getCompatibleVehicles(supabase: any, partId: number): Promise<any[]> {
  try {
    // First, get the variation_trim_ids for this part
    const { data: partsCar, error: partsCarError } = await supabase
      .from('parts_car')
      .select('variation_trim_id')
      .eq('part_id', partId)
      .limit(5); // Limit to first 5 for performance

    if (partsCarError || !partsCar || partsCar.length === 0) {
      console.error('Error fetching parts_car data:', partsCarError);
      return [];
    }

    const variationTrimIds = partsCar.map((pc: any) => pc.variation_trim_id);

    // Get vehicle details step by step (simpler than complex nested joins)
    const vehicleDetails = [];

    for (const trimId of variationTrimIds) {
      try {
        // Get variation_trim data
        const { data: trimData } = await supabase
          .from('variation_trim')
          .select('trim, variation_id')
          .eq('id', trimId)
          .single();

        if (!trimData) continue;

        // Get car_variation data
        const { data: variationData } = await supabase
          .from('car_variation')
          .select('variation, generation_id')
          .eq('id', trimData.variation_id)
          .single();

        if (!variationData) continue;

        // Get car_generation data
        const { data: generationData } = await supabase
          .from('car_generation')
          .select('name, model_id')
          .eq('id', variationData.generation_id)
          .single();

        if (!generationData) continue;

        // Get car_models data
        const { data: modelData } = await supabase
          .from('car_models')
          .select('model_name, brand_id')
          .eq('id', generationData.model_id)
          .single();

        if (!modelData) continue;

        // Get car_brands data
        const { data: brandData } = await supabase
          .from('car_brands')
          .select('brand_name')
          .eq('brand_id', modelData.brand_id)
          .single();

        if (!brandData) continue;

        vehicleDetails.push({
          brand_name: brandData.brand_name || '',
          model_name: modelData.model_name || '',
          generation_name: generationData.name || '',
          variation: variationData.variation || '',
          trim: trimData.trim || ''
        });
      } catch (error) {
        console.error('Error fetching vehicle details for trim ID:', trimId, error);
        continue;
      }
    }

    return vehicleDetails;
  } catch (error) {
    console.error('Error fetching compatible vehicles:', error);
    return [];
  }
}

/**
 * Helper function to get compatible part numbers
 */
async function getCompatiblePartNumbers(supabase: any, partnumberGroup?: number): Promise<string[]> {
  if (!partnumberGroup) return [];

  try {
    const { data, error } = await supabase
      .from('part_to_group')
      .select('partnumber')
      .eq('group_id', partnumberGroup);
      // Removed limit - get ALL compatible part numbers

    if (error || !data) return [];

    // Get unique part numbers and filter out empty ones
    const uniquePartNumbers = Array.from(new Set(data
      .map((item: any) => item.partnumber)
      .filter((pn: string) => pn && pn.trim() !== '')));

    return uniquePartNumbers;
  } catch (error) {
    console.error('Error fetching compatible part numbers:', error);
    return [];
  }
}
