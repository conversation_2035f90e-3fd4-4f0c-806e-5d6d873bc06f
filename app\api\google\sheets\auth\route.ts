import { NextRequest, NextResponse } from 'next/server';
import { OAuth2Client } from 'google-auth-library';
import { createRouteHandlerClient } from '@/app/libs/supabase/server';

/**
 * GET: Start Google OAuth flow
 */
export async function GET(request: NextRequest) {
  try {
    // Get the user ID from the query parameter
    const requestUrl = new URL(request.url);
    const userId = requestUrl.searchParams.get('userId') || '';
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required for authentication' },
        { status: 400 }
      );
    }
    // Create OAuth client
    const oauth2Client = new OAuth2Client(
      process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI || 'http://localhost:3000/api/google/sheets/auth/callback'
    );

    // Generate auth URL
    const scopes = [
      'https://www.googleapis.com/auth/spreadsheets',
      'https://www.googleapis.com/auth/drive.file'
    ];

    // Create a state parameter with the user ID
    const state = userId ? Buffer.from(JSON.stringify({ userId })).toString('base64') : '';
    
    const authUrl = oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent',
      state: state // Include the state parameter with the user ID
    });

    return NextResponse.json({ url: authUrl });
  } catch (error: any) {
    console.error('Error generating auth URL:', error);
    return NextResponse.json(
      { error: 'Failed to generate auth URL', details: error?.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
