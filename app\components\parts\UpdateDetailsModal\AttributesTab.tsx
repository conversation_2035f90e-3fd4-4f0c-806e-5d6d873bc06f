// /components/modals/UpdateDetailsModal/AttributesTab.tsx
import React, { useEffect, useState } from 'react';
import { AttributesTabProps } from './types';
import Input from '@/app/components/ui/inputs/Input'; // Assuming path
import NumberInput from '@/app/components/ui/inputs/NumberInput'; // Assuming path
import { updateTitleWithAttributes } from './utils/attributeTitleUpdater';

const AttributesTab: React.FC<AttributesTabProps> = ({
  register,
  control,
  setValue,
  watch,
  categoryAttributes,
  attributeOptions,
  isLoadingAttributes,
  initialAttributeValues, // Use initial values to set defaults
}) => {
  // Track attribute changes to update title
  const [previousAttributes, setPreviousAttributes] = useState<any[]>([]);
  const watchedAttributes = watch('attributes') || [];

  // Effect to set default values for attributes once they are loaded
  useEffect(() => {
    if (!isLoadingAttributes && categoryAttributes.length > 0 && initialAttributeValues) {
      categoryAttributes.forEach((attribute, index) => {
        const initialValue = initialAttributeValues.find(v => v.attribute_id === attribute.id);
        let defaultValue: string | string[] | null = null;

        if (initialValue) {
          if (attribute.input_type === 'checkbox') {
             // Assuming checkbox value is stored as a comma-separated string or similar
             // Adjust parsing if your storage format is different (e.g., JSON array)
             defaultValue = initialValue.selection_value ? initialValue.selection_value.split(',') : [];
          } else {
             defaultValue = initialValue.value ?? initialValue.selection_value ?? '';
          }
        }

        // Use setValue to populate the form state
        // Note: Checkbox might need special handling depending on how react-hook-form manages array values
        setValue(`attributes.${index}.value`, defaultValue ?? '');
        setValue(`attributes.${index}.id`, attribute.id.toString());
        setValue(`attributes.${index}.name`, attribute.attribute);
        setValue(`attributes.${index}.inputType`, attribute.input_type);
      });

      // Initialize previous attributes
      setPreviousAttributes(watch('attributes') || []);
    }
  }, [isLoadingAttributes, categoryAttributes, initialAttributeValues, setValue, watch]);

  // Effect to update title when attributes change
  useEffect(() => {
    const updateTitle = async () => {
      // Only proceed if we have attributes and they've changed
      if (!watchedAttributes || watchedAttributes.length === 0) return;

      // Check if attributes have changed
      const hasChanged = JSON.stringify(watchedAttributes) !== JSON.stringify(previousAttributes);
      if (!hasChanged) return;

      try {
        // Get current title
        const currentTitle = watch('title');
        if (!currentTitle) return;

        // Update title with attribute changes
        const updatedTitle = await updateTitleWithAttributes(
          currentTitle,
          watchedAttributes,
          categoryAttributes
        );

        // Update the title if it changed
        if (updatedTitle !== currentTitle) {
          setValue('title', updatedTitle);
          console.log('Title updated based on attributes:', updatedTitle);
        }

        // Update previous attributes
        setPreviousAttributes(watchedAttributes);
      } catch (err) {
        console.error('Error updating title based on attributes:', err);
      }
    };

    updateTitle();
  }, [watchedAttributes, previousAttributes, watch, setValue, categoryAttributes]);


  if (isLoadingAttributes) {
    return <div className="text-center py-6 text-gray-500">Loading attributes...</div>;
  }

  if (!categoryAttributes || categoryAttributes.length === 0) {
    return <div className="text-center py-6 text-gray-500">No category attributes defined for this part.</div>;
  }

  return (
    <div className="space-y-4 pt-4">
      <h4 className="font-medium mb-2 text-gray-700">Category Attributes</h4>
      {categoryAttributes.map((attribute, index) => {
        const options = attributeOptions[attribute.id] || [];
        // Find the corresponding initial value for default checking (needed for radio/checkbox)
        const initialValueRecord = initialAttributeValues.find(v => v.attribute_id === attribute.id);
        const initialSimpleValue = initialValueRecord?.value ?? initialValueRecord?.selection_value ?? '';
        // For checkboxes, we might need an array
        const initialCheckboxValue = attribute.input_type === 'checkbox' && initialValueRecord?.selection_value
            ? initialValueRecord.selection_value.split(',') // Adjust if stored differently
            : [];

        return (
          <div key={attribute.id} className="bg-gray-50 p-3 rounded-md border border-gray-200">
            <label
              htmlFor={`attributes.${index}.value`}
              className="block text-sm font-medium mb-2 text-gray-800"
            >
              {attribute.attribute} {attribute.is_required && <span className="text-red-500">*</span>}
            </label>

            {/* Render input based on attribute.input_type */}
            {attribute.input_type === 'text' && (
              <Input
                id={`attributes.${index}.value`}
                {...register(`attributes.${index}.value`, { required: attribute.is_required ? `${attribute.attribute} is required` : false })}
                className="w-full"
              />
            )}

            {attribute.input_type === 'number' && (
              <NumberInput
                name={`attributes.${index}.value`}
                control={control}
                rules={{ required: attribute.is_required ? `${attribute.attribute} is required` : false }}
                className="w-full"
              />
            )}

            {attribute.input_type === 'select' && (
              <select
                id={`attributes.${index}.value`}
                {...register(`attributes.${index}.value`, { required: attribute.is_required ? `${attribute.attribute} is required` : false })}
                className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
              >
                <option value="">Select {attribute.attribute}...</option>
                {options.map((option) => (
                  <option key={option.id} value={option.option_value}>
                    {option.option_value}
                  </option>
                ))}
              </select>
            )}

             {attribute.input_type === 'radio' && options.length > 0 && (
               <div className="flex flex-col space-y-1 mt-1">
                 {options.map((option) => (
                   <label key={option.id} className="inline-flex items-center cursor-pointer">
                     <input
                       type="radio"
                       {...register(`attributes.${index}.value`, {
                         required: attribute.is_required ? `${attribute.attribute} is required` : false,
                         onChange: (e) => {
                           // Special handling for side attribute to ensure title updates correctly
                           if (attribute.attribute.toLowerCase().includes('side')) {
                             console.log(`Side attribute changed to: ${e.target.value}`);
                             // Force immediate title update for side changes
                             setTimeout(() => {
                               // Reset previous attributes to force update
                               setPreviousAttributes([]);
                             }, 0);
                           }
                         }
                       })}
                       value={option.option_value}
                       // Check against the simple initial value
                       defaultChecked={initialSimpleValue === option.option_value}
                       className="form-radio h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                     />
                     <span className="ml-2 text-sm text-gray-700">{option.option_value}</span>
                   </label>
                 ))}
               </div>
             )}

            {attribute.input_type === 'checkbox' && options.length > 0 && (
              <div className="flex flex-col space-y-1 mt-1">
                {options.map((option) => (
                  <label key={option.id} className="inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      // Registering checkboxes for multiple values requires careful handling.
                      // The value might need to be an array managed via setValue/watch or a custom Controller.
                      // For simplicity here, we register it, but managing the array state might need adjustment.
                      {...register(`attributes.${index}.value.${option.option_value}` as any)} // Example: Register each option
                      // Or register as a single field and manage the array:
                      // {...register(`attributes.${index}.value`)}
                      value={option.option_value}
                      // Check against the array of initial values
                      defaultChecked={initialCheckboxValue.includes(option.option_value)}
                      className="form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">{option.option_value}</span>
                  </label>
                ))}
                {/* If registering as a single field, you'd likely use Controller or manage value array manually */}
              </div>
            )}

            {/* Hidden fields already set by useEffect */}
            {/*
            <input type="hidden" {...register(`attributes.${index}.id`)} />
            <input type="hidden" {...register(`attributes.${index}.name`)} />
            <input type="hidden" {...register(`attributes.${index}.inputType`)} />
            */}
          </div>
        );
      })}
    </div>
  );
};

export default AttributesTab;
