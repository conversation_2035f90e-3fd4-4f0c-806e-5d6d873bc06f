# Jiji.co.ke Automation Tool

An automated browser tool for posting automotive parts from your Autoflow database to the Jiji.co.ke marketplace.

## Features

- 🚀 **Automated Listing**: Automatically create part listings on Jiji.co.ke
- 🖼️ **Image Upload**: Download and upload part images from your database
- 🔄 **Batch Processing**: Process multiple parts in configurable batches
- 🛡️ **Error Handling**: Robust retry logic and error recovery
- 📊 **Progress Tracking**: Database tracking of listing status
- 🧪 **Test Mode**: Dry-run capability for testing
- ⚡ **Rate Limiting**: Built-in delays to avoid being blocked
- 📝 **Comprehensive Logging**: Detailed logs for debugging and monitoring

## Prerequisites

1. **Node.js** (v16 or higher)
2. **Playwright** browsers installed
3. **Supabase** database with Autoflow schema
4. **Jiji.co.ke** account with valid credentials

## Installation

1. Install dependencies:
```bash
npm install playwright @playwright/test dotenv commander
```

2. Install Playwright browsers:
```bash
npx playwright install
```

3. Set up the database schema:
```bash
# Run the SQL schema in your Supabase database
psql -h your-db-host -U your-user -d your-db -f automation/database-schema.sql
```

4. Configure environment variables:
```bash
# Copy and edit the environment file
cp .env.example .env.local
```

Required environment variables:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Optional: Set Jiji credentials (or provide via CLI)
JIJI_EMAIL=your_jiji_email
JIJI_PASSWORD=your_jiji_password
```

## Usage

### Basic Usage

Run the automation with default settings:
```bash
npx ts-node automation/run-jiji-automation.ts run
```

### With Credentials

Provide Jiji credentials via command line:
```bash
npx ts-node automation/run-jiji-automation.ts run \
  --email <EMAIL> \
  --password yourpassword
```

### Test Mode

Run in test mode (no actual listings created):
```bash
npx ts-node automation/run-jiji-automation.ts test
```

### Advanced Options

```bash
npx ts-node automation/run-jiji-automation.ts run \
  --headless \
  --batch-size 10 \
  --delay 5000 \
  --max-retries 5 \
  --no-skip-existing
```

### Configuration File

Generate a sample configuration file:
```bash
npx ts-node automation/run-jiji-automation.ts config
```

Use a configuration file:
```bash
npx ts-node automation/run-jiji-automation.ts run --config-file config.json
```

## Configuration Options

| Option | Default | Description |
|--------|---------|-------------|
| `headless` | `false` | Run browser without GUI |
| `batchSize` | `5` | Number of parts to process per batch |
| `delayBetweenActions` | `3000` | Delay between actions (ms) |
| `maxRetries` | `3` | Maximum retries per part |
| `skipExisting` | `true` | Skip already listed parts |
| `testMode` | `false` | Dry-run mode |

## Database Schema

The tool creates the following tables:

### `jiji_listings`
Tracks the status of each part listing:
- `part_id`: Reference to the part
- `jiji_listing_id`: Jiji's internal listing ID
- `listing_url`: URL of the created listing
- `status`: Current status (pending, listed, failed, updated, removed)
- `error_message`: Error details if failed
- `listed_at`: When the listing was created

### `jiji_automation_logs`
Stores automation logs for debugging:
- `session_id`: Unique session identifier
- `level`: Log level (info, warn, error)
- `message`: Log message
- `part_id`: Associated part (if applicable)
- `error_details`: Detailed error information

## How It Works

1. **Database Query**: Fetches parts with images and pricing from your Autoflow database
2. **Browser Automation**: Opens Jiji.co.ke and logs in with your credentials
3. **Category Selection**: Navigates to Vehicle Parts & Accessories category
4. **Form Filling**: Fills out the listing form with part details:
   - Title and description
   - Price and condition
   - Make/brand (if available)
   - Category matching
5. **Image Upload**: Downloads images from your storage and uploads to Jiji
6. **Submission**: Submits the listing and captures the result
7. **Status Tracking**: Updates the database with listing status

## Part Selection Logic

The tool prioritizes parts based on:
1. **Images**: Parts with more images are processed first
2. **Price**: Higher-priced items get priority
3. **Recency**: Recently updated parts are preferred
4. **Stock**: Only parts with available stock are processed

## Error Handling

- **Retry Logic**: Failed operations are retried up to `maxRetries` times
- **Graceful Degradation**: Missing optional fields don't stop the process
- **Detailed Logging**: All errors are logged with context for debugging
- **Status Tracking**: Failed listings are marked in the database

## Rate Limiting

To avoid being blocked by Jiji:
- Configurable delays between actions
- Human-like interaction patterns
- Randomized timing variations
- Respectful request rates

## Monitoring

### View Listing Statistics
```sql
SELECT * FROM jiji_listing_stats;
```

### Get Performance Metrics
```sql
SELECT * FROM get_jiji_listing_metrics();
```

### Check Recent Logs
```sql
SELECT * FROM jiji_automation_logs 
WHERE created_at > NOW() - INTERVAL '1 day'
ORDER BY created_at DESC;
```

## Troubleshooting

### Common Issues

1. **Login Failed**
   - Verify Jiji credentials
   - Check for CAPTCHA requirements
   - Ensure account is not locked

2. **Form Fields Not Found**
   - Jiji may have updated their website
   - Run in non-headless mode to debug
   - Check browser console for errors

3. **Image Upload Failed**
   - Verify image URLs are accessible
   - Check image file formats
   - Ensure sufficient disk space

4. **Database Connection Issues**
   - Verify Supabase credentials
   - Check network connectivity
   - Ensure database schema is up to date

### Debug Mode

Run with GUI to see what's happening:
```bash
npx ts-node automation/run-jiji-automation.ts run --no-headless
```

### Logs

Check the log files in `automation/logs/` for detailed information about each run.

## Security Considerations

- Store credentials securely (use environment variables)
- Don't commit credentials to version control
- Use strong, unique passwords for Jiji account
- Monitor for unusual account activity
- Respect Jiji's terms of service

## Limitations

- Maximum 10 images per listing (Jiji limitation)
- Rate limited to avoid being blocked
- Requires manual CAPTCHA solving if encountered
- Subject to Jiji website changes

## Support

For issues and questions:
1. Check the logs for error details
2. Review this documentation
3. Test in non-headless mode for debugging
4. Check Jiji's website for changes

## License

This tool is for internal use with your Autoflow database and Jiji.co.ke account.
