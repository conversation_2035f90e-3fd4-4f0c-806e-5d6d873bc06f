import {
  useState,
  createContext,
  useContext,
  forwardRef,
  useEffect,
  useRef,
  ReactNode,
  Ref,
} from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown, Loader2 } from "lucide-react";
import React from "react";

interface SelectProps {
  children?: React.ReactNode;
  defaultValue?: string;
  onValueChange?: (value: string, name?: string, years?: string) => void;
  onChange?: (value: string, name?: string, years?: string) => void;
  value?: string;
  label?: string;
  disabled?: boolean;
  name?: string;
  control?: any;
  endpoint?: string;
  loading?: boolean;
}

interface SelectContextType {
  isOpen: boolean;
  selectedValue: string;
  selectedDisplayValue: ReactNode;
  toggleOpen: () => void;
  handleSelect: (value: string, displayValue: ReactNode, years?: string) => void;
  triggerRef: React.RefObject<HTMLButtonElement | null>;
  contentRef: React.RefObject<HTMLDivElement | null>;
  disabled: boolean;
  name?: string;
  loading?: boolean;
}

const SelectContext = createContext<SelectContextType | null>(null);

const useSelect = () => {
  const context = useContext(SelectContext);
  if (!context) {
    throw new Error("useSelect must be used within a Select provider");
  }
  return context;
};

export const Select = ({
  children,
  defaultValue,
  onValueChange,
  onChange,
  value,
  label,
  disabled = false,
  name,
  control,
  endpoint,
  loading = false,
}: SelectProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState<string>(defaultValue || value || '');
  const [selectedDisplayValue, setSelectedDisplayValue] = useState<ReactNode>(null);
  const [items, setItems] = useState<any[]>([]);
  const [isFetching, setIsFetching] = useState(false);
  const triggerRef = useRef<HTMLButtonElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Always provide an undefined error check function
  const handleUndefinedValue = (value: any, defaultValue: any) => {
    return value === undefined ? defaultValue : value;
  };

  // Improved error handling for control prop
  const safeSetControlValue = (fieldName: string, fieldValue: string) => {
    if (!control || !fieldName) return;

    try {
      console.log('Attempting to set form control value:', { fieldName, fieldValue });

      // Handle different control structures
      if (typeof control.setValue === 'function') {
        control.setValue(fieldName, fieldValue, {
          shouldValidate: true,
          shouldDirty: true
        });
      } else if (control._formState?.form && typeof control._formState.form.setValue === 'function') {
        control._formState.form.setValue(fieldName, fieldValue, {
          shouldValidate: true,
          shouldDirty: true
        });
      } else {
        console.warn('Could not identify setValue method on control:', control);
      }
    } catch (error) {
      console.error('Error setting control value:', error);
    }
  };

  console.log('Select component value:', value);
  console.log('Select component selectedValue:', selectedValue);

  // Effect to handle controlled component behavior
  useEffect(() => {
    if (value !== undefined) {
      console.log('Setting selectedValue to:', value);
      setSelectedValue(value);
    }
  }, [value]);

  // Effect to fetch data if endpoint is provided
  useEffect(() => {
    if (endpoint && !disabled) {
      fetchData();
    }
  }, [endpoint, disabled]);

  const fetchData = async () => {
    if (!endpoint) return;

    try {
      const response = await fetch(endpoint);
      const data = await response.json();
      setItems(data);
    } catch (error) {
      console.error("Failed to fetch data:", error);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node) &&
        contentRef.current &&
        !contentRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const toggleOpen = () => {
    if (!disabled && !loading) {
      setIsOpen(!isOpen);
    }
  };

  const handleSelect = (value: string, displayValue: ReactNode, years?: string) => {
    if (disabled || loading) return;

    console.log('handleSelect called with:', { value, displayValue, years, name });

    setSelectedValue(value);
    setSelectedDisplayValue(displayValue);
    setIsOpen(false);

    // Extract displayName from displayValue if it's a string
    let displayName: string | undefined;

    if (typeof displayValue === 'string') {
      displayName = displayValue;
    }

    // Log the values for debugging
    console.log('Final Select values:', {
      value,
      displayName,
      years,
      name
    });

    if (onValueChange) {
      onValueChange(value, displayName, years);
    }

    if (onChange) {
      onChange(value, displayName, years);
    }

    // Handle form control if provided
    if (name) {
      safeSetControlValue(name, value);
    }
  };

  // Render select items from endpoint data if provided
  const renderEndpointItems = () => {
    if (!items.length) return null;

    return items.map((item) => {
      // For generations, use displayName if available
      const displayText = name === 'generationId' && item.displayName ?
        item.displayName :
        (item.name || item.title || item.label);

      console.log('Rendering endpoint item:', {
        id: item.id,
        displayText,
        name,
        years: item.years
      });

      return (
        <SelectItem
          key={item.id}
          value={item.id.toString()}
          years={name === 'generationId' ? item.years : undefined}
        >
          {displayText}
        </SelectItem>
      );
    });
  };

  return (
    <SelectContext.Provider
      value={{
        isOpen,
        selectedValue,
        selectedDisplayValue,
        toggleOpen,
        handleSelect,
        triggerRef,
        contentRef,
        disabled,
        name,
        loading
      }}
    >
      <div className="relative w-full">
        {label && <label className="block text-sm font-medium mb-1" htmlFor={name}>{label}</label>}
        {children || (
          <>
            <SelectTrigger placeholder={label || "Select an option"}>
              {loading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            </SelectTrigger>
            <SelectContent>
              {renderEndpointItems()}
            </SelectContent>
            {name && (
              <input
                type="hidden"
                name={name}
                value={selectedValue}
                id={name}
              />
            )}
          </>
        )}
      </div>
    </SelectContext.Provider>
  );
};

interface SelectTriggerProps {
  className?: string;
  placeholder: string;
  children?: ReactNode;
  ref?: Ref<HTMLButtonElement>;
  disabled?: boolean;
}

const SelectTrigger = forwardRef<
  HTMLButtonElement,
  SelectTriggerProps
>(({ className, placeholder, children, disabled: propDisabled }, ref) => {
  const {
    isOpen,
    toggleOpen,
    selectedValue,
    selectedDisplayValue,
    triggerRef,
    disabled: contextDisabled,
    name,
    loading
  } = useSelect();

  // Use prop disabled if provided, otherwise use context disabled
  const isDisabled = propDisabled !== undefined ? propDisabled : contextDisabled;

  return (
    <motion.button
      ref={ref || triggerRef}
      onClick={toggleOpen}
      disabled={isDisabled || loading}
      id={name ? `${name}-trigger` : undefined}
      aria-haspopup="listbox"
      aria-expanded={isOpen}
      aria-labelledby={name}
      aria-busy={loading}
      type="button"
      className={`flex items-center justify-between w-full px-4 py-3 text-sm bg-white border rounded-md shadow-sm
        ${(isDisabled || loading)
          ? 'cursor-not-allowed bg-gray-100 text-gray-400 border-gray-300'
          : 'hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500'
        }
        ${className || ""}`}
      whileTap={(isDisabled || loading) ? {} : { scale: 0.98 }}
    >
      <div className="relative flex-1 h-full text-left">
        <motion.span
          className={`absolute left-2 pointer-events-none transition-all duration-200 ease-in-out ${isDisabled ? 'text-gray-400' : ''}`}
          animate={{
            y: selectedValue ? -12 : 0,
            scale: selectedValue ? 0.75 : 1,
            color: selectedValue ? (isDisabled ? "#9ca3af" : "#6b7280") : (isDisabled ? "#9ca3af" : "#9ca3af"),
            top: selectedValue ? 2 : '50%',
            left: selectedValue ? -10 : -10,
            translateX: !selectedValue ? '0%' : '0%',
            translateY: !selectedValue ? '-50%' : '0',
          }}
          transition={{ duration: 0.2 }}
        >
          {placeholder}
        </motion.span>
        {selectedValue && (
          <motion.span
            className={`block pt-2 ${isDisabled ? 'text-gray-400' : ''}`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.1 }}
          >
            {selectedDisplayValue}
          </motion.span>
        )}
        {!selectedValue && children && (
          <motion.span
            className={`block pt-2 ${isDisabled ? 'text-gray-400' : ''}`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.1 }}
          >
            {children}
          </motion.span>
        )}
      </div>
      {loading ? (
        <Loader2 className="w-4 h-4 animate-spin text-gray-400" />
      ) : (
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronDown className={`w-4 h-4 ${isDisabled ? 'text-gray-300' : 'text-gray-400'}`} />
        </motion.div>
      )}
    </motion.button>
  );
});
SelectTrigger.displayName = "SelectTrigger";

interface SelectContentProps {
  children: React.ReactNode;
  className?: string;
  ref?: Ref<HTMLDivElement>;
}

const SelectContent = forwardRef<
  HTMLDivElement,
  SelectContentProps
>(({ children, className }, ref) => {
  const { isOpen, contentRef, disabled, loading, name } = useSelect();

  // Don't render content if disabled or loading
  if (disabled || loading) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          ref={ref || contentRef}
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ type: "spring", damping: 20, stiffness: 300 }}
          className={`absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg overflow-hidden ${className || ""}`}
          role="listbox"
          aria-labelledby={name ? `${name}-trigger` : undefined}
        >
          <ul className="py-1 text-sm text-gray-700">{children}</ul>
        </motion.div>
      )}
    </AnimatePresence>
  );
});
SelectContent.displayName = "SelectContent";

interface SelectItemProps {
  value: string;
  children: React.ReactNode;
  disabled?: boolean;
  years?: string;
}

const SelectItem = ({
  value,
  children,
  disabled: itemDisabled = false,
  years,
}: SelectItemProps) => {
  const { handleSelect, disabled: contextDisabled, selectedValue, name } = useSelect();

  // Item is disabled if either the item itself or the parent context is disabled
  const isDisabled = itemDisabled || contextDisabled;

  // Check if this item is currently selected
  const isSelected = selectedValue === value;

  // Get the display value from the children
  let displayValue: React.ReactNode = value;
  if (typeof children === 'string') {
    displayValue = children;
  } else if (React.isValidElement(children)) {
    const childElement = children as React.ReactElement<any>;
    if (childElement.props && childElement.props.children !== undefined) {
      displayValue = childElement.props.children;
    }
  }

  console.log('SelectItem rendering:', { value, displayValue, years, name });

  const handleItemClick = () => {
    if (isDisabled) return;

    console.log('SelectItem clicked:', { value, displayValue, years });
    // For generations, we need to pass both the name and years
    if (name === 'generationId') {
      if (years) {
        // If years are directly provided as a prop
        console.log('Using provided years:', years);
        handleSelect(value, displayValue, years);
      } else if (typeof displayValue === 'string') {
        // Try to extract years from display value (e.g., "B8 2008-2016")
        const match = displayValue.match(/(.*?)\s*(\d{4}-\d{4})/);
        if (match) {
          const extractedName = match[1].trim();
          const extractedYears = match[2];
          console.log('Extracted years from display value:', { extractedName, extractedYears });
          handleSelect(value, extractedName, extractedYears);
        } else {
          handleSelect(value, displayValue);
        }
      } else {
        handleSelect(value, displayValue);
      }
    } else {
      // For non-generation items, just pass the display value
      handleSelect(value, displayValue);
    }
  };

  return (
    <motion.li
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -10 }}
      transition={{ duration: 0.2 }}
      className={`px-4 py-2 ${
        isDisabled
          ? 'cursor-not-allowed text-gray-400 bg-gray-50'
          : isSelected
            ? 'bg-indigo-100 text-indigo-800'
            : 'cursor-pointer hover:bg-indigo-50 hover:text-indigo-700'
      }`}
      onClick={handleItemClick}
      role="option"
      aria-selected={isSelected}
      id={name ? `${name}-option-${value}` : undefined}
    >
      {children}
    </motion.li>
  );
};

interface SelectValueProps {
  children: ReactNode;
}

const SelectValue = ({ children }: SelectValueProps) => {
  return <span>{children}</span>;
};

// Export all components except Select which is already exported above
export {
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
};