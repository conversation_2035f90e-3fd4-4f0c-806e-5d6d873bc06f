'use client'; // This directive is required for client-side components

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion'; // Import framer-motion for animations
import { Phone, LogIn, Search } from 'lucide-react'; // Import icons from lucide-react
import Link from 'next/link';

// --- Animation Variants (Reusable) ---
const fadeIn = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { duration: 0.8, ease: "easeOut" } },
};

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
};

const staggerContainer = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.2, // Stagger animation of child elements
      delayChildren: 0.1,  // Optional delay before children start animating
    },
  },
};

// --- Header Component ---
const Header: React.FC = () => {
  return (
    <motion.header
      className="absolute top-0 left-0 right-0 z-20 p-4 md:p-6" // Increased z-index
      variants={fadeIn} // Apply fade-in animation
      initial="hidden"
      animate="visible"
    >
      <div className="container mx-auto flex justify-between items-center">
        {/* Logo */}
        <div className="text-white text-2xl font-semibold tracking-tighter">
          Autoflow
        </div>

        {/* Right side actions: Contact & Login */}
        <div className="flex items-center space-x-4 md:space-x-6">
          {/* Phone Contact Info */}
          <motion.a
            href="tel:+254724288400"
            className="flex items-center space-x-2 text-white hover:text-teal-300 transition duration-300"
            whileHover={{ scale: 1.05 }} // Subtle hover effect
            whileTap={{ scale: 0.95 }}   // Subtle tap effect
          >
            <Phone size={20} /> {/* Phone Icon */}
            <span className="text-sm md:text-base font-medium">+254724288400</span>
          </motion.a>

          {/* Minimalist Login Button with Animated Hover Text */}
          <div className="relative group">
            <Link href="/login" passHref>
              <motion.div
                className="bg-transparent border border-teal-500 hover:border-teal-300 text-white rounded-full p-2 transition-all duration-300 cursor-pointer hover:shadow-[0_0_10px_rgba(20,184,166,0.5)] hover:shadow-teal-500/30"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                onClick={(e) => {
                  // Prevent default to ensure we don't have any issues with the Link
                  e.preventDefault();
                  // Navigate to login page
                  window.location.href = '/login';
                }}
              >
                <LogIn size={20} className="text-teal-400 group-hover:text-teal-300 transition-all duration-300 group-hover:rotate-12" />

                {/* Animated hover text */}
                <span className="absolute left-1/2 -translate-x-1/2 -bottom-8 opacity-0 group-hover:opacity-100 group-hover:-bottom-6 transition-all duration-300 text-teal-300 text-sm whitespace-nowrap font-medium">
                  Login
                </span>
              </motion.div>
            </Link>
          </div>
        </div>
      </div>
    </motion.header>
  );
};

// --- Hero Section Component ---
const HeroSection: React.FC = () => {
  // State to track if the background image has loaded
  const [imageLoaded, setImageLoaded] = useState(false);

  // Background image URLs - using the Supabase URLs
  const desktopBackgroundUrl = 'https://excgraelqcvcdsnlvrtv.supabase.co/storage/v1/object/public/autoflow/bg.png';
  const mobileBackgroundUrl = 'https://excgraelqcvcdsnlvrtv.supabase.co/storage/v1/object/public/autoflow//mobile_bg.png';

  // Fallback background style with a gradient
  const fallbackBackground = {
    background: 'linear-gradient(135deg, #1a202c 0%, #2d3748 100%)',
  };

  // Preload the desktop background image
  useEffect(() => {
    const img = new Image();
    img.src = desktopBackgroundUrl;
    img.onload = () => setImageLoaded(true);
    img.onerror = () => console.error('Failed to load background image');
  }, []);

  // Handle search form submission - redirect to shop page with search query
  const handleSearchSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault(); // Prevent default form submission
    const formData = new FormData(event.currentTarget);
    const searchQuery = formData.get('search');

    if (searchQuery && typeof searchQuery === 'string' && searchQuery.trim()) {
      // Redirect to shop page with the search query
      window.location.href = `/shop?query=${encodeURIComponent(searchQuery.trim())}&page=1`;
    }
  };

  return (
    <>
      <style jsx>{`
        .hero-background {
          background-image: url('${desktopBackgroundUrl}');
        }

        @media (max-width: 767px) {
          .hero-background {
            background-image: url('${mobileBackgroundUrl}');
          }
        }
      `}</style>

      <div
        className="hero-background relative flex items-center justify-center min-h-screen bg-cover bg-center bg-no-repeat px-4"
        style={!imageLoaded ? fallbackBackground : {}}
      >
        {/* Dark Overlay for better text contrast - more transparent on mobile */}
        <div className="absolute inset-0 bg-black bg-opacity-40 md:bg-opacity-50 z-0"></div>

        {/* Content Container */}
        <motion.div
          className="relative z-10 text-center text-white max-w-3xl"
          variants={staggerContainer} // Apply stagger effect to children
          initial="hidden"
          animate="visible"
        >
          {/* Heading */}
          <motion.h1
            id="hero-title"
            className="text-2xl md:text-4xl lg:text-5xl font-bold mb-3 leading-tight"
            variants={fadeInUp} // Apply fade-in-up animation
          >
            Find your VW and Audi Parts...
          </motion.h1>

          {/* Subheading */}
          <motion.p
            id="hero-subtitle"
            className="text-base md:text-lg text-gray-200 mb-6"
            variants={fadeInUp} // Apply fade-in-up animation
          >
            We specialize in VW and Audi Spare parts in Nairobi. Give us a call on +254724288400
          </motion.p>

          {/* Hero Search Form */}
          <motion.div
            id="hero-search-container" // Changed ID for clarity
            className="relative mt-32 md:mt-10 max-w-md mx-auto" // Added more margin-top on mobile
            variants={fadeInUp} // Apply fade-in-up animation
          >
            <form onSubmit={handleSearchSubmit} className="flex items-center"> {/* Use form element */}
              <div className="relative flex-grow">
                <input
                  type="search"
                  name="search" // Added name attribute for form submission
                  placeholder="Search parts..."
                  required // Make search input required
                  className="bg-gray-100 bg-opacity-70 md:bg-opacity-90 text-gray-900 placeholder-gray-500 rounded-l-md py-3 pl-12 pr-4 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:bg-opacity-100 transition duration-300 w-full text-base shadow-md"
                />
                {/* Search Icon inside the input */}
                <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 pointer-events-none">
                   <Search size={20} />
                </div>
              </div>
              {/* Submit Button */}
              <motion.button
                type="submit"
                className="bg-orange-500 bg-opacity-90 hover:bg-opacity-100 hover:bg-orange-600 text-white font-semibold py-3 px-5 rounded-r-md transition duration-300 shadow-md text-base"
                whileHover={{ scale: 1.05 }} // Button hover effect
                whileTap={{ scale: 0.95 }}   // Button tap effect
              >
                Search
              </motion.button>
            </form>
          </motion.div>
        </motion.div>
      </div>
    </>
  );
};

// --- Footer Component ---
const Footer: React.FC = () => {
  const footerLinks = [
    { name: 'About', href: '#' },
    { name: 'View All Parts', href: '/shop' }, // Added parts link
    { name: 'Privacy', href: '#' },
    { name: 'Terms', href: '#' },
    { name: 'Login', href: '/login' },
  ];

  return (
    <motion.footer
      className="absolute bottom-0 left-0 right-0 bg-gray-900 bg-opacity-70 p-4 z-10" // Added z-index
      variants={fadeIn} // Apply fade-in animation
      initial="hidden"
      animate="visible"
    >
      <div className="container mx-auto text-center md:text-left">
        <ul className="flex flex-wrap justify-center md:justify-start space-x-4">
          {/* Map through footer links */}
          {footerLinks.map((link) => (
            <li key={link.name}>
              <Link href={link.href} className="text-gray-400 hover:text-orange-400 text-sm transition duration-300">
                {link.name}
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </motion.footer>
  );
};

// --- Main Landing Page Component (Default Export for Next.js) ---
const LandingPage: React.FC = () => {
  return (
    // Use a relative container to position absolute header/footer
    <div className="relative min-h-screen bg-gray-900 font-sans overflow-hidden"> {/* Prevent potential overflow issues */}
      {/* Header */}
      <Header />

      {/* Hero Section */}
      <HeroSection />

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default LandingPage; // Export the main component
