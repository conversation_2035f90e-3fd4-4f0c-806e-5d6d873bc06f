import { NextRequest, NextResponse } from 'next/server';
import { updateTransactionStatus } from '@/app/libs/mpesa/service';

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    
    // Extract the callback data
    const { Body } = body;
    
    if (!Body || !Body.stkCallback) {
      return NextResponse.json(
        { success: false, message: 'Invalid callback data' },
        { status: 400 }
      );
    }
    
    const { 
      MerchantRequestID, 
      CheckoutRequestID, 
      ResultCode, 
      ResultDesc 
    } = Body.stkCallback;
    
    // Get the callback metadata if available
    let mpesaReceiptNumber = null;
    if (Body.stkCallback.CallbackMetadata && Body.stkCallback.CallbackMetadata.Item) {
      const receiptItem = Body.stkCallback.CallbackMetadata.Item.find(
        (item: any) => item.Name === 'MpesaReceiptNumber'
      );
      
      if (receiptItem && receiptItem.Value) {
        mpesaReceiptNumber = receiptItem.Value;
      }
    }
    
    // Update the transaction status in the database
    if (ResultCode === '0') {
      // Transaction was successful
      await updateTransactionStatus(
        CheckoutRequestID,
        'completed',
        ResultCode,
        ResultDesc,
        mpesaReceiptNumber
      );
    } else {
      // Transaction failed
      await updateTransactionStatus(
        CheckoutRequestID,
        'failed',
        ResultCode,
        ResultDesc
      );
    }
    
    // Return a success response to M-PESA
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error processing M-PESA callback:', error);
    
    // Still return a success response to M-PESA to avoid retries
    return NextResponse.json({ success: true });
  }
}
