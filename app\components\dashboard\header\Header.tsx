'use client';

import React, { useState, useEffect, FormEvent, KeyboardEvent } from 'react';
import Link from "next/link"
import { Search, X } from 'lucide-react'
import LogoComponent from '../../ui/Logo';
import Avatar from '../../ui/Avatar';
import UserDropdown from './UserDropdown';
import Button from "../../ui/inputs/Button"
import { Sheet, SheetContent, SheetTrigger } from "../../ui/Sheet"
import MegaMenu from './menu/megaMenu';
import { getMenuItems, MenuItem } from "./menu/menuData"
import { useRouter, usePathname } from 'next/navigation';

const Header: React.FC = () => {
  const [isUserDropdownOpen, setUserDropdownOpen] = useState(false);
  const [isMobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [menuItemsData, setMenuItemsData] = useState<MenuItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchInput, setShowSearchInput] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const fetchMenuData = async () => {
      const items = await getMenuItems();
      setMenuItemsData(items);
    };
    fetchMenuData();
  }, []);

  const toggleUserDropdown = () => {
    setUserDropdownOpen(!isUserDropdownOpen);
  };

  const toggleSearchInput = () => {
    setShowSearchInput(!showSearchInput);
    if (!showSearchInput) {
      // Focus the search input when it appears
      setTimeout(() => {
        const searchInput = document.getElementById('header-search-input');
        if (searchInput) {
          searchInput.focus();
        }
      }, 100);
    }
  };

  const handleSearch = (e: FormEvent) => {
    e.preventDefault();
    performSearch();
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      performSearch();
    }
  };

  const performSearch = () => {
    if (searchQuery.trim()) {
      // Determine which page to redirect to based on the current path
      if (pathname?.includes('/shop')) {
        // If we're on the shop page, update the query parameter
        router.push(`/shop?query=${encodeURIComponent(searchQuery)}&page=1`);
      } else if (pathname?.includes('/parts')) {
        // If we're on the parts page, update the query parameter
        router.push(`/parts?query=${encodeURIComponent(searchQuery)}&page=1`);
      } else {
        // Check if we're in the dashboard or frontend
        if (pathname?.includes('dashboard')) {
          // If in dashboard, go to parts page
          router.push(`/parts?query=${encodeURIComponent(searchQuery)}&page=1`);
        } else {
          // If in frontend, go to shop page
          router.push(`/shop?query=${encodeURIComponent(searchQuery)}&page=1`);
        }
      }
      // Hide the search input after search is performed
      setShowSearchInput(false);
    }
  };

  const clearSearch = () => {
    setSearchQuery('');
    // Focus back on the input
    const searchInput = document.getElementById('header-search-input');
    if (searchInput) {
      searchInput.focus();
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 p-4">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center">
          <Link href="/" className="mr-6 flex items-center space-x-2">
            <LogoComponent />
          </Link>
          <div className="hidden md:block">
            <MegaMenu items={menuItemsData} />
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {showSearchInput ? (
            <form onSubmit={handleSearch} className="relative hidden md:flex">
              <input
                id="header-search-input"
                type="text"
                placeholder="Search parts..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={handleKeyDown}
                className="w-64 h-10 pl-3 pr-10 rounded-lg border border-gray-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all duration-200"
              />
              {searchQuery && (
                <button
                  type="button"
                  onClick={clearSearch}
                  className="absolute right-12 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-gray-100 text-gray-400 hover:text-gray-600"
                  aria-label="Clear search"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
              <button
                type="submit"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-gray-100 text-gray-500 hover:text-gray-700"
                aria-label="Search"
              >
                <Search className="h-5 w-5" />
              </button>
            </form>
          ) : (
            <Button
              variant="ghost"
              size="icon"
              className="hidden md:block"
              onClick={toggleSearchInput}
            >
              <Search className="h-5 w-5" />
              <span className="sr-only">Search</span>
            </Button>
          )}
          <div className="relative">
            <div onClick={toggleUserDropdown} className="cursor-pointer">
              <Avatar />
            </div>
            {isUserDropdownOpen && <UserDropdown />}
          </div>
          <Sheet open={isMobileMenuOpen} onOpenChange={setMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" className="md:hidden">
                Menu
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <MegaMenu items={menuItemsData} />
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
};

export default Header;