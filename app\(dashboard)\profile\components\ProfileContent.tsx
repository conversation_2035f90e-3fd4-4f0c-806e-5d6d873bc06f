'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/app/hooks/useAuth';
import { createClient } from '@/app/libs/supabase/client';
import ProfileWrapper from './ProfileWrapper';
import Spinner from '@/app/components/ui/Spinner';

export default function ProfileContent() {
  const { user } = useAuth();
  const [profile, setProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const fetchProfile = async () => {
      if (!user) {
        router.push('/login');
        return;
      }

      try {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error fetching profile:', error);
          return;
        }

        if (!data) {
          router.push('/complete-profile');
          return;
        }

        setProfile(data);
      } catch (error) {
        console.error('Error in fetchProfile:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfile();
  }, [user, router]);

  if (isLoading) {
    return (
      <div className="flex min-h-[300px] items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  if (!user || !profile) {
    return null;
  }

  return (
    <div>
      <h1 className="mb-4 text-2xl font-bold">Profile Settings</h1>
      <ProfileWrapper user={user} profile={profile} />
    </div>
  );
}
