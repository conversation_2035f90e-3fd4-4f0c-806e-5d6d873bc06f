// Database verification script
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkDatabase() {
  console.log('🔍 Checking Database Tables and Data...\n');
  
  // Check parts table
  console.log('📦 PARTS TABLE:');
  try {
    const { data: parts, error: partsError, count: partsCount } = await supabase
      .from('parts')
      .select('id, title, is_active', { count: 'exact' })
      .limit(5);
    
    if (partsError) {
      console.log('   ❌ Error:', partsError.message);
    } else {
      console.log(`   ✅ Total parts: ${partsCount || 0}`);
      if (parts && parts.length > 0) {
        console.log('   📋 Sample parts:');
        parts.forEach(part => {
          console.log(`      - ID: ${part.id}, Title: "${part.title}", Active: ${part.is_active}`);
        });
      } else {
        console.log('   📋 No parts found');
      }
    }
  } catch (error) {
    console.log('   ❌ Exception:', error.message);
  }
  
  console.log('\n🏷️ CATEGORIES TABLE:');
  try {
    const { data: categories, error: categoriesError, count: categoriesCount } = await supabase
      .from('categories')
      .select('id, name, is_active', { count: 'exact' })
      .limit(5);
    
    if (categoriesError) {
      console.log('   ❌ Error:', categoriesError.message);
    } else {
      console.log(`   ✅ Total categories: ${categoriesCount || 0}`);
      if (categories && categories.length > 0) {
        console.log('   📋 Sample categories:');
        categories.forEach(cat => {
          console.log(`      - ID: ${cat.id}, Name: "${cat.name}", Active: ${cat.is_active}`);
        });
      } else {
        console.log('   📋 No categories found');
      }
    }
  } catch (error) {
    console.log('   ❌ Exception:', error.message);
  }
  
  console.log('\n🚗 CAR BRANDS TABLE:');
  try {
    const { data: brands, error: brandsError, count: brandsCount } = await supabase
      .from('car_brands')
      .select('brand_id, brand_name', { count: 'exact' })
      .limit(5);
    
    if (brandsError) {
      console.log('   ❌ Error:', brandsError.message);
    } else {
      console.log(`   ✅ Total brands: ${brandsCount || 0}`);
      if (brands && brands.length > 0) {
        console.log('   📋 Sample brands:');
        brands.forEach(brand => {
          console.log(`      - ID: ${brand.brand_id}, Name: "${brand.brand_name}"`);
        });
      } else {
        console.log('   📋 No brands found');
      }
    }
  } catch (error) {
    console.log('   ❌ Exception:', error.message);
  }
  
  console.log('\n🚙 CAR MODELS TABLE:');
  try {
    const { data: models, error: modelsError, count: modelsCount } = await supabase
      .from('car_models')
      .select('id, model_name', { count: 'exact' })
      .limit(5);
    
    if (modelsError) {
      console.log('   ❌ Error:', modelsError.message);
    } else {
      console.log(`   ✅ Total models: ${modelsCount || 0}`);
      if (models && models.length > 0) {
        console.log('   📋 Sample models:');
        models.forEach(model => {
          console.log(`      - ID: ${model.id}, Name: "${model.model_name}"`);
        });
      } else {
        console.log('   📋 No models found');
      }
    }
  } catch (error) {
    console.log('   ❌ Exception:', error.message);
  }
  
  console.log('\n📊 SUMMARY:');
  console.log('===========');
  
  // Check if any tables have data for sitemap generation
  let hasData = false;
  
  try {
    const { count: partsCount } = await supabase
      .from('parts')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true);
    
    const { count: categoriesCount } = await supabase
      .from('categories')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true);
    
    const { count: brandsCount } = await supabase
      .from('car_brands')
      .select('*', { count: 'exact', head: true });
    
    if (partsCount > 0 || categoriesCount > 0 || brandsCount > 0) {
      hasData = true;
      console.log('✅ Database has data for sitemap generation');
      console.log(`   - Active parts: ${partsCount || 0}`);
      console.log(`   - Active categories: ${categoriesCount || 0}`);
      console.log(`   - Brands: ${brandsCount || 0}`);
    } else {
      console.log('❌ Database appears to be empty - no active data found');
      console.log('   This explains why sitemaps are empty');
    }
  } catch (error) {
    console.log('❌ Error checking data summary:', error.message);
  }
  
  console.log('\n🌐 SITEMAP IMPLICATIONS:');
  console.log('========================');
  if (hasData) {
    console.log('✅ Sitemaps should contain data');
    console.log('   If sitemaps are still empty, there may be a code issue');
  } else {
    console.log('ℹ️  Empty sitemaps are expected - database has no active data');
    console.log('   Add some parts, categories, or brands to populate sitemaps');
  }
}

checkDatabase().catch(console.error);
