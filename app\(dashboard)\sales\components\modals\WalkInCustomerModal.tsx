'use client';

import React, { useState, useEffect } from 'react';
import { X, User, Phone, AlertCircle, CheckCircle, UserPlus } from 'lucide-react';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import { 
  checkExistingCustomer, 
  registerWalkInCustomer, 
  searchCustomersByPhone,
  WalkInCustomer 
} from '../../utils/walkInCustomerUtils';

interface WalkInCustomerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCustomerSelected: (customer: WalkInCustomer) => void;
  initialPhone?: string;
}

export default function WalkInCustomerModal({
  isOpen,
  onClose,
  onCustomerSelected,
  initialPhone = ''
}: WalkInCustomerModalProps) {
  const [step, setStep] = useState<'phone' | 'register' | 'existing'>('phone');
  const [phoneNumber, setPhoneNumber] = useState(initialPhone);
  const [customerName, setCustomerName] = useState('');
  const [existingCustomer, setExistingCustomer] = useState<WalkInCustomer | null>(null);
  const [suggestedCustomers, setSuggestedCustomers] = useState<WalkInCustomer[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setStep('phone');
      setPhoneNumber(initialPhone);
      setCustomerName('');
      setExistingCustomer(null);
      setSuggestedCustomers([]);
      setError(null);
    }
  }, [isOpen, initialPhone]);

  // Search for customers as user types phone number
  useEffect(() => {
    const searchCustomers = async () => {
      if (phoneNumber.length >= 3) {
        try {
          const customers = await searchCustomersByPhone(phoneNumber);
          setSuggestedCustomers(customers);
        } catch (error) {
          console.error('Error searching customers:', error);
        }
      } else {
        setSuggestedCustomers([]);
      }
    };

    const timeoutId = setTimeout(searchCustomers, 300);
    return () => clearTimeout(timeoutId);
  }, [phoneNumber]);

  const handlePhoneSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
      // Validate phone number
      if (!phoneNumber.trim()) {
        throw new Error('Phone number is required');
      }

      // Check if customer already exists
      const result = await checkExistingCustomer(phoneNumber);
      
      if (result.exists && result.customer) {
        setExistingCustomer(result.customer);
        setStep('existing');
      } else {
        setStep('register');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to check customer');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegisterCustomer = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    try {
      // Validate inputs
      if (!customerName.trim()) {
        throw new Error('Customer name is required');
      }

      if (!phoneNumber.trim()) {
        throw new Error('Phone number is required');
      }

      // Register the new customer
      const newCustomer = await registerWalkInCustomer(customerName, phoneNumber);
      
      // Select the newly registered customer
      onCustomerSelected(newCustomer);
      onClose();
    } catch (error: any) {
      setError(error.message || 'Failed to register customer');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectExistingCustomer = () => {
    if (existingCustomer) {
      onCustomerSelected(existingCustomer);
      onClose();
    }
  };

  const handleSelectSuggestedCustomer = (customer: WalkInCustomer) => {
    onCustomerSelected(customer);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[10001]">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">
            Walk-in Customer
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Error Message */}
          {error && (
            <div className="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              {error}
            </div>
          )}

          {/* Step 1: Phone Number Entry */}
          {step === 'phone' && (
            <form onSubmit={handlePhoneSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Customer Phone Number
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    placeholder="Enter phone number"
                    className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    disabled={isLoading}
                    autoFocus
                  />
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  We'll check if this customer already exists in our system
                </p>
              </div>

              {/* Suggested Customers */}
              {suggestedCustomers.length > 0 && (
                <div className="border border-gray-200 rounded-md">
                  <div className="px-3 py-2 bg-gray-50 border-b border-gray-200">
                    <p className="text-sm font-medium text-gray-700">Existing Customers</p>
                  </div>
                  <div className="max-h-32 overflow-y-auto">
                    {suggestedCustomers.map((customer) => (
                      <button
                        key={customer.id}
                        type="button"
                        onClick={() => handleSelectSuggestedCustomer(customer)}
                        className="w-full px-3 py-2 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                      >
                        <div className="flex justify-between items-center">
                          <span className="font-medium text-gray-900">{customer.name}</span>
                          <span className="text-sm text-gray-500">{customer.phone_number}</span>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              <button
                type="submit"
                disabled={isLoading || !phoneNumber.trim()}
                className="w-full flex items-center justify-center px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <>
                    <Phone className="h-4 w-4 mr-2" />
                    Check Customer
                  </>
                )}
              </button>
            </form>
          )}

          {/* Step 2: Existing Customer Found */}
          {step === 'existing' && existingCustomer && (
            <div className="space-y-4">
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                  <h4 className="font-medium text-green-900">Customer Found!</h4>
                </div>
                <div className="mt-2">
                  <p className="text-sm text-green-800">
                    <strong>Name:</strong> {existingCustomer.name}
                  </p>
                  <p className="text-sm text-green-800">
                    <strong>Phone:</strong> {existingCustomer.phone_number}
                  </p>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setStep('phone')}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Back
                </button>
                <button
                  onClick={handleSelectExistingCustomer}
                  className="flex-1 px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
                >
                  Select Customer
                </button>
              </div>
            </div>
          )}

          {/* Step 3: Register New Customer */}
          {step === 'register' && (
            <form onSubmit={handleRegisterCustomer} className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div className="flex items-center">
                  <UserPlus className="h-5 w-5 text-blue-600 mr-2" />
                  <h4 className="font-medium text-blue-900">New Customer</h4>
                </div>
                <p className="mt-1 text-sm text-blue-800">
                  This phone number is not in our system. Let's register this customer.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Customer Name *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={customerName}
                    onChange={(e) => setCustomerName(e.target.value)}
                    placeholder="Enter customer name"
                    className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    disabled={isLoading}
                    autoFocus
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    disabled={isLoading}
                  />
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setStep('phone')}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                  disabled={isLoading}
                >
                  Back
                </button>
                <button
                  type="submit"
                  disabled={isLoading || !customerName.trim() || !phoneNumber.trim()}
                  className="flex-1 flex items-center justify-center px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <LoadingSpinner size="sm" />
                  ) : (
                    <>
                      <UserPlus className="h-4 w-4 mr-2" />
                      Register Customer
                    </>
                  )}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
