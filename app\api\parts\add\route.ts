// app/api/parts/add/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { checkApiAuth } from '@/app/utils/apiAuth';

export async function POST(request: NextRequest) {
  // Check authentication
  const { authenticated, user, supabase, errorResponse } = await checkApiAuth(request);
  
  // If not authenticated, return the error response
  if (!authenticated || !user || !supabase) {
    return errorResponse;
  }
  
  try {
    // Parse the request body
    const partData = await request.json();
    
    // Validate the part data
    if (!partData || !partData.title) {
      return NextResponse.json(
        { error: 'Invalid part data. Title is required.' },
        { status: 400 }
      );
    }
    
    // Add the part to the database
    const { data, error } = await supabase
      .from('parts')
      .insert([partData])
      .select()
      .single();
    
    if (error) {
      console.error('Error adding part:', error);
      return NextResponse.json(
        { error: 'Failed to add part', details: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: 'Part added successfully',
      part: data
    });
  } catch (error: any) {
    console.error('Unexpected error in add part API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
