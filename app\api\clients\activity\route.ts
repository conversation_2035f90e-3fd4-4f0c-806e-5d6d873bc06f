import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const { clientId, activityType, description, metadata } = await request.json();

    // --- Input Validation ---
    if (!clientId || typeof clientId !== 'string') {
      return NextResponse.json({ error: 'Client ID is required' }, { status: 400 });
    }

    if (!activityType || typeof activityType !== 'string') {
      return NextResponse.json({ error: 'Activity type is required' }, { status: 400 });
    }

    if (!description || typeof description !== 'string') {
      return NextResponse.json({ error: 'Description is required' }, { status: 400 });
    }

    // --- Create Supabase Client ---
    const supabase = createClient();

    // --- Verify Client Exists ---
    const { data: client, error: clientError } = await supabase
      .from('clients')
      .select('id')
      .eq('id', clientId)
      .single();

    if (clientError) {
      console.error('Error fetching client:', clientError);
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    // --- Record Activity ---
    const { data: activity, error: activityError } = await supabase
      .from('client_activities')
      .insert({
        client_id: clientId,
        activity_type: activityType,
        description,
        metadata: metadata || null,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (activityError) {
      console.error('Error recording client activity:', activityError);
      return NextResponse.json({ error: 'Failed to record client activity' }, { status: 500 });
    }

    // --- Return Success Response ---
    return NextResponse.json({
      success: true,
      message: 'Activity recorded successfully',
      activity
    });

  } catch (error) {
    console.error('Error in client activity process:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get client ID from query parameters
    const { searchParams } = new URL(request.url);
    const clientId = searchParams.get('clientId');
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const offset = (page - 1) * limit;

    // --- Input Validation ---
    if (!clientId) {
      return NextResponse.json({ error: 'Client ID is required' }, { status: 400 });
    }

    // --- Create Supabase Client ---
    const supabase = createClient();

    // --- Verify Client Exists ---
    const { data: client, error: clientError } = await supabase
      .from('clients')
      .select('id')
      .eq('id', clientId)
      .single();

    if (clientError) {
      console.error('Error fetching client:', clientError);
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    // --- Fetch Activities ---
    const { data: activities, error: activitiesError, count } = await supabase
      .from('client_activities')
      .select('*', { count: 'exact' })
      .eq('client_id', clientId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (activitiesError) {
      console.error('Error fetching client activities:', activitiesError);
      return NextResponse.json({ error: 'Failed to fetch client activities' }, { status: 500 });
    }

    // --- Return Success Response ---
    return NextResponse.json({
      success: true,
      activities,
      pagination: {
        total: count,
        page,
        limit,
        pages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Error in client activity process:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
