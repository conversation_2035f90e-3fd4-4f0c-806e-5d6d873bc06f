import { encrypt, decrypt } from './encryption';
import Cookies from 'js-cookie';

const COOKIE_NAME = 'watu';

interface UserData {
  id: string;
  name: string;
  roleId: string;
  roleName: string;
}

export async function setUserCookie(userData: UserData) {
  try {
    // Validate input to ensure we're not storing supabase cookies
    if (!userData || typeof userData !== 'object') {

      return false;
    }



    // First, remove any existing cookies with our name
    await removeUserCookie();

    // Encrypt the cookie name
    const encryptedCookieName = await encrypt(COOKIE_NAME);


    // Create the data object to encrypt
    const dataToEncrypt = JSON.stringify(userData);


    // Encrypt the user data
    const encryptedData = await encrypt(dataToEncrypt);


    // Set the cookie with encrypted name and data
    Cookies.set(encryptedCookieName, encryptedData, {
      expires: 7, // Cookie expires in 7 days
      secure: process.env.NODE_ENV === 'production', // Use secure in production
      sameSite: 'strict',
      path: '/' // Ensure cookie is available on all paths
    });




    return true;
  } catch (error) {

    return false;
  }
}

export async function getUserCookie(): Promise<UserData | null> {
  try {
    // Get all cookies
    const allCookies = Cookies.get();
    console.log('[getUserCookie] Checking cookies:', Object.keys(allCookies).join(', '));

    // Find the encrypted cookie name
    let encryptedCookieName = null;
    let rawWatuCookie = null;

    // First, check if there's a direct 'watu' cookie (unencrypted fallback)
    if (allCookies[COOKIE_NAME]) {
      console.log('[getUserCookie] Found direct watu cookie');
      rawWatuCookie = allCookies[COOKIE_NAME];
    }

    // We need to loop through each cookie and check if it's our cookie
    for (const key of Object.keys(allCookies)) {
      // Skip decryption for any Supabase cookies (prefixed with sb-) or carFilter
      if (key.startsWith('sb-') || key === 'carFilter') {
        continue;
      }

      try {
        const decryptedName = await decrypt(key);

        if (decryptedName === COOKIE_NAME) {
          encryptedCookieName = key;
          console.log('[getUserCookie] Found encrypted watu cookie');
          break;
        }
      } catch (e) {
        // Skip cookies that can't be decrypted
        continue;
      }
    }

    // If we found an encrypted cookie name, use it
    if (encryptedCookieName) {
      // Get the encrypted data
      const encryptedData = Cookies.get(encryptedCookieName);
      if (!encryptedData) {
        console.log('[getUserCookie] No data in encrypted cookie');
        return null;
      }

      try {
        // Decrypt the data
        const decryptedData = await decrypt(encryptedData);

        // Parse the JSON data
        const userData: UserData = JSON.parse(decryptedData);

        console.log('[getUserCookie] User data from encrypted cookie:', userData);
        console.log('[getUserCookie] Role name:', userData.roleName);
        console.log('[getUserCookie] Is Super Admin:', userData.roleName === 'Super Admin');

        return userData;
      } catch (decryptError) {
        console.error('[getUserCookie] Error decrypting cookie data:', decryptError);
        // Fall through to try the raw cookie if available
      }
    }

    // If we have a raw watu cookie or decryption failed, try to parse it directly
    if (rawWatuCookie) {
      try {
        // Try to parse it as JSON directly
        // First, check if it's a valid JSON string
        if (rawWatuCookie &&
            (rawWatuCookie.startsWith('{') || rawWatuCookie.startsWith('['))) {
          const userData: UserData = JSON.parse(rawWatuCookie);
          console.log('[getUserCookie] User data from raw cookie:', userData);

          // Validate the data has the expected structure
          if (userData && userData.id && userData.roleId) {
            return userData;
          }
        } else {
          console.log('[getUserCookie] Raw cookie is not a valid JSON string');
        }
      } catch (parseError) {
        console.error('[getUserCookie] Error parsing raw cookie:', parseError);
      }
    }

    console.log('[getUserCookie] No valid cookie found');
    return null;
  } catch (error) {
    console.error('[getUserCookie] Unexpected error:', error);
    return null;
  }
}

export async function removeUserCookie() {
  try {
    // Get all cookies
    const allCookies = Cookies.get();


    // Find all encrypted cookie names that match our cookie name
    const cookiesToRemove: string[] = [];

    // We need to loop through each cookie and check if it's our cookie
    for (const key of Object.keys(allCookies)) {
      // Skip decryption for any Supabase cookies (prefixed with sb-) or carFilter
      if (key.startsWith('sb-') || key === 'carFilter') {
        continue;
      }

      try {
        const decryptedName = await decrypt(key);
        if (decryptedName === COOKIE_NAME) {
          cookiesToRemove.push(key);
        }
      } catch (e) {
        // Skip cookies that can't be decrypted
        continue;
      }
    }

    if (cookiesToRemove.length > 0) {
      // Remove all cookies with explicit path
      for (const cookieName of cookiesToRemove) {
        Cookies.remove(cookieName, { path: '/' });

      }
      return true;
    }

    return false;
  } catch (error) {

    return false;
  }
}