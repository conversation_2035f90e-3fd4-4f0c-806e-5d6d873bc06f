import { useState, useCallback } from 'react';
import { createClient } from '@/app/libs/supabase/client';

export interface CompatibleVehicle {
  id: number;
  variation_trim_id: number;
  brand: string;
  model: string;
  generation: string;
  variation: string;
  trim: string;
  years: string;
}

// Helper function to remove duplicate vehicles
const removeDuplicateVehicles = (vehicles: CompatibleVehicle[]): CompatibleVehicle[] => {
  return vehicles.filter((vehicle, index, self) =>
    index === self.findIndex(v =>
      v.brand === vehicle.brand &&
      v.model === vehicle.model &&
      v.generation === vehicle.generation &&
      v.variation === vehicle.variation &&
      v.trim === vehicle.trim
    )
  );
};

export const useCompatibleVehicles = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClient();

  const fetchCompatibleVehicles = useCallback(async (partId: string | number): Promise<CompatibleVehicle[]> => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🚗 Fetching compatible vehicles for part:', partId);

      // First, verify the part exists
      const { data: partExists, error: partExistsError } = await supabase
        .from('parts')
        .select('id, title')
        .eq('id', parseInt(partId.toString()))
        .single();

      console.log('🔍 Part existence check:', { partExists, partExistsError });

      if (partExistsError || !partExists) {
        console.error('🚨 Part not found in database:', partExistsError);
        throw new Error(`Part with ID ${partId} not found`);
      }

      console.log(`✅ Part found: ${partExists.title}`);

      // Get parts_car relationships
      console.log('🔍 Querying parts_car table for part_id:', partId);
      const { data: partCars, error: partCarsError } = await supabase
        .from('parts_car')
        .select('variation_trim_id')
        .eq('part_id', parseInt(partId.toString()));

      console.log('🔍 Parts_car query result:', { partCars, partCarsError });

      if (partCarsError) {
        console.error('🚨 Parts_car query error:', partCarsError);
        throw new Error(`Failed to fetch part-car relationships: ${partCarsError.message}`);
      }

      if (!partCars || partCars.length === 0) {
        console.log('🚗 No compatible vehicles found for this part in parts_car table');

        // Let's check if there are ANY records in parts_car table
        const { data: anyPartCars, error: anyPartCarsError } = await supabase
          .from('parts_car')
          .select('part_id, variation_trim_id')
          .limit(5);

        console.log('🔍 Sample parts_car records:', { anyPartCars, anyPartCarsError });

        // Let's also check if this part has records in a different relationship table
        const { data: alternativeRecords, error: altError } = await supabase
          .from('parts')
          .select('id, title')
          .ilike('title', '%7L6199207%')
          .limit(5);

        console.log('🔍 Parts with similar part number:', { alternativeRecords, altError });

        console.log('🔍 This could mean:');
        console.log('   - Part has no compatible vehicles assigned');
        console.log('   - Part ID is incorrect');
        console.log('   - Data is in a different table structure');
        console.log('   - Relationship table has different name');
        return [];
      }

      console.log(`🚗 Found ${partCars.length} part-car relationships:`, partCars);

      // Get unique variation_trim_ids
      const variationTrimIds = Array.from(new Set(partCars.map(pc => pc.variation_trim_id)));
      console.log('🚗 Unique variation trim IDs:', variationTrimIds);

      // Use a more efficient approach with a single query using SQL
      const { data: vehicleDetails, error: vehicleError } = await supabase
        .rpc('get_compatible_vehicles_for_part', {
          part_id_param: parseInt(partId.toString())
        });

      if (vehicleError) {
        console.warn('🚗 RPC function not available, falling back to manual queries');

        // Fallback: Fetch vehicle details manually using simpler approach
        console.log('🔍 Fetching vehicle details for trim IDs:', variationTrimIds);

        const vehiclePromises = variationTrimIds.map(async (trimId) => {
          try {
            console.log(`🔍 Processing trim ID: ${trimId}`);

            // Step 1: Get variation_trim
            const { data: trimData, error: trimError } = await supabase
              .from('variation_trim')
              .select('id, trim, variation_id')
              .eq('id', trimId)
              .single();

            console.log(`🔍 Trim data for ${trimId}:`, { trimData, trimError });

            if (trimError || !trimData) {
              console.warn(`🚗 Failed to fetch trim data for ID ${trimId}:`, trimError);
              return null;
            }

            // Step 2: Get car_variation
            const { data: variationData, error: variationError } = await supabase
              .from('car_variation')
              .select('id, variation, generation_id')
              .eq('id', trimData.variation_id)
              .single();

            console.log(`🔍 Variation data for ${trimData.variation_id}:`, { variationData, variationError });

            if (variationError || !variationData) {
              console.warn(`🚗 Failed to fetch variation data for ID ${trimData.variation_id}:`, variationError);
              return null;
            }

            // Step 3: Get car_generation
            const { data: generationData, error: generationError } = await supabase
              .from('car_generation')
              .select('id, name, model_id')
              .eq('id', variationData.generation_id)
              .single();

            console.log(`🔍 Generation data for ${variationData.generation_id}:`, { generationData, generationError });

            if (generationError || !generationData) {
              console.warn(`🚗 Failed to fetch generation data for ID ${variationData.generation_id}:`, generationError);
              return null;
            }

            // Step 4: Get car_models
            const { data: modelData, error: modelError } = await supabase
              .from('car_models')
              .select('id, model_name, brand_id')
              .eq('id', generationData.model_id)
              .single();

            console.log(`🔍 Model data for ${generationData.model_id}:`, { modelData, modelError });

            if (modelError || !modelData) {
              console.warn(`🚗 Failed to fetch model data for ID ${generationData.model_id}:`, modelError);
              return null;
            }

            // Step 5: Get car_brands
            const { data: brandData, error: brandError } = await supabase
              .from('car_brands')
              .select('brand_id, brand_name')
              .eq('brand_id', modelData.brand_id)
              .single();

            console.log(`🔍 Brand data for ${modelData.brand_id}:`, { brandData, brandError });

            if (brandError || !brandData) {
              console.warn(`🚗 Failed to fetch brand data for ID ${modelData.brand_id}:`, brandError);
              return null;
            }

            const vehicle: CompatibleVehicle = {
              id: trimId,
              variation_trim_id: trimId,
              brand: brandData.brand_name,
              model: modelData.model_name,
              generation: generationData.name,
              variation: variationData.variation, // Keep for data completeness
              trim: trimData.trim,
              years: ''
            };

            console.log(`✅ Successfully processed vehicle:`, vehicle);
            return vehicle;

          } catch (err) {
            console.error(`🚗 Error processing vehicle for trim ID ${trimId}:`, err);
            return null;
          }
        });

        // Wait for all vehicle data to be fetched
        const vehicleResults = await Promise.all(vehiclePromises);
        const validVehicles = vehicleResults.filter((v): v is CompatibleVehicle => v !== null);

        return removeDuplicateVehicles(validVehicles);
      }

      // If RPC function worked, process the results
      if (vehicleDetails && vehicleDetails.length > 0) {
        const vehicles: CompatibleVehicle[] = vehicleDetails.map((row: any) => ({
          id: row.variation_trim_id,
          variation_trim_id: row.variation_trim_id,
          brand: row.brand_name,
          model: row.model_name,
          generation: row.generation_name,
          variation: row.variation_name,
          trim: row.trim_name,
          years: row.years || ''
        }));

        return removeDuplicateVehicles(vehicles);
      }

      console.log(`🚗 Successfully fetched compatible vehicles`);
      return [];

    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch compatible vehicles';
      console.error('🚗 Error fetching compatible vehicles:', err);
      setError(errorMessage);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [supabase]);

  return {
    fetchCompatibleVehicles,
    isLoading,
    error
  };
};
