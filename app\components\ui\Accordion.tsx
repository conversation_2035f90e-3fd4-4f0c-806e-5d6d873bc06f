"use client";

import { ChevronDown } from "lucide-react";
import {
  useState,
  createContext,
  useContext,
  useRef,
  useEffect,
  useCallback,
} from "react";
import { motion, AnimatePresence } from "framer-motion";

const AccordionItemContext = createContext<{
  isOpen: boolean;
  toggle: () => void;
} | null>(null);

// Accordion Container
export function Accordion({ children }: { children: React.ReactNode }) {
  return <div className="w-full space-y-2">{children}</div>;
}

// Accordion Item
export function AccordionItem({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  const [isOpen, setIsOpen] = useState(false);
  const toggle = useCallback(() => setIsOpen((prev) => !prev), []);

  return (
    <AccordionItemContext.Provider value={{ isOpen, toggle }}>
      <div className={`border rounded-lg overflow-hidden ${className}`}>
        {children}
      </div>
    </AccordionItemContext.Provider>
  );
}

// Accordion Trigger
export function AccordionTrigger({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  const { isOpen, toggle } = useContext(AccordionItemContext)!;

  return (
    <button
      onClick={toggle}
      className={`flex items-center justify-between w-full p-4 text-left bg-white hover:bg-gray-50 transition-colors ${className}`}
    >
      {children}
      <motion.div
        animate={{ rotate: isOpen ? 180 : 0 }}
        transition={{ duration: 0.3 }}
      >
        <ChevronDown className="w-5 h-5 text-gray-600" />
      </motion.div>
    </button>
  );
}

// Accordion Content
export function AccordionContent({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  const { isOpen } = useContext(AccordionItemContext)!;
  const contentRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState(0);

  useEffect(() => {
    if (contentRef.current) {
      setHeight(contentRef.current.scrollHeight);
    }
  }, [isOpen]);

  return (
    <AnimatePresence initial={false}>
      <motion.div
        initial={false}
        animate={isOpen ? "open" : "collapsed"}
        variants={{
          open: { 
            height: height,
            opacity: 1,
            transition: { 
              height: { duration: 0.3, ease: "easeInOut" },
              opacity: { duration: 0.2, delay: 0.1 }
            }
          },
          collapsed: { 
            height: 0,
            opacity: 0,
            transition: { 
              height: { duration: 0.3, ease: "easeInOut" },
              opacity: { duration: 0.2 }
            }
          }
        }}
        className={`overflow-hidden ${className}`}
      >
        <div ref={contentRef} className="p-4 pt-0 text-gray-600">
          {children}
        </div>
      </motion.div>
    </AnimatePresence>
  );
}