'use client';

import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Plus, Minus } from 'lucide-react';
import { Control, Controller } from 'react-hook-form';

const hideArrowsStyle = `
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  input[type=number] {
    -moz-appearance: textfield;
  }
`;

interface NumberInputProps {
  name: string;
  control?: Control<any>;
  label?: string;
  errorMessage?: string;
  className?: string;
  max?: number;
  placeholder?: string;
  rules?: Record<string, any>;
  defaultValue?: number;
}

const NumberInput: React.FC<NumberInputProps> = ({
  name,
  control,
  label,
  errorMessage,
  className,
  max,
  rules,
  defaultValue
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const labelVariants = {
    focused: {
      y: -5,
      scale: 0.8,
      transition: { type: 'spring', stiffness: 100, damping: 15 }
    },
    idle: {
      y: 0,
      scale: 1,
      transition: { type: 'spring', stiffness: 100, damping: 15 }
    }
  };

  const formatNumber = (value: string): string => {
    const numericValue = value.replace(/[^0-9]/g, '');
    if (!numericValue) return '';
    const number = parseInt(numericValue, 10);
    return number.toLocaleString('en-US');
  };

  const parseNumber = (value: string): number => {
    return parseInt(value.replace(/[^0-9]/g, ''), 10) || 0;
  };

  if (control) {
    return (
      <Controller
        control={control}
        name={name}
        rules={rules}
        defaultValue={defaultValue}
        render={({ field, fieldState }) => (
          <div className={`relative w-full ${className}`}>
            <style>{hideArrowsStyle}</style>
            <motion.div
              className={`
                relative rounded-md border transition-colors duration-200 px-4 py-0
                ${errorMessage || fieldState.error
                  ? 'border-red-500 dark:border-red-400'
                  : isFocused
                  ? 'border-blue-500 dark:border-blue-500'
                  : 'border-gray-300 dark:border-gray-600'
                }
                ${errorMessage || fieldState.error ? 'dark:bg-red-100' : 'dark:bg-gray-700'}
              `}
            >
              <motion.label
                initial={false}
                animate="focused"
                variants={labelVariants}
                className="pointer-events-none absolute top-2 left-4 text-gray-500 text-xs origin-top-left"
                htmlFor={name}
              >
                {label}
              </motion.label>
              <div className="relative flex items-center">
                <input
                  {...field}
                  type="text"
                  id={name}
                  className="peer block w-full appearance-none bg-transparent py-2 pt-3 pr-10 text-gray-900 placeholder-transparent focus:outline-none focus:ring-0 dark:text-white"
                  placeholder=" "
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => setIsFocused(false)}
                  ref={inputRef}
                  value={field.value ? formatNumber(field.value.toString()) : ''}
                  onChange={(e) => {
                    const formattedValue = formatNumber(e.target.value);
                    const numericValue = parseNumber(e.target.value);
                    const limitedValue = max !== undefined ? Math.min(numericValue, max) : numericValue;
                    field.onChange(limitedValue);
                  }}
                />
                <div className="absolute right-0 top-1/2 -translate-y-1/2 flex flex-col">
                  <button
                    type="button"
                    className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                    onClick={() => {
                      const currentValue = parseNumber(field.value?.toString() || '0');
                      const newValue = max !== undefined ? Math.min(currentValue + 1, max) : currentValue + 1;
                      field.onChange(newValue);
                    }}
                  >
                    <Plus size={14} />
                  </button>
                  <button
                    type="button"
                    className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                    onClick={() => {
                      const currentValue = parseNumber(field.value?.toString() || '0');
                      field.onChange(Math.max(currentValue - 1, 0));
                    }}
                  >
                    <Minus size={14} />
                  </button>
                </div>
              </div>
            </motion.div>
            {(errorMessage || fieldState.error) && (
              <p className="mt-1 text-sm text-red-500">{errorMessage || fieldState.error?.message}</p>
            )}
          </div>
        )}
      />
    );
  }

  return (
    <div className={`relative w-full ${className}`}>
      <style>{hideArrowsStyle}</style>
      <div className="relative rounded-md border border-gray-300 px-4 py-0 dark:border-gray-600">
        <label className="pointer-events-none absolute top-2 left-4 text-gray-500 text-xs">
          {label}
        </label>
        <div className="relative flex items-center">
          <input
            type="number"
            className="peer block w-full appearance-none bg-transparent py-2 pt-3 pr-10 text-gray-900 placeholder-transparent focus:outline-none focus:ring-0 dark:text-white"
            placeholder=" "
            min={0}
            max={max}
          />
          <div className="absolute right-0 top-1/2 -translate-y-1/2 flex flex-col">
            <button
              type="button"
              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              <Plus size={14} />
            </button>
            <button
              type="button"
              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              <Minus size={14} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NumberInput;