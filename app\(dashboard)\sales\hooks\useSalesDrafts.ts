import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { toast } from 'react-hot-toast';

export interface SalesDraft {
  id: string;
  draft_name: string;
  current_step: number;
  form_data: any;
  selected_products: any[];
  created_at: string;
  updated_at: string;
  expires_at: string;
}

export interface SalesDraftData {
  draftName?: string;
  currentStep: number;
  formData: any;
  selectedProducts: any[];
}

export const useSalesDrafts = () => {
  const [drafts, setDrafts] = useState<SalesDraft[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentDraftId, setCurrentDraftId] = useState<string | null>(null);

  const supabase = createClient();

  // Load all drafts for the current user
  const loadDrafts = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase.rpc('get_sales_drafts');

      if (error) {
        console.error('Error loading drafts:', error);
        setError(error.message);
        return;
      }

      setDrafts(data || []);
    } catch (err: any) {
      console.error('Error loading drafts:', err);
      setError(err.message || 'Failed to load drafts');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save or update a draft
  const saveDraft = useCallback(async (draftData: SalesDraftData, draftId?: string) => {
    try {
      const { data, error } = await supabase.rpc('save_sales_draft', {
        p_draft_id: draftId || null,
        p_draft_name: draftData.draftName,
        p_current_step: draftData.currentStep,
        p_form_data: draftData.formData,
        p_selected_products: draftData.selectedProducts
      });

      if (error) {
        console.error('Error saving draft:', error);
        throw new Error(error.message);
      }

      // Update current draft ID if this is a new draft
      if (!draftId) {
        setCurrentDraftId(data);
      }

      // Reload drafts to get updated list
      await loadDrafts();

      return data;
    } catch (err: any) {
      console.error('Error saving draft:', err);
      toast.error(err.message || 'Failed to save draft');
      throw err;
    }
  }, [loadDrafts]);

  // Auto-save draft (debounced)
  const autoSaveDraft = useCallback(async (draftData: SalesDraftData, draftId?: string) => {
    try {
      const { data, error } = await supabase.rpc('save_sales_draft', {
        p_draft_id: draftId || currentDraftId,
        p_draft_name: draftData.draftName,
        p_current_step: draftData.currentStep,
        p_form_data: draftData.formData,
        p_selected_products: draftData.selectedProducts
      });

      if (error) {
        console.error('Error auto-saving draft:', error);
        return null;
      }

      // Update current draft ID if this is a new draft
      if (!draftId && !currentDraftId) {
        setCurrentDraftId(data);
      }

      return data;
    } catch (err: any) {
      console.error('Error auto-saving draft:', err);
      return null;
    }
  }, [currentDraftId]);

  // Delete a draft
  const deleteDraft = useCallback(async (draftId: string) => {
    try {
      const { data, error } = await supabase.rpc('delete_sales_draft', {
        p_draft_id: draftId
      });

      if (error) {
        console.error('Error deleting draft:', error);
        throw new Error(error.message);
      }

      if (data) {
        toast.success('Draft deleted successfully');
        // Clear current draft ID if we deleted the current draft
        if (currentDraftId === draftId) {
          setCurrentDraftId(null);
        }
        // Reload drafts
        await loadDrafts();
      } else {
        throw new Error('Draft not found or could not be deleted');
      }

      return data;
    } catch (err: any) {
      console.error('Error deleting draft:', err);
      toast.error(err.message || 'Failed to delete draft');
      throw err;
    }
  }, [currentDraftId, loadDrafts]);

  // Load a specific draft
  const loadDraft = useCallback((draftId: string): SalesDraft | null => {
    const draft = drafts.find(d => d.id === draftId);
    if (draft) {
      setCurrentDraftId(draftId);
    }
    return draft || null;
  }, [drafts]);

  // Clear current draft
  const clearCurrentDraft = useCallback(() => {
    setCurrentDraftId(null);
  }, []);

  // Load drafts on mount
  useEffect(() => {
    loadDrafts();
  }, [loadDrafts]);

  return {
    drafts,
    isLoading,
    error,
    currentDraftId,
    loadDrafts,
    saveDraft,
    autoSaveDraft,
    deleteDraft,
    loadDraft,
    clearCurrentDraft,
    setCurrentDraftId
  };
};
