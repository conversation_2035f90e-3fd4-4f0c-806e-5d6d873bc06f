// app/(dashboard)/profile/components/ProfileWrapper.tsx
'use client';

import { useState, useEffect } from 'react';
import ProfileUpdateModal from './ProfileUpdateModal';
import UpdateProfileDetailsForm from './forms/UpdateProfileDetailsForm';
import ProfileData from './ProfileData';
import { MissingProfileDetails, UserProfile } from '@/app/types/profile';
import { User } from '@/app/types/authTypes';

interface ProfileWrapperProps {
  user: User;
  profile: UserProfile | null;
}

export default function ProfileWrapper({ user, profile }: ProfileWrapperProps) {
  const [missingDetails, setMissingDetails] = useState<MissingProfileDetails>({
    phone: false,
    full_name: false,
  });

  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  useEffect(() => {
    if (profile) {
      setMissingDetails({
        phone: !profile.phone,
        full_name: !profile.full_name,
      });
      setIsModalOpen(!profile.phone || !profile.full_name);
    }
  }, [profile]);

  const handleProfileUpdate = (updatedProfile: Partial<UserProfile>) => {
    setMissingDetails((prevMissingDetails) => ({
      phone:
        updatedProfile.phone === undefined
          ? prevMissingDetails.phone
          : !updatedProfile.phone,
      full_name:
        updatedProfile.full_name === undefined
          ? prevMissingDetails.full_name
          : !updatedProfile.full_name,
    }));

    setIsModalOpen(
      (updatedProfile.phone === undefined ||
        updatedProfile.phone === null ||
        updatedProfile.phone === '') ||
        (updatedProfile.full_name === undefined ||
          updatedProfile.full_name === null ||
          updatedProfile.full_name === '')
    );
  };

  return (
    <div>
      <ProfileData user={user} onProfileUpdate={handleProfileUpdate} />

      <ProfileUpdateModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        missingDetails={missingDetails}
        onProfileUpdate={handleProfileUpdate}
      >
        <UpdateProfileDetailsForm
          missingDetails={missingDetails}
          onUpdate={handleProfileUpdate}
        />
      </ProfileUpdateModal>
    </div>
  );
}