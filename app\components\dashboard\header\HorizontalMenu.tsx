'use client';

import React from 'react';

interface MenuItem {
  label: string;
  href: string;
  children?: MenuItem[];
}

const HorizontalMegaMenu: React.FC = () => {
  const menuItems: MenuItem[] = [
    { label: 'HOME', href: '/', children: [
      { label: 'Business 1', href: '/business1' },
      { label: 'Business 2', href: '/business2' },
      { label: 'Business 3', href: '/business3' },
      { label: 'Agency 1', href: '/agency1' },
      { label: 'Agency 2', href: '/agency2' },
      { label: 'Creative Light', href: '/creative-light' },
      { label: 'Creative Dark', href: '/creative-dark' },
      { label: 'Personal Portfolio', href: '/personal-portfolio' },
    ]},
    { label: 'LEVEL 1', href: '/level1', children: [
      { label: 'Vimeo Video', href: '/vimeo' },
      { label: 'YouTube Video', href: '/youtube' },
      { label: 'HTML5 Local Video', href: '/html5-local' },
      { label: 'Agency', href: '/agency' },
      { label: 'App Showcase', href: '/app-showcase' },
      { label: 'Creative', href: '/creative' },
      { label: 'Business', href: '/business' },
      { label: 'Education', href: '/education' },
    ]},
    { label: 'PAGES', href: '/pages', children: [
      { label: 'Fitness & Gym', href: '/fitness' },
      { label: 'Gardening', href: '/gardening' },
      { label: 'Hosting', href: '/hosting' },
      { label: 'HTML5 Video', href: '/html5-video' },
      { label: 'Medical', href: '/medical' },
      { label: 'Minimal Portfolio', href: '/minimal-portfolio' },
      { label: 'Photography', href: '/photography' },
      { label: 'Product Showcase', href: '/product-showcase' },
    ]},
    { label: 'PORTFOLIO', href: '/portfolio', children: [
      { label: 'Restaurant', href: '/restaurant' },
      { label: 'Resume & CV', href: '/resume' },
      { label: 'Service Landing', href: '/service-landing' },
      { label: 'Slideshows', href: '/slideshows' },
      { label: 'Spa & Beauty', href: '/spa' },
      { label: 'Startup', href: '/startup' },
      { label: 'Vimeo Video', href: '/vimeo-video-2' },
      { label: 'YouTube Video', href: '/youtube-video-2' },
    ]},
    { label: 'BLOG', href: '/blog' },
    { label: 'SHORTCODES NEW', href: '/shortcodes' },
    { label: 'ONE PAGE', href: '/one-page' },
  ];

  const renderMenuItems = (items: MenuItem[]) => {
    return (
      <ul className="absolute left-0 mt-2 w-96 bg-white border border-gray-200 divide-y divide-gray-100 rounded-md shadow-lg ring-1 ring-gray-900/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none group-hover:pointer-events-auto z-10">
        {items.map((item, index) => (
          <li key={index} className="px-4 py-2 hover:bg-gray-100">
            <a href={item.href} className="block">
              {item.label}
            </a>
          </li>
        ))}
      </ul>
    );
  };

  return (
    <nav className="bg-white text-gray-600 p-4">
      <div className="container mx-auto flex items-center justify-between">
        <a href="/" className="text-lg font-bold">LOGO</a>
        <ul className="flex space-x-8">
          {menuItems.map((item, index) => (
            <li key={index} className="relative group">
              <a href={item.href} className="hover:text-gray-900 py-2 px-3 font-medium">
                {item.label}
              </a>
              {item.children && (
                <div className="absolute left-0 top-full w-full bg-white border-b border-gray-200 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none group-hover:pointer-events-auto z-10">
                  <div className='container mx-auto flex'>
                  {renderMenuItems(item.children)}
                  </div>
                  
                </div>
              )}
            </li>
          ))}
        </ul>
        <div className='flex items-center'>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
          <path strokeLinecap="round" strokeLinejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
          </svg>
        </div>
      </div>
    </nav>
  );
};

export default HorizontalMegaMenu;