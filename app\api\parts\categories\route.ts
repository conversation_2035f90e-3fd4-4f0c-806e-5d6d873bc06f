// app/api/categories/route.ts
import { NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';

export async function GET() {
  const supabase = createClient();

  try {
    const { data: categories, error } = await supabase
      .from('categories')
      .select('id, name')
      .order('name', { ascending: true });

    if (error) throw error;

    return NextResponse.json(categories);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch categories';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}