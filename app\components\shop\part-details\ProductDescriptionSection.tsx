'use client';

import React, { useState } from 'react';
// import type { PartDetails } from '@/app/shop/[param]/page';
type PartDetails = any;
import { ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/app/components/ui/Button';

interface ProductDescriptionSectionProps {
  partDetails: PartDetails | null;
}

const ProductDescriptionSection: React.FC<ProductDescriptionSectionProps> = ({ partDetails }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!partDetails) {
    return null;
  }

  const description = partDetails.description || '';
  // Show a snippet if the description is long and not expanded
  const snippetLength = 250; // characters
  const needsTruncation = description.length > snippetLength;
  const displayDescription = isExpanded || !needsTruncation ? description : `${description.substring(0, snippetLength)}...`;

  // If there's no description, show a default message
  if (!description || description.trim() === 'No description available for this part.') {
    return (
      <div id="product-description" className="mt-4 py-4 border-t border-gray-200">
        <h3 className="text-xl font-semibold text-gray-800 mb-4">Product Description</h3>
        <div className="bg-gray-50 p-4 rounded-lg shadow-sm mb-4">
          <p className="text-gray-500 italic">No detailed description available for this part. Please contact us for more information.</p>
        </div>

        {/* Additional information section */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div className="bg-gray-50 p-4 rounded-lg shadow-sm">
            <h4 className="text-base font-semibold text-gray-800 mb-2">Installation Tips</h4>
            <ul className="list-disc pl-5 space-y-1 text-gray-700 text-sm">
              <li>Always refer to your vehicle's service manual for specific instructions</li>
              <li>Ensure the vehicle is safely supported before working underneath</li>
              <li>Compare the new part with the old one before installation</li>
              <li>Use proper tools to avoid damage to the part or vehicle</li>
            </ul>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg shadow-sm">
            <h4 className="text-base font-semibold text-gray-800 mb-2">Quality Assurance</h4>
            <p className="text-gray-700 mb-2 text-sm">
              All our parts undergo rigorous quality control to ensure they meet or exceed OEM specifications.
            </p>
            <p className="text-gray-700 text-sm">
              Each part is inspected for proper fit, function, and finish before shipping.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div id="product-description" className="mt-4 py-4 border-t border-gray-200">
      <h3 className="text-xl font-semibold text-gray-800 mb-4">Product Description</h3>
      <div className="bg-gray-50 p-5 rounded-lg shadow-sm mb-4 border border-gray-200">
        <div className="prose prose-sm sm:prose max-w-none text-gray-700 leading-relaxed whitespace-pre-wrap">
          {displayDescription}
        </div>
        {needsTruncation && (
          <Button
            variant="link"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-blue-600 hover:text-blue-700 mt-3 px-0 text-sm font-medium flex items-center"
          >
            {isExpanded ? 'Read Less' : 'Read More'}
            {isExpanded ? <ChevronUp size={18} className="ml-1" /> : <ChevronDown size={18} className="ml-1" />}
          </Button>
        )}
      </div>

      {/* Additional information section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <div className="bg-gray-50 p-4 rounded-lg shadow-sm">
          <h4 className="text-base font-semibold text-gray-800 mb-2">Installation Tips</h4>
          <ul className="list-disc pl-5 space-y-1 text-gray-700 text-sm">
            <li>Always refer to your vehicle's service manual for specific instructions</li>
            <li>Ensure the vehicle is safely supported before working underneath</li>
            <li>Compare the new part with the old one before installation</li>
            <li>Use proper tools to avoid damage to the part or vehicle</li>
          </ul>
        </div>
        <div className="bg-gray-50 p-4 rounded-lg shadow-sm">
          <h4 className="text-base font-semibold text-gray-800 mb-2">Quality Assurance</h4>
          <p className="text-gray-700 mb-2 text-sm">
            All our parts undergo rigorous quality control to ensure they meet or exceed OEM specifications.
          </p>
          <p className="text-gray-700 text-sm">
            Each part is inspected for proper fit, function, and finish before shipping.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProductDescriptionSection;
