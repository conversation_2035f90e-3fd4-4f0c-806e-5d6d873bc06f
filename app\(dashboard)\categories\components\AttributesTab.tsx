'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { CategoryAttribute, AttributeOption, AttributeWithOptions } from '../types';
import { Plus, Edit, Trash2, Settings, AlertTriangle, Save, X } from 'lucide-react';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import AttributeForm from './AttributeForm';

interface AttributesTabProps {
  categoryId: number;
  onRefresh?: (updatedCategory?: any) => void;
}

const AttributesTab: React.FC<AttributesTabProps> = ({ categoryId, onRefresh }) => {
  const [attributes, setAttributes] = useState<AttributeWithOptions[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAddingAttribute, setIsAddingAttribute] = useState(false);
  const [editingAttributeId, setEditingAttributeId] = useState<number | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const supabase = createClient();

  // Fetch attributes and their options
  useEffect(() => {
    const fetchAttributes = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch attributes for this category
        const { data: attributesData, error: attributesError } = await supabase
          .from('parts_category_attributes')
          .select('*')
          .eq('category_id', categoryId)
          .order('id');

        if (attributesError) throw attributesError;

        // Initialize attributes with empty options arrays
        const attributesWithOptions: AttributeWithOptions[] = (attributesData || []).map(attr => ({
          ...attr,
          options: []
        }));

        // Fetch options for attributes that need them
        const attributesNeedingOptions = attributesWithOptions.filter(
          attr => ['radio', 'dropdown', 'checkbox'].includes(attr.input_type)
        );

        if (attributesNeedingOptions.length > 0) {
          const attributeIds = attributesNeedingOptions.map(attr => attr.id);

          const { data: optionsData, error: optionsError } = await supabase
            .from('parts_category_attribute_input_option')
            .select('*')
            .in('attribute_id', attributeIds)
            .order('id');

          if (optionsError) throw optionsError;

          // Add options to their respective attributes
          if (optionsData) {
            optionsData.forEach(option => {
              const attributeIndex = attributesWithOptions.findIndex(attr => attr.id === option.attribute_id);
              if (attributeIndex !== -1) {
                attributesWithOptions[attributeIndex].options.push(option);
              }
            });
          }
        }

        setAttributes(attributesWithOptions);
      } catch (err: any) {
        console.error('Error fetching attributes:', err);
        setError(err.message || 'Failed to fetch attributes');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAttributes();
  }, [categoryId, supabase, refreshTrigger]);

  // Handle attribute refresh
  const handleRefresh = async () => {
    setRefreshTrigger(prev => prev + 1);

    // Fetch the updated category data to pass back to the parent
    if (onRefresh) {
      try {
        const { data, error } = await supabase
          .from('car_part_categories')
          .select('*')
          .eq('id', categoryId)
          .single();

        if (error) throw error;

        // Pass the updated category data to the parent
        onRefresh(data);
      } catch (err) {
        console.error('Error fetching updated category:', err);
        // Still call onRefresh even if there's an error
        onRefresh();
      }
    }
  };

  // Delete attribute
  const handleDeleteAttribute = async (attributeId: number) => {
    if (!confirm('Are you sure you want to delete this attribute? This will also delete all options and values associated with it.')) {
      return;
    }

    try {
      // First delete all options for this attribute
      const { error: optionsError } = await supabase
        .from('parts_category_attribute_input_option')
        .delete()
        .eq('attribute_id', attributeId);

      if (optionsError) throw optionsError;

      // Then delete the attribute itself
      const { error: attributeError } = await supabase
        .from('parts_category_attributes')
        .delete()
        .eq('id', attributeId);

      if (attributeError) throw attributeError;

      // Refresh the list
      handleRefresh();
    } catch (err: any) {
      console.error('Error deleting attribute:', err);
      alert(`Failed to delete attribute: ${err.message}`);
    }
  };

  // Render attribute type badge
  const renderAttributeTypeBadge = (type: string) => {
    const typeColors: Record<string, string> = {
      text: 'bg-blue-100 text-blue-800',
      radio: 'bg-green-100 text-green-800',
      dropdown: 'bg-purple-100 text-purple-800',
      checkbox: 'bg-orange-100 text-orange-800',
      number: 'bg-teal-100 text-teal-800',
      date: 'bg-pink-100 text-pink-800'
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${typeColors[type] || 'bg-gray-100 text-gray-800'}`}>
        {type}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Category Attributes</h3>
        <button
          onClick={() => setIsAddingAttribute(true)}
          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Attribute
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center py-8">
          <LoadingSpinner size={40} />
        </div>
      ) : attributes.length === 0 ? (
        <div className="bg-gray-50 rounded-lg p-6 text-center">
          <Settings className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No attributes</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating a new attribute for this category.
          </p>
          <div className="mt-6">
            <button
              type="button"
              onClick={() => setIsAddingAttribute(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
            >
              <Plus className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
              Add Attribute
            </button>
          </div>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {attributes.map((attribute) => (
              <li key={attribute.id} className="px-4 py-4 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Settings className="h-6 w-6 text-gray-400" />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{attribute.attribute}</div>
                      <div className="text-sm text-gray-500">ID: {attribute.id}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {renderAttributeTypeBadge(attribute.input_type)}
                    <button
                      onClick={() => setEditingAttributeId(attribute.id || 0)}
                      className="p-1 rounded-full text-gray-400 hover:text-teal-600 focus:outline-none"
                    >
                      <Edit className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleDeleteAttribute(attribute.id || 0)}
                      className="p-1 rounded-full text-gray-400 hover:text-red-600 focus:outline-none"
                    >
                      <Trash2 className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                {/* Show options if this attribute type has them */}
                {['radio', 'dropdown', 'checkbox'].includes(attribute.input_type) && attribute.options.length > 0 && (
                  <div className="mt-2 ml-10">
                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">Options:</div>
                    <div className="flex flex-wrap gap-2">
                      {attribute.options.map(option => (
                        <span key={option.id} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          {option.option_value}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Dependency information if applicable */}
                {attribute.depends_on_attribute_id && (
                  <div className="mt-2 ml-10">
                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-1">Depends on:</div>
                    <div className="text-sm text-gray-600">
                      Attribute ID: {attribute.depends_on_attribute_id}, Option ID: {attribute.depends_on_option_id || 'Any'}
                    </div>
                  </div>
                )}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Add/Edit Attribute Form */}
      {(isAddingAttribute || editingAttributeId !== null) && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-4 border-b">
              <h3 className="text-lg font-medium text-gray-900">
                {editingAttributeId !== null ? 'Edit Attribute' : 'Add Attribute'}
              </h3>
              <button
                onClick={() => {
                  setIsAddingAttribute(false);
                  setEditingAttributeId(null);
                }}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <AttributeForm
              categoryId={categoryId}
              attributeId={editingAttributeId}
              attribute={attributes.find(attr => attr.id === editingAttributeId)}
              onSuccess={() => {
                handleRefresh();
                setIsAddingAttribute(false);
                setEditingAttributeId(null);
              }}
              onCancel={() => {
                setIsAddingAttribute(false);
                setEditingAttributeId(null);
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default AttributesTab;
