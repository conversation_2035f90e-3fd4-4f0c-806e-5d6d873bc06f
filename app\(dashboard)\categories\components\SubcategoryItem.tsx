'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Edit, Trash2, ChevronDown, ChevronUp, FolderTree, ExternalLink } from 'lucide-react';
import { CategoryWithChildren } from '../types';
import EditCategoryModal from './EditCategoryModal';
import DeleteConfirmModal from './DeleteConfirmModal';
import Link from 'next/link';

interface SubcategoryItemProps {
  subcategory: CategoryWithChildren;
  parentId?: number;
  level: number;
  onEdit: () => void;
  onRefresh: () => void;
  isExpanded: boolean;
  onToggleExpand: () => void;
  expandedSubcategories: Record<number, Record<number, boolean>>;
  onToggleSubcategoryExpand: (parentId: number, childId: number) => void;
  onShowBatchRename: (categoryId: number, categoryName: string) => void;
}

const SubcategoryItem: React.FC<SubcategoryItemProps> = ({
  subcategory,
  parentId,
  level,
  onEdit,
  onRefresh,
  isExpanded,
  onToggleExpand,
  expandedSubcategories,
  onToggleSubcategoryExpand,
  onShowBatchRename
}) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const hasChildren = subcategory.children && subcategory.children.length > 0;
  const maxLevel = 4; // Maximum nesting level to display

  // Animation variants
  const childrenVariants = {
    hidden: { opacity: 0, height: 0 },
    visible: {
      opacity: 1,
      height: "auto",
      transition: {
        duration: 0.3,
        ease: "easeInOut"
      }
    }
  };

  // Calculate indentation based on level
  const indentationClass = `ml-${Math.min(level * 3, 12)}`;

  // Determine background color based on level
  const getBgColorClass = () => {
    switch (level % 4) {
      case 1: return 'bg-white';
      case 2: return 'bg-gray-50';
      case 3: return 'bg-white';
      case 0: return 'bg-gray-50';
      default: return 'bg-white';
    }
  };

  // Determine border color based on level
  const getBorderColorClass = () => {
    switch (level % 3) {
      case 1: return 'border-gray-200';
      case 2: return 'border-gray-300';
      case 0: return 'border-gray-200';
      default: return 'border-gray-200';
    }
  };

  // Determine icon color based on level
  const getIconColorClass = () => {
    switch (level % 4) {
      case 1: return 'text-teal-500';
      case 2: return 'text-orange-500';
      case 3: return 'text-blue-500';
      case 0: return 'text-purple-500';
      default: return 'text-gray-500';
    }
  };

  return (
    <>
      <div className={`${getBgColorClass()} p-3 rounded-md border ${getBorderColorClass()} transition-all duration-200 ${!isEditModalOpen ? 'hover:shadow-md' : ''}`}>
        <div className="flex justify-between items-center">
          <Link
            href={`/categories/${subcategory.id}`}
            className="flex items-center flex-1 group"
          >
            {level <= 2 ? (
              <FolderTree size={16} className={`${getIconColorClass()} mr-2 flex-shrink-0`} />
            ) : (
              <div className="w-4 h-4 mr-2 border-l-2 border-b-2 border-gray-300"></div>
            )}
            <div className="min-w-0 flex-1">
              <div className="flex items-center">
                <p className={`text-sm font-medium text-gray-700 truncate transition-colors ${!isEditModalOpen ? 'group-hover:text-teal-600' : ''}`}>{subcategory.label}</p>
                <ExternalLink size={12} className={`ml-1 text-gray-400 transition-all ${!isEditModalOpen ? 'opacity-0 group-hover:opacity-100' : 'opacity-0'}`} />
              </div>
              <p className="text-xs text-gray-500 truncate">{subcategory.href}</p>
            </div>
          </Link>

          <div className="flex items-center space-x-1">
            <button
              onClick={(e) => {
                e.stopPropagation();
                // Open this specific subcategory's edit modal
                setIsEditModalOpen(true);
              }}
              className={`p-1 rounded-md text-gray-500 transition-colors ${!isEditModalOpen ? 'hover:bg-gray-100 hover:text-gray-700' : ''}`}
              aria-label="Edit subcategory"
            >
              <Edit size={14} />
            </button>

            {hasChildren && (
              <button
                onClick={(e) => {
                  e.stopPropagation(); // Stop event propagation
                  onToggleExpand(); // Use the prop function instead of local state
                }}
                className={`p-1 rounded-md text-gray-500 transition-colors ${!isEditModalOpen ? 'hover:bg-gray-100 hover:text-gray-700' : ''}`}
                aria-label={isExpanded ? "Collapse" : "Expand"}
              >
                {isExpanded ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
              </button>
            )}
          </div>
        </div>

        {/* Status indicators */}
        {level <= 2 && (
          <div className="flex mt-2 space-x-2">
            <span className={`text-xs px-2 py-0.5 rounded-full ${subcategory.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
              {subcategory.isActive ? 'Active' : 'Inactive'}
            </span>
            {subcategory.isEnginePart && (
              <span className="text-xs px-2 py-0.5 rounded-full bg-blue-100 text-blue-800">
                Engine Part
              </span>
            )}
            {hasChildren && (
              <span className="text-xs px-2 py-0.5 rounded-full bg-orange-100 text-orange-800">
                {subcategory.children.length} subcategories
              </span>
            )}
          </div>
        )}

        {/* Nested subcategories */}
        {hasChildren && isExpanded && level < maxLevel && (
          <div
            className="mt-2 pl-2 space-y-2 border-l-2 border-gray-200"
          >
            {subcategory.children.map((child) => (
              <SubcategoryItem
                key={child.id}
                subcategory={child}
                parentId={subcategory.id}
                level={level + 1}
                onEdit={onEdit}
                onRefresh={onRefresh}
                isExpanded={Boolean(expandedSubcategories?.[subcategory.id]?.[child.id])}
                onToggleExpand={() => onToggleSubcategoryExpand(subcategory.id, child.id)}
                expandedSubcategories={expandedSubcategories}
                onToggleSubcategoryExpand={onToggleSubcategoryExpand}
                onShowBatchRename={onShowBatchRename}
              />
            ))}
          </div>
        )}

        {/* Show message if max level reached */}
        {hasChildren && isExpanded && level >= maxLevel && (
          <div className="mt-2 pl-4 text-xs text-gray-500 italic">
            More subcategories available. Edit this category to view all.
          </div>
        )}
      </div>

      {/* Edit Modal */}
      <EditCategoryModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        category={subcategory}
        onSuccess={onRefresh}
        onShowBatchRename={onShowBatchRename}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        categoryId={subcategory.id}
        categoryName={subcategory.label}
        hasChildren={hasChildren}
        onSuccess={onRefresh}
      />
    </>
  );
};

export default SubcategoryItem;
