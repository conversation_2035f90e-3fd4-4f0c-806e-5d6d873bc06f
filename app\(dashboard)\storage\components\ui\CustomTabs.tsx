'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface Tab {
  id: string | number;
  label: string;
}

interface CustomTabsProps {
  tabs: Tab[];
  activeTabId: string | number;
  onTabChange: (tabId: string | number) => void;
  className?: string;
}

const CustomTabs: React.FC<CustomTabsProps> = ({
  tabs,
  activeTabId,
  onTabChange,
  className = ''
}) => {
  return (
    <div className={`border-b border-gray-200 ${className}`}>
      <div className="flex space-x-4">
        {tabs.map((tab) => {
          const isActive = tab.id === activeTabId;
          
          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`relative py-2 px-1 text-sm font-medium transition-colors duration-200 ${
                isActive
                  ? 'text-teal-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.label}
              {isActive && (
                <motion.div
                  layoutId="activeTab"
                  className="absolute bottom-0 left-0 right-0 h-0.5 bg-teal-600"
                  initial={false}
                  transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                />
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default CustomTabs;
