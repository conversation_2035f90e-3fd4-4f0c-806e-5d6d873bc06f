// Structured data helpers for rich snippets and SEO

// Organization structured data
export const organizationStructuredData = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "AutoFlow Parts Kenya",
  "alternateName": "AutoFlow",
  "url": process.env.NEXT_PUBLIC_BASE_URL || "https://autoflow.co.ke",
  "logo": `${process.env.NEXT_PUBLIC_BASE_URL}/images/logo.png`,
  "description": "Leading supplier of genuine and aftermarket VW & Audi parts in Kenya",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "Industrial Area",
    "addressLocality": "Nairobi",
    "addressCountry": "KE"
  },
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "+254-XXX-XXXXXX",
    "contactType": "customer service",
    "availableLanguage": ["English", "Swahili"]
  },
  "sameAs": [
    "https://facebook.com/autoflowke",
    "https://twitter.com/autoflowke",
    "https://instagram.com/autoflowke"
  ]
};

// Website structured data
export const websiteStructuredData = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "AutoFlow Parts Kenya",
  "url": process.env.NEXT_PUBLIC_BASE_URL || "https://autoflow.co.ke",
  "description": "Find genuine and aftermarket VW & Audi parts in Kenya",
  "publisher": {
    "@type": "Organization",
    "name": "AutoFlow Parts Kenya"
  },
  "potentialAction": {
    "@type": "SearchAction",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": `${process.env.NEXT_PUBLIC_BASE_URL}/search?q={search_term_string}`
    },
    "query-input": "required name=search_term_string"
  }
};

// Product structured data for parts
export function generateProductStructuredData(part: {
  id: number;
  title: string;
  description?: string;
  partNumber?: string;
  condition?: string;
  price?: number;
  currency?: string;
  images?: string[];
  category?: string;
  brand?: string;
  model?: string;
  availability?: string;
  sku?: string;
}) {
  const slug = part.title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-+|-+$/g, '');

  const url = `${process.env.NEXT_PUBLIC_BASE_URL}/parts/${slug}-${part.id}`;

  return {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": part.title,
    "description": part.description || `${part.condition || 'Used'} ${part.title} for ${part.brand || 'VW/Audi'} vehicles`,
    "sku": part.sku || part.partNumber || part.id.toString(),
    "mpn": part.partNumber,
    "url": url,
    "image": part.images || [],
    "brand": {
      "@type": "Brand",
      "name": part.brand || "VW/Audi"
    },
    "category": part.category,
    "offers": {
      "@type": "Offer",
      "price": part.price?.toString() || "0",
      "priceCurrency": part.currency || "KES",
      "availability": part.availability === 'in_stock' 
        ? "https://schema.org/InStock" 
        : "https://schema.org/OutOfStock",
      "seller": {
        "@type": "Organization",
        "name": "AutoFlow Parts Kenya"
      },
      "url": url
    },
    "additionalProperty": [
      {
        "@type": "PropertyValue",
        "name": "Part Number",
        "value": part.partNumber
      },
      {
        "@type": "PropertyValue",
        "name": "Condition",
        "value": part.condition || "Used"
      },
      {
        "@type": "PropertyValue",
        "name": "Compatible Vehicle",
        "value": `${part.brand || 'VW/Audi'} ${part.model || 'Various Models'}`
      }
    ]
  };
}

// Breadcrumb structured data
export function generateBreadcrumbStructuredData(breadcrumbs: Array<{
  name: string;
  url: string;
}>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url
    }))
  };
}

// FAQ structured data
export function generateFAQStructuredData(faqs: Array<{
  question: string;
  answer: string;
}>) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
}

// Local business structured data
export const localBusinessStructuredData = {
  "@context": "https://schema.org",
  "@type": "AutomotivePartsStore",
  "name": "AutoFlow Parts Kenya",
  "description": "Leading supplier of genuine and aftermarket VW & Audi parts in Kenya",
  "url": process.env.NEXT_PUBLIC_BASE_URL || "https://autoflow.co.ke",
  "telephone": "+254-XXX-XXXXXX",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "Industrial Area",
    "addressLocality": "Nairobi",
    "addressRegion": "Nairobi County",
    "postalCode": "00100",
    "addressCountry": "KE"
  },
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": -1.3194,
    "longitude": 36.8507
  },
  "openingHoursSpecification": [
    {
      "@type": "OpeningHoursSpecification",
      "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
      "opens": "08:00",
      "closes": "17:00"
    },
    {
      "@type": "OpeningHoursSpecification",
      "dayOfWeek": "Saturday",
      "opens": "08:00",
      "closes": "13:00"
    }
  ],
  "priceRange": "$$",
  "paymentAccepted": ["Cash", "Credit Card", "M-Pesa"],
  "currenciesAccepted": "KES"
};

// Collection page structured data
export function generateCollectionStructuredData(collection: {
  name: string;
  description: string;
  url: string;
  items: Array<{
    name: string;
    url: string;
    image?: string;
  }>;
}) {
  return {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": collection.name,
    "description": collection.description,
    "url": collection.url,
    "mainEntity": {
      "@type": "ItemList",
      "numberOfItems": collection.items.length,
      "itemListElement": collection.items.map((item, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "url": item.url,
        "name": item.name,
        "image": item.image
      }))
    }
  };
}
