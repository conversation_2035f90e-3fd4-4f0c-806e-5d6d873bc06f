// /components/modals/UpdateDetailsModal/PartInfoTab.tsx
import React, { useState } from 'react';
import { PartInfoTabProps } from './types';
import Input from '@/app/components/ui/inputs/Input'; // Assuming path
import NumberInput from '@/app/components/ui/inputs/NumberInput'; // Assuming path
import { Textarea } from '@/app/components/ui/Textarea'; // Correct import
import { Button } from '@/app/components/ui/Button'; // Correct import with named export
import { RefreshCw, Sparkles, Zap, X } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { useCompatibleVehicles } from '@/app/hooks/useCompatibleVehicles';
import { useAlternativePartNumbers } from '@/app/hooks/useAlternativePartNumbers';

const PartInfoTab: React.FC<PartInfoTabProps> = ({
  register,
  errors,
  control,
  conditions, // Use the passed conditions prop
  watch,
  setValue,
  getValues,
  onTabModified,
  partId,
  compatibleVehicles = [], // Add compatible vehicles prop with default empty array
  onAiAlternativesGenerated
}) => {
  // Get the initial conditions data from the form state if needed,
  // but the structure is primarily driven by the `conditions` prop passed down.
  // const formConditions = watch('conditions'); // Example if needed

  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  const [isUpdatingTitle, setIsUpdatingTitle] = useState(false);
  const supabase = createClient();
  const { fetchCompatibleVehicles, isLoading: isLoadingVehicles } = useCompatibleVehicles();
  const {
    alternativeNumbers,
    isLoading: isLoadingAlternatives,
    isGenerating: isGeneratingAlternatives,
    generateAlternativeNumbers,
    deleteAlternativeNumber
  } = useAlternativePartNumbers(partId, onAiAlternativesGenerated);

  // Function to handle generating alternative part numbers
  const handleGenerateAlternatives = async () => {
    try {
      const currentPartNumber = getValues('partNumber');

      if (!currentPartNumber || currentPartNumber.trim() === '') {
        alert('Please enter a part number first');
        return;
      }

      console.log('🤖 Generating alternatives for part number:', currentPartNumber);
      await generateAlternativeNumbers(currentPartNumber.trim());

    } catch (error) {
      console.error('Error generating alternatives:', error);
      alert('Failed to generate alternative part numbers');
    }
  };

  // Function to handle deleting alternative part numbers
  const handleDeleteAlternative = async (partNumber: string, source: string) => {
    const isAiGenerated = source === 'ai_generated';
    const confirmMessage = isAiGenerated
      ? `Remove "${partNumber}" from the list?\n\nThis AI-generated alternative will be removed and won't be saved to the database.`
      : `Delete "${partNumber}" from the database?\n\nThis will permanently remove this alternative part number from the database. This action cannot be undone.`;

    if (confirm(confirmMessage)) {
      try {
        await deleteAlternativeNumber(partNumber);
        console.log(`✅ Successfully ${isAiGenerated ? 'removed' : 'deleted'} alternative: ${partNumber}`);
      } catch (error) {
        console.error('Error deleting alternative:', error);
        alert(`Failed to ${isAiGenerated ? 'remove' : 'delete'} alternative part number: ${partNumber}`);
      }
    }
  };

  // Function to handle updating title based on compatible vehicles
  const handleUpdateTitle = async () => {
    try {
      setIsUpdatingTitle(true);

      console.log('🔄 Starting title update process...');

      // Get current part data first
      const { data: partData, error: partError } = await supabase
        .from('parts')
        .select(`
          title,
          category_id,
          partnumber_group
        `)
        .eq('id', parseInt(partId.toString()))
        .single();

      console.log('Part data:', partData);
      console.log('Part error:', partError);
      console.log('Part ID:', partId);

      if (partError) {
        console.error('Error fetching part data:', partError);
        alert(`Failed to fetch part data: ${partError.message}`);
        return;
      }

      if (!partData) {
        alert('No part data found');
        return;
      }

      // Get category template
      const { data: categoryData, error: categoryError } = await supabase
        .from('car_part_categories')
        .select('title_template, label')
        .eq('id', partData.category_id)
        .single();

      console.log('Category data:', categoryData);
      console.log('Category error:', categoryError);
      console.log('Part category_id:', partData.category_id);

      if (categoryError) {
        console.error('Error fetching category:', categoryError);
        alert(`Error fetching category: ${categoryError.message}`);
        return;
      }

      if (!categoryData) {
        alert('Category not found');
        return;
      }

      // Check if category has a title template, if not use a default one
      let titleTemplate = categoryData.title_template;
      if (!titleTemplate || titleTemplate.trim() === '') {
        console.warn(`No title template found for category "${categoryData.label}". Using default template.`);
        // Use a default template
        titleTemplate = 'Volkswagen (VW) Audi {category_name} {part_number} {compatible_vehicles}';

        // Optionally show a warning to the user
        alert(`No title template found for category "${categoryData.label}". Using default template. Please set up a custom title template for this category in the admin panel.`);
      }

      // Fetch compatible vehicles using the hook
      console.log('🔄 Fetching compatible vehicles...');
      const fetchedVehicles = await fetchCompatibleVehicles(partId);

      let compatibleVehiclesText = '';

      if (fetchedVehicles && fetchedVehicles.length > 0) {
        console.log(`✅ Found ${fetchedVehicles.length} compatible vehicles`);

        // Format vehicle strings (excluding variation)
        const vehicleStrings = fetchedVehicles.map(vehicle => {
          const parts = [
            vehicle.brand,
            vehicle.model,
            vehicle.generation,
            // vehicle.variation, // Removed variation from title
            vehicle.trim
          ].filter(part => part && String(part).trim() !== '');

          return parts.join(' ').trim();
        }).filter(vehicle => vehicle.length > 0);

        console.log('🚗 Formatted vehicle strings:', vehicleStrings);

        // Remove duplicates and join
        const uniqueVehicles = Array.from(new Set(vehicleStrings));
        compatibleVehiclesText = uniqueVehicles.join(', ');

        console.log('✅ Final compatible vehicles text:', compatibleVehiclesText);
      } else {
        console.log('⚠️ No compatible vehicles found for this part');
        compatibleVehiclesText = 'Universal VW/Audi Part';
      }

      // Get part condition from the form or part data
      let partCondition = 'Used'; // Default condition
      try {
        // Try to get condition from the form first
        const currentConditions = getValues('conditions');
        if (currentConditions && currentConditions.length > 0) {
          // Get the first condition or the one marked as primary
          const primaryCondition = currentConditions.find((c: any) => c.isPrimary) || currentConditions[0];
          if (primaryCondition && primaryCondition.condition) {
            partCondition = primaryCondition.condition;
          }
        }
      } catch (error) {
        console.warn('Could not get condition from form, using default:', error);
      }

      console.log('Part condition:', partCondition);

      // Get part number from compatibility group
      let partNumber = '';
      if (partData.partnumber_group) {
        const { data: compatibilityGroup, error: compatibilityError } = await supabase
          .from('part_compatibility_groups')
          .select('part_number')
          .eq('id', partData.partnumber_group)
          .single();

        console.log('Compatibility group data:', compatibilityGroup);
        console.log('Compatibility group error:', compatibilityError);

        if (compatibilityGroup && !compatibilityError) {
          partNumber = compatibilityGroup.part_number || '';
        }
      }

      console.log('Final part number:', partNumber);

      // Get alternative part numbers from the database for title generation (excluding main part number)
      console.log('🔍 Fetching alternative part numbers for title generation...');
      let alternativePartNumbersOnly = []; // Only alternatives, not main part number

      if (partData.partnumber_group) {
        try {
          const { data: alternativePartNumbers, error: altError } = await supabase
            .from('part_to_group')
            .select('partnumber')
            .eq('group_id', partData.partnumber_group);

          if (altError) {
            console.warn('Error fetching alternative part numbers for title:', altError);
          } else if (alternativePartNumbers && alternativePartNumbers.length > 0) {
            const altNumbers = alternativePartNumbers.map(alt => alt.partnumber);
            console.log('🔍 Found all part numbers in group:', altNumbers);

            // Filter out the main part number to avoid duplication in title
            alternativePartNumbersOnly = altNumbers.filter(num => num !== partNumber);
            console.log('🔍 Alternative part numbers (excluding main):', alternativePartNumbersOnly);
          }
        } catch (err) {
          console.warn('Error fetching alternative part numbers:', err);
        }
      }

      const compatiblePartNumbersText = alternativePartNumbersOnly.length > 0
        ? alternativePartNumbersOnly.join(', ')
        : partNumber; // Fallback to main part number if no alternatives

      console.log('=== TITLE GENERATION DEBUG ===');
      console.log('Template:', titleTemplate);
      console.log('Part condition:', partCondition);
      console.log('Category label:', categoryData.label);
      console.log('Main part number:', partNumber);
      console.log('Alternative part numbers only:', alternativePartNumbersOnly);
      console.log('Compatible part numbers text:', compatiblePartNumbersText);
      console.log('Compatible vehicles text:', compatibleVehiclesText);

      // Update title using template
      let newTitle = titleTemplate
        .replace(/\{condition\}/g, partCondition)
        .replace(/\{category_name\}/g, categoryData.label || '')
        .replace(/\{part_number\}/g, partNumber)
        .replace(/\{compatible_part_numbers\}/g, compatiblePartNumbersText)
        .replace(/\{compatible_vehicles\}/g, compatibleVehiclesText);

      console.log('Generated title (before cleanup):', newTitle);

      // Clean up extra spaces
      newTitle = newTitle.replace(/\s+/g, ' ').trim();

      console.log('Final generated title:', newTitle);
      console.log('=== END TITLE GENERATION DEBUG ===');

      // Update the form field with the new title
      setValue('title', newTitle, {
        shouldDirty: true,
        shouldValidate: true,
        shouldTouch: true
      });

      // Mark tab as modified
      onTabModified('partInfo');

      console.log('✅ Successfully updated title');
      console.log(`📝 New title: ${newTitle}`);

    } catch (error) {
      console.error('Error updating title:', error);
      alert('Failed to update title');
    } finally {
      setIsUpdatingTitle(false);
    }
  };

  // Function to handle generating description
  const handleGenerateDescription = async () => {
    try {
      setIsGeneratingDescription(true);
      const title = getValues('title');

      if (!title) {
        alert('Please enter a part title first');
        setIsGeneratingDescription(false);
        return;
      }

      // Access the global function from the window object
      if (typeof (window as any).generatePartDescription === 'function') {
        const description = await (window as any).generatePartDescription(title);

        if (description) {
          setValue('description', description, { shouldDirty: true });
          onTabModified('partInfo');
        }
      } else {
        console.error('generatePartDescription function not available');
        alert('Description generation is not available. Please try again later.');
      }
    } catch (error) {
      console.error('Error generating description:', error);
      alert('Failed to generate description. Please try again.');
    } finally {
      setIsGeneratingDescription(false);
    }
  };

  return (
    <div className="space-y-4 pt-4">
      {/* Part Title */}
      <div>
        <label htmlFor="title" className="block text-sm font-medium mb-1">Part Title</label>
        <div className="flex items-center space-x-2">
          <Input
            id="title"
            {...register('title', { required: 'Title is required' })}
            className="flex-1"
            aria-invalid={errors.title ? "true" : "false"}
          />
          <button
            type="button"
            onClick={handleUpdateTitle}
            disabled={isUpdatingTitle || isLoadingVehicles}
            className="flex items-center justify-center w-10 h-10 rounded-md border border-gray-300 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Update title based on compatible vehicles"
          >
            {(isUpdatingTitle || isLoadingVehicles) ? (
              <RefreshCw className="h-4 w-4 animate-spin text-gray-600" />
            ) : (
              <RefreshCw className="h-4 w-4 text-gray-600" />
            )}
          </button>
        </div>
        {errors.title && (
          <p className="text-red-500 text-sm mt-1" role="alert">{errors.title.message}</p>
        )}
      </div>

      {/* Part Number */}
      <div>
        <label htmlFor="partNumber" className="block text-sm font-medium mb-1">Part Number</label>
        <div className="flex items-center space-x-2">
          <Input
            id="partNumber"
            {...register('partNumber', { required: 'Part number is required' })}
            className="flex-1"
            aria-invalid={errors.partNumber ? "true" : "false"}
          />
          <button
            type="button"
            onClick={handleGenerateAlternatives}
            disabled={isGeneratingAlternatives}
            className="flex items-center justify-center w-10 h-10 rounded-md border border-gray-300 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Generate alternative part numbers with AI"
          >
            {isGeneratingAlternatives ? (
              <Zap className="h-4 w-4 animate-pulse text-yellow-600" />
            ) : (
              <Zap className="h-4 w-4 text-yellow-600" />
            )}
          </button>
        </div>
        {errors.partNumber && (
          <p className="text-red-500 text-sm mt-1" role="alert">{errors.partNumber.message}</p>
        )}

        {/* Alternative Part Numbers Display */}
        {(alternativeNumbers.length > 0 || isLoadingAlternatives) && (
          <div className="mt-3 p-3 bg-gray-50 rounded-lg border">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-700">
                Alternative Part Numbers
                {alternativeNumbers.length > 0 && (
                  <span className="ml-1 text-xs text-gray-500">({alternativeNumbers.length})</span>
                )}
              </h4>
              {isLoadingAlternatives && (
                <div className="flex items-center space-x-1">
                  <div className="animate-spin rounded-full h-3 w-3 border-b border-gray-400"></div>
                  <span className="text-xs text-gray-500">Loading...</span>
                </div>
              )}
            </div>

            {alternativeNumbers.length > 0 ? (
              <div className="space-y-3">
                {/* Database Alternatives Section */}
                {alternativeNumbers.filter(alt => alt.source !== 'ai_generated').length > 0 && (
                  <div>
                    <div className="flex items-center mb-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                      <span className="text-xs font-medium text-gray-600">
                        Saved in Database ({alternativeNumbers.filter(alt => alt.source !== 'ai_generated').length})
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {alternativeNumbers
                        .filter(alt => alt.source !== 'ai_generated')
                        .map((alt) => (
                          <div
                            key={alt.id}
                            className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium group bg-blue-100 text-blue-800 border border-blue-200"
                            title="Saved in Database"
                          >
                            <span className="flex items-center">
                              {alt.part_number}
                            </span>
                            <button
                              type="button"
                              onClick={() => handleDeleteAlternative(alt.part_number, alt.source || 'part_to_group')}
                              className="ml-2 p-0.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-blue-200 text-blue-600 hover:text-blue-800"
                              title="Delete from database"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </div>
                        ))}
                    </div>
                  </div>
                )}

                {/* AI Generated Alternatives Section */}
                {alternativeNumbers.filter(alt => alt.source === 'ai_generated').length > 0 && (
                  <div>
                    <div className="flex items-center mb-2">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                      <span className="text-xs font-medium text-gray-600">
                        AI Generated ({alternativeNumbers.filter(alt => alt.source === 'ai_generated').length})
                        <span className="ml-1 text-gray-500">- will be saved when form is submitted</span>
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {alternativeNumbers
                        .filter(alt => alt.source === 'ai_generated')
                        .map((alt) => (
                          <div
                            key={alt.id}
                            className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium group bg-yellow-100 text-yellow-800 border border-yellow-200"
                            title="AI Generated - will be saved to database"
                          >
                            <span className="flex items-center">
                              {alt.part_number}
                              <Sparkles className="ml-1 h-3 w-3" />
                            </span>
                            <button
                              type="button"
                              onClick={() => handleDeleteAlternative(alt.part_number, alt.source || 'part_to_group')}
                              className="ml-2 p-0.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-yellow-200 text-yellow-600 hover:text-yellow-800"
                              title="Remove from list"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            ) : !isLoadingAlternatives ? (
              <div className="text-center py-4">
                <p className="text-xs text-gray-500 mb-2">No alternative part numbers found</p>
                <p className="text-xs text-gray-400">Click the ⚡ button above to generate alternatives with AI</p>
              </div>
            ) : null}
          </div>
        )}
      </div>

      {/* Description */}
      <div>
        <div className="flex justify-between items-center mb-1">
          <label htmlFor="description" className="block text-sm font-medium text-gray-600">
            Description
          </label>
          <Button
            type="button"
            onClick={handleGenerateDescription}
            disabled={isGeneratingDescription}
            size="sm"
            variant="outline"
            className="text-xs py-1 h-8"
          >
            {isGeneratingDescription ? 'Generating...' : 'Generate with AI'}
          </Button>
        </div>
        <Textarea
          id="description"
          {...register('description')}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter or generate a description for this part..."
          rows={3}
        />
        {errors.description && (
          <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
        )}
      </div>

      {/* Conditions, Stock and Prices */}
      <div>
        <h4 className="font-medium mb-2 text-gray-700">Stock & Pricing</h4>
        {/* Use the conditions prop passed from the parent modal */}
        {conditions?.map((condition, index) => (
          <div key={condition.id} className="bg-gray-50 p-3 rounded-md mb-3 border border-gray-200">
            <div className="flex justify-between items-center mb-2">
              <h5 className="font-medium text-gray-800">{condition.condition} Condition</h5>
              {/* Condition selector - Registers to a NEW field to track changes */}
              <select
                {...register(`conditions.${index}.newCondition`)}
                defaultValue={condition.condition ?? ''} // Set default based on current condition, fallback to empty string
                className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="New">New</option>
                <option value="Used">Used</option>
                {/* Add other condition options if necessary */}
              </select>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 mb-2">
              <div>
                <label htmlFor={`conditions.${index}.stock`} className="block text-sm font-medium mb-1 text-gray-600">Stock</label>
                <NumberInput
                  name={`conditions.${index}.stock`}
                  control={control}
                  rules={{ required: 'Stock is required', min: { value: 0, message: 'Stock cannot be negative' } }}
                  className="w-full"
                  aria-invalid={errors.conditions?.[index]?.stock ? "true" : "false"}
                />
                 {errors.conditions?.[index]?.stock && (
                   <p className="text-red-500 text-sm mt-1" role="alert">{errors.conditions[index]?.stock?.message}</p>
                 )}
              </div>
              <div>
                <label htmlFor={`conditions.${index}.reorderLevel`} className="block text-sm font-medium mb-1 text-gray-600">Reorder Level</label>
                <NumberInput
                  name={`conditions.${index}.reorderLevel`}
                  control={control}
                  rules={{ required: 'Reorder level is required', min: { value: 0, message: 'Reorder level cannot be negative' } }}
                  className="w-full"
                  aria-invalid={errors.conditions?.[index]?.reorderLevel ? "true" : "false"}
                />
                 {errors.conditions?.[index]?.reorderLevel && (
                   <p className="text-red-500 text-sm mt-1" role="alert">{errors.conditions[index]?.reorderLevel?.message}</p>
                 )}
              </div>
              <div>
                <label htmlFor={`conditions.${index}.price`} className="block text-sm font-medium mb-1 text-gray-600">Price (Kshs)</label>
                <NumberInput
                  name={`conditions.${index}.price`}
                  control={control}
                  rules={{ required: 'Price is required', min: { value: 0, message: 'Price cannot be negative' } }}
                  className="w-full"
                  aria-invalid={errors.conditions?.[index]?.price ? "true" : "false"}
                />
                 {errors.conditions?.[index]?.price && (
                   <p className="text-red-500 text-sm mt-1" role="alert">{errors.conditions[index]?.price?.message}</p>
                 )}
              </div>
            </div>

            <div>
              <label htmlFor={`conditions.${index}.discountedPrice`} className="block text-sm font-medium mb-1 text-gray-600">Discounted Price (Optional)</label>
              <NumberInput
                name={`conditions.${index}.discountedPrice`}
                control={control}
                rules={{
                    validate: (value: number | null | undefined, formValues: { conditions: Array<{ price: number | null | undefined }> }) => {
                        const price = formValues.conditions[index].price;
                        if (value !== null && value !== undefined && price !== null && price !== undefined && value >= price) {
                            return 'Discount price must be less than regular price';
                        }
                        if (value !== null && value !== undefined && value < 0) {
                            return 'Discount price cannot be negative';
                        }
                        return true;
                    }
                }}
                className="w-full"
                aria-invalid={errors.conditions?.[index]?.discountedPrice ? "true" : "false"}
              />
              {errors.conditions?.[index]?.discountedPrice && (
                <p className="text-red-500 text-sm mt-1" role="alert">{errors.conditions[index]?.discountedPrice?.message}</p>
              )}
            </div>
             {/* Hidden field to store the original condition ID */}
             <input type="hidden" {...register(`conditions.${index}.id`)} defaultValue={condition.id} />
             {/* Hidden field to store the original condition name for comparison */}
             <input type="hidden" {...register(`conditions.${index}.condition`)} defaultValue={condition.condition} />
          </div>
        ))}
         {(!conditions || conditions.length === 0) && (
            <p className="text-sm text-gray-500">No conditions found for this part.</p>
        )}
      </div>
    </div>
  );
};

export default PartInfoTab;
