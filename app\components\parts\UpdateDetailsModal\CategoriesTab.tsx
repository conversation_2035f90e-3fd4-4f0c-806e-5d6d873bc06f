'use client';

import React, { useState, useEffect } from 'react';
import { Controller } from 'react-hook-form';
import { createClient } from '@/app/libs/supabase/client';
import { CategoriesTabProps } from './types';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import { NestedSelect, NestedSelectItem } from '@/app/components/ui/inputs/NestedSelect';

const CategoriesTab: React.FC<CategoriesTabProps> = ({
  register,
  control,
  errors,
  setValue,
  watch,
  initialCategoryId,
  isLoadingCategories,
}) => {
  const [categories, setCategories] = useState<any[]>([]);
  const [nestedCategories, setNestedCategories] = useState<NestedSelectItem[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const supabase = createClient();

  // Watch form fields
  const selectedCategoryId = watch('categoryData.categoryId');

  // Process categories to create a hierarchical structure
  const processCategories = (data: any[]): NestedSelectItem[] => {
    // Create a map for quick lookup
    const categoryMap = new Map();
    data.forEach(category => {
      categoryMap.set(category.id, {
        ...category,
        children: []
      });
    });

    // Build the tree structure
    const rootCategories: any[] = [];
    data.forEach(category => {
      if (category.parent_category_id) {
        const parent = categoryMap.get(category.parent_category_id);
        if (parent) {
          parent.children.push(categoryMap.get(category.id));
        }
      } else {
        rootCategories.push(categoryMap.get(category.id));
      }
    });

    // Convert to NestedSelectItem format
    const convertToNestedSelectItem = (categories: any[]): NestedSelectItem[] => {
      return categories.map(category => ({
        label: category.label,
        value: category.id.toString(),
        children: category.children.length > 0 ? convertToNestedSelectItem(category.children) : undefined
      }));
    };

    return convertToNestedSelectItem(rootCategories);
  };

  // Load all categories when the component mounts
  useEffect(() => {
    const fetchCategories = async () => {
      setLoadingCategories(true);
      try {
        const { data, error } = await supabase
          .from('car_part_categories')
          .select('*')
          .eq('isActive', true) // Only fetch active categories
          .order('label');

        if (error) throw error;

        // Store the flat list of categories for details display
        setCategories(data || []);

        // Process categories to create a hierarchical structure for the nested select
        const processedCategories = processCategories(data || []);
        setNestedCategories(processedCategories);

        // If we have an initial category ID, set it in the form
        if (initialCategoryId) {
          setValue('categoryData.categoryId', String(initialCategoryId));
        }
      } catch (err: any) {
        console.error('Error fetching categories:', err);
        setError(err.message || 'Failed to fetch categories');
      } finally {
        setLoadingCategories(false);
      }
    };

    fetchCategories();
  }, [initialCategoryId, setValue, supabase]);

  // Handle category selection
  const handleCategoryChange = (value: string) => {
    setValue('categoryData.categoryId', value);
  };

  if (isLoadingCategories || loadingCategories) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size={40} />
      </div>
    );
  }

  return (
    <div className="space-y-6 pt-4">
      <h4 className="font-medium mb-2 text-gray-700">Category Information</h4>

      {!initialCategoryId && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md text-blue-700 text-sm">
          <p>This part doesn't have a category assigned yet. Use the dropdown below to select a category.</p>
        </div>
      )}

      <div className="space-y-4">
        {/* Category Selection */}
        <div>
          <label htmlFor="categoryData.categoryId" className="block text-sm font-medium mb-1 text-gray-700">
            Category
          </label>
          <Controller
            name="categoryData.categoryId"
            control={control}
            render={({ field }) => (
              <NestedSelect
                items={nestedCategories}
                placeholder="Select a category"
                onValueChange={handleCategoryChange}
                value={field.value?.toString()}
                disabled={loadingCategories}
              />
            )}
          />
          {errors.categoryData?.categoryId && (
            <p className="text-red-500 text-sm mt-1">{errors.categoryData.categoryId.message}</p>
          )}
        </div>

        {/* Category Details */}
        {selectedCategoryId && (
          <div className="mt-4 p-4 bg-gray-50 rounded-md">
            <h5 className="font-medium text-gray-700 mb-2">Category Details</h5>
            {categories.filter(c => c.id.toString() === selectedCategoryId.toString()).map(category => (
              <div key={category.id} className="space-y-2">
                <div>
                  <span className="text-sm font-medium text-gray-600">Name:</span>
                  <span className="text-sm ml-2">{category.label}</span>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-600">Part Number Required:</span>
                  <span className="text-sm ml-2">{category.requirePartNumber ? 'Yes' : 'No'}</span>
                </div>
                <div>
                  <span className="text-sm font-medium text-gray-600">Engine Part:</span>
                  <span className="text-sm ml-2">{category.isEnginePart ? 'Yes' : 'No'}</span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoriesTab;
