'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { DollarSign, ShoppingCart, Calendar, TrendingUp, TrendingDown } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  change?: number;
  changeType?: 'increase' | 'decrease';
  color: string;
  delay?: number;
}

const StatsCard: React.FC<StatsCardProps> = ({ 
  title, 
  value, 
  icon, 
  change, 
  changeType = 'increase',
  color,
  delay = 0
}) => {
  const colorClasses = {
    teal: 'bg-teal-100 text-teal-600',
    blue: 'bg-blue-100 text-blue-600',
    orange: 'bg-orange-100 text-orange-600',
    gray: 'bg-gray-100 text-gray-600',
  };
  
  const bgColorClass = colorClasses[color as keyof typeof colorClasses] || colorClasses.gray;
  const isIncrease = changeType === 'increase';
  const ChangeIcon = isIncrease ? TrendingUp : TrendingDown;
  const changeColorClass = isIncrease ? 'text-green-500' : 'text-red-500';

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      className="bg-white rounded-lg shadow-sm p-6"
    >
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-full ${bgColorClass}`}>
          {icon}
        </div>
        {change !== undefined && (
          <div className="flex items-center text-sm">
            <ChangeIcon className={`w-4 h-4 mr-1 ${changeColorClass}`} />
            <span className={changeColorClass}>{change}%</span>
          </div>
        )}
      </div>
      <h3 className="text-lg font-semibold text-gray-800 mb-1">{title}</h3>
      <p className="text-2xl font-bold text-gray-900">{value}</p>
    </motion.div>
  );
};

interface SalesStatsProps {
  refreshTrigger: number;
}

const SalesStats: React.FC<SalesStatsProps> = ({ refreshTrigger }) => {
  const [stats, setStats] = useState({
    totalSales: 0,
    totalAmount: 0,
    salesThisMonth: 0,
    averageDiscount: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      setIsLoading(true);
      try {
        const supabase = createClient();
        
        // Get current date info for monthly calculations
        const now = new Date();
        const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
        const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString();
        
        // Fetch total sales count
        const { count: totalSales } = await supabase
          .from('sales')
          .select('*', { count: 'exact', head: true });
        
        // Fetch total sales amount
        const { data: amountData } = await supabase
          .from('sales')
          .select('total_amount');
        
        const totalAmount = amountData?.reduce((sum, sale) => sum + parseFloat(sale.total_amount), 0) || 0;
        
        // Fetch sales this month
        const { count: salesThisMonth } = await supabase
          .from('sales')
          .select('*', { count: 'exact', head: true })
          .gte('sale_timestamp', firstDayOfMonth)
          .lte('sale_timestamp', lastDayOfMonth);
        
        // Fetch average discount
        const { data: discountData } = await supabase
          .from('sales')
          .select('total_discount, total_amount');
        
        let avgDiscount = 0;
        if (discountData && discountData.length > 0) {
          const totalDiscount = discountData.reduce((sum, sale) => sum + parseFloat(sale.total_discount), 0);
          const totalBeforeDiscount = discountData.reduce((sum, sale) => 
            sum + parseFloat(sale.total_amount) + parseFloat(sale.total_discount), 0);
          
          avgDiscount = totalBeforeDiscount > 0 
            ? Math.round((totalDiscount / totalBeforeDiscount) * 100) 
            : 0;
        }
        
        setStats({
          totalSales: totalSales || 0,
          totalAmount: totalAmount,
          salesThisMonth: salesThisMonth || 0,
          averageDiscount: avgDiscount
        });
      } catch (error) {
        console.error('Error fetching sales stats:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchStats();
  }, [refreshTrigger]);

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm p-6 flex justify-center items-center h-32">
            <LoadingSpinner size={24} />
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      <StatsCard
        title="Total Sales"
        value={stats.totalSales}
        icon={<ShoppingCart className="w-5 h-5" />}
        color="teal"
        delay={0.1}
      />
      <StatsCard
        title="Total Revenue"
        value={`Kshs ${stats.totalAmount.toLocaleString()}`}
        icon={<DollarSign className="w-5 h-5" />}
        color="blue"
        delay={0.2}
      />
      <StatsCard
        title="Sales This Month"
        value={stats.salesThisMonth}
        icon={<Calendar className="w-5 h-5" />}
        color="orange"
        delay={0.3}
      />
      <StatsCard
        title="Average Discount"
        value={`${stats.averageDiscount}%`}
        icon={<TrendingDown className="w-5 h-5" />}
        color="gray"
        delay={0.4}
      />
    </div>
  );
};

export default SalesStats;
