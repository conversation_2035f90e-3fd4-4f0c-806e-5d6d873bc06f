'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/app/utils/cn';

interface SwitchProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label?: string;
  disabled?: boolean;
  hideLabel?: boolean;
  className?: string;
  id?: string;
}

const Switch = React.forwardRef<HTMLInputElement, SwitchProps>(
  ({ checked, onChange, label, disabled = false, className, id, hideLabel = false, ...props }, ref) => {
    const switchId = id || `switch-${Math.random().toString(36).substring(2, 9)}`;

    return (
      <div className={cn('flex items-center space-x-2', className)}>
        <div
          className={cn(
            'relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
            checked ? 'bg-blue-600' : 'bg-gray-200',
            disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
          )}
          onClick={() => {
            if (!disabled) {
              onChange(!checked);
            }
          }}
          role="switch"
          aria-checked={checked}
          aria-labelledby={label ? switchId : undefined}
          tabIndex={disabled ? -1 : 0}
        >
          <motion.div
            className={cn(
              'pointer-events-none block h-5 w-5 rounded-full bg-white shadow-lg',
              'transform transition-transform'
            )}
            layout
            animate={{
              x: checked ? 22 : 2,
            }}
            transition={{
              type: 'spring',
              stiffness: 500,
              damping: 30,
            }}
          />
          <input
            type="checkbox"
            ref={ref}
            id={switchId}
            checked={checked}
            onChange={(e) => onChange(e.target.checked)}
            className="sr-only"
            disabled={disabled}
            {...props}
          />
        </div>
        {label && !hideLabel && (
          <label
            htmlFor={switchId}
            id={switchId}
            className={cn(
              'text-sm font-medium',
              disabled ? 'cursor-not-allowed text-gray-500' : 'cursor-pointer text-gray-900'
            )}
          >
            {label}
          </label>
        )}
      </div>
    );
  }
);

Switch.displayName = 'Switch';

export default Switch;
