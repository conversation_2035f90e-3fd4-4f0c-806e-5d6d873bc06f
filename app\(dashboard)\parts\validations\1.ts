// app/(dashboard)/parts/add/validations/partSchema.ts
import { z } from 'zod';

// Define the schema for individual attributes
const attributeSchema = z.record(z.string(), z.string());

export const partFormSchema = z.object({
  partNumber: z.string().min(1, "Part number required").optional()
    .refine(val => !!val, "Part number required when needed"),
  brandId: z.string({ // Changed to string
    required_error: "Please select a brand.",
    invalid_type_error: "Brand must be a string" // Updated message
  }).optional(),
  modelId: z.string({ // Changed to string
    invalid_type_error: "Model must be a string" // Updated message
  }).optional(),
  stock: z.number().min(0, { message: 'Stock must be a positive number' }),
  price: z.number().min(0, { message: 'Price must be a positive number' }),
  imageUrl: z.string().url({ message: 'Invalid image URL' }).optional(),
  imageType: z.enum(['camera', 'upload']).optional(),
  condition: z.enum(['New', 'Used']),
  generationId: z.string().optional(),
  variationId: z.string().optional(),
  trimId: z.string().optional(),
  categoryId: z.string().min(1, "Category is required"),
  attributes: attributeSchema.optional(), // Attributes as a key-value object
});

export type PartFormValues = z.infer<typeof partFormSchema>;