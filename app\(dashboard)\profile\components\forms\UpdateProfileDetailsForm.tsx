// components/forms/UpdateProfileDetailsForm.tsx
'use client'

import { useState, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import Input from '@/app/components/ui/inputs/Input';
import Button from '@/app/components/ui/inputs/Button';
import { UpdateProfileDetailsFormProps } from '@/app/types/profile';
import toast from 'react-hot-toast';

const UpdateProfileDetailsForm: React.FC<UpdateProfileDetailsFormProps> = ({
  missingDetails,
  onUpdate,
}) => {
  const [phone, setPhone] = useState('');
  const [fullName, setFullName] = useState('');
  const [loading, setLoading] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);
  const supabase = createClient();

  useEffect(() => {
    const fetchUserId = async () => {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error) {
        console.error('Error fetching user:', error);
        toast.error('Error fetching user data');
      } else if (user) {
        setUserId(user.id);
      }
    };

    fetchUserId();
  }, []);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    if (!userId) {
      toast.error('User ID not found. Please try again.');
      setLoading(false);
      return;
    }

    const updates = {
      id: userId,
      phone: missingDetails.phone ? phone : undefined,
      full_name: missingDetails.full_name ? fullName : undefined,
      updated_at: new Date().toISOString(),
    };

    const { error } = await supabase.from('profiles').upsert([updates]);

    if (error) {
      toast.error('Error updating profile: ' + error.message);
      console.error('Error updating profile:', error);
    } else {
      toast.success('Profile updated successfully!');
      onUpdate({ phone, full_name: fullName });
    }

    setLoading(false);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {missingDetails.phone && (
        <Input
          label="Phone Number"
          value={phone}
          onChange={(e) => setPhone(e.target.value)}
        />
      )}
      {missingDetails.full_name && (
        <Input
          label="Full Name"
          value={fullName}
          onChange={(e) => setFullName(e.target.value)}
        />
      )}
      <Button type="submit" disabled={loading}>
        {loading ? 'Updating...' : 'Update'}
      </Button>
    </form>
  );
};

export default UpdateProfileDetailsForm;