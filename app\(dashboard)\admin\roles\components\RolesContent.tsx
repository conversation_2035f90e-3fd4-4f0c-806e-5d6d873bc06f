'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { usePermissions } from '@/app/hooks/usePermissions';
import { getRoles, createRole, updateRole, deleteRole } from '@/app/libs/rbac/api';
import Spinner from '@/app/components/ui/Spinner';
import { Input } from '@/app/components/ui/Input'; 
import { Button } from '@/app/components/ui/Button';

interface Role {
  id: string;
  name: string;
  description: string | null;
}

const RolesContent = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState<Role | null>(null);
  const [formData, setFormData] = useState<{ name: string; description: string }>({ name: '', description: '' });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { hasPermission, isLoading: isPermissionLoading, error: permissionError } = usePermissions('admin:manage_roles');
  const router = useRouter();

  useEffect(() => {
    if (!isPermissionLoading) {
      console.log(`RolesPage: Permission check complete. hasPermission('admin:manage_roles'): ${hasPermission}`);
      if (!hasPermission) {
        console.log('RolesPage: Redirecting to /admin due to missing permission.');
        setError('You do not have permission to manage roles.');
        setTimeout(() => router.push('/admin'), 50); 
      }
    }
  }, [hasPermission, isPermissionLoading, router]);

  useEffect(() => {
    if (hasPermission && !isPermissionLoading) {
      console.log('RolesPage: Permission granted. Fetching roles...');
      fetchRoles();
    } else if (!isPermissionLoading && !hasPermission) {
      setIsLoading(false); // Ensure loading stops if permission denied
    }
  }, [hasPermission, isPermissionLoading]);

  const fetchRoles = async () => {
    setIsLoading(true); 
    setError(null);
    try {
      const { data, error: fetchError } = await getRoles(); 
      if (fetchError) throw fetchError;
      setRoles(data || []);
    } catch (err: any) {
      setError('Failed to fetch roles. Please try again.');
      console.error('Error fetching roles:', err);
      setRoles([]); // Clear roles on error
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleEdit = (role: Role) => {
    setIsEditing(role);
    setFormData({ name: role.name, description: role.description || '' });
    setError(null); // Clear any previous errors when starting edit
  };

  const handleCancelEdit = () => {
    setIsEditing(null);
    setFormData({ name: '', description: '' });
    setError(null); // Clear errors when cancelling
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      setError('Role name cannot be empty.');
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      if (isEditing) {
        const { error: updateError } = await updateRole(isEditing.id, formData);
        if (updateError) throw updateError;
      } else {
        const { error: createError } = await createRole(formData.name, formData.description);
        if (createError) throw createError;
      }
      handleCancelEdit(); // Reset form
      await fetchRoles(); // Refresh the list
    } catch (err: any) {
      setError(`Failed to ${isEditing ? 'update' : 'create'} role: ${err.message || 'Please check the details and try again.'}`);
      console.error(`Error ${isEditing ? 'updating' : 'creating'} role:`, err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (roleId: string) => {
    if (!window.confirm('Are you sure you want to delete this role? This action cannot be undone and might affect users with this role.')) {
      return;
    }
    
    setIsSubmitting(true); 
    setError(null);

    try {
      const { error: deleteError } = await deleteRole(roleId);
      if (deleteError) throw deleteError;
      await fetchRoles(); // Refresh the list
      if (isEditing?.id === roleId) {
        handleCancelEdit(); // If the deleted role was being edited, cancel edit
      }
    } catch (err: any) {
      setError(`Failed to delete role: ${err.message || 'It might be in use or another error occurred.'}`);
      console.error('Error deleting role:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isPermissionLoading || isLoading) {
    return (
      <div className="flex min-h-[300px] items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  if (!isPermissionLoading && !hasPermission) { 
     return (
      <div className="rounded-md border border-red-200 bg-red-50 p-6 text-center shadow-sm">
        <h2 className="mb-2 text-xl font-semibold text-red-700">Access Denied</h2>
        <p className="mb-4 text-red-600">
          {error || 'You do not have the required permissions (admin:manage_roles) to view or manage roles.'}
        </p>
        {permissionError && (
           <p className="mb-4 text-sm text-red-500">Details: {permissionError.message}</p>
        )}
        <button
          onClick={() => router.push('/admin')}
          className="rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        >
          Return to Admin Home
        </button>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold">Role Management</h2>
      </div>

      <form onSubmit={handleSubmit} className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
        <h3 className="mb-4 text-lg font-medium">{isEditing ? `Edit Role: ${isEditing.name}` : 'Create New Role'}</h3>
        {error && (
          <div className="mb-4 rounded-md bg-red-50 p-3 text-sm text-red-700" role="alert">
            <p>{error}</p>
          </div>
        )}
        <div className="mb-4">
          <label htmlFor="name" className="mb-1 block text-sm font-medium text-gray-700">Role Name</label>
          <Input 
            id="name"
            name="name"
            type="text"
            value={formData.name}
            onChange={handleInputChange}
            placeholder="e.g., Content Editor"
            required
            aria-required="true"
            className="w-full"
            disabled={isSubmitting}
          />
        </div>
        <div className="mb-4">
          <label htmlFor="description" className="mb-1 block text-sm font-medium text-gray-700">Description (Optional)</label>
          <Input 
            id="description"
            name="description"
            type="text" 
            value={formData.description}
            onChange={handleInputChange}
            placeholder="Briefly describe the role's purpose (e.g., Can create and edit blog posts)"
            className="w-full"
            disabled={isSubmitting}
          />
        </div>
        <div className="flex items-center justify-end space-x-3 border-t border-gray-200 pt-4">
          {isEditing && (
            <Button type="button" variant="outline" onClick={handleCancelEdit} disabled={isSubmitting}>
              Cancel Edit
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting || !formData.name.trim()}>
            {isSubmitting ? <Spinner size="sm" /> : (isEditing ? 'Save Changes' : 'Create Role')}
          </Button>
        </div>
      </form>

      <div className="rounded-lg border border-gray-200 bg-white shadow-sm">
        <h3 className="border-b border-gray-200 p-4 text-lg font-medium">Existing Roles</h3>
        {roles.length === 0 && !isLoading ? (
          <p className="p-4 text-center text-sm text-gray-500">No roles found. Create one above!</p>
        ) : (
          <ul className="divide-y divide-gray-200">
            {roles.map((role) => (
              <li key={role.id} className="flex flex-wrap items-center justify-between gap-4 p-4 hover:bg-gray-50">
                <div className="flex-1">
                  <p className="font-semibold text-gray-900">{role.name}</p>
                  {role.description && (
                    <p className="text-sm text-gray-500">{role.description}</p>
                  )}
                </div>
                <div className="flex flex-shrink-0 items-center space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleEdit(role)} 
                    disabled={isSubmitting || isEditing?.id === role.id}
                    aria-label={`Edit role ${role.name}`}
                  >
                    Edit
                  </Button>
                  <Button 
                    variant="destructive" 
                    size="sm" 
                    onClick={() => handleDelete(role.id)}
                    disabled={isSubmitting || role.name === 'Super Admin'} 
                    aria-label={`Delete role ${role.name}`}
                  >
                    Delete
                  </Button>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default RolesContent;
