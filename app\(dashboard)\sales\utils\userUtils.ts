import { getUserCookie } from '@/app/utils/cookies';

interface UserData {
  id: string;
  name: string;
  roleId: string;
  roleName: string;
}

/**
 * Get the current user ID from the encrypted watu cookie
 * @returns Promise<string | null> - User ID or null if not found
 */
export async function getCurrentUserId(): Promise<string | null> {
  try {
    const userData: UserData | null = await getUserCookie();
    
    if (userData && userData.id) {
      return userData.id;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting current user ID:', error);
    return null;
  }
}

/**
 * Get the current user data from the encrypted watu cookie
 * @returns Promise<UserData | null> - User data or null if not found
 */
export async function getCurrentUser(): Promise<UserData | null> {
  try {
    return await getUserCookie();
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}
