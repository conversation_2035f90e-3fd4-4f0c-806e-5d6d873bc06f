'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { Search, X, User, Phone, UserPlus } from 'lucide-react';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import { SaleFormData } from '../AddSaleModal';
import WalkInCustomerModal from '../WalkInCustomerModal';
import { WalkInCustomer } from '../../../utils/walkInCustomerUtils';

interface ClientDetailsStepProps {
  onSubmit: (formData: SaleFormData) => void;
  onBack: () => void;
  initialData: SaleFormData;
}

interface Client {
  id: string;
  name: string;
  phone_number: string;
  client_type: string;
  email?: string;
}

const ClientDetailsStep: React.FC<ClientDetailsStepProps> = ({
  onSubmit,
  onBack,
  initialData
}) => {
  const [formData, setFormData] = useState<SaleFormData>(initialData);
  const [clients, setClients] = useState<Client[]>([]);
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isClientDropdownOpen, setIsClientDropdownOpen] = useState(false);
  const [isWalkInModalOpen, setIsWalkInModalOpen] = useState(false);

  // Fetch clients on component mount
  useEffect(() => {
    const fetchClients = async () => {
      try {
        // Create a new Supabase client with a timeout
        const supabase = createClient();

        // Add a loading state
        setIsSearching(true);
        setError(null);

        // Use a Promise with a timeout to handle network issues
        const fetchWithTimeout = async (timeoutMs = 5000) => {
          let timeoutId;

          const timeoutPromise = new Promise((_, reject) => {
            timeoutId = setTimeout(() => {
              reject(new Error('Connection timed out. Please check your internet connection.'));
            }, timeoutMs);
          });

          try {
            const result = await Promise.race([
              supabase
                .from('clients')
                .select('id, name, phone_number, client_type, email')
                .order('name'),
              timeoutPromise
            ]);

            clearTimeout(timeoutId);
            return result;
          } catch (error) {
            clearTimeout(timeoutId);
            throw error;
          }
        };

        // Fetch with timeout
        const { data, error } = await fetchWithTimeout();

        if (error) {
          console.error('Supabase error fetching clients:', error.message, error.code, error.details);
          throw new Error(`Database error: ${error.message}`);
        }

        // Set the clients data
        setClients(data || []);

        // Create mock clients for testing if no clients are found
        if (!data || data.length === 0) {
          // This is just for development/testing purposes
          const mockClients: Client[] = [
            {
              id: '1',
              name: 'John Doe',
              phone_number: '0712345678',
              client_type: 'cash',
              email: '<EMAIL>'
            },
            {
              id: '2',
              name: 'Jane Smith',
              phone_number: '0723456789',
              client_type: 'credit',
              email: '<EMAIL>'
            }
          ];

          setClients(mockClients);
          console.log('Using mock clients for testing');
        }
      } catch (error) {
        // Properly handle and log the error with more details
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error('Error fetching clients:', errorMessage);

        // Check if it's a network error
        if (errorMessage.includes('Failed to fetch') || errorMessage.includes('timed out')) {
          setError('Network error: Unable to connect to the database. Please check your internet connection and try again.');

          // Create mock clients for testing
          const mockClients: Client[] = [
            {
              id: '1',
              name: 'John Doe (Mock)',
              phone_number: '0712345678',
              client_type: 'cash',
              email: '<EMAIL>'
            },
            {
              id: '2',
              name: 'Jane Smith (Mock)',
              phone_number: '0723456789',
              client_type: 'credit',
              email: '<EMAIL>'
            }
          ];

          setClients(mockClients);
          console.log('Using mock clients due to network error');
        } else {
          // For other errors, try to check if the clients table exists
          try {
            const supabase = createClient();
            const { count, error: countError } = await supabase
              .from('clients')
              .select('*', { count: 'exact', head: true });

            if (countError) {
              console.error('Error checking clients table:', countError.message);
              // Check if the error is because the table doesn't exist
              if (countError.code === '42P01') { // PostgreSQL code for undefined_table
                setError('Clients table does not exist. Please set up the clients module first.');
              } else {
                setError(`Failed to load clients: ${errorMessage}`);
              }
            }
          } catch (checkError) {
            console.error('Error during clients table check:', checkError);
            setError(`Failed to load clients: ${errorMessage}`);
          }
        }
      } finally {
        // Always turn off the loading state
        setIsSearching(false);
      }
    };

    fetchClients();
  }, []);

  // Filter clients based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredClients([]);
      return;
    }

    const filtered = clients.filter(client => {
      const searchLower = searchTerm.toLowerCase();
      return (
        client.name?.toLowerCase().includes(searchLower) ||
        client.phone_number?.toLowerCase().includes(searchLower) ||
        client.email?.toLowerCase().includes(searchLower)
      );
    });

    setFilteredClients(filtered);
  }, [searchTerm, clients]);

  // Filter clients based on sale type (credit/cash)
  useEffect(() => {
    if (formData.saleType === 'credit') {
      // For credit sales, only show credit clients
      const creditClients = clients.filter(client => client.client_type === 'credit');
      setFilteredClients(creditClients);

      // If there are no credit clients, show a helpful error message
      if (clients.length > 0 && creditClients.length === 0) {
        setError('No credit clients found. Please add credit clients first or choose cash sale.');
      }
    }
  }, [formData.saleType, clients]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Reset dependent fields when changing sale type
    if (name === 'saleType') {
      if (value === 'credit') {
        setFormData({
          saleType: 'credit',
          clientId: undefined,
          cashPaymentMethod: undefined,
          deliveryOption: undefined,
          clientType: undefined,
          oneOffClientName: undefined,
          oneOffClientPhone: undefined,
          mpesaConfirmation: undefined
        });
      } else {
        setFormData({
          ...formData,
          saleType: 'cash',
          clientId: undefined
        });
      }
    } else if (name === 'cashPaymentMethod') {
      if (value === 'mpesa') {
        setFormData({
          ...formData,
          cashPaymentMethod: 'mpesa',
          deliveryOption: undefined
        });
      } else {
        setFormData({
          ...formData,
          cashPaymentMethod: 'cash',
          mpesaConfirmation: undefined
        });
      }
    } else if (name === 'clientType') {
      if (value === 'one_off') {
        setFormData({
          ...formData,
          clientType: 'one_off',
          clientId: undefined
        });
      } else {
        setFormData({
          ...formData,
          clientType: 'regular',
          oneOffClientName: undefined,
          oneOffClientPhone: undefined
        });
      }
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const selectClient = (client: Client) => {
    setFormData({
      ...formData,
      clientId: client.id
    });
    setSearchTerm(client.name);
    setIsClientDropdownOpen(false);
  };

  const handleWalkInCustomerSelected = (customer: WalkInCustomer) => {
    setFormData({
      ...formData,
      clientId: customer.id,
      clientType: 'regular' // Set to regular since they're now in the system
    });
    setSearchTerm(customer.name);
    setIsWalkInModalOpen(false);

    // Add the new customer to the clients list so they appear in future searches
    const newClient: Client = {
      id: customer.id,
      name: customer.name,
      phone_number: customer.phone_number,
      client_type: customer.client_type,
      email: undefined
    };
    setClients(prev => [...prev, newClient]);
  };

  const validateForm = (): boolean => {
    setError(null);

    if (formData.saleType === 'credit') {
      if (!formData.clientId) {
        setError('Please select a credit client');
        return false;
      }
    } else if (formData.saleType === 'cash') {
      if (!formData.cashPaymentMethod) {
        setError('Please select a payment method');
        return false;
      }

      if (formData.cashPaymentMethod === 'cash' && !formData.deliveryOption) {
        setError('Please select a delivery option');
        return false;
      }

      if (formData.cashPaymentMethod === 'cash' && formData.deliveryOption) {
        if (!formData.clientType) {
          setError('Please select a client type');
          return false;
        }

        if (formData.clientType === 'regular' && !formData.clientId) {
          setError('Please select a regular client');
          return false;
        }

        if (formData.clientType === 'one_off' && !formData.oneOffClientPhone) {
          setError('Phone number is required for one-off clients');
          return false;
        }
      }

      // For M-PESA payments, we need either:
      // 1. A client selected (for automated STK push), or
      // 2. A manual M-PESA confirmation message
      if (formData.cashPaymentMethod === 'mpesa') {
        const hasClientForAutomatedPayment = formData.clientId ||
          (formData.clientType === 'one_off' && formData.oneOffClientPhone);

        const hasManualConfirmation = !!formData.mpesaConfirmation;

        if (!hasClientForAutomatedPayment && !hasManualConfirmation) {
          setError('Please either select a client for automated M-PESA payment or enter a manual M-PESA confirmation message');
          return false;
        }
      }
    }

    return true;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit(formData);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Loading Indicator */}
      {isSearching && (
        <div className="flex justify-center items-center py-4">
          <LoadingSpinner size="md" />
          <span className="ml-2 text-gray-600">Loading clients...</span>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Sale Type Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Sale Type</label>
        <div className="flex space-x-4">
          <label className="inline-flex items-center">
            <input
              type="radio"
              name="saleType"
              value="cash"
              checked={formData.saleType === 'cash'}
              onChange={handleInputChange}
              className="h-4 w-4 text-teal-600 border-gray-300 focus:ring-teal-500"
            />
            <span className="ml-2 text-gray-700">Cash Sale</span>
          </label>
          <label className="inline-flex items-center">
            <input
              type="radio"
              name="saleType"
              value="credit"
              checked={formData.saleType === 'credit'}
              onChange={handleInputChange}
              className="h-4 w-4 text-teal-600 border-gray-300 focus:ring-teal-500"
            />
            <span className="ml-2 text-gray-700">Credit Sale</span>
          </label>
        </div>
      </div>

      {/* Cash Sale Options */}
      {formData.saleType === 'cash' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
            <div className="flex space-x-4">
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  name="cashPaymentMethod"
                  value="cash"
                  checked={formData.cashPaymentMethod === 'cash'}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-teal-600 border-gray-300 focus:ring-teal-500"
                />
                <span className="ml-2 text-gray-700">Cash</span>
              </label>
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  name="cashPaymentMethod"
                  value="mpesa"
                  checked={formData.cashPaymentMethod === 'mpesa'}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-teal-600 border-gray-300 focus:ring-teal-500"
                />
                <span className="ml-2 text-gray-700">MPESA</span>
              </label>
            </div>
          </div>

          {/* Cash Payment - Delivery Options */}
          {formData.cashPaymentMethod === 'cash' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Delivery Option</label>
              <div className="flex space-x-4">
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    name="deliveryOption"
                    value="at_shop"
                    checked={formData.deliveryOption === 'at_shop'}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-teal-600 border-gray-300 focus:ring-teal-500"
                  />
                  <span className="ml-2 text-gray-700">At Shop</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    name="deliveryOption"
                    value="delivered"
                    checked={formData.deliveryOption === 'delivered'}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-teal-600 border-gray-300 focus:ring-teal-500"
                  />
                  <span className="ml-2 text-gray-700">Delivered</span>
                </label>
              </div>
            </div>
          )}

          {/* Client Type Selection (for Cash Payment) */}
          {formData.cashPaymentMethod === 'cash' && formData.deliveryOption && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Client Type</label>
              <div className="flex space-x-4">
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    name="clientType"
                    value="regular"
                    checked={formData.clientType === 'regular'}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-teal-600 border-gray-300 focus:ring-teal-500"
                  />
                  <span className="ml-2 text-gray-700">Regular Client</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    name="clientType"
                    value="one_off"
                    checked={formData.clientType === 'one_off'}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-teal-600 border-gray-300 focus:ring-teal-500"
                  />
                  <span className="ml-2 text-gray-700">One-off Client</span>
                </label>
              </div>
            </div>
          )}

          {/* Regular Client Selection */}
          {((formData.cashPaymentMethod === 'cash' && formData.deliveryOption && formData.clientType === 'regular') ||
            (formData.cashPaymentMethod === 'mpesa')) && (
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Select Client
              </label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search clients by name or phone..."
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setIsClientDropdownOpen(true);
                  }}
                  onFocus={() => setIsClientDropdownOpen(true)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
              </div>

              {/* Client Dropdown */}
              {isClientDropdownOpen && filteredClients.length > 0 && (
                <div className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md max-h-60 overflow-auto">
                  <ul className="py-1">
                    {filteredClients.map((client) => (
                      <li
                        key={client.id}
                        onClick={() => selectClient(client)}
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                      >
                        <div className="flex justify-between">
                          <div>
                            <span className="font-medium">{client.name}</span>
                            <span className="text-sm text-gray-500 ml-2">
                              ({client.client_type})
                            </span>
                          </div>
                          <span className="text-sm text-gray-500">{client.phone_number}</span>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Walk-in Customer Button */}
              <div className="mt-3">
                <button
                  type="button"
                  onClick={() => setIsWalkInModalOpen(true)}
                  className="w-full flex items-center justify-center px-4 py-2 border border-teal-300 text-teal-700 bg-teal-50 rounded-md hover:bg-teal-100 focus:outline-none focus:ring-2 focus:ring-teal-500"
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  Walk-in Customer
                </button>
                <p className="mt-1 text-xs text-gray-500 text-center">
                  Register a new customer or find existing by phone number
                </p>
              </div>
            </div>
          )}

          {/* One-off Client Details */}
          {formData.cashPaymentMethod === 'cash' && formData.deliveryOption && formData.clientType === 'one_off' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Client Name (Optional)
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    name="oneOffClientName"
                    value={formData.oneOffClientName || ''}
                    onChange={handleInputChange}
                    placeholder="Enter client name"
                    className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Client Phone Number (Required)
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    name="oneOffClientPhone"
                    value={formData.oneOffClientPhone || ''}
                    onChange={handleInputChange}
                    placeholder="Enter phone number"
                    className="pl-10 w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  />
                </div>
              </div>
            </div>
          )}

          {/* MPESA Payment Options */}
          {formData.cashPaymentMethod === 'mpesa' && (
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                <h4 className="font-medium text-gray-900 mb-2">M-PESA Payment</h4>
                <p className="text-sm text-gray-600 mb-2">
                  When you proceed to the next step, the client will receive an M-PESA payment prompt on their phone.
                </p>
                <p className="text-sm text-gray-600">
                  Make sure the client's phone number is correct and they have sufficient funds in their M-PESA account.
                </p>
              </div>

              {/* Manual MPESA Confirmation (Alternative) */}
              <div className="pt-2">
                <div className="flex items-center mb-2">
                  <hr className="flex-grow border-gray-200" />
                  <span className="px-2 text-sm text-gray-500">OR</span>
                  <hr className="flex-grow border-gray-200" />
                </div>

                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Manual M-PESA Confirmation Message
                </label>
                <textarea
                  name="mpesaConfirmation"
                  value={formData.mpesaConfirmation || ''}
                  onChange={handleInputChange}
                  placeholder="If the client has already paid, paste the complete M-PESA confirmation message here"
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                ></textarea>
                <p className="mt-1 text-sm text-gray-500">
                  Only use this if the client has already paid and you have received the M-PESA confirmation message.
                </p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Credit Sale Options */}
      {formData.saleType === 'credit' && (
        <div className="space-y-4">
          <div className="relative">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Select Credit Client
            </label>
            <div className="relative">
              <input
                type="text"
                placeholder="Search credit clients..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setIsClientDropdownOpen(true);
                }}
                onFocus={() => setIsClientDropdownOpen(true)}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
            </div>

            {/* Client Dropdown */}
            {isClientDropdownOpen && filteredClients.length > 0 && (
              <div className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md max-h-60 overflow-auto">
                <ul className="py-1">
                  {filteredClients.map((client) => (
                    <li
                      key={client.id}
                      onClick={() => selectClient(client)}
                      className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                    >
                      <div className="flex justify-between">
                        <div>
                          <span className="font-medium">{client.name}</span>
                          <span className="text-sm text-gray-500 ml-2">
                            (Credit Client)
                          </span>
                        </div>
                        <span className="text-sm text-gray-500">{client.phone_number}</span>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {isClientDropdownOpen && filteredClients.length === 0 && searchTerm && (
              <div className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md">
                <div className="px-4 py-2 text-sm text-gray-500">
                  No credit clients found matching "{searchTerm}"
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-between pt-4">
        <button
          type="button"
          onClick={onBack}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          Back
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
        >
          Next
        </button>
      </div>

      {/* Walk-in Customer Modal */}
      <WalkInCustomerModal
        isOpen={isWalkInModalOpen}
        onClose={() => setIsWalkInModalOpen(false)}
        onCustomerSelected={handleWalkInCustomerSelected}
      />
    </form>
  );
};

export default ClientDetailsStep;
