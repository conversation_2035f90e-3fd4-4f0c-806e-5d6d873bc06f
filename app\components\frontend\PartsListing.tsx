'use client';

import React, { useState, useEffect, useRef } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Heart, Phone, MessageCircle, Filter, ChevronDown,
  ChevronsLeft, ChevronLeft, ChevronRight, ChevronsRight
} from 'lucide-react';
import WhatsAppModal from './WhatsAppModal';
import MobileProductCard from './MobileProductCard';
import { generateProductSlug } from '@/app/utils/slugify';
import { getAdjustedPrice } from '@/app/utils/priceAdjustment';
import CarFilterDropdown, { CarFilter } from './CarFilterDropdown';
import CarFilterBanner from './CarFilterBanner';
import Cookies from 'js-cookie';

interface Part {
  id: string;
  title: string;
  partNumber: string;
  stock: number;
  price: number;
  discountedPrice?: number;
  thumbnailUrl: string;
}

interface PartsListingProps {
  categoryId?: number;
  searchQuery?: string;
  initialPage?: number;
  initialSort?: string;
  initialCarFilter?: CarFilter;
}

const PartsListing: React.FC<PartsListingProps> = ({
  categoryId,
  searchQuery,
  initialPage = 1,
  initialSort = 'featured',
  initialCarFilter
}) => {
  const [parts, setParts] = useState<Part[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [favorites, setFavorites] = useState<Record<string, boolean>>({});
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState(initialSort);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [totalPages, setTotalPages] = useState(1);
  const [categoryName, setCategoryName] = useState<string>('');
  const prevCategoryIdRef = useRef<number | undefined>(categoryId);
  const [isWhatsAppModalOpen, setIsWhatsAppModalOpen] = useState(false);
  const [selectedPart, setSelectedPart] = useState<Part | null>(null);

  // Car filter state - initialize from prop if provided, otherwise from cookie
  const [carFilter, setCarFilter] = useState<CarFilter | null>(initialCarFilter || null);

  const router = useRouter();
  const searchParams = useSearchParams();

  // Debug initial values
  console.log('Initial values - page:', initialPage, 'sort:', initialSort, 'categoryId:', categoryId, 'searchQuery:', searchQuery, 'initialCarFilter:', initialCarFilter);

  // Load car filter from cookie on component mount if not provided via props
  useEffect(() => {
    // Skip loading from cookie if initialCarFilter was provided
    if (initialCarFilter) {
      console.log('Using initialCarFilter from props:', initialCarFilter);
      return;
    }

    const savedFilter = Cookies.get('carFilter');
    console.log('Checking for carFilter cookie on component mount:', savedFilter);

    if (savedFilter) {
      try {
        // Check if the cookie value is already a JSON object (starts with '{')
        const parsedFilter = savedFilter.startsWith('{')
          ? JSON.parse(savedFilter)
          : JSON.parse(decodeURIComponent(savedFilter));

        console.log('Loaded car filter from cookie:', parsedFilter);

        // Validate the filter has the expected structure
        if (parsedFilter && (
          parsedFilter.brandId ||
          parsedFilter.modelId ||
          parsedFilter.generationId ||
          parsedFilter.variationId ||
          parsedFilter.trimId
        )) {
          setCarFilter(parsedFilter);
        } else {
          console.warn('Invalid car filter structure in cookie:', parsedFilter);
          // Remove cookie with explicit path to ensure it's removed from all paths
          Cookies.remove('carFilter', { path: '/' });
          setCarFilter(null);
        }
      } catch (err) {
        console.error('Error parsing saved car filter:', err);
        // Remove cookie with explicit path to ensure it's removed from all paths
        Cookies.remove('carFilter', { path: '/' });
        setCarFilter(null);
      }
    } else {
      // Ensure carFilter state is null when no cookie is found
      setCarFilter(null);
    }
  }, [initialCarFilter]);

  // Fetch category name when categoryId changes
  useEffect(() => {
    const fetchCategoryName = async () => {
      if (categoryId) {
        try {
          const supabase = createClient();
          const { data, error } = await supabase
            .from('car_part_categories')
            .select('label')
            .eq('id', categoryId)
            .single();

          if (error) {
            console.error('Error fetching category name:', error);
            setCategoryName('');
          } else if (data) {
            setCategoryName(data.label);
          }
        } catch (err) {
          console.error('Error fetching category name:', err);
          setCategoryName('');
        }
      } else {
        setCategoryName('');
      }
    };

    fetchCategoryName();

    // Reset to page 1 when category changes (but not on initial render)
    if (categoryId !== prevCategoryIdRef.current && prevCategoryIdRef.current !== undefined) {
      console.log('Category changed in PartsListing from', prevCategoryIdRef.current, 'to', categoryId);
      setCurrentPage(1);
    }

    // Update the ref with current category ID
    prevCategoryIdRef.current = categoryId;
  }, [categoryId]);

  // Handle car filter changes
  const handleCarFilterChange = (filter: CarFilter | null) => {
    setCarFilter(filter);
    setCurrentPage(1); // Reset to first page when filter changes

    // Get current search params to preserve other parameters
    const params = new URLSearchParams(searchParams.toString());

    // If filter is null (being cleared), remove the cookie and URL parameters
    if (filter === null) {
      console.log('Removing carFilter cookie and URL parameters');
      // Remove cookie with explicit path to ensure it's removed from all paths
      Cookies.remove('carFilter', { path: '/' });

      // Remove car filter parameters from URL
      params.delete('brandId');
      params.delete('modelId');
      params.delete('generationId');
      params.delete('variationId');
      params.delete('trimId');
      params.delete('brandName');
      params.delete('modelName');
      params.delete('generationName');
      params.delete('variationName');
      params.delete('trimName');

      // Reset to page 1
      params.set('page', '1');

      // Update the URL
      router.push(`/shop?${params.toString()}`);

      // Verify cookie was removed
      setTimeout(() => {
        const cookieValue = Cookies.get('carFilter');
        console.log('After removal, carFilter cookie value:', cookieValue);
      }, 100);
    } else {
      // Add car filter parameters to URL
      if (filter.brandId) params.set('brandId', filter.brandId.toString());
      if (filter.modelId) params.set('modelId', filter.modelId.toString());
      if (filter.generationId) params.set('generationId', filter.generationId.toString());
      if (filter.variationId) params.set('variationId', filter.variationId.toString());
      if (filter.trimId) params.set('trimId', filter.trimId.toString());

      // Add names for display purposes
      if (filter.brandName) params.set('brandName', filter.brandName);
      if (filter.modelName) params.set('modelName', filter.modelName);
      if (filter.generationName) params.set('generationName', filter.generationName);
      if (filter.variationName) params.set('variationName', filter.variationName);
      if (filter.trimName) params.set('trimName', filter.trimName);

      // Reset to page 1
      params.set('page', '1');

      // Update the URL
      router.push(`/shop?${params.toString()}`);
    }
  };

  // Cache for API responses to avoid redundant requests
  const [apiCache, setApiCache] = useState<Record<string, any>>({});

  // Use a ref to access the latest cache state in the cleanup function
  const apiCacheRef = useRef<Record<string, any>>({});

  // Keep the ref in sync with the state
  useEffect(() => {
    apiCacheRef.current = apiCache;
  }, [apiCache]);

  // Cache expiration time in milliseconds (5 minutes)
  const CACHE_EXPIRATION = 5 * 60 * 1000;

  // Maximum number of items to keep in cache
  const MAX_CACHE_SIZE = 20;

  // Clean up old cache entries periodically
  useEffect(() => {
    const cleanupCache = () => {
      const now = Date.now();
      const currentCache = apiCacheRef.current;
      const cacheEntries = Object.entries(currentCache);

      // If cache is smaller than max size, only remove expired entries
      if (cacheEntries.length <= MAX_CACHE_SIZE) {
        const updatedCache = { ...currentCache };
        let hasExpired = false;

        // Remove expired entries
        for (const [key, value] of cacheEntries) {
          if (value.timestamp && (now - value.timestamp > CACHE_EXPIRATION)) {
            delete updatedCache[key];
            hasExpired = true;
          }
        }

        // Only update state if we removed something
        if (hasExpired) {
          console.log('Cleaned up expired cache entries');
          setApiCache(updatedCache);
        }
      } else {
        // If cache is larger than max size, keep only the most recent entries
        console.log('Cache size limit reached, pruning oldest entries');

        // Sort entries by timestamp (newest first)
        const sortedEntries = cacheEntries.sort((a, b) =>
          (b[1].timestamp || 0) - (a[1].timestamp || 0)
        );

        // Keep only the most recent entries
        const newCache: Record<string, any> = {};
        sortedEntries.slice(0, MAX_CACHE_SIZE).forEach(([key, value]) => {
          newCache[key] = value;
        });

        setApiCache(newCache);
      }
    };

    // Run cleanup when component mounts
    cleanupCache();

    // Also set up an interval to clean up periodically
    const interval = setInterval(cleanupCache, CACHE_EXPIRATION);

    return () => clearInterval(interval);

    // We're using a ref to avoid the dependency on apiCache which would cause infinite loops
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Fetch parts using the appropriate API
  useEffect(() => {
    const fetchParts = async () => {
      setIsLoading(true);
      setError(null);

      try {
        let apiUrl: URL;
        let cacheKey: string;

        // If we have a car filter, use the filter-by-car API
        if (carFilter && (carFilter.brandId || carFilter.modelId || carFilter.generationId || carFilter.variationId || carFilter.trimId)) {
          apiUrl = new URL('/api/parts/filter-by-car', window.location.origin);

          // Add car filter parameters - use the most specific filter available
          if (carFilter.trimId) {
            apiUrl.searchParams.append('trimId', carFilter.trimId.toString());
          } else if (carFilter.variationId) {
            apiUrl.searchParams.append('variationId', carFilter.variationId.toString());
          } else if (carFilter.generationId) {
            apiUrl.searchParams.append('generationId', carFilter.generationId.toString());
          } else if (carFilter.modelId) {
            apiUrl.searchParams.append('modelId', carFilter.modelId.toString());
          } else if (carFilter.brandId) {
            apiUrl.searchParams.append('brandId', carFilter.brandId.toString());
          }

          // Add category filter if provided
          if (categoryId) {
            apiUrl.searchParams.append('category', categoryId.toString());
          }
        } else {
          // Otherwise use the regular search API
          apiUrl = new URL('/api/parts/search', window.location.origin);

          // Add query parameters
          if (searchQuery) {
            apiUrl.searchParams.append('query', searchQuery);
          }

          // Add category filter if provided
          if (categoryId) {
            apiUrl.searchParams.append('filter', `category:${categoryId}`);
          }
        }

        // Common parameters for both APIs
        apiUrl.searchParams.append('page', currentPage.toString());
        apiUrl.searchParams.append('limit', '12'); // Page size
        apiUrl.searchParams.append('sort', sortBy);

        // Create a cache key based on the URL
        cacheKey = apiUrl.toString();
        console.log('Fetching parts from API:', cacheKey);

        // Check if we have a valid cached response for this exact request
        const cachedData = apiCache[cacheKey];
        const now = Date.now();

        if (cachedData && cachedData.timestamp && (now - cachedData.timestamp < CACHE_EXPIRATION)) {
          console.log('Using cached response (age: ' + ((now - cachedData.timestamp) / 1000).toFixed(1) + ' seconds)');
          setParts(cachedData.parts);
          setTotalPages(cachedData.totalPages || 1);
          setIsLoading(false);
          return;
        } else if (cachedData) {
          console.log('Cache expired, fetching fresh data');
        }

        // Make the API request
        const response = await fetch(cacheKey);

        if (!response.ok) {
          console.error(`API request failed with status ${response.status}`);

          // Try to get more detailed error information from the response
          try {
            const errorData = await response.json();
            console.error('API error details:', errorData);

            // Use the error message from the API if available
            if (errorData && errorData.error) {
              setError(`Error: ${errorData.error}`);
            } else if (errorData && errorData.message) {
              setError(errorData.message);
            } else {
              setError(carFilter ? 'No parts found for the selected car.' : 'Failed to load parts. Please try again later.');
            }
          } catch (parseError) {
            // If we can't parse the error response, use a generic message
            console.error('Failed to parse error response:', parseError);
            setError(carFilter ? 'No parts found for the selected car.' : 'Failed to load parts. Please try again later.');
          }

          // Don't throw an error, just set empty parts and show a message
          setParts([]);
          setTotalPages(1);
          setIsLoading(false);
          return;
        }

        const data = await response.json();
        console.log('API response:', data);

        if (!data.parts || data.parts.length === 0) {
          console.log('No parts found in the API response');
          setParts([]);
          setTotalPages(1);

          // Use the message from the API if available
          if (data.message && carFilter) {
            setError(data.message);
          }
          // Otherwise set a user-friendly error message based on the filter context
          else if (carFilter) {
            setError('No parts found for the selected car. Try a different car or category.');
          } else if (categoryId) {
            setError('No parts found in this category.');
          } else if (searchQuery) {
            setError(`No parts found for "${searchQuery}". Try a different search term.`);
          } else {
            setError('No parts found. Please try different filters.');
          }

          return;
        } else {
          // Check if we're showing category parts without car compatibility
          if (data.showingCategoryPartsOnly && data.message) {
            // Show a warning message but still display the parts
            setError(data.message);
          } else {
            // Clear any previous error if we have parts
            setError(null);
          }
        }

        // Set total pages from the API response
        const totalPages = data.totalPages || 1;
        console.log(`Setting totalPages to ${totalPages} (from API response: ${data.totalPages})`);
        setTotalPages(totalPages);

        // Transform the data if needed
        const transformedParts = data.parts.map((part: any) => ({
          id: part.id,
          title: part.title || 'Unnamed Part',
          partNumber: part.actualPartNumber || part.partNumber || 'Unknown',
          stock: part.stock || 0,
          price: part.price || 0,
          discountedPrice: part.discountedPrice,
          thumbnailUrl: part.thumbnailUrl || part.imageUrl || '/images/placeholder.jpg',
          relevanceScore: part.relevanceScore // Include relevance score from advanced search
        }));

        // Store the response in the cache
        setApiCache(prevCache => ({
          ...prevCache,
          [cacheKey]: {
            parts: transformedParts,
            totalPages: totalPages,
            timestamp: Date.now()
          }
        }));

        console.log('Transformed parts:', transformedParts);
        setParts(transformedParts);
      } catch (err) {
        console.error('Error fetching parts:', err);
        // Provide more detailed error information
        if (err instanceof Error) {
          setError(`Failed to load parts: ${err.message}`);
        } else {
          setError('Failed to load parts. Please try again.');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchParts();
  }, [categoryId, searchQuery, sortBy, currentPage, carFilter]);

  // Toggle favorite status
  const toggleFavorite = (id: string) => {
    setFavorites(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // Handle sort change
  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSortBy = e.target.value;
    setSortBy(newSortBy);

    // Reset to first page when sorting changes
    setCurrentPage(1);

    // Update URL with the new sort and reset page to 1
    const params = new URLSearchParams(searchParams.toString());
    params.set('sort', newSortBy);
    params.set('page', '1');

    // Preserve category and search query if they exist
    if (categoryId) {
      params.set('category', categoryId.toString());
    }
    if (searchQuery) {
      params.set('query', searchQuery);
    }

    // Update the URL without refreshing the page
    router.push(`/shop?${params.toString()}`);
  };

  // Use searchParams to preserve them when changing page

  // Handle page change
  const handlePageChange = (page: number) => {
    // Update state
    setCurrentPage(page);

    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });

    // Update URL with the new page number
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', page.toString());

    // Preserve category and search query if they exist
    if (categoryId) {
      params.set('category', categoryId.toString());
    }
    if (searchQuery) {
      params.set('query', searchQuery);
    }

    // Update the URL without refreshing the page
    router.push(`/shop?${params.toString()}`);
  };

  // Debug output
  useEffect(() => {
    console.log('Current totalPages state:', totalPages);
  }, [totalPages]);

  // Debug component render
  console.log('Component rendering, currentPage:', currentPage, 'totalPages:', totalPages);

  return (
    <div className="container mx-auto px-4 py-8 bg-white">
      {/* Header with count and filters */}
      {/* Car Filter Banner */}
      <AnimatePresence>
        {carFilter && (
          <CarFilterBanner
            filter={carFilter}
            onClearFilter={() => handleCarFilterChange(null)}
          />
        )}
      </AnimatePresence>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4 md:mb-0">
          {searchQuery
            ? `Search Results for "${searchQuery}"`
            : categoryId && categoryName
              ? `${categoryName} Parts`
              : 'All Parts'}
        </h1>

        <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
          <CarFilterDropdown
            onFilterApply={handleCarFilterChange}
            initialFilter={carFilter}
          />
        </div>
      </div>



      {/* Parts count and pagination info */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <p className="text-gray-600">
          {isLoading ? 'Loading...' : `Showing ${parts.length} parts`}
        </p>

        {/* Pagination info - only show when there's more than one page */}
        {totalPages > 1 && (
          <div className="flex items-center mt-2 sm:mt-0">
            <p className="text-gray-600 px-3 py-1 border border-gray-300 rounded-md bg-white">
              Page {currentPage} of {totalPages}
            </p>
          </div>
        )}
      </div>

      {/* Parts Grid */}
      {isLoading ? (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
              <div className="h-48 bg-gray-200"></div>
              <div className="p-4">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3 mb-2"></div>
                <div className="h-6 bg-gray-200 rounded w-1/3 mt-4"></div>
              </div>
            </div>
          ))}
        </div>
      ) : error && parts.length === 0 ? (
        // Error with no parts
        <div className="text-center py-12">
          <p className="text-red-500 mb-2">{error}</p>
          {carFilter && (
            <div className="mt-4 flex flex-col items-center">
              <p className="text-gray-600 mb-4">
                We couldn't find any parts compatible with your selected vehicle:
                <br />
                <span className="font-semibold">
                  {[
                    carFilter.brandName,
                    carFilter.modelName,
                    carFilter.generationName,
                    carFilter.variationName,
                    carFilter.trimName
                  ].filter(Boolean).join(' ')}
                </span>
              </p>
              <button
                onClick={() => handleCarFilterChange(null)}
                className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
              >
                Clear Car Filter
              </button>
            </div>
          )}
          {!carFilter && (
            <button
              onClick={() => setCurrentPage(1)}
              className="mt-4 px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
            >
              Try Again
            </button>
          )}
        </div>
      ) : error && parts.length > 0 ? (
        // Warning message with parts (showing category parts without car compatibility)
        <div>
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-yellow-700">{error}</p>
            {carFilter && (
              <button
                onClick={() => handleCarFilterChange(null)}
                className="mt-2 px-3 py-1 bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200 text-sm"
              >
                Clear Car Filter
              </button>
            )}
          </div>

          {/* Show parts grid even though there's a warning */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
            {parts.map((part) => (
              <motion.div
                key={part.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                {/* Mobile View */}
                <div className="block md:hidden">
                  <MobileProductCard
                    id={part.id}
                    title={part.title}
                    price={part.price}
                    discountedPrice={part.discountedPrice}
                    thumbnailUrl={part.thumbnailUrl}
                  />
                </div>

                {/* Desktop View */}
                <div className="hidden md:block bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg h-full">
                  {/* Part Image */}
                  <div className="relative h-48 bg-gray-50">
                    <Link href={`/shop/product/${generateProductSlug(part.title, part.id)}`}>
                      <div className="relative h-full w-full">
                        <Image
                          src={part.thumbnailUrl}
                          alt={part.title}
                          fill
                          className="object-cover"
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        />
                      </div>
                    </Link>
                  </div>

                  {/* Part Info */}
                  <div className="p-4">
                    <Link href={`/shop/product/${generateProductSlug(part.title, part.id)}`}>
                      <h3 className="text-sm font-medium text-gray-900 mb-1 line-clamp-2 hover:text-teal-600">
                        {part.title}
                      </h3>
                    </Link>

                    {/* Part Number */}
                    <p className="text-xs text-gray-500 mb-2">
                      Part #: {part.partNumber}
                    </p>

                    {/* Price */}
                    <div className="flex items-center justify-between">
                      <div>
                        {part.discountedPrice ? (
                          <div className="flex items-center">
                            <span className="text-sm font-semibold text-gray-900">
                              Kshs {getAdjustedPrice(part.discountedPrice).toLocaleString()}
                            </span>
                            <span className="text-xs text-gray-500 line-through ml-2">
                              Kshs {getAdjustedPrice(part.price).toLocaleString()}
                            </span>
                          </div>
                        ) : (
                          <span className="text-sm font-semibold text-gray-900">
                            Kshs {getAdjustedPrice(part.price).toLocaleString()}
                          </span>
                        )}
                      </div>

                      {/* Contact Buttons */}
                      <div className="flex space-x-2">
                        {/* Call Button */}
                        <a
                          href="tel:+254724288400"
                          className="p-1.5 bg-blue-500 text-white rounded-full hover:bg-blue-600"
                          title="Call +254724288400"
                        >
                          <Phone size={16} />
                        </a>

                        {/* WhatsApp Button */}
                        <button
                          className="p-1.5 bg-green-500 text-white rounded-full hover:bg-green-600"
                          title="Contact via WhatsApp"
                          onClick={() => {
                            setSelectedPart(part);
                            setIsWhatsAppModalOpen(true);
                          }}
                        >
                          <MessageCircle size={16} />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      ) : parts.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-600 mb-2">No parts found</p>
          <p className="text-gray-500">Try adjusting your search criteria</p>
          {carFilter && (
            <button
              onClick={() => handleCarFilterChange(null)}
              className="mt-4 px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
            >
              Clear Car Filter
            </button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
          {parts.map((part) => (
            <motion.div
              key={part.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {/* Mobile View - Similar to the image provided */}
              <div className="block md:hidden">
                <MobileProductCard
                  id={part.id}
                  title={part.title}
                  price={part.price}
                  discountedPrice={part.discountedPrice}
                  thumbnailUrl={part.thumbnailUrl}
                />
              </div>

              {/* Desktop View - Original card design */}
              <div className="hidden md:block bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg h-full">
                {/* Part Image */}
                <div className="relative h-48 bg-gray-50">
                  <Link href={`/shop/product/${generateProductSlug(part.title, part.id)}`}>
                    <div className="relative h-full w-full">
                      <Image
                        src={part.thumbnailUrl}
                        alt={part.title}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      />
                    </div>
                  </Link>
                </div>

                {/* Part Info */}
                <div className="p-4">
                  <Link href={`/shop/product/${generateProductSlug(part.title, part.id)}`}>
                    <h3 className="text-sm font-medium text-gray-900 mb-1 line-clamp-2 hover:text-teal-600">
                      {part.title}
                    </h3>
                  </Link>

                  {/* Part Number */}
                  <p className="text-xs text-gray-500 mb-2">
                    Part #: {part.partNumber}
                  </p>

                  {/* Price */}
                  <div className="flex items-center justify-between">
                    <div>
                      {part.discountedPrice ? (
                        <div className="flex items-center">
                          <span className="text-sm font-semibold text-gray-900">
                            Kshs {getAdjustedPrice(part.discountedPrice).toLocaleString()}
                          </span>
                          <span className="text-xs text-gray-500 line-through ml-2">
                            Kshs {getAdjustedPrice(part.price).toLocaleString()}
                          </span>
                        </div>
                      ) : (
                        <span className="text-sm font-semibold text-gray-900">
                          Kshs {getAdjustedPrice(part.price).toLocaleString()}
                        </span>
                      )}
                    </div>

                    {/* Contact Buttons */}
                    <div className="flex space-x-2">
                      {/* Call Button */}
                      <a
                        href="tel:+254724288400"
                        className="p-1.5 bg-blue-500 text-white rounded-full hover:bg-blue-600"
                        title="Call +254724288400"
                      >
                        <Phone size={16} />
                      </a>

                      {/* WhatsApp Button */}
                      <button
                        className="p-1.5 bg-green-500 text-white rounded-full hover:bg-green-600"
                        title="Contact via WhatsApp"
                        onClick={() => {
                          setSelectedPart(part);
                          setIsWhatsAppModalOpen(true);
                        }}
                      >
                        <MessageCircle size={16} />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Pagination - Only show when there's more than one page */}
      {!isLoading && !error && parts.length > 0 && totalPages > 1 && (
        <div className="mt-8 flex justify-center">
          <div className="flex flex-wrap gap-1 p-2 md:p-4 bg-gray-100 rounded-lg border border-gray-300">
            {/* First Page Button - Hidden on first page */}
            {currentPage > 1 && (
              <button
                onClick={() => handlePageChange(1)}
                className="px-2 md:px-3 py-1 md:py-2 border border-gray-300 rounded-md bg-white text-gray-700"
                aria-label="First page"
              >
                <ChevronsLeft size={16} />
              </button>
            )}

            {/* Previous Button - Hidden on first page */}
            {currentPage > 1 && (
              <button
                onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                className="px-2 md:px-3 py-1 md:py-2 border border-gray-300 rounded-md bg-white text-gray-700"
                aria-label="Previous page"
              >
                <ChevronLeft size={16} />
              </button>
            )}

            {/* Page Numbers */}
            {Array.from({ length: Math.min(5, Math.max(1, totalPages)) }, (_, i) => {
              // Calculate the range of page numbers to show
              let startPage = Math.max(1, currentPage - 2);
              const endPage = Math.min(startPage + 4, Math.max(1, totalPages));

              // Adjust startPage if we're near the end
              if (endPage - startPage < 4) {
                startPage = Math.max(1, endPage - 4);
              }

              // Calculate the actual page number to display
              const page = startPage + i;

              // Don't show page numbers beyond totalPages
              if (page > totalPages && totalPages > 0) return null;

              return (
                <button
                  key={page}
                  onClick={() => handlePageChange(page)}
                  className={`px-2 md:px-3 py-1 md:py-2 border rounded-md ${
                    currentPage === page
                      ? 'bg-teal-600 text-white border-teal-600'
                      : 'bg-white text-gray-700 border-gray-300'
                  }`}
                >
                  {page}
                </button>
              );
            })}

            {/* Next Button - Hidden on last page */}
            {(currentPage < totalPages || totalPages === 0) && (
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                className="px-2 md:px-3 py-1 md:py-2 border border-gray-300 rounded-md bg-white text-gray-700"
                aria-label="Next page"
              >
                <ChevronRight size={16} />
              </button>
            )}

            {/* Last Page Button - Hidden on last page or if totalPages is unknown */}
            {currentPage < totalPages && totalPages > 0 && (
              <button
                onClick={() => handlePageChange(totalPages)}
                className="px-2 md:px-3 py-1 md:py-2 border border-gray-300 rounded-md bg-white text-gray-700"
                aria-label="Last page"
              >
                <ChevronsRight size={16} />
              </button>
            )}
          </div>
        </div>
      )}

      {/* WhatsApp Modal */}
      {isWhatsAppModalOpen && selectedPart && (
        <WhatsAppModal

          onClose={() => setIsWhatsAppModalOpen(false)}
          partTitle={selectedPart.title}
          partImage={selectedPart.thumbnailUrl}
          partId={selectedPart.id}
        />
      )}
    </div>
  );
};

export default PartsListing;