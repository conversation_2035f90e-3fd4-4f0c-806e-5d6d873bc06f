import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Check, Square } from 'lucide-react';

interface CheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label?: string;
  className?: string;
  onCheckedChange?: (checked: boolean) => void; // Added onCheckedChange prop
}

const Checkbox: React.FC<CheckboxProps> = ({
  checked,
  onChange,
  label,
  className = '',
  onCheckedChange, // Destructured onCheckedChange prop
}) => {
  const handleToggle = useCallback(() => {
    const newChecked = !checked;
    onChange(newChecked);
    if (onCheckedChange) {
      onCheckedChange(newChecked); // Call onCheckedChange with the new checked value
    }
  }, [checked, onChange, onCheckedChange]);

  return (
    <button
      className={`group relative inline-flex items-center justify-center rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 ${className}`}
      role="checkbox"
      aria-checked={checked}
      onClick={handleToggle}
    >
      <motion.div
        className="peer-focus:ring-2 peer-focus:ring-ring dark:peer-focus:ring-offset-gray-900" // Example focus ring if needed
        style={{ borderRadius: '0.375rem' }} // Tailwind's `rounded-md` equivalent for motion.div
      >
        <motion.div
          className="bg-transparent border-2 border-gray-300 dark:border-gray-600 rounded-md w-6 h-6 flex items-center justify-center cursor-pointer group-active:scale-95 transition-transform"
          style={{ backgroundColor: checked ? '#3B82F6' : 'transparent', borderColor: checked ? '#3B82F6' : 'border-gray-300 dark:border-gray-600' }} // Dynamic background and border
          transition={{ type: "spring", stiffness: 200, damping: 20 }}
        >
          <AnimatePresence mode="wait">
            {checked ? (
              <motion.div
                key="check"
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.5 }}
                transition={{ type: "spring", stiffness: 200, damping: 20 }}
              >
                <Check className="w-4 h-4 text-white" />
              </motion.div>
            ) : (
              <motion.div
                key="square"
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.5 }}
                transition={{ type: "spring", stiffness: 200, damping: 20 }}
              >
                <Square className="w-4 h-4 text-gray-500 dark:text-gray-400" />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </motion.div>
      {label && <span className="ml-2 text-gray-700 dark:text-gray-200">{label}</span>}
    </button>
  );
};

export default Checkbox;