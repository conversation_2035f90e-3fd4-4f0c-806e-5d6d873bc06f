'use client';

import React from 'react';
import Link from 'next/link';

const ClientPortalFooter: React.FC = () => {
  return (
    <footer className="bg-white border-t border-gray-200 py-4">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <p className="text-sm text-gray-500">
              &copy; {new Date().getFullYear()} Autoflow. All rights reserved.
            </p>
          </div>
          
          <div className="flex space-x-6">
            <Link href="/client/terms" className="text-sm text-gray-500 hover:text-gray-900">
              Terms of Service
            </Link>
            <Link href="/client/privacy" className="text-sm text-gray-500 hover:text-gray-900">
              Privacy Policy
            </Link>
            <Link href="/client/help" className="text-sm text-gray-500 hover:text-gray-900">
              Help Center
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default ClientPortalFooter;
