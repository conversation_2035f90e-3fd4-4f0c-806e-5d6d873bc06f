import React from 'react';
import { BrandModelSelectionProps } from '../../types';
import { Select } from '@/app/components/ui/inputs/Select';
import AnimatedEllipsisLoader from '@/app/components/ui/AnimatedEllipsisLoader';
import { useFormContext } from 'react-hook-form';

export default function VehicleSelection({
  control,
  watch,
  loadingStates,
  onSelectionChange,
  error
}: BrandModelSelectionProps) {
  const brandId = watch('brandId');
  const modelId = watch('modelId');
  const generationId = watch('generationId');
  const variationId = watch('variationId');

  const handleItemSelection = (field: string, value: string, name?: string, years?: string) => {
    console.log(`VehicleSelection: ${field} selected:`, { value, name, years });
    
    try {
      onSelectionChange(field, value, name, years);
      
      if (name) {
        console.log(`Setting ${field}Name to:`, name);
      }
      
      if (years) {
        console.log(`Setting ${field}Years to:`, years);
      }
    } catch (error) {
      console.error('Error in handleItemSelection:', error);
    }
  };

  return (
    <div className="space-y-4">
      <div className="relative">
        <Select
          label="Brand"
          name="brandId"
          control={control}
          endpoint="/api/car/brands"
          loading={loadingStates.brands}
          onChange={(value: string, name?: string) => {
            handleItemSelection('brandId', value, name);
          }}
        />
        {loadingStates.brands && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            <AnimatedEllipsisLoader text="" textColor="gray-500" />
          </div>
        )}
      </div>

      {brandId && (
        <div className="relative">
          <Select
            label="Model"
            name="modelId"
            control={control}
            endpoint={`/api/car/models?brandId=${brandId}`}
            loading={loadingStates.models}
            onChange={(value: string, name?: string) => {
              handleItemSelection('modelId', value, name);
            }}
          />
          {loadingStates.models && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              <AnimatedEllipsisLoader text="" textColor="gray-500" />
            </div>
          )}
        </div>
      )}

      {modelId && (
        <div className="relative">
          <Select
            label="Generation"
            name="generationId"
            control={control}
            endpoint={`/api/car/generations?modelId=${modelId}`}
            loading={loadingStates.generations}
            onChange={(value: string, name?: string, years?: string) => {
              handleItemSelection('generationId', value, name, years);
            }}
            disabled={!modelId}
          />
          {loadingStates.generations && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              <AnimatedEllipsisLoader text="" textColor="gray-500" />
            </div>
          )}
        </div>
      )}

      {generationId && (
        <div className="relative">
          <Select
            label="Variation"
            name="variationId"
            control={control}
            endpoint={`/api/car/variations?generationId=${generationId}`}
            loading={loadingStates.variations}
            onChange={(value: string, name?: string) => {
              handleItemSelection('variationId', value, name);
            }}
            disabled={!generationId}
          />
          {loadingStates.variations && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              <AnimatedEllipsisLoader text="" textColor="gray-500" />
            </div>
          )}
        </div>
      )}

      {variationId && (
        <div className="relative">
          <Select
            label="Trim"
            name="trimId"
            control={control}
            endpoint={`/api/car/trims?variationId=${variationId}`}
            loading={loadingStates.trims}
            onChange={(value: string, name?: string) => {
              handleItemSelection('trimId', value, name);
            }}
            disabled={!variationId}
          />
          {loadingStates.trims && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              <AnimatedEllipsisLoader text="" textColor="gray-500" />
            </div>
          )}
        </div>
      )}
      
      {error && (
        <div className="text-red-500 text-sm mt-1">{error}</div>
      )}
    </div>
  );
}