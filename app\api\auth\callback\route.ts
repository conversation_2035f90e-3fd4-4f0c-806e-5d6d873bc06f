// app/api/auth/callback/route.ts
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse, type NextRequest } from 'next/server'
import type { Database } from '@/app/types/database'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')

  try {
    if (code) {
      const supabase = createRouteHandlerClient<Database>({
        cookies: async () => await cookies()
      })
      await supabase.auth.exchangeCodeForSession(code)
      
      // Create a proper redirect response with cookies
      const response = NextResponse.redirect(requestUrl.origin + '/verification-success')
      
      // Set success cookie directly in response
      response.cookies.set({
        name: 'verification-success',
        value: 'true',
        maxAge: 60 * 60 * 24,
        path: '/',
      })

      return response
    }
  } catch (error) {
    console.error('Authentication callback error:', error)
  }

  // Redirect to login with error message
  return NextResponse.redirect(
    new URL('/login?error=Authentication+failed', request.url)
  )
}