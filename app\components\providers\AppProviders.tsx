'use client';

import { ReactNode, createContext, useContext, useState } from 'react';
import { Toaster } from 'react-hot-toast';
import { RBACProvider } from '@/app/providers/RBACProvider';
import { RBACErrorBoundary } from './RBACErrorBoundary';

interface AppContextType {
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  error: Error | null;
  setError: (error: Error | null) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}

interface AppProvidersProps {
  children: ReactNode;
}

export default function AppProviders({ children }: AppProvidersProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const value = {
    isLoading,
    setIsLoading,
    error,
    setError,
  };

  return (
    <RBACErrorBoundary>
      <RBACProvider>
        <AppContext.Provider value={value}>
          {children}
          <Toaster
            position="top-center"
            toastOptions={{
              duration: 3000,
              style: {
                background: '#fff',
                color: '#333',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                borderRadius: '0.5rem',
                padding: '1rem',
                zIndex: 9999, // Ensure toasts appear above modals
              },
              success: {
                iconTheme: {
                  primary: '#059669',
                  secondary: '#fff',
                },
              },
              error: {
                iconTheme: {
                  primary: '#dc2626',
                  secondary: '#fff',
                },
              },
            }}
            containerStyle={{
              zIndex: 9999, // Ensure the container also has high z-index
            }}
          />
        </AppContext.Provider>
      </RBACProvider>
    </RBACErrorBoundary>
  );
} 