-- Update the delete_part_and_references function to handle part compatibility groups and part_to_group records
-- This migration adds deletion of related records from part_compatibility_groups and part_to_group tables

-- Create a simplified version that focuses on the essential deletions
CREATE OR REPLACE FUNCTION public.delete_part_and_references(p_part_id integer)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $BODY$
DECLARE
    v_partnumber_group INTEGER;
    v_part_number TEXT;
    v_group_part_count INTEGER;
BEGIN
    -- Get the partnumber_group and partnumber from the part before deletion
    SELECT partnumber_group, partnumber INTO v_partnumber_group, v_part_number
    FROM public.parts
    WHERE part_id = p_part_id;

    -- Delete from related tables in the correct order

    -- 1. Delete from part_price (via parts_condition)
    DELETE FROM public.part_price
    WHERE condition_id IN (SELECT id FROM public.parts_condition WHERE part_id = p_part_id);

    -- 2. Delete from parts_car
    DELETE FROM public.parts_car WHERE part_id = p_part_id;

    -- 3. Delete from parts_engines
    DELETE FROM public.parts_engines WHERE part_id = p_part_id;

    -- 4. Delete from part_images
    DELETE FROM public.part_images WHERE part_id = p_part_id;

    -- 5. Delete from parts_condition
    DELETE FROM public.parts_condition WHERE part_id = p_part_id;

    -- 6. Delete from part_to_group if part has a partnumber
    IF v_part_number IS NOT NULL THEN
        DELETE FROM public.part_to_group WHERE partnumber = v_part_number;
    END IF;

    -- 7. Delete the part itself
    DELETE FROM public.parts WHERE part_id = p_part_id;

    -- 8. Check if the partnumber_group should be deleted (if no other parts reference it)
    IF v_partnumber_group IS NOT NULL THEN
        -- Count how many parts still reference this group
        SELECT COUNT(*) INTO v_group_part_count
        FROM public.parts
        WHERE partnumber_group = v_partnumber_group;

        -- Also check if any part_to_group records reference this group
        IF v_group_part_count = 0 THEN
            SELECT COUNT(*) INTO v_group_part_count
            FROM public.part_to_group
            WHERE group_id = v_partnumber_group;
        END IF;

        -- If no parts or part_to_group records reference this group, delete it
        IF v_group_part_count = 0 THEN
            DELETE FROM public.part_compatibility_groups WHERE id = v_partnumber_group;
        END IF;
    END IF;

END;
$BODY$;

-- Set ownership and permissions
ALTER FUNCTION public.delete_part_and_references(integer) OWNER TO postgres;
GRANT EXECUTE ON FUNCTION public.delete_part_and_references(integer) TO authenticated;
GRANT EXECUTE ON FUNCTION public.delete_part_and_references(integer) TO service_role;
