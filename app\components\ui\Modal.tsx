import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ReactDOM from 'react-dom';

interface ModalProps {
  /** Whether the modal is open or not. */
  isOpen: boolean;
  /** Function to close the modal. */
  onClose: () => void;
  /** The type of animation to use. Defaults to 'fade'. */
  animationType?: 'fade' | 'slide-in-top' | 'slide-in-bottom' | 'slide-in-left' | 'slide-in-right';
  /** The title of the modal. */
  title?: string;
  /** Custom header content. Overrides the title if provided. */
  header?: React.ReactNode;
  /** The body content of the modal. */
  children?: React.ReactNode;
  /** The footer content of the modal. */
  footer?: React.ReactNode;
  /** Duration in seconds after which the modal will automatically close. */
  timer?: number;
  /** Width of the modal. Defaults to '50%' on larger screens and '100%' on mobile screens. You can use tailwind width classes like 'w-1/2', 'w-full', 'w-[300px]' etc. */
  width?: string;
  /** URL to be displayed in an iframe within the modal. */
  url?: string;
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  animationType = 'fade',
  title,
  header,
  children,
  footer,
  timer,
  width = 'w-full md:w-1/2',
  url,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const [iframeLoaded, setIframeLoaded] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // Track if we're running in the browser environment
  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  // Handle body overflow when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  // Auto close timer
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    if (isOpen && timer) {
      timeoutId = setTimeout(onClose, timer * 1000);
    }
    return () => clearTimeout(timeoutId);
  }, [isOpen, onClose, timer]);

  // Debug logging for isOpen state and portal target
  useEffect(() => {
    console.log('Modal isOpen:', isOpen);
    if (isOpen) {
      const portalTarget = document.getElementById('modal-root') || document.body;
      console.log('Modal portal target exists:', !!portalTarget);
    }
  }, [isOpen]);

  // Only render in browser environment
  if (!isMounted) return null;

  // Animation variants
  const variants = {
    fade: {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 },
    },
    'slide-in-top': {
      initial: { y: '-100%' },
      animate: { y: 0 },
      exit: { y: '-100%' },
    },
    'slide-in-bottom': {
      initial: { y: '100%' },
      animate: { y: 0 },
      exit: { y: '100%' },
    },
    'slide-in-left': {
      initial: { x: '-100%' },
      animate: { x: 0 },
      exit: { x: '-100%' },
    },
    'slide-in-right': {
      initial: { x: '100%' },
      animate: { x: 0 },
      exit: { x: '100%' },
    },
  };

  // Create portal
  const modalContent = (
    <AnimatePresence mode="wait">
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            className="fixed inset-0 z-50 bg-black bg-opacity-50 modal-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            style={{ pointerEvents: 'auto' }}
            onClick={onClose}
          />

          {/* Modal Content */}
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center pointer-events-none"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <motion.div
              ref={modalRef}
              className={`bg-white rounded-lg shadow-xl ${width} h-[95vh] flex flex-col pointer-events-auto`}
              variants={variants}
              initial="initial"
              animate="animate"
              exit="exit"
              transition={{ duration: 0.3 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Modal Header */}
              {(header || title) && (
                <div className="flex-shrink-0 border-b border-gray-200">
                  {header ? (
                    header
                  ) : (
                    <h2 className="text-lg font-semibold text-gray-800">{title}</h2>
                  )}
                </div>
              )}

              {/* Modal Body */}
              <div className="flex-grow px-6 py-4 overflow-y-auto">
                {url ? (
                  <div className="h-full">
                    {!iframeLoaded && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="animate-spin w-6 h-6 border-t-2 border-b-2 border-blue-500 rounded-full"></div>
                      </div>
                    )}
                    <iframe
                      src={url}
                      className="w-full h-full border-none"
                      onLoad={() => setIframeLoaded(true)}
                    />
                  </div>
                ) : (
                  children
                )}
              </div>

              {/* Modal Footer */}
              {footer && (
                <div className="flex-shrink-0 px-6 py-4 border-t border-gray-200">
                  {footer}
                </div>
              )}
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );

  // Use portal for modal rendering
  if (!isMounted) return null;

  try {
    const portalTarget = document.getElementById('modal-root');
    if (!portalTarget) {
      console.warn('Modal-root element not found, falling back to document.body');
    }
    return ReactDOM.createPortal(
      modalContent,
      portalTarget || document.body
    );
  } catch (error) {
    console.error('Error creating portal for modal:', error);
    return null;
  }
};

export default Modal;