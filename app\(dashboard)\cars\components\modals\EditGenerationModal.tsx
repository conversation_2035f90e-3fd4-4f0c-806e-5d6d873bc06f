'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { createClient } from '@/app/libs/supabase/client';
import { Generation, GenerationFormData, Model, Brand } from '../../types';

interface EditGenerationModalProps {
  isOpen: boolean;
  onClose: () => void;
  generation: Generation;
  models: Model[];
  brands: Brand[];
  onSuccess: () => void;
}

const EditGenerationModal: React.FC<EditGenerationModalProps> = ({
  isOpen,
  onClose,
  generation,
  models,
  brands,
  onSuccess
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedBrandId, setSelectedBrandId] = useState<number | ''>('');
  const [filteredModels, setFilteredModels] = useState<Model[]>([]);
  
  const { register, handleSubmit, reset, watch, setValue, formState: { errors } } = useForm<GenerationFormData>({
    defaultValues: {
      model_id: generation.model_id,
      name: generation.name,
      start_production_year: generation.start_production_year,
      end_production_year: generation.end_production_year
    }
  });
  
  const watchStartYear = watch('start_production_year');
  
  const supabase = createClient();

  // Set initial brand ID based on the model
  useEffect(() => {
    if (isOpen && generation.model_id) {
      const model = models.find(m => m.id === generation.model_id);
      if (model) {
        setSelectedBrandId(model.brand_id);
      }
    }
  }, [isOpen, generation, models]);

  // Reset form when generation changes
  useEffect(() => {
    if (isOpen) {
      reset({
        model_id: generation.model_id,
        name: generation.name,
        start_production_year: generation.start_production_year,
        end_production_year: generation.end_production_year
      });
    }
  }, [generation, isOpen, reset]);

  // Filter models based on selected brand
  useEffect(() => {
    if (selectedBrandId) {
      const filtered = models.filter(model => model.brand_id === selectedBrandId);
      setFilteredModels(filtered);
      
      // Reset model selection if the current model doesn't belong to the selected brand
      const modelId = watch('model_id');
      if (modelId && !filtered.some(model => model.id === modelId)) {
        setValue('model_id', undefined);
      }
    } else {
      setFilteredModels(models);
    }
  }, [selectedBrandId, models, watch, setValue]);

  const onSubmit = async (data: GenerationFormData) => {
    setIsSubmitting(true);
    setError(null);
    
    try {
      // Update the generation
      const { error: updateError } = await supabase
        .from('car_generation')
        .update({
          model_id: data.model_id,
          name: data.name,
          start_production_year: data.start_production_year,
          end_production_year: data.end_production_year || null
        })
        .eq('id', generation.id);
        
      if (updateError) throw updateError;
      
      onSuccess();
      onClose();
    } catch (err: any) {
      console.error('Error updating generation:', err);
      setError(err.message || 'Failed to update generation. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.2 }}
          className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex justify-between items-center p-6 border-b border-gray-200">
            <h3 className="text-xl font-semibold text-gray-800">Edit Generation</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              disabled={isSubmitting}
            >
              <X size={24} />
            </button>
          </div>
          
          <form onSubmit={handleSubmit(onSubmit)} className="p-6">
            <div className="mb-6">
              <label htmlFor="brand_id" className="block text-sm font-medium text-gray-700 mb-2">
                Brand
              </label>
              <select
                id="brand_id"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                value={selectedBrandId}
                onChange={(e) => setSelectedBrandId(e.target.value ? Number(e.target.value) : '')}
              >
                <option value="">Select a brand</option>
                {brands.map((brand) => (
                  <option key={brand.brand_id} value={brand.brand_id}>
                    {brand.brand_name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="mb-6">
              <label htmlFor="model_id" className="block text-sm font-medium text-gray-700 mb-2">
                Model
              </label>
              <select
                id="model_id"
                className={`w-full px-4 py-2 border ${errors.model_id ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500`}
                {...register('model_id', { 
                  required: 'Model is required',
                  valueAsNumber: true
                })}
                disabled={!selectedBrandId}
              >
                <option value="">Select a model</option>
                {filteredModels.map((model) => (
                  <option key={model.id} value={model.id}>
                    {model.model_name}
                  </option>
                ))}
              </select>
              {errors.model_id && (
                <p className="mt-1 text-sm text-red-600">{errors.model_id.message}</p>
              )}
            </div>
            
            <div className="mb-6">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                Generation Name
              </label>
              <input
                id="name"
                type="text"
                className={`w-full px-4 py-2 border ${errors.name ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500`}
                placeholder="Enter generation name (e.g., MK1, First Gen)"
                {...register('name', { required: 'Generation name is required' })}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div>
                <label htmlFor="start_production_year" className="block text-sm font-medium text-gray-700 mb-2">
                  Start Year
                </label>
                <input
                  id="start_production_year"
                  type="number"
                  min="1900"
                  max={new Date().getFullYear()}
                  className={`w-full px-4 py-2 border ${errors.start_production_year ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500`}
                  placeholder="Start year"
                  {...register('start_production_year', { 
                    required: 'Start year is required',
                    valueAsNumber: true,
                    min: {
                      value: 1900,
                      message: 'Year must be 1900 or later'
                    },
                    max: {
                      value: new Date().getFullYear(),
                      message: 'Year cannot be in the future'
                    }
                  })}
                />
                {errors.start_production_year && (
                  <p className="mt-1 text-sm text-red-600">{errors.start_production_year.message}</p>
                )}
              </div>
              
              <div>
                <label htmlFor="end_production_year" className="block text-sm font-medium text-gray-700 mb-2">
                  End Year (Optional)
                </label>
                <input
                  id="end_production_year"
                  type="number"
                  min={watchStartYear || 1900}
                  max={new Date().getFullYear()}
                  className={`w-full px-4 py-2 border ${errors.end_production_year ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500`}
                  placeholder="End year (or leave blank if ongoing)"
                  {...register('end_production_year', { 
                    valueAsNumber: true,
                    min: {
                      value: watchStartYear || 1900,
                      message: 'End year must be after start year'
                    },
                    max: {
                      value: new Date().getFullYear(),
                      message: 'Year cannot be in the future'
                    }
                  })}
                />
                {errors.end_production_year && (
                  <p className="mt-1 text-sm text-red-600">{errors.end_production_year.message}</p>
                )}
              </div>
            </div>
            
            {error && (
              <div className="bg-red-50 text-red-600 p-4 rounded-md mb-6">
                {error}
              </div>
            )}
            
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 transition-colors"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default EditGenerationModal;
