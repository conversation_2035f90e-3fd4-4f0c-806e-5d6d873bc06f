#!/usr/bin/env ts-node

import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

// Load environment variables from parent directory
dotenv.config({ path: '../.env.local' });

async function setupDatabase(): Promise<void> {
  console.log('🗄️  Setting up Jiji Automation Database Schema');
  console.log('===============================================\n');

  try {
    // Validate environment variables
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      console.error('❌ Missing required environment variables:');
      console.error('   - NEXT_PUBLIC_SUPABASE_URL');
      console.error('   - NEXT_PUBLIC_SUPABASE_ANON_KEY');
      console.error('\nPlease set these in your .env file');
      process.exit(1);
    }

    // Initialize Supabase client
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Read SQL schema file
    const schemaPath = path.join(__dirname, 'database-schema.sql');
    if (!fs.existsSync(schemaPath)) {
      console.error(`❌ Schema file not found: ${schemaPath}`);
      process.exit(1);
    }

    const sqlSchema = fs.readFileSync(schemaPath, 'utf8');

    // Split SQL into individual statements
    const statements = sqlSchema
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`📝 Found ${statements.length} SQL statements to execute\n`);

    // Execute each statement
    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';';

      try {
        console.log(`⏳ Executing statement ${i + 1}/${statements.length}...`);

        // Execute the SQL statement
        const { error } = await supabase.rpc('exec_sql', { sql: statement });

        if (error) {
          // Try alternative method for DDL statements
          const { error: directError } = await supabase
            .from('_dummy_table_that_does_not_exist')
            .select('*')
            .limit(0);

          // If it's a DDL statement, we might need to use a different approach
          console.log(`⚠️  Statement ${i + 1} may require manual execution:`, error.message);
          errorCount++;
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`);
          successCount++;
        }
      } catch (error) {
        console.log(`⚠️  Statement ${i + 1} failed:`, error);
        errorCount++;
      }
    }

    console.log('\n📊 Database Setup Summary:');
    console.log(`   ✅ Successful: ${successCount}`);
    console.log(`   ⚠️  Errors: ${errorCount}`);

    if (errorCount > 0) {
      console.log('\n⚠️  Some statements failed. This is normal for DDL operations.');
      console.log('   Please run the SQL schema manually in your Supabase SQL editor:');
      console.log(`   ${schemaPath}`);
    }

    // Test the setup by checking if tables exist
    console.log('\n🔍 Verifying table creation...');

    const tables = ['jiji_listings', 'jiji_automation_logs'];
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);

        if (error) {
          console.log(`❌ Table '${table}' not accessible:`, error.message);
        } else {
          console.log(`✅ Table '${table}' is accessible`);
        }
      } catch (error) {
        console.log(`❌ Error checking table '${table}':`, error);
      }
    }

    // Test the functions
    console.log('\n🔍 Testing database functions...');

    try {
      const { data, error } = await supabase.rpc('get_parts_ready_for_jiji_listing', { limit_count: 1 });

      if (error) {
        console.log('❌ Function get_parts_ready_for_jiji_listing failed:', error.message);
      } else {
        console.log('✅ Function get_parts_ready_for_jiji_listing is working');
        console.log(`   Found ${data?.length || 0} parts ready for listing`);
      }
    } catch (error) {
      console.log('❌ Error testing functions:', error);
    }

    console.log('\n🎉 Database setup completed!');
    console.log('\nNext steps:');
    console.log('1. Verify all tables and functions are created in Supabase');
    console.log('2. Test the automation tool with: npm run test');
    console.log('3. Run the automation with: npm run start');

  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  }
}

async function checkDatabaseConnection(): Promise<void> {
  console.log('🔌 Testing database connection...');

  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing Supabase credentials');
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Test connection by querying a system table
    const { data, error } = await supabase
      .from('parts')
      .select('id')
      .limit(1);

    if (error) {
      console.log('❌ Database connection failed:', error.message);
      console.log('   Please check your Supabase credentials and network connection');
      return;
    }

    console.log('✅ Database connection successful');
    console.log(`   Found ${data?.length || 0} parts in database`);

  } catch (error) {
    console.log('❌ Database connection test failed:', error);
  }
}

async function showDatabaseStats(): Promise<void> {
  console.log('📊 Database Statistics');
  console.log('=====================\n');

  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing Supabase credentials');
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Get parts count
    const { count: partsCount } = await supabase
      .from('parts')
      .select('*', { count: 'exact', head: true });

    console.log(`📦 Total Parts: ${partsCount || 0}`);

    // Get parts with images count
    const { count: partsWithImagesCount } = await supabase
      .from('parts')
      .select('*, part_images!inner(*)', { count: 'exact', head: true });

    console.log(`🖼️  Parts with Images: ${partsWithImagesCount || 0}`);

    // Get existing listings count
    const { count: listingsCount } = await supabase
      .from('jiji_listings')
      .select('*', { count: 'exact', head: true });

    console.log(`📋 Existing Listings: ${listingsCount || 0}`);

    // Get listing status breakdown
    const { data: statusBreakdown } = await supabase
      .from('jiji_listings')
      .select('status')
      .then(({ data }) => {
        const breakdown = data?.reduce((acc, item) => {
          acc[item.status] = (acc[item.status] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        return { data: breakdown };
      });

    if (statusBreakdown) {
      console.log('\n📈 Listing Status Breakdown:');
      Object.entries(statusBreakdown).forEach(([status, count]) => {
        console.log(`   ${status}: ${count}`);
      });
    }

    // Get parts ready for listing
    const { data: readyParts } = await supabase.rpc('get_parts_ready_for_jiji_listing', { limit_count: 100 });
    console.log(`\n🚀 Parts Ready for Listing: ${readyParts?.length || 0}`);

  } catch (error) {
    console.log('❌ Error getting database statistics:', error);
  }
}

// CLI interface
const command = process.argv[2];

switch (command) {
  case 'setup':
    setupDatabase();
    break;
  case 'test':
    checkDatabaseConnection();
    break;
  case 'stats':
    showDatabaseStats();
    break;
  default:
    console.log('Jiji Automation Database Setup Tool');
    console.log('===================================\n');
    console.log('Usage:');
    console.log('  ts-node setup-database.ts setup  - Set up database schema');
    console.log('  ts-node setup-database.ts test   - Test database connection');
    console.log('  ts-node setup-database.ts stats  - Show database statistics');
    break;
}
