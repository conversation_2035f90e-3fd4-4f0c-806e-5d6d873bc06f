import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import PartAttribute from './PartAttribute';
import { generateProductSlug } from '@/app/utils/slugify';

interface PartCardProps {
  id: string;
  name: string;
  partNumber: string;
  price: number;
  imageSrc: string;
  category: string;
  attributes: {
    icon: string;
    value: string | number;
    label?: string;
  }[];
}

const PartCard: React.FC<PartCardProps> = ({
  id,
  name,
  partNumber,
  price,
  imageSrc,
  category,
  attributes,
}) => {
  return (
    <motion.div
      whileHover={{ y: -5 }}
      transition={{ duration: 0.2 }}
      className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow"
    >
      <Link href={`/shop/product/${generateProductSlug(name, id)}`} className="block">
        <div className="relative h-48 w-full">
          <Image
            src={imageSrc}
            alt={name}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>
        <div className="p-4">
          <div className="text-sm text-blue-600 font-medium mb-1">{category}</div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">{name}</h3>
          <div className="text-sm text-gray-500 mb-2">Part #: {partNumber}</div>
          <div className="text-xl font-bold text-gray-900 mb-3">Kshs {price.toLocaleString()}</div>

          <div className="flex flex-wrap gap-4 mt-2">
            {attributes.map((attr, index) => (
              <PartAttribute
                key={index}
                icon={attr.icon}
                value={attr.value}
                label={attr.label}
                className="text-sm"
              />
            ))}
          </div>
        </div>
      </Link>
    </motion.div>
  );
};

export default PartCard;
