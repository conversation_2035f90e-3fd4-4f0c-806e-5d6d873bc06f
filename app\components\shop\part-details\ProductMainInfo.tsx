'use client';

import React from 'react';
import { Button } from '@/app/components/ui/Button';
import { Heart, Share2 } from 'lucide-react';
import type { PartDetails, PartAttributeType } from '@/app/shop/product/[slug]/page'; // Import the types
import Icon from '@/app/components/ui/Icon';
import { getAdjustedPrice } from '@/app/utils/priceAdjustment';

interface ProductMainInfoProps {
  partDetails: PartDetails | null; // Can be null initially
  isFavorite: boolean;
  onToggleFavorite: () => void;
  onOpenWhatsAppModal: () => void;
  getIconForAttribute?: (attributeName: string) => string;
}

const ProductMainInfo: React.FC<ProductMainInfoProps> = ({
  partDetails,
  isFavorite,
  onToggleFavorite,
  onOpenWhatsAppModal,
  getIconForAttribute,
}) => {
  if (!partDetails) {
    // Optionally render a loading state or null
    return <div className="w-full md:w-1/2 lg:w-3/5 xl:w-2/3 p-4">Loading product details...</div>;
  }

  return (
    <div className="w-full md:w-1/2 lg:w-2/5 flex flex-col">
      <div className="flex-grow">

        {/* Title */}
        <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-2 leading-tight">{partDetails.title}</h1>

        {/* Part Number */}
        <div className="text-gray-600 mb-4">Part #: {partDetails.partnumber}</div>

        {/* Price */}
        <div className="mb-6">
          {partDetails.discountedPrice && partDetails.discountedPrice < partDetails.price ? (
            <div className="flex items-center">
              <span className="text-3xl font-bold text-gray-900">
                Kshs {getAdjustedPrice(partDetails.discountedPrice).toLocaleString()}
              </span>
              <span className="ml-2 text-xl text-gray-500 line-through">
                Kshs {getAdjustedPrice(partDetails.price).toLocaleString()}
              </span>
            </div>
          ) : (
            <span className="text-3xl font-bold text-gray-900">
              Kshs {getAdjustedPrice(partDetails.price).toLocaleString()}
            </span>
          )}
        </div>

        {/* Part Attributes */}
        <div className="my-4 border-t border-b border-gray-200 py-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {/* Stock Status as an attribute */}
            <div className="flex items-start p-2 bg-gray-50 rounded-lg shadow-sm hover:shadow-md transition-shadow">
              <Icon name="package-variant-closed" size={18} className="text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-xs font-medium text-gray-500">Stock</p>
                <p className="text-sm font-medium text-gray-800">{partDetails.stock > 0 ? `${partDetails.stock} in stock` : 'Out of stock'}</p>
              </div>
            </div>

            {/* Actual attributes from Supabase */}
            {partDetails.attributes && partDetails.attributes.map((attr, index) => {
              const iconName = attr.icon || (getIconForAttribute ? getIconForAttribute(attr.name) : 'settings');
              return (
                <div key={index} className="flex items-start p-2 bg-gray-50 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                  <Icon name={iconName} size={18} className="text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-xs font-medium text-gray-500">{attr.name}</p>
                    <p className="text-sm font-medium text-gray-800">{attr.value}</p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <a
            href="tel:+254700000000"
            className="py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors text-center flex items-center justify-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
              <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
            </svg>
            Call Now
          </a>
          <button
            className="py-3 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md transition-colors flex items-center justify-center"
            onClick={onOpenWhatsAppModal}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="white" className="mr-2">
              <path d="M.057 24l1.687-6.163c-1.041-1.804-1.588-3.849-1.587-5.946.003-6.556 5.338-11.891 11.893-11.891 3.181.001 6.167 1.24 8.413 3.488 2.245 2.248 3.481 5.236 3.48 8.414-.003 6.557-5.338 11.892-11.893 11.892-1.99-.001-3.951-.5-5.688-1.448l-6.305 1.654zm6.597-3.807c1.676.995 3.276 1.591 5.392 1.592 5.448 0 9.886-4.434 9.889-9.885.002-5.462-4.415-9.89-9.881-9.892-5.452 0-9.887 4.434-9.889 9.884-.001 2.225.651 3.891 1.746 5.634l-.999 3.648 3.742-.981zm11.387-5.464c-.074-.124-.272-.198-.57-.347-.297-.149-1.758-.868-2.031-.967-.272-.099-.47-.149-.669.149-.198.297-.768.967-.941 1.165-.173.198-.347.223-.644.074-.297-.149-1.255-.462-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.297-.347.446-.521.151-.172.2-.296.3-.495.099-.198.05-.372-.025-.521-.075-.148-.669-1.611-.916-2.206-.242-.579-.487-.501-.669-.51l-.57-.01c-.198 0-.52.074-.792.372s-1.04 1.016-1.04 2.479 1.065 2.876 1.213 3.074c.149.198 2.095 3.2 5.076 4.487.709.306 1.263.489 1.694.626.712.226 1.36.194 1.872.118.571-.085 1.758-.719 2.006-1.413.248-.695.248-1.29.173-1.414z"/>
            </svg>
            WhatsApp
          </button>
        </div>
      </div>

      {/* Product Description Section */}
      <div className="mt-2 border border-gray-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold text-gray-800 mb-2">Product Description</h2>
        {!partDetails.description ? (
          <p className="text-gray-500 italic">No detailed description available for this part. Please contact us for more information.</p>
        ) : (
          <p className="text-gray-700">{partDetails.description}</p>
        )}
      </div>
        {/* Social Sharing */}
      <div className="mt-auto pt-4 border-t border-gray-200">
        <div className="flex items-center space-x-4 text-sm">
          <Button variant="ghost" className="text-gray-600 hover:text-blue-600 px-2 py-1">
            <Share2 size={16} className="mr-1.5" /> Share
          </Button>
          <Button variant="ghost" onClick={onToggleFavorite} className="text-gray-600 hover:text-blue-600 px-2 py-1">
            <Heart size={16} className={`mr-1.5 ${isFavorite ? 'fill-red-500 text-red-500' : ''}`} />
            {isFavorite ? 'Saved' : 'Save'}
          </Button>
        </div>
      </div>

    </div>
  );
};

export default ProductMainInfo;
