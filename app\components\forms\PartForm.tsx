import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2, CheckCircle2, XCircle, AlertCircle, Plus } from 'lucide-react';
import { Button } from '@/app/components/ui/Button';
import { Input } from '@/app/components/ui/Input';
import Label from '@/app/components/ui/inputs/Label';
import { Textarea } from '@/app/components/ui/Textarea';
import { Card } from '@/app/components/ui/Card';
import { useToast } from '@/app/components/ui/use-toast';
import { cn } from '@/app/utils/cn';
import DynamicInput from '@/app/components/ui/inputs/DynamicInput';

interface AnalysisResult {
  title?: string;
  engineCodes?: string[];
  engineCapacity?: string;
  fuelType?: string;
  engineType?: string;
  isEnginePart?: boolean;
}

interface PartFormProps {
  onSubmit: (data: any) => void;
  initialData?: any;
  isLoading?: boolean;
  flatCategories: any[];
  selectedCategory: string;
}

const PartForm: React.FC<PartFormProps> = ({ onSubmit, initialData, isLoading, flatCategories, selectedCategory }) => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [partNumbers, setPartNumbers] = useState<string[]>(['']);
  const [additionalEngineCodes, setAdditionalEngineCodes] = useState<string[]>(['']);
  const [showEngineCodes, setShowEngineCodes] = useState(false);
  const { toast } = useToast();

  // Initialize form
  const form = useForm({
    defaultValues: initialData || {
      partNumber: '',
      stock: 0,
      price: 0,
      discountPrice: 0,
      condition: 'New',
      imageUrl: '',
      imageType: '',
      categoryId: selectedCategory,
      additionalEngineCodes: []
    }
  });

  // Get the current category's requirePartNumber property
  const currentCategory = flatCategories.find(cat => cat.id.toString() === selectedCategory);
  const requirePartNumber = currentCategory?.requirePartNumber ?? true;

  // Add useEffect to debug analysis result
  useEffect(() => {
    console.log('Analysis Result:', analysisResult);
    console.log('Show Engine Codes:', showEngineCodes);
    console.log('Require Part Number:', requirePartNumber);
    console.log('Should Show Link:', analysisResult && requirePartNumber && !showEngineCodes);
  }, [analysisResult, showEngineCodes, requirePartNumber]);

  const handlePartNumbersChange = (newValues: string[]) => {
    setPartNumbers(newValues);
    form.setValue('partNumber', newValues[0] || '');
  };

  const handleEngineCodesChange = (newValues: string[]) => {
    setAdditionalEngineCodes(newValues);
    form.setValue('additionalEngineCodes', newValues.filter(code => code.trim() !== ''));
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <div className="text-sm font-medium">Part Number</div>
          {!requirePartNumber ? (
            <DynamicInput
              label="Part Numbers"
              values={partNumbers}
              onChange={handlePartNumbersChange}
              placeholder="Enter part number (optional)"
              className="mt-2"
            />
          ) : (
            <Input
              id="partNumber"
              {...form.register('partNumber')}
              placeholder="Enter part number"
              className={cn(
                'w-full',
                form.formState.errors.partNumber && 'border-red-500'
              )}
            />
          )}
          {form.formState.errors.partNumber && (
            <p className="text-sm text-red-500">
              {String(form.formState.errors.partNumber.message || 'Part number is required')}
            </p>
          )}
        </div>

        {/* Show link to add engine codes after AI analysis and when part number is required */}
        {analysisResult && requirePartNumber && !showEngineCodes && (
          <div className="flex items-center gap-2 mt-4">
            <Button
              type="button"
              variant="link"
              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              onClick={() => setShowEngineCodes(true)}
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Missing Engine Codes
            </Button>
          </div>
        )}

        {/* Show additional engine codes input when link is clicked */}
        {analysisResult && requirePartNumber && showEngineCodes && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-2 mt-4"
          >
            <div className="flex items-center justify-between">
              <Label>Additional Engine Codes</Label>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                onClick={() => setShowEngineCodes(false)}
              >
                Close
              </Button>
            </div>
            <DynamicInput
              label="Engine Codes"
              values={additionalEngineCodes}
              onChange={handleEngineCodesChange}
              placeholder="Enter additional engine code"
              className="mt-2"
            />
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Add any other engine codes that the AI might have missed
            </p>
          </motion.div>
        )}

        {/* Stock input field */}
        <div className="space-y-2">
          <div className="text-sm font-medium">Stock</div>
          <Input
            id="stock"
            type="number"
            {...form.register('stock', { valueAsNumber: true })}
            placeholder="Enter stock quantity"
            className={cn(
              'w-full',
              form.formState.errors.stock && 'border-red-500'
            )}
          />
          {form.formState.errors.stock && (
            <p className="text-sm text-red-500">
              {String(form.formState.errors.stock.message || 'Invalid stock value')}
            </p>
          )}
        </div>

        {/* ... rest of the form fields ... */}
      </div>
    </form>
  );
};

export default PartForm;