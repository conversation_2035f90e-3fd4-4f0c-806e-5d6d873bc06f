'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Database, FileText } from 'lucide-react';
import Link from 'next/link';
import MigrationRunner from '../components/MigrationRunner';
import { generatePDFReceipt } from '../utils/pdfGenerator';

// Sample sale data for testing PDF generation
const sampleSaleData = {
  id: 'test-sale-123',
  sale_timestamp: new Date().toISOString(),
  sale_type: 'cash' as const,
  payment_method: 'cash',
  total_amount: 15000,
  discount_total: 500,
  total_before_vat: 14500,
  vat_amount: 2320,
  vat_rate: 16,
  total_with_vat: 16820,
  one_off_client_name: '<PERSON>',
  one_off_client_phone: '+254712345678',
  clients: {
    name: '<PERSON>',
    phone_number: '+254712345678'
  },
  profiles: {
    full_name: 'Sales Staff'
  },
  sale_items: [
    {
      id: 'item-1',
      quantity: 2,
      price_at_sale: 4500,
      discount_amount: 200,
      discount_reason: 'Bulk discount',
      parts: {
        title: 'VW Golf Front Brake Pads - Ceramic'
      },
      part_id: 123
    },
    {
      id: 'item-2',
      quantity: 1,
      price_at_sale: 8500,
      discount_amount: 0,
      discount_reason: '',
      parts: {
        title: 'Audi A4 Headlight Assembly - LED Right'
      },
      part_id: 456
    },
    {
      id: 'item-3',
      quantity: 4,
      price_at_sale: 1200,
      discount_amount: 100,
      discount_reason: 'Customer loyalty',
      parts: {
        title: 'Engine Oil Filter - Premium Grade'
      },
      part_id: 789
    }
  ],
  mpesa_payments: []
};

export default function SalesSetupPage() {
  const handleGenerateTestPDF = async () => {
    try {
      await generatePDFReceipt(sampleSaleData);
      alert('Test PDF generated successfully! Check your downloads folder.');
    } catch (error) {
      console.error('Error generating test PDF:', error);
      if (error instanceof Error) {
        alert(`Failed to generate test PDF: ${error.message}`);
      } else {
        alert('Failed to generate test PDF. Check console for details.');
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/sales"
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Sales
          </Link>
          <h1 className="text-3xl font-bold text-gray-900">Sales Module Setup</h1>
          <p className="mt-2 text-gray-600">
            Configure the sales module and test PDF receipt generation
          </p>
        </div>

        <div className="space-y-8">
          {/* Migration Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <MigrationRunner />
          </motion.div>

          {/* PDF Test Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-white rounded-lg shadow-sm p-6 border border-gray-200"
          >
            <div className="flex items-center mb-4">
              <FileText className="h-6 w-6 text-orange-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">PDF Receipt Test</h3>
            </div>
            
            <div className="space-y-4">
              <div className="bg-orange-50 border border-orange-200 rounded-md p-4">
                <h4 className="text-sm font-medium text-orange-900 mb-2">Test PDF Generation</h4>
                <p className="text-sm text-orange-700">
                  Generate a sample PDF receipt to test the PDF generation functionality.
                </p>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleGenerateTestPDF}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Generate Test PDF Receipt
                </button>
              </div>

              <div className="mt-6 text-xs text-gray-500">
                <h4 className="font-medium text-gray-700 mb-2">Sample Receipt Data:</h4>
                <ul className="space-y-1">
                  <li>• Client: John Doe (+254712345678)</li>
                  <li>• Parts: VW Brake Pads, Audi Headlight, Oil Filters</li>
                  <li>• Subtotal: Kshs 18,300</li>
                  <li>• Discount: Kshs 300</li>
                  <li>• VAT (16%): Kshs 2,320</li>
                  <li>• Total: Kshs 20,320</li>
                  <li>• Staff: Sales Staff</li>
                  <li>• Clean minimalist corporate receipt design</li>
                </ul>
              </div>
            </div>
          </motion.div>

          {/* Instructions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-blue-50 border border-blue-200 rounded-md p-6"
          >
            <h3 className="text-lg font-medium text-blue-900 mb-3">Setup Instructions</h3>
            <ol className="list-decimal list-inside space-y-2 text-sm text-blue-700">
              <li>First, run the database migration to add the <code className="bg-blue-100 px-1 rounded">created_by</code> column</li>
              <li>Test the PDF generation to ensure it works correctly</li>
              <li>Once both are successful, you can start using the enhanced sales module</li>
              <li>PDF receipts will now include staff information and can be generated from multiple locations</li>
            </ol>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
