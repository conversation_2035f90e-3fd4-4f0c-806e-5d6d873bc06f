'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Search, RefreshCw, Edit, Trash2, Warehouse, MapPin, Home, ArrowUpDown } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { StorageArea } from '../types';
import AddAreaModal from './modals/AddAreaModal';
import EditAreaModal from './modals/EditAreaModal';
import DeleteConfirmModal from './modals/DeleteConfirmModal';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

interface StorageAreasTabProps {
  onRefresh: () => void;
}

const StorageAreasTab: React.FC<StorageAreasTabProps> = ({ onRefresh }) => {
  const [areas, setAreas] = useState<StorageArea[]>([]);
  const [filteredAreas, setFilteredAreas] = useState<StorageArea[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedArea, setSelectedArea] = useState<StorageArea | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const supabase = createClient();

  // Fetch storage areas
  useEffect(() => {
    const fetchAreas = async () => {
      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from('storage_areas')
          .select('*')
          .order('name');

        if (error) throw error;

        setAreas(data || []);
        setFilteredAreas(data || []);
      } catch (error) {
        console.error('Error fetching storage areas:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAreas();
  }, [refreshTrigger, supabase]);

  // Filter areas based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredAreas(areas);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = areas.filter(
      area => 
        area.name.toLowerCase().includes(query) ||
        area.description?.toLowerCase().includes(query) ||
        area.location_type.toLowerCase().includes(query) ||
        area.level.toLowerCase().includes(query)
    );

    setFilteredAreas(filtered);
  }, [searchQuery, areas]);

  // Handle refresh
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
    onRefresh();
  };

  // Handle edit
  const handleEdit = (area: StorageArea) => {
    setSelectedArea(area);
    setIsEditModalOpen(true);
  };

  // Handle delete
  const handleDelete = (area: StorageArea) => {
    setSelectedArea(area);
    setIsDeleteModalOpen(true);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3
      }
    },
    hover: {
      y: -5,
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      transition: {
        duration: 0.2
      }
    }
  };

  return (
    <div>
      {/* Search and Actions */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
        <div className="relative w-full md:w-96">
          <input
            type="text"
            placeholder="Search storage areas..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
        </div>

        <div className="flex gap-2 w-full md:w-auto">
          <button
            onClick={handleRefresh}
            className="flex items-center gap-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
          >
            <RefreshCw size={18} />
            <span>Refresh</span>
          </button>

          <button
            onClick={() => setIsAddModalOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <Plus size={18} />
            <span>Add Area</span>
          </button>
        </div>
      </div>

      {/* Areas Grid */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size={40} />
        </div>
      ) : filteredAreas.length > 0 ? (
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {filteredAreas.map((area) => (
            <motion.div
              key={area.area_id}
              className="bg-white rounded-lg shadow-md overflow-hidden"
              variants={itemVariants}
              whileHover="hover"
            >
              <div className="p-5 border-b border-gray-100">
                <div className="flex justify-between items-start mb-3">
                  <div className="flex items-center">
                    <div className={`p-2 rounded-md mr-3 ${
                      area.location_type === 'indoor' 
                        ? 'bg-teal-100 text-teal-600' 
                        : 'bg-orange-100 text-orange-600'
                    }`}>
                      {area.location_type === 'indoor' ? <Home size={20} /> : <MapPin size={20} />}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-800">{area.name}</h3>
                      <p className="text-sm text-gray-500">{area.location_type} - {area.level}</p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleEdit(area)}
                      className="p-1.5 rounded-md hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
                      aria-label="Edit area"
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={() => handleDelete(area)}
                      className="p-1.5 rounded-md hover:bg-gray-100 text-gray-500 hover:text-red-500 transition-colors"
                      aria-label="Delete area"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2 mb-3">
                  <div className="bg-gray-50 p-2 rounded-md">
                    <p className="text-xs text-gray-500">Location Type</p>
                    <p className={`text-sm font-medium ${
                      area.location_type === 'indoor' ? 'text-teal-600' : 'text-orange-600'
                    }`}>
                      {area.location_type === 'indoor' ? 'Indoor' : 'Outdoor'}
                    </p>
                  </div>
                  <div className="bg-gray-50 p-2 rounded-md">
                    <p className="text-xs text-gray-500">Level</p>
                    <p className="text-sm font-medium text-gray-700">
                      {area.level === 'upstairs' ? 'Upstairs' : 
                       area.level === 'downstairs' ? 'Downstairs' : 'Ground Floor'}
                    </p>
                  </div>
                </div>

                {area.description && (
                  <div className="bg-gray-50 p-2 rounded-md">
                    <p className="text-xs text-gray-500">Description</p>
                    <p className="text-sm text-gray-700 line-clamp-2">{area.description}</p>
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </motion.div>
      ) : (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <h3 className="text-xl font-semibold text-gray-700 mb-2">No storage areas found</h3>
          <p className="text-gray-500 mb-4">
            {searchQuery ? 'No areas match your search criteria.' : 'Start by adding a new storage area.'}
          </p>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <Plus size={18} />
            <span>Add Storage Area</span>
          </button>
        </div>
      )}

      {/* Add Area Modal */}
      <AddAreaModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSuccess={handleRefresh}
      />

      {/* Edit Area Modal */}
      {selectedArea && (
        <EditAreaModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          area={selectedArea}
          onSuccess={handleRefresh}
        />
      )}

      {/* Delete Confirmation Modal */}
      {selectedArea && (
        <DeleteConfirmModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          itemId={selectedArea.area_id}
          itemName={selectedArea.name}
          itemType="area"
          onSuccess={handleRefresh}
        />
      )}
    </div>
  );
};

export default StorageAreasTab;
