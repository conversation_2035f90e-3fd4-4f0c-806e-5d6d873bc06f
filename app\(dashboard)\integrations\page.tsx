'use client';

import { useState } from 'react';
import Link from 'next/link';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle
} from '@/app/components/ui/Card';
import { Button } from "@/app/components/ui/Button";
import { Badge } from '@/app/components/ui/badge';
import {
  ShoppingCart,
  CreditCard,
  Share2,
  Mail,
  MessageSquare,
  ArrowRight,
  FileSpreadsheet
} from 'lucide-react';
import DashboardHeader from '@/app/components/dashboard/DashboardHeader';

interface IntegrationCard {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  status: 'active' | 'inactive' | 'coming-soon';
  href: string;
}

export default function IntegrationsPage() {
  const [integrations] = useState<IntegrationCard[]>([
    {
      id: 'google-sheets',
      title: 'Google Sheets',
      description: 'Sync your products with Google Sheets for Google Merchant Center feed',
      icon: <FileSpreadsheet className="h-8 w-8" />,
      status: 'inactive',
      href: '/integrations/google-sheets'
    },
    {
      id: 'google-merchant',
      title: 'Google Merchant Center',
      description: 'Sync your products with Google Merchant Center for Google Shopping ads',
      icon: <ShoppingCart className="h-8 w-8" />,
      status: 'inactive',
      href: '/integrations/google-merchant'
    },
    {
      id: 'mpesa',
      title: 'M-PESA',
      description: 'Process payments using Safaricom M-PESA',
      icon: <CreditCard className="h-8 w-8" />,
      status: 'active',
      href: '/integrations/mpesa'
    },
    {
      id: 'social-media',
      title: 'Social Media',
      description: 'Share products on social media platforms',
      icon: <Share2 className="h-8 w-8" />,
      status: 'coming-soon',
      href: '/integrations/social-media'
    },
    {
      id: 'email-marketing',
      title: 'Email Marketing',
      description: 'Connect with email marketing platforms',
      icon: <Mail className="h-8 w-8" />,
      status: 'coming-soon',
      href: '/integrations/email-marketing'
    },
    {
      id: 'whatsapp',
      title: 'WhatsApp Business',
      description: 'Connect with WhatsApp Business API for customer communication',
      icon: <MessageSquare className="h-8 w-8" />,
      status: 'coming-soon',
      href: '/integrations/whatsapp'
    }
  ]);

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'inactive':
        return <Badge variant="outline">Inactive</Badge>;
      case 'coming-soon':
        return <Badge variant="secondary">Coming Soon</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto py-6">
      <DashboardHeader
        heading="Integrations"
        text="Connect your store with external services and platforms"
        icon={<Share2 className="h-6 w-6" />}
      />

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mt-8">
        {integrations.map((integration) => (
          <Card key={integration.id} className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div className="p-2 bg-primary/10 rounded-md">
                  {integration.icon}
                </div>
                {getStatusBadge(integration.status)}
              </div>
              <CardTitle className="mt-4">{integration.title}</CardTitle>
              <CardDescription>{integration.description}</CardDescription>
            </CardHeader>
            <CardFooter className="pt-2">
              {integration.status === 'coming-soon' ? (
                <Button variant="outline" disabled>
                  Coming Soon
                </Button>
              ) : (
                <Link href={integration.href} passHref>
                  <Button variant="default" className="w-full">
                    Configure
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              )}
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}
