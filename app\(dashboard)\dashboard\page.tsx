export const dynamic = 'force-dynamic';

import { Suspense } from 'react';
import Spinner from '@/app/components/ui/Spinner';
import DashboardContent from './components/DashboardContent';

export default function DashboardPage() {
    return (
        <Suspense fallback={<div className="flex min-h-[300px] items-center justify-center">
            <Spinner size="lg" />
        </div>}>
            <DashboardContent />
        </Suspense>
    );
}
