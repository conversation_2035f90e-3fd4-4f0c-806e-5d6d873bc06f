'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { createClient } from '@/app/libs/supabase/client';
import { generateProductSlug } from '@/app/utils/slugify';
import { getAdjustedPrice } from '@/app/utils/priceAdjustment';

interface RelatedPart {
  part_id: number;
  title: string;
  part_number: string;
  price: number;
  discounted_price: number | null;
  image_url: string;
  condition: string;
  category_id: number;
  category_name: string;
}

interface RelatedPartsForCarSectionProps {
  partId: string;
  limit?: number;
}

const RelatedPartsForCarSection: React.FC<RelatedPartsForCarSectionProps> = ({ 
  partId, 
  limit = 4 
}) => {
  const [relatedParts, setRelatedParts] = useState<RelatedPart[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRelatedParts = async () => {
      if (!partId) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        const supabase = createClient();
        
        // Call the get_related_parts_for_car function
        const { data, error } = await supabase
          .rpc('get_related_parts_for_car', {
            p_part_id: parseInt(partId),
            p_limit: limit
          });
        
        if (error) {
          console.error('Error fetching related parts:', error);
          setError('Failed to load related parts');
          return;
        }
        
        if (data && data.length > 0) {
          setRelatedParts(data);
        } else {
          // No related parts found
          setRelatedParts([]);
        }
      } catch (err) {
        console.error('Exception fetching related parts:', err);
        setError('An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchRelatedParts();
  }, [partId, limit]);

  // If there are no related parts and we're not loading, don't render anything
  if (!isLoading && relatedParts.length === 0) {
    return null;
  }

  return (
    <div className="container mx-auto px-2 sm:px-4 max-w-full xl:max-w-screen-2xl mt-8">
      <div className="bg-white p-3 md:p-4 rounded-lg shadow-sm mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">Related Parts for this Car</h2>
        </div>

        {isLoading ? (
          // Loading skeleton
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.from({ length: limit }).map((_, index) => (
              <div key={index} className="border border-gray-200 rounded-lg overflow-hidden animate-pulse">
                <div className="h-48 bg-gray-200"></div>
                <div className="p-4">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/3 mt-4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          // Error state
          <div className="text-center py-6 text-red-500">{error}</div>
        ) : (
          // Related parts grid
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {relatedParts.map((part) => (
              <Link 
                href={`/shop/product/${generateProductSlug(part.title, part.part_id.toString())}`} 
                key={part.part_id} 
                className="block"
              >
                <div className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                  <div className="relative h-48">
                    <Image
                      src={part.image_url || '/images/placeholder.jpg'}
                      alt={part.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold text-lg mb-1 line-clamp-2">{part.title}</h3>
                    <div className="text-sm text-gray-500 mb-2">Part #: {part.part_number}</div>
                    <div className="flex justify-between items-center">
                      {part.discounted_price ? (
                        <div className="flex flex-col">
                          <span className="font-bold text-lg">
                            Kshs {getAdjustedPrice(part.discounted_price).toLocaleString()}
                          </span>
                          <span className="text-xs text-gray-500 line-through">
                            Kshs {getAdjustedPrice(part.price).toLocaleString()}
                          </span>
                        </div>
                      ) : (
                        <span className="font-bold text-lg">
                          Kshs {getAdjustedPrice(part.price).toLocaleString()}
                        </span>
                      )}
                      <div className="flex items-center text-sm text-gray-600">
                        <span className="flex items-center">
                          <span className="mr-1">{part.condition}</span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default RelatedPartsForCarSection;
