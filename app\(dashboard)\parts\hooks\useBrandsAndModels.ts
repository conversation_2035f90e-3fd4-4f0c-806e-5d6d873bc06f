'use client'

import { useState, useEffect, useCallback } from 'react';
import { UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { PartFormValues } from '../types';

// Define the types needed for the hook
interface Brand {
  id: number | string;
  name: string;
}

interface Model {
  id: number | string;
  brandId: number | string;
  name: string;
}

interface Generation {
  id: number | string;
  name: string;
  model_id: number | string;
  start_production_year: number;
  end_production_year?: number;
  displayName: string;
  years: string;
}

interface Variation {
  id: number | string;
  name: string;
  generation_id: number | string;
  variation: string;
}

interface Trim {
  id: number | string;
  name: string;
  variation_id: number | string;
  trim: string;
}

interface useBrandsAndModelsProps {
  setValue: UseFormSetValue<PartFormValues>;
  watch: UseFormWatch<PartFormValues>;
}

// Fetch functions
const fetchBrands = async (): Promise<Brand[]> => {
  const response = await fetch('/api/car/brands');
  if (!response.ok) throw new Error('Failed to fetch brands');
  return response.json();
};

const fetchModelsByBrandId = async (brandId: number): Promise<Model[]> => {
  const response = await fetch(`/api/car/models?brandId=${brandId}`);
  if (!response.ok) throw new Error('Failed to fetch models');
  return response.json();
};

const fetchGenerationsByModelId = async (modelId: number): Promise<Generation[]> => {
  const response = await fetch(`/api/car/generations?modelId=${modelId}`);
  if (!response.ok) throw new Error('Failed to fetch generations');
  return response.json();
};

const fetchVariationsByGenerationId = async (generationId: number): Promise<Variation[]> => {
  const response = await fetch(`/api/car/variations?generationId=${generationId}`);
  if (!response.ok) throw new Error('Failed to fetch variations');
  return response.json();
};

const fetchTrimsByVariationId = async (variationId: number): Promise<Trim[]> => {
  const response = await fetch(`/api/car/trims?variationId=${variationId}`);
  if (!response.ok) throw new Error('Failed to fetch trims');
  return response.json();
};

export const useBrandsAndModels = ({ setValue, watch }: useBrandsAndModelsProps) => {
  const [brands, setBrands] = useState<Brand[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [generations, setGenerations] = useState<Generation[]>([]);
  const [variations, setVariations] = useState<Variation[]>([]);
  const [trims, setTrims] = useState<Trim[]>([]);

  const [selectedBrand, setSelectedBrand] = useState<string | null>(null);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);
  const [selectedGeneration, setSelectedGeneration] = useState<string | null>(null);
  const [selectedVariation, setSelectedVariation] = useState<string | null>(null);

  // Fetch initial brands
  useEffect(() => {
    fetchBrands().then(setBrands).catch(console.error);
  }, []);

  // Fetch models when brand changes
  const handleBrandChange = useCallback(async (brandId: string) => {
    setSelectedBrand(brandId);
    await setValue('brandId', brandId);
    await setValue('modelId', '');
    await setValue('generationId', '');
    await setValue('variationId', '');
    await setValue('trimId', '');

    try {
      const modelsData = await fetchModelsByBrandId(parseInt(brandId));
      setModels(modelsData);
    } catch (error) {
      console.error("Failed to fetch models:", error);
      setModels([]);
    }
  }, [setValue]);

  // Fetch generations when model changes
  const handleModelChange = useCallback(async (modelId: string) => {
    setSelectedModel(modelId);
    setValue('modelId', modelId);

    // Reset dependent values
    setValue('generationId', '');
    setValue('variationId', '');
    setValue('trimId', '');

    try {
      const generationsData = await fetchGenerationsByModelId(parseInt(modelId));
      setGenerations(generationsData);
    } catch (error) {
      console.error("Failed to fetch generations:", error);
      setGenerations([]);
    }
  }, [setValue]);

  // Fetch variations when generation changes
  const handleGenerationChange = useCallback(async (generationId: string) => {
    setSelectedGeneration(generationId);
    setValue('generationId', generationId);

    // Reset dependent values
    setValue('variationId', '');
    setValue('trimId', '');

    try {
      const variationsData = await fetchVariationsByGenerationId(parseInt(generationId));
      setVariations(variationsData);
    } catch (error) {
      console.error("Failed to fetch variations:", error);
      setVariations([]);
    }
  }, [setValue]);

  // Fetch trims when variation changes
  const handleVariationChange = useCallback(async (variationId: string) => {
    setSelectedVariation(variationId);
    setValue('variationId', variationId);

    // Reset dependent values
    setValue('trimId', '');

    try {
      const trimsData = await fetchTrimsByVariationId(parseInt(variationId));
      setTrims(trimsData);
    } catch (error) {
      console.error("Failed to fetch trims:", error);
      setTrims([]);
    }
  }, [setValue]);

  // Handle trim selection
  const handleTrimChange = useCallback((trimId: string) => {
    setValue('trimId', trimId);
  }, [setValue]);

  return {
    brands,
    models,
    generations,
    variations,
    trims,
    selectedBrand,
    selectedModel,
    selectedGeneration,
    selectedVariation,
    handleBrandChange,
    handleModelChange,
    handleGenerationChange,
    handleVariationChange,
    handleTrimChange
  };
};