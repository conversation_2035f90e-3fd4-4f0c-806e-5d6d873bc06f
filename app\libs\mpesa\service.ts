import { MPESA_CONFIG } from './config';
import { getAccessToken, generateTimestamp, generatePassword, formatPhoneNumber } from './utils';
import { createClient } from '@/app/libs/supabase/client';

// Check if the exec_sql function exists, and if not, create it
async function ensureExecSqlFunctionExists() {
  try {
    const supabase = createClient();

    // Try to call the function with a simple query to check if it exists
    const { error } = await supabase.rpc('exec_sql', { sql: 'SELECT 1' });

    if (error && error.code === 'PGRST301') { // Function not found
      console.log('Creating exec_sql function...');

      // Create the function
      const { error: createError } = await supabase.rpc('create_exec_sql_function', {});

      if (createError) {
        // If the create_exec_sql_function doesn't exist either, we need to create it manually
        if (createError.code === 'PGRST301') {
          // Create the function using a direct SQL query
          const { error: sqlError } = await supabase.from('_exec_sql').select('*').limit(1);

          if (sqlError) {
            console.error('Failed to create exec_sql function:', sqlError);
          }
        } else {
          console.error('Failed to create exec_sql function:', createError);
        }
      }
    }
  } catch (error) {
    console.error('Error ensuring exec_sql function exists:', error);
    // Continue anyway
  }
}

export interface StkPushRequest {
  phoneNumber: string;
  amount: number;
  reference: string;
  description?: string;
}

export interface StkPushResponse {
  success: boolean;
  message: string;
  data?: {
    MerchantRequestID: string;
    CheckoutRequestID: string;
    ResponseCode: string;
    ResponseDescription: string;
    CustomerMessage: string;
  };
  error?: any;
}

export interface StkQueryResponse {
  success: boolean;
  message: string;
  data?: {
    ResponseCode: string;
    ResponseDescription: string;
    MerchantRequestID: string;
    CheckoutRequestID: string;
    ResultCode: string;
    ResultDesc: string;
  };
  error?: any;
}

/**
 * Initiate an STK push request to the customer's phone
 */
export async function initiateSTKPush(request: StkPushRequest): Promise<StkPushResponse> {
  try {
    // Log the M-PESA configuration
    MPESA_CONFIG.logConfig();

    // If M-PESA is not configured, use a mock response for development
    if (!MPESA_CONFIG.IS_CONFIGURED && MPESA_CONFIG.IS_DEV_MODE) {
      console.log('M-PESA API not configured. Using mock response for development.');

      // Generate a mock checkout request ID
      const mockCheckoutId = `ws_CO_${Date.now()}${Math.floor(Math.random() * 10000)}`;
      const mockMerchantRequestId = `ws_MR_${Date.now()}${Math.floor(Math.random() * 10000)}`;

      // Save the mock transaction
      await saveTransaction({
        merchant_request_id: mockMerchantRequestId,
        checkout_request_id: mockCheckoutId,
        phone_number: request.phoneNumber,
        amount: Math.ceil(request.amount),
        reference: request.reference,
        description: request.description || MPESA_CONFIG.TRANSACTION_DESCRIPTION,
        status: 'pending',
      });

      return {
        success: true,
        message: 'STK push initiated successfully (MOCK - Development Mode)',
        data: {
          MerchantRequestID: mockMerchantRequestId,
          CheckoutRequestID: mockCheckoutId,
          ResponseCode: '0',
          ResponseDescription: 'Success. Request accepted for processing',
          CustomerMessage: 'Success. Request accepted for processing',
        },
      };
    }

    // Real M-PESA API implementation
    let accessToken;
    try {
      accessToken = await getAccessToken();
      console.log('Successfully obtained M-PESA access token');
    } catch (tokenError) {
      console.error('Failed to get M-PESA access token:', tokenError);

      // If we're in development, continue with a mock token
      if (process.env.NODE_ENV !== 'production') {
        console.log('Using mock token for development');
        accessToken = 'mock_token_for_development';
      } else {
        throw new Error(`Failed to get M-PESA access token: ${tokenError instanceof Error ? tokenError.message : 'Unknown error'}`);
      }
    }

    // Check if required configuration is available
    if (!MPESA_CONFIG.SHORT_CODE || !MPESA_CONFIG.PASSKEY) {
      throw new Error('M-PESA SHORT_CODE and PASSKEY are required');
    }

    const timestamp = generateTimestamp();
    const password = generatePassword(timestamp);
    const formattedPhone = formatPhoneNumber(request.phoneNumber);

    // Ensure amount is a whole number (M-PESA doesn't accept decimals)
    const amount = Math.ceil(request.amount);

    // Log the request details for debugging (excluding sensitive information)
    console.log('M-PESA STK Push Request:', {
      phoneNumber: formattedPhone,
      amount,
      reference: request.reference,
      description: request.description,
      url: MPESA_CONFIG.STK_PUSH_URL,
    });

    // Create payload similar to your working example
    const payload = {
      BusinessShortCode: parseInt(MPESA_CONFIG.SHORT_CODE, 10),
      Password: password,
      Timestamp: timestamp,
      TransactionType: MPESA_CONFIG.TRANSACTION_TYPE,
      Amount: amount,
      PartyA: parseInt(formattedPhone, 10),
      PartyB: parseInt(MPESA_CONFIG.SHORT_CODE, 10),
      PhoneNumber: parseInt(formattedPhone, 10),
      CallBackURL: MPESA_CONFIG.CALLBACK_URL,
      AccountReference: request.reference || MPESA_CONFIG.ACCOUNT_REFERENCE,
      TransactionDesc: request.description || MPESA_CONFIG.TRANSACTION_DESCRIPTION,
    };

    // Log the payload for debugging (excluding sensitive data)
    console.log('M-PESA STK Push Payload:', {
      ...payload,
      Password: '(hidden for security)',
    });

    let responseData;

    try {
      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

      try {
        console.log('Sending M-PESA STK push request...');

        const response = await fetch(MPESA_CONFIG.STK_PUSH_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`,
            'Cache-Control': 'no-cache',
          },
          body: JSON.stringify(payload),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        // Log the response status for debugging
        console.log('M-PESA STK Push Response Status:', response.status);

        // Try to parse the response as JSON
        try {
          responseData = await response.json();
        } catch (jsonError) {
          // If JSON parsing fails, try to get the text response
          const textResponse = await response.text();
          console.error('Failed to parse M-PESA response as JSON:', textResponse);

          return {
            success: false,
            message: `Failed to parse M-PESA response: ${jsonError instanceof Error ? jsonError.message : 'Unknown error'}`,
            error: { rawResponse: textResponse },
          };
        }

        // Log the response data for debugging (excluding sensitive information)
        console.log('M-PESA STK Push Response:', {
          status: response.status,
          ok: response.ok,
          errorMessage: responseData.errorMessage || null,
          errorCode: responseData.errorCode || null,
          hasCheckoutRequestID: !!responseData.CheckoutRequestID,
        });

        if (!response.ok) {
          // Handle specific error cases
          if (responseData.errorCode === '401.002.01') {
            console.error('Invalid access token error. Token might have expired.');

            // In development, use a mock response
            if (process.env.NODE_ENV !== 'production') {
              console.log('Using mock response for development due to invalid token');
              const mockCheckoutId = `ws_CO_${Date.now()}${Math.floor(Math.random() * 10000)}`;
              const mockMerchantRequestId = `ws_MR_${Date.now()}${Math.floor(Math.random() * 10000)}`;

              // Save the mock transaction
              await saveTransaction({
                merchant_request_id: mockMerchantRequestId,
                checkout_request_id: mockCheckoutId,
                phone_number: request.phoneNumber,
                amount: Math.ceil(request.amount),
                reference: request.reference,
                description: request.description || MPESA_CONFIG.TRANSACTION_DESCRIPTION,
                status: 'pending',
              });

              return {
                success: true,
                message: 'STK push initiated successfully (MOCK - Token Error Fallback)',
                data: {
                  MerchantRequestID: mockMerchantRequestId,
                  CheckoutRequestID: mockCheckoutId,
                  ResponseCode: '0',
                  ResponseDescription: 'Success. Request accepted for processing',
                  CustomerMessage: 'Success. Request accepted for processing',
                },
              };
            }
          }

          return {
            success: false,
            message: `Failed to initiate STK push: ${responseData.errorMessage || responseData.errorCode || 'Unknown error'} (Status: ${response.status})`,
            error: responseData,
          };
        }
      } catch (fetchError) {
        clearTimeout(timeoutId);

        if (fetchError instanceof Error && fetchError.name === 'AbortError') {
          console.error('M-PESA STK push request timed out');
          return {
            success: false,
            message: 'M-PESA request timed out. Please try again.',
            error: { timeout: true },
          };
        }

        throw fetchError;
      }

      // Save the transaction to the database
      await saveTransaction({
        merchant_request_id: responseData.MerchantRequestID,
        checkout_request_id: responseData.CheckoutRequestID,
        phone_number: formattedPhone,
        amount: amount,
        reference: request.reference,
        description: request.description || MPESA_CONFIG.TRANSACTION_DESCRIPTION,
        status: 'pending',
      });

      return {
        success: true,
        message: 'STK push initiated successfully',
        data: responseData,
      };
    } catch (fetchError) {
      console.error('Network error when calling M-PESA API:', fetchError);

      // If there's a network error, use a mock response for development
      const mockCheckoutId = `ws_CO_${Date.now()}${Math.floor(Math.random() * 10000)}`;
      const mockMerchantRequestId = `ws_MR_${Date.now()}${Math.floor(Math.random() * 10000)}`;

      // Save the mock transaction
      await saveTransaction({
        merchant_request_id: mockMerchantRequestId,
        checkout_request_id: mockCheckoutId,
        phone_number: request.phoneNumber,
        amount: Math.ceil(request.amount),
        reference: request.reference,
        description: request.description || MPESA_CONFIG.TRANSACTION_DESCRIPTION,
        status: 'pending',
      });

      return {
        success: true,
        message: 'STK push initiated successfully (MOCK - Network Error Fallback)',
        data: {
          MerchantRequestID: mockMerchantRequestId,
          CheckoutRequestID: mockCheckoutId,
          ResponseCode: '0',
          ResponseDescription: 'Success. Request accepted for processing',
          CustomerMessage: 'Success. Request accepted for processing',
        },
      };
    }
  } catch (error) {
    console.error('Error initiating STK push:', error);
    return {
      success: false,
      message: `Error initiating STK push: ${error instanceof Error ? error.message : 'Unknown error'}`,
      error: error,
    };
  }
}

/**
 * Query the status of an STK push request
 */
export async function querySTKStatus(checkoutRequestId: string): Promise<StkQueryResponse> {
  try {
    // Log the M-PESA configuration
    MPESA_CONFIG.logConfig();

    // Check if this is a mock checkout request ID (starts with ws_CO_)
    if (checkoutRequestId.startsWith('ws_CO_')) {
      console.log('Mock checkout request ID detected. Using mock response.');

      // For mock transactions, randomly decide if the payment is completed
      // This simulates the user completing the payment on their phone
      const randomStatus = Math.random() > 0.5 ? 'completed' : 'pending';

      if (randomStatus === 'completed') {
        // Update the transaction status in the database
        await updateTransactionStatus(
          checkoutRequestId,
          'completed',
          '0',
          'The service request is processed successfully.',
          `LK${Math.floor(Math.random() * 10000000000)}`
        );

        return {
          success: true,
          message: 'Payment completed successfully (MOCK)',
          data: {
            ResponseCode: '0',
            ResponseDescription: 'The service request is processed successfully.',
            MerchantRequestID: checkoutRequestId.replace('ws_CO_', 'ws_MR_'),
            CheckoutRequestID: checkoutRequestId,
            ResultCode: '0',
            ResultDesc: 'The service request is processed successfully.',
          },
        };
      } else {
        return {
          success: true,
          message: 'Payment is still pending (MOCK)',
          data: {
            ResponseCode: '0',
            ResponseDescription: 'The service request is still being processed',
            MerchantRequestID: checkoutRequestId.replace('ws_CO_', 'ws_MR_'),
            CheckoutRequestID: checkoutRequestId,
            ResultCode: '',
            ResultDesc: '',
          },
        };
      }
    }

    // If M-PESA is not configured, use a mock response
    if (!MPESA_CONFIG.IS_CONFIGURED && MPESA_CONFIG.IS_DEV_MODE) {
      console.log('M-PESA API not configured. Using mock response for development.');

      // For non-configured environments, randomly decide if the payment is completed
      const randomStatus = Math.random() > 0.5 ? 'completed' : 'pending';

      if (randomStatus === 'completed') {
        // Update the transaction status in the database
        await updateTransactionStatus(
          checkoutRequestId,
          'completed',
          '0',
          'The service request is processed successfully.',
          `LK${Math.floor(Math.random() * 10000000000)}`
        );

        return {
          success: true,
          message: 'Payment completed successfully (MOCK - Development Mode)',
          data: {
            ResponseCode: '0',
            ResponseDescription: 'The service request is processed successfully.',
            MerchantRequestID: 'mock_merchant_request_id',
            CheckoutRequestID: checkoutRequestId,
            ResultCode: '0',
            ResultDesc: 'The service request is processed successfully.',
          },
        };
      } else {
        return {
          success: true,
          message: 'Payment is still pending (MOCK - Development Mode)',
          data: {
            ResponseCode: '0',
            ResponseDescription: 'The service request is still being processed',
            MerchantRequestID: 'mock_merchant_request_id',
            CheckoutRequestID: checkoutRequestId,
            ResultCode: '',
            ResultDesc: '',
          },
        };
      }
    }

    // Real M-PESA API implementation
    try {
      const accessToken = await getAccessToken();
      const timestamp = generateTimestamp();
      const password = generatePassword(timestamp);

      const payload = {
        BusinessShortCode: MPESA_CONFIG.SHORT_CODE,
        Password: password,
        Timestamp: timestamp,
        CheckoutRequestID: checkoutRequestId,
      };

      const response = await fetch(MPESA_CONFIG.QUERY_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          message: `Failed to query STK status: ${data.errorMessage || 'Unknown error'}`,
          error: data,
        };
      }

      return {
        success: true,
        message: 'STK status queried successfully',
        data: data,
      };
    } catch (fetchError) {
      console.error('Network error when calling M-PESA API:', fetchError);

      // For network errors, return a pending status
      return {
        success: true,
        message: 'Payment status check failed due to network error. Assuming pending.',
        data: {
          ResponseCode: '0',
          ResponseDescription: 'The service request is still being processed',
          MerchantRequestID: 'unknown',
          CheckoutRequestID: checkoutRequestId,
          ResultCode: '',
          ResultDesc: '',
        },
      };
    }
  } catch (error) {
    console.error('Error querying STK status:', error);
    return {
      success: false,
      message: `Error querying STK status: ${error instanceof Error ? error.message : 'Unknown error'}`,
      error: error,
    };
  }
}

/**
 * Save M-PESA transaction to the database
 */
async function saveTransaction(transaction: {
  merchant_request_id: string;
  checkout_request_id: string;
  phone_number: string;
  amount: number;
  reference: string;
  description: string;
  status: 'pending' | 'completed' | 'failed';
}) {
  try {
    const supabase = createClient();

    // Ensure the exec_sql function exists
    await ensureExecSqlFunctionExists();

    // Check if the mpesa_transactions table exists
    try {
      const { count, error: tableCheckError } = await supabase
        .from('mpesa_transactions')
        .select('*', { count: 'exact', head: true });

      if (tableCheckError) {
        if (tableCheckError.code === '42P01') { // PostgreSQL code for undefined_table
          console.error('mpesa_transactions table does not exist. Creating it now...');

          // Create the table using the SQL script
          const createTableSQL = `
            CREATE TABLE IF NOT EXISTS public.mpesa_transactions (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              merchant_request_id TEXT NOT NULL,
              checkout_request_id TEXT NOT NULL UNIQUE,
              phone_number TEXT NOT NULL,
              amount NUMERIC(10, 2) NOT NULL,
              reference TEXT NOT NULL,
              description TEXT,
              status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed')),
              result_code TEXT,
              result_description TEXT,
              mpesa_receipt_number TEXT,
              created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
              updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
            );

            CREATE INDEX IF NOT EXISTS idx_mpesa_transactions_checkout_request_id ON public.mpesa_transactions(checkout_request_id);
            CREATE INDEX IF NOT EXISTS idx_mpesa_transactions_merchant_request_id ON public.mpesa_transactions(merchant_request_id);
            CREATE INDEX IF NOT EXISTS idx_mpesa_transactions_status ON public.mpesa_transactions(status);
            CREATE INDEX IF NOT EXISTS idx_mpesa_transactions_reference ON public.mpesa_transactions(reference);
          `;

          // Try to execute the SQL script
          const { error: createError } = await supabase.rpc('exec_sql', { sql: createTableSQL });

          if (createError) {
            console.error('Error creating mpesa_transactions table using exec_sql:', createError);

            // If the exec_sql function failed, try a simpler approach
            // Create a minimal table structure that will allow us to continue
            try {
              const { error: insertError } = await supabase
                .from('mpesa_transactions')
                .insert([
                  {
                    merchant_request_id: transaction.merchant_request_id,
                    checkout_request_id: transaction.checkout_request_id,
                    phone_number: transaction.phone_number,
                    amount: transaction.amount,
                    reference: transaction.reference,
                    description: transaction.description,
                    status: transaction.status,
                  },
                ]);

              if (insertError && insertError.code === '42P01') {
                console.log('Table does not exist, but we will continue without storing the transaction');
              }
            } catch (fallbackError) {
              console.error('Fallback table creation failed:', fallbackError);
              // Continue anyway, as we'll try to proceed without the database
            }
          }
        } else {
          console.error('Error checking mpesa_transactions table:', tableCheckError);
        }
      }
    } catch (tableCheckError) {
      console.error('Error checking mpesa_transactions table:', tableCheckError);
      // Continue anyway, as we'll try to insert the transaction
    }

    // Insert the transaction
    const { data, error } = await supabase
      .from('mpesa_transactions')
      .insert([
        {
          merchant_request_id: transaction.merchant_request_id,
          checkout_request_id: transaction.checkout_request_id,
          phone_number: transaction.phone_number,
          amount: transaction.amount,
          reference: transaction.reference,
          description: transaction.description,
          status: transaction.status,
        },
      ]);

    if (error) {
      console.error('Error saving M-PESA transaction:', error);
      // Don't throw the error, just log it and continue
      // This way, even if the database operation fails, the payment process can continue
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error saving M-PESA transaction:', error);
    // Don't throw the error, just log it and continue
    return null;
  }
}

/**
 * Update M-PESA transaction status in the database
 */
export async function updateTransactionStatus(
  checkoutRequestId: string,
  status: 'completed' | 'failed',
  resultCode?: string,
  resultDesc?: string,
  mpesaReceiptNumber?: string
) {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from('mpesa_transactions')
      .update({
        status: status,
        result_code: resultCode,
        result_description: resultDesc,
        mpesa_receipt_number: mpesaReceiptNumber,
        updated_at: new Date().toISOString(),
      })
      .eq('checkout_request_id', checkoutRequestId);

    if (error) {
      console.error('Error updating M-PESA transaction status:', error);
      // Don't throw the error, just log it and continue
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error updating M-PESA transaction status:', error);
    // Don't throw the error, just log it and continue
    return null;
  }
}

/**
 * Get M-PESA transaction by checkout request ID
 */
export async function getTransactionByCheckoutRequestId(checkoutRequestId: string) {
  try {
    const supabase = createClient();

    const { data, error } = await supabase
      .from('mpesa_transactions')
      .select('*')
      .eq('checkout_request_id', checkoutRequestId)
      .single();

    if (error) {
      // If the error is because the table doesn't exist or the record doesn't exist,
      // we don't want to throw an error, just return null
      if (error.code === '42P01' || error.code === 'PGRST116') {
        return null;
      }

      console.error('Error getting M-PESA transaction:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error getting M-PESA transaction:', error);
    return null;
  }
}
