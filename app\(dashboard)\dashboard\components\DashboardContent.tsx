'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { useAuth } from '@/app/hooks/useAuth';
import { MoreVertical, Wallet, PieChart } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/app/components/ui/Button';
import Spinner from '@/app/components/ui/Spinner';
import { usePermissions } from '@/app/hooks/usePermissions';
import { useUserCookie } from '@/app/hooks/useUserCookie';

function DashboardContent() {
    const { user } = useAuth();
    const { id, name, roleId, roleName, isLoading: cookieLoading } = useUserCookie();
    const [isEmployee, setIsEmployee] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    // Function to get time-based greeting
    const getGreeting = () => {
        const hour = new Date().getHours();

        if (hour < 12) {
            return 'Good Morning';
        } else if (hour < 18) {
            return 'Good Afternoon';
        } else {
            return 'Good Evening';
        }
    };

    // State to track user role type
    const [userRoleType, setUserRoleType] = useState<string>('');

    // Set role status based on role name from cookie
    useEffect(() => {
        if (!cookieLoading) {
            if (roleName) {


                // Store the actual role name
                setUserRoleType(roleName);

                // Check if the role is Employee
                const isEmployeeRole = roleName.toLowerCase() === 'employee';
                setIsEmployee(isEmployeeRole);
                setIsLoading(false);
            } else if (user) {
                // If we have a user but no cookie data, fetch from database
                fetchUserRoleFromDatabase();
            } else {
                setIsLoading(false);
            }
        }
    }, [cookieLoading, roleName, user]);

    // Function to fetch user role from database if needed
    const fetchUserRoleFromDatabase = async () => {
        if (!user) {
            setIsLoading(false);
            return;
        }

        try {
            const supabase = createClient();

            // First, get the user_role entry
            const { data: userRoleData, error: userRoleError } = await supabase
                .from('user_roles')
                .select('role_id')
                .eq('user_id', user.id)
                .single();

            if (userRoleError) {
                console.error('Error fetching user role:', userRoleError);
                setIsEmployee(false);
                return;
            }

            if (!userRoleData || !userRoleData.role_id) {
                console.error('No role found for user');
                setIsEmployee(false);
                return;
            }

            // Get the role name from the roles table
            const { data: roleData, error: roleError } = await supabase
                .from('roles')
                .select('*')
                .eq('id', userRoleData.role_id)
                .single();

            if (roleError) {
                console.error('Error fetching role name:', roleError);
                setIsEmployee(false);
                return;
            }

            if (!roleData || !roleData.name) {
                console.error('No role name found');
                setIsEmployee(false);
                return;
            }

            const dbRoleName = roleData.name;
            setUserRoleType(dbRoleName);
            setIsEmployee(dbRoleName.toLowerCase() === 'employee');
        } catch (error) {
            console.error('Unexpected error in fetchUserRoleFromDatabase:', error);
            setIsEmployee(false);
        } finally {
            setIsLoading(false);
        }
    };

    // Check if user has permission to view parts
    const { hasPermission: canViewParts } = usePermissions('parts:view');

    if (isLoading) {
        return (
            <div className="flex h-full items-center justify-center">
                <Spinner size="lg" />
            </div>
        );
    }



    // Function to determine which dashboard to render based on role
    const getDashboardForRole = () => {
        switch(userRoleType.toLowerCase()) {
            case 'employee':
                return renderEmployeeDashboard();
            case 'admin':
                return renderAdminDashboard();
            case 'super admin':
                return renderSuperAdminDashboard();
            case 'supplier':
                return renderSupplierDashboard();
            default:
                return renderDefaultDashboard();
        }
    };

    // Render functions for different dashboard types
    const renderEmployeeDashboard = () => (
        <div className="mb-8">
            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                <p className="mb-4 text-gray-700">
                    Welcome to the Autoflow system. Here are some quick links:
                </p>
                <div className="space-y-3">
                    {canViewParts && (
                        <Link href="/parts">
                            <Button variant="outline" className="w-full justify-start">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                                    <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z" />
                                </svg>
                                Browse Parts
                            </Button>
                        </Link>
                    )}
                    <Link href="/profile">
                        <Button variant="outline" className="w-full justify-start">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                                <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                                <circle cx="12" cy="7" r="4" />
                            </svg>
                            My Profile
                        </Button>
                    </Link>
                </div>
            </div>
        </div>
    );

    const renderAdminDashboard = () => (
        <>
            <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                {/* Total Parts Card */}
                <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-500">Total Parts</p>
                            <h3 className="mt-1 text-2xl font-semibold text-gray-800">1,234</h3>
                        </div>
                        <div className="rounded-full bg-blue-100 p-3 text-blue-600">
                            <PieChart size={24} />
                        </div>
                    </div>
                    <div className="mt-4">
                        <Link href="/parts">
                            <Button variant="outline" className="w-full justify-between">
                                View All Parts
                                <MoreVertical size={16} />
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Low Stock Card */}
                <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-500">Low Stock</p>
                            <h3 className="mt-1 text-2xl font-semibold text-gray-800">24</h3>
                        </div>
                        <div className="rounded-full bg-red-100 p-3 text-red-600">
                            <Wallet size={24} />
                        </div>
                    </div>
                    <div className="mt-4">
                        <Button variant="outline" className="w-full justify-between">
                            View Low Stock
                            <MoreVertical size={16} />
                        </Button>
                    </div>
                </div>

                {/* Quick Actions Card */}
                <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                    <h3 className="mb-4 text-lg font-semibold text-gray-800">Admin Actions</h3>
                    <div className="space-y-3">
                        <Link href="/admin/users">
                            <Button variant="outline" className="w-full justify-start">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="9" cy="7" r="4"></circle>
                                    <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                </svg>
                                Manage Users
                            </Button>
                        </Link>
                        <Link href="/admin/roles">
                            <Button variant="outline" className="w-full justify-start">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                                </svg>
                                Manage Roles
                            </Button>
                        </Link>
                    </div>
                </div>
            </div>

            {/* Recent Activity Section */}
            <div className="mb-8">
                <h2 className="mb-4 text-xl font-semibold text-gray-800">Recent Activity</h2>
                <div className="rounded-lg border border-gray-200 bg-white shadow-sm">
                    <div className="p-4">
                        <p className="text-center text-gray-500">No recent activity to display.</p>
                    </div>
                </div>
            </div>
        </>
    );

    const renderSuperAdminDashboard = () => (
        <>
            <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                {/* System Stats Card */}
                <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-500">System Users</p>
                            <h3 className="mt-1 text-2xl font-semibold text-gray-800">42</h3>
                        </div>
                        <div className="rounded-full bg-purple-100 p-3 text-purple-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                <circle cx="9" cy="7" r="4"></circle>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                            </svg>
                        </div>
                    </div>
                    <div className="mt-4">
                        <Link href="/admin/users">
                            <Button variant="outline" className="w-full justify-between">
                                Manage Users
                                <MoreVertical size={16} />
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* System Health Card */}
                <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-500">System Health</p>
                            <h3 className="mt-1 text-2xl font-semibold text-green-600">Good</h3>
                        </div>
                        <div className="rounded-full bg-green-100 p-3 text-green-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                        </div>
                    </div>
                    <div className="mt-4">
                        <Link href="/admin/system">
                            <Button variant="outline" className="w-full justify-between">
                                System Dashboard
                                <MoreVertical size={16} />
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Super Admin Actions Card */}
                <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                    <h3 className="mb-4 text-lg font-semibold text-gray-800">Super Admin Actions</h3>
                    <div className="space-y-3">
                        <Link href="/admin/settings">
                            <Button variant="outline" className="w-full justify-start">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                                    <circle cx="12" cy="12" r="3"></circle>
                                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                                </svg>
                                System Settings
                            </Button>
                        </Link>
                        <Link href="/admin/audit-logs">
                            <Button variant="outline" className="w-full justify-start">
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                    <polyline points="14 2 14 8 20 8"></polyline>
                                    <line x1="16" y1="13" x2="8" y2="13"></line>
                                    <line x1="16" y1="17" x2="8" y2="17"></line>
                                    <polyline points="10 9 9 9 8 9"></polyline>
                                </svg>
                                Audit Logs
                            </Button>
                        </Link>
                    </div>
                </div>
            </div>
        </>
    );

    const renderSupplierDashboard = () => (
        <div className="mb-8">
            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                <p className="mb-4 text-gray-700">
                    Welcome to the Autoflow system. As a supplier, you have access to the following features:
                </p>
                <div className="space-y-3">
                    <Link href="/supplier/inventory">
                        <Button variant="outline" className="w-full justify-start">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                                <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                            </svg>
                            Inventory Status
                        </Button>
                    </Link>
                    <Link href="/supplier/orders">
                        <Button variant="outline" className="w-full justify-start">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                                <circle cx="9" cy="21" r="1"></circle>
                                <circle cx="20" cy="21" r="1"></circle>
                                <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                            </svg>
                            Orders
                        </Button>
                    </Link>
                </div>
            </div>
        </div>
    );

    const renderDefaultDashboard = () => (
        <div className="mb-8">
            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                <p className="mb-4 text-gray-700">
                    Welcome to the Autoflow system. Please contact an administrator if you need access to additional features.
                </p>
                <div className="space-y-3">
                    <Link href="/profile">
                        <Button variant="outline" className="w-full justify-start">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                                <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2" />
                                <circle cx="12" cy="7" r="4" />
                            </svg>
                            My Profile
                        </Button>
                    </Link>
                </div>
            </div>
        </div>
    );

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="mb-8">
                <h1 className="text-2xl font-bold text-gray-800">
                    {getGreeting()}, {user?.user_metadata?.full_name || name || 'User'}
                </h1>
                <p className="text-gray-600">Welcome to your dashboard</p>
            </div>

            {/* Render the appropriate dashboard based on user role */}
            {getDashboardForRole()}
        </div>
    );
}

export default DashboardContent;
