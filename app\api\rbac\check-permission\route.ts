import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/app/libs/supabase/server';

interface UserRole {
  role_id: string;
  roles: {
    name: string;
    description: string;
  };
}

interface RolePermission {
  permission_id: string;
  permissions: {
    name: string;
    description: string;
  };
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({
      request,
      response: NextResponse.next()
    });

    // Get the permission name from the query string
    const url = new URL(request.url);
    const permissionName = url.searchParams.get('permission') || 'admin:access_panel';

    // Get the current user (more secure than getSession)
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError) {
      return NextResponse.json({ error: 'Authentication error', details: authError.message }, { status: 401 });
    }

    if (!user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const userId = user.id;

    // Get user roles first to check if user is a Super Admin
    const { data: userRoles, error: rolesError } = await supabase
      .from('user_roles')
      .select('role_id, roles(name, description)')
      .eq('user_id', userId);

    if (rolesError) {
      return NextResponse.json({
        error: 'Error fetching user roles',
        details: rolesError.message
      }, { status: 500 });
    }

    // Use double type assertion to avoid TypeScript errors
    const typedUserRoles = (userRoles as unknown) as UserRole[] || [];
    const isSuperAdmin = typedUserRoles.some(ur => ur.roles?.name === 'Super Admin');

    console.log('[API] User roles:', typedUserRoles);
    console.log('[API] Is Super Admin:', isSuperAdmin);

    // If user is a Super Admin, they automatically have all permissions
    if (isSuperAdmin) {
      return NextResponse.json({
        userId,
        permissionName,
        hasPermission: true,
        userRoles: typedUserRoles,
        isSuperAdmin,
        message: 'Super Admin role has all permissions'
      });
    }

    // For non-Super Admin users, check for the specified permission
    const { data: hasPermission, error: permissionError } = await supabase.rpc('check_user_permission', {
      p_user_id: userId,
      p_permission_name: permissionName
    });

    if (permissionError) {
      return NextResponse.json({
        error: 'Error checking permission',
        details: permissionError.message
      }, { status: 500 });
    }



    // Get permissions for the Super Admin role
    const superAdminRole = typedUserRoles.find(ur => ur.roles?.name === 'Super Admin');
    let rolePermissions: RolePermission[] = [];

    if (superAdminRole) {
      const { data: permissions, error: permissionsError } = await supabase
        .from('role_permissions')
        .select('permission_id, permissions(name, description)')
        .eq('role_id', superAdminRole.role_id);

      if (!permissionsError && permissions) {
        // Use type assertion with unknown as intermediate step
        rolePermissions = (permissions as unknown) as RolePermission[];
      }
    }

    // Check if the function exists
    const { data: functions, error: functionsError } = await supabase
      .from('pg_catalog.pg_proc')
      .select('proname')
      .eq('proname', 'check_user_permission')
      .limit(1);

    const functionExists = functions && functions.length > 0;

    return NextResponse.json({
      userId,
      permissionName,
      hasPermission,
      userRoles: typedUserRoles,
      superAdminRole: superAdminRole || null,
      isSuperAdmin,
      rolePermissions,
      functionExists,
      session: {
        id: (session as any).user.id,
        email: (session as any).user.email,
        role: (session as any).user.role,
      }
    });

  } catch (error: any) {
    console.error('Error in permission check:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}
