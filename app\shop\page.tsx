'use client';

import React, { Suspense, useEffect, useRef } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import FrontendLayout from '../components/frontend/FrontendLayout';
import PartsListing from '../components/frontend/PartsListing';

// Fallback component to show while loading
const PartsListingFallback = () => (
  <div className="container mx-auto px-4 py-8 bg-white">
    <div className="animate-pulse">
      <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="bg-gray-200 rounded-lg h-64"></div>
        ))}
      </div>
    </div>
  </div>
);

// Component that uses useSearchParams
const ShopContent = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const categoryId = searchParams.get('category') ? parseInt(searchParams.get('category') as string) : undefined;
  const searchQuery = searchParams.get('query') || undefined;
  const page = searchParams.get('page') ? parseInt(searchParams.get('page') as string) : 1;
  const sort = searchParams.get('sort') || 'featured';

  // Get car filter parameters from URL if present
  const carParams = {
    brandId: searchParams.get('brandId') ? parseInt(searchParams.get('brandId') as string) : undefined,
    modelId: searchParams.get('modelId') ? parseInt(searchParams.get('modelId') as string) : undefined,
    generationId: searchParams.get('generationId') ? parseInt(searchParams.get('generationId') as string) : undefined,
    variationId: searchParams.get('variationId') ? parseInt(searchParams.get('variationId') as string) : undefined,
    trimId: searchParams.get('trimId') ? parseInt(searchParams.get('trimId') as string) : undefined,
    brandName: searchParams.get('brandName') || undefined,
    modelName: searchParams.get('modelName') || undefined,
    generationName: searchParams.get('generationName') || undefined,
    variationName: searchParams.get('variationName') || undefined,
    trimName: searchParams.get('trimName') || undefined
  };

  // Check if we have any car filter parameters
  const hasCarFilter = Object.values(carParams).some(value => value !== undefined);

  // Debug car filter parameters
  console.log('Car filter parameters from URL:', hasCarFilter ? carParams : 'None');

  // Store the previous category ID to detect changes
  const prevCategoryIdRef = useRef<number | undefined>(categoryId);

  // Reset to page 1 when category changes
  useEffect(() => {
    // If category has changed and it's not the initial render
    if (categoryId !== prevCategoryIdRef.current && prevCategoryIdRef.current !== undefined) {
      console.log('Category changed from', prevCategoryIdRef.current, 'to', categoryId);
      const params = new URLSearchParams(searchParams.toString());
      params.set('page', '1');
      router.replace(`/shop?${params.toString()}`, { scroll: false });
    }

    // Update the ref with current category ID
    prevCategoryIdRef.current = categoryId;
  }, [categoryId, searchParams, router]);

  return (
    <PartsListing
      categoryId={categoryId}
      searchQuery={searchQuery}
      initialPage={page}
      initialSort={sort}
      initialCarFilter={hasCarFilter ? carParams : undefined}
    />
  );
};

export default function ShopPage() {
  return (
    <FrontendLayout>
      <Suspense fallback={<PartsListingFallback />}>
        <ShopContent />
      </Suspense>
    </FrontendLayout>
  );
}
