'use client';

import React, { useState, useEffect } from 'react';
import MultiLevelMenu from './MultiLevelMenu';
import { useMediaQuery } from 'react-responsive';
import { getMenuItems, MenuItem } from './menuData'; // Import the function

interface SidebarProps {}

const Sidebar: React.FC<SidebarProps> = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]); // State for menu items

  useEffect(() => {
    const fetchMenuItems = async () => {
      const items = await getMenuItems();
      setMenuItems(items);
    };
    fetchMenuItems();
  }, []);

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  const isMobile = useMediaQuery({ query: '(max-width: 768px)' });

  return (
    <>
      {isMobile && (
        <button
          onClick={toggleSidebar}
          className="fixed top-4 left-4 z-30 text-gray-600 hover:text-gray-800"
        >
          <svg
            className="h-6 w-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 6h16M4 12h16m-7 6h7"
            />
          </svg>
        </button>
      )}

      <aside
        className={`${
          isMobile
            ? isOpen
              ? 'fixed z-20 inset-y-0 left-0 w-64 transition duration-300 ease-in-out transform translate-x-0'
              : 'fixed z-20 inset-y-0 left-0 w-64 transition duration-300 ease-in-out transform -translate-x-full'
            : 'h-screen w-100'
        } bg-gray-800 text-white`}
      >
        <div className="p-4">
          <h2 className="text-lg font-semibold">Eagle 1</h2>
        </div>
        <nav>
          <MultiLevelMenu items={menuItems} /> {/* Use imported menuItems */}
        </nav>
      </aside>
    </>
  );
};

export default Sidebar;