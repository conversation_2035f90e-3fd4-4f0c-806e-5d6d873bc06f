'use client';

import React, { useState, Suspense } from 'react';
import DesktopSidebar from '@/app/components/dashboard/sidebar/DesktopSidebar';
import BottomNavigation from '@/app/components/ui/menu/BottomNavigation';
import Notification from '@/app/components/ui/Notification';
import Link from 'next/link';
import { menuItems } from '@/app/config/menu';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import Drawer from '@/app/components/ui/Drawer';
import CategoriesMenu from '@/app/components/ui/CategoriesMenu';

// MobileMenu component for the drawer
const MobileMenu = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => {
    const pathname = usePathname();

    // Filter menu items for main navigation (excluding bottom items)
    const bottomItemIds = ['settings', 'profile', 'logout'];
    const mainMenuItems = menuItems.filter(item => !bottomItemIds.includes(item.id));
    const bottomMenuItems = menuItems.filter(item => bottomItemIds.includes(item.id));

    return (
        <Drawer
            isOpen={isOpen}
            onClose={onClose}
            direction="left"
            title="Menu"
        >
            <div className="flex flex-col h-full">
                <div className="flex-1">
                    <div className="py-4">
                        <div className="space-y-1">
                            {mainMenuItems.map((item) => {
                                const isActive = pathname === item.href ||
                                                (item.id === 'admin' && pathname?.startsWith('/admin'));
                                const Icon = item.icon;

                                return (
                                    <Link
                                        key={item.id}
                                        href={item.href}
                                        className={`flex items-center px-4 py-3 text-base font-medium rounded-md ${isActive ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100'}`}
                                        onClick={onClose}
                                    >
                                        <Icon size={20} className={`mr-3 ${isActive ? 'text-indigo-600' : 'text-gray-500'}`} />
                                        {item.label}
                                    </Link>
                                );
                            })}
                        </div>
                    </div>
                </div>

                {/* Bottom items (settings, profile, logout) */}
                <div className="py-4 border-t border-gray-200">
                    <div className="space-y-1">
                        {bottomMenuItems.map((item) => {
                            const isActive = pathname === item.href;
                            const Icon = item.icon;

                            return (
                                <Link
                                    key={item.id}
                                    href={item.href}
                                    className={`flex items-center px-4 py-3 text-base font-medium rounded-md ${isActive ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700 hover:bg-gray-100'}`}
                                    onClick={onClose}
                                >
                                    <Icon size={20} className={`mr-3 ${isActive ? 'text-indigo-600' : 'text-gray-500'}`} />
                                    {item.label}
                                </Link>
                            );
                        })}
                    </div>
                </div>
            </div>
        </Drawer>
    );
};

// MobileHeader component
const MobileHeader = ({ onSearch }: { onSearch: (v: string) => void }) => {
    const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
    const [isCategoriesDrawerOpen, setIsCategoriesDrawerOpen] = useState(false);
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');

    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();

    // Find menu items from the shared menu items
    const profileMenuItem = menuItems.find(item => item.id === 'profile');
    const logoutMenuItem = menuItems.find(item => item.id === 'logout');
    const categoriesMenuItem = menuItems.find(item => item.id === 'categories');

    // Initialize search query from URL if on parts page
    React.useEffect(() => {
        if (pathname?.includes('/parts')) {
            const query = searchParams?.get('query');
            if (query) {
                setSearchQuery(query);
            }
        }
    }, [pathname, searchParams]);

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(e.target.value);
    };

    const handleSearchSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSearch(searchQuery);
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            onSearch(searchQuery);
        }
    };

    return (
        <header className="bg-white p-3 border-b border-gray-200">
            {/* Top row with hamburger, title and profile */}
            <div className="flex justify-between items-center mb-3">
                {/* Hamburger menu button */}
                <button
                    className="p-1.5 rounded-md text-gray-500 hover:bg-gray-100"
                    onClick={() => setIsMobileMenuOpen(true)}
                    aria-label="Menu"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>

                {/* Centered title */}
                <h1 className="text-lg font-bold text-center">Autoflow</h1>

                <div className="flex items-center space-x-2">
                    <button
                        className="flex items-center p-1.5 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
                        onClick={() => setIsCategoriesDrawerOpen(true)}
                    >
                        {categoriesMenuItem && <categoriesMenuItem.icon size={16} className="text-gray-500" />}
                    </button>

                    <div className="relative">
                        <button
                            onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
                            className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors"
                            aria-label="User profile"
                        >
                            {profileMenuItem && <profileMenuItem.icon size={18} />}
                        </button>

                        {/* Profile Dropdown Menu */}
                        {isProfileMenuOpen && (
                            <>
                                <div
                                    className="fixed inset-0 z-10"
                                    onClick={() => setIsProfileMenuOpen(false)}
                                />
                                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 border border-gray-200 z-20">
                                    {profileMenuItem && (
                                        <Link
                                            href={profileMenuItem.href}
                                            className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100"
                                            onClick={() => setIsProfileMenuOpen(false)}
                                        >
                                            <profileMenuItem.icon size={16} className="mr-2" />
                                            {profileMenuItem.label}
                                        </Link>
                                    )}
                                    {logoutMenuItem && (
                                        <Link
                                            href={logoutMenuItem.href}
                                            className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100"
                                            onClick={() => setIsProfileMenuOpen(false)}
                                        >
                                            <logoutMenuItem.icon size={16} className="mr-2" />
                                            {logoutMenuItem.label}
                                        </Link>
                                    )}
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </div>

            {/* Search row */}
            <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                    </svg>
                </div>
                <form onSubmit={handleSearchSubmit}>
                    <input
                        type="text"
                        placeholder="Search parts..."
                        className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                        value={searchQuery}
                        onChange={handleSearchChange}
                        onKeyDown={handleKeyDown}
                    />
                </form>
            </div>

            {/* Categories Drawer */}
            <Drawer
                isOpen={isCategoriesDrawerOpen}
                onClose={() => setIsCategoriesDrawerOpen(false)}
                direction="right"
                title="Categories"
            >
                <CategoriesMenu />
            </Drawer>

            {/* Mobile Menu Drawer */}
            <MobileMenu
                isOpen={isMobileMenuOpen}
                onClose={() => setIsMobileMenuOpen(false)}
            />
        </header>
    );
};

// DesktopHeader component
const DesktopHeader = ({ onSearch }: { onSearch: (v: string) => void }) => {
    const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
    const [isCategoriesDrawerOpen, setIsCategoriesDrawerOpen] = useState(false);

    const [searchQuery, setSearchQuery] = useState('');
    const router = useRouter();
    const pathname = usePathname();
    const searchParams = useSearchParams();

    // Find menu items from the shared menu items
    const profileMenuItem = menuItems.find(item => item.id === 'profile');
    const logoutMenuItem = menuItems.find(item => item.id === 'logout');
    const categoriesMenuItem = menuItems.find(item => item.id === 'categories');

    // Initialize search query from URL if on parts page
    React.useEffect(() => {
        if (pathname?.includes('/parts')) {
            const query = searchParams?.get('query');
            if (query) {
                setSearchQuery(query);
            }
        }
    }, [pathname, searchParams]);

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setSearchQuery(e.target.value);
    };

    const handleSearchSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSearch(searchQuery);
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            onSearch(searchQuery);
        }
    };

    return (
        <header className="bg-white p-4 border-b border-gray-200 flex justify-between items-center">
            <h1 className="text-xl font-bold">Autoflow</h1>

            <div className="flex-1 max-w-md mx-8">
                <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <form onSubmit={handleSearchSubmit}>
                        <input
                            type="text"
                            placeholder="Search parts... (⌘+F)"
                            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                            value={searchQuery}
                            onChange={handleSearchChange}
                            onKeyDown={handleKeyDown}
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 text-xs">
                            ⌘+F
                        </div>
                    </form>
                </div>
            </div>

            <div className="flex items-center space-x-4">
                <button
                    className="flex items-center px-3 py-1.5 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
                    onClick={() => {}}
                >
                    <svg className="h-4 w-4 mr-1.5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                    </svg>
                    Date Range
                </button>

                <button
                    className="flex items-center px-3 py-1.5 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
                    onClick={() => {}}
                >
                    <svg className="h-4 w-4 mr-1.5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
                    </svg>
                    Filter by Team
                </button>

                <button
                    className="flex items-center px-3 py-1.5 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
                    onClick={() => setIsCategoriesDrawerOpen(true)}
                >
                    {categoriesMenuItem && <categoriesMenuItem.icon size={16} className="mr-1.5 text-gray-500" />}
                    Categories
                </button>

                {/* Categories Drawer */}
                <Drawer
                    isOpen={isCategoriesDrawerOpen}
                    onClose={() => setIsCategoriesDrawerOpen(false)}
                    direction="right"
                    title="Categories"
                >
                    <CategoriesMenu />
                </Drawer>

                <div className="relative">
                    <button
                        onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
                        className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-600 hover:bg-gray-200 transition-colors"
                        aria-label="User profile"
                    >
                        {profileMenuItem && <profileMenuItem.icon size={20} />}
                    </button>

                    {/* Profile Dropdown Menu */}
                    {isProfileMenuOpen && (
                        <>
                            <div
                                className="fixed inset-0 z-10"
                                onClick={() => setIsProfileMenuOpen(false)}
                            />
                            <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 border border-gray-200 z-20">
                                {profileMenuItem && (
                                    <Link
                                        href={profileMenuItem.href}
                                        className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100"
                                        onClick={() => setIsProfileMenuOpen(false)}
                                    >
                                        <profileMenuItem.icon size={16} className="mr-2" />
                                        {profileMenuItem.label}
                                    </Link>
                                )}
                                {logoutMenuItem && (
                                    <Link
                                        href={logoutMenuItem.href}
                                        className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100"
                                        onClick={() => setIsProfileMenuOpen(false)}
                                    >
                                        <logoutMenuItem.icon size={16} className="mr-2" />
                                        {logoutMenuItem.label}
                                    </Link>
                                )}
                            </div>
                        </>
                    )}
                </div>
            </div>
        </header>
    );
};

// Loading fallback component
function DashboardSkeleton() {
  return (
    <div className="flex bg-gray-50 min-h-screen">
      <div className="hidden md:block md:w-64 bg-white border-r border-gray-200 animate-pulse">
        <div className="h-full p-4">
          <div className="h-8 bg-gray-200 rounded w-32 mb-8"></div>
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map(i => (
              <div key={i} className="h-10 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>

      <div className="flex-1 flex flex-col">
        <div className="bg-white p-4 border-b border-gray-200 animate-pulse">
          <div className="flex justify-between items-center">
            <div className="h-8 bg-gray-200 rounded w-32"></div>
            <div className="h-8 bg-gray-200 rounded w-48"></div>
          </div>
        </div>

        <main className="flex-1 p-4 md:p-6 lg:p-8 animate-pulse">
          <div className="space-y-4">
            <div className="h-8 bg-gray-200 rounded w-64"></div>
            <div className="h-32 bg-gray-200 rounded w-full"></div>
            <div className="h-64 bg-gray-200 rounded w-full"></div>
          </div>
        </main>
      </div>
    </div>
  );
}

// Main layout component
function DashboardLayoutContent({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  const handleSearch = (searchQuery: string) => {
    if (searchQuery.trim()) {
      router.push(`/parts?query=${encodeURIComponent(searchQuery)}&page=1`);
    }
  };

  const handleOpenModal = () => {
    console.log('Dashboard layout handleOpenModal called, pathname:', pathname);

    // If we're not on the parts page, redirect there with the openAddModal parameter
    if (!pathname?.startsWith('/parts')) {
      router.push('/parts?openAddModal=true');
      return;
    }

    // Otherwise, set the modal state (though this won't do anything since the modal is in PartsContent)
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSuccess = () => {
    setIsModalOpen(false);
    setShowSuccessNotification(true);

    // Hide notification after 5 seconds
    setTimeout(() => {
      setShowSuccessNotification(false);
    }, 5000);
  };

  return (
    <div className="flex bg-gray-50 min-h-screen">
      <DesktopSidebar /> {/* Sidebar visible on md screens and up */}

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Desktop Header (visible on md+) */}
        <div className="hidden md:block">
          <DesktopHeader onSearch={handleSearch} />
        </div>

        {/* Mobile Header (visible on small screens only) */}
        <div className="md:hidden">
          <MobileHeader onSearch={handleSearch} />
        </div>

        {/* Page Content */}
        <main className="flex-1 overflow-y-auto p-4 md:p-6 lg:p-8">
          {children}
        </main>

        {/* Bottom Navigation (Mobile only - hidden on md+) */}
        {/* Only show the bottom navigation if we're not on the parts page, since parts has its own */}
        {!pathname?.startsWith('/parts') && (
          <div className="md:hidden">
            <BottomNavigation onOpenAddModal={handleOpenModal} />
          </div>
        )}
      </div>

      {/* Notification */}
      {showSuccessNotification && (
        <Notification
          header="Success!"
          body="Operation completed successfully"
          type="success"
          duration={5000}
          position="bottom-middle"
        />
      )}
    </div>
  );
}

// Export the layout with Suspense
export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <Suspense fallback={<DashboardSkeleton />}>
      <DashboardLayoutContent>{children}</DashboardLayoutContent>
    </Suspense>
  );
}
