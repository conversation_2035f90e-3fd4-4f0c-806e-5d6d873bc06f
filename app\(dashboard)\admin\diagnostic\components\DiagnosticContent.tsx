'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/app/components/ui/Button';

const DiagnosticContent = () => {
  const [focusEvents, setFocusEvents] = useState<string[]>([]);
  const [permissionChecks, setPermissionChecks] = useState<string[]>([]);
  const [visibilityEvents, setVisibilityEvents] = useState<string[]>([]);
  
  // Track focus/blur events
  useEffect(() => {
    const handleFocus = () => {
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] Window focus event`);
      setFocusEvents(prev => [...prev, `[${timestamp}] Window focused`]);
    };
    
    const handleBlur = () => {
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] Window blur event`);
      setFocusEvents(prev => [...prev, `[${timestamp}] Window blurred`]);
    };
    
    // Track visibility changes
    const handleVisibilityChange = () => {
      const timestamp = new Date().toISOString();
      const state = document.visibilityState;
      console.log(`[${timestamp}] Visibility changed to: ${state}`);
      setVisibilityEvents(prev => [...prev, `[${timestamp}] Visibility: ${state}`]);
    };

    // Listen for permission check logs from other admin pages
    const originalConsoleLog = console.log;
    console.log = (...args) => {
      originalConsoleLog(...args);
      
      // Check if this is a permission check log
      const logStr = args.join(' ');
      if (logStr.includes('Permission check complete') || logStr.includes('Permission granted')) {
        const timestamp = new Date().toISOString();
        setPermissionChecks(prev => [...prev, `[${timestamp}] ${logStr}`]);
      }
    };

    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', handleBlur);
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Log initial state
    const timestamp = new Date().toISOString();
    setFocusEvents([`[${timestamp}] Component mounted`]);
    setVisibilityEvents([`[${timestamp}] Initial visibility: ${document.visibilityState}`]);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', handleBlur);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      console.log = originalConsoleLog;
    };
  }, []);
  
  const clearLogs = () => {
    setFocusEvents([]);
    setPermissionChecks([]);
    setVisibilityEvents([]);
  };
  
  return (
    <div className="p-6">
      <h1 className="mb-6 text-2xl font-bold">Admin Diagnostic Page</h1>
      
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Event Logs</h2>
          <Button onClick={clearLogs} variant="outline">Clear Logs</Button>
        </div>
        <p className="mb-4 text-gray-600">
          This page tracks browser events that might cause admin pages to refresh.
          Open this page in one tab, then navigate to other admin pages in another tab to see how they interact.
        </p>
      </div>
      
      <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2">
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
          <h3 className="mb-2 text-lg font-medium">Focus/Blur Events</h3>
          <div className="max-h-60 overflow-y-auto rounded bg-gray-50 p-3">
            {focusEvents.length === 0 ? (
              <p className="text-gray-500">No events recorded yet</p>
            ) : (
              <ul className="space-y-1 text-sm">
                {focusEvents.map((event, index) => (
                  <li key={index} className="font-mono">{event}</li>
                ))}
              </ul>
            )}
          </div>
        </div>
        
        <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
          <h3 className="mb-2 text-lg font-medium">Visibility Events</h3>
          <div className="max-h-60 overflow-y-auto rounded bg-gray-50 p-3">
            {visibilityEvents.length === 0 ? (
              <p className="text-gray-500">No events recorded yet</p>
            ) : (
              <ul className="space-y-1 text-sm">
                {visibilityEvents.map((event, index) => (
                  <li key={index} className="font-mono">{event}</li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </div>
      
      <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
        <h3 className="mb-2 text-lg font-medium">Permission Check Events</h3>
        <div className="max-h-60 overflow-y-auto rounded bg-gray-50 p-3">
          {permissionChecks.length === 0 ? (
            <p className="text-gray-500">No permission checks recorded yet</p>
          ) : (
            <ul className="space-y-1 text-sm">
              {permissionChecks.map((event, index) => (
                <li key={index} className="font-mono">{event}</li>
              ))}
            </ul>
          )}
        </div>
      </div>
      
      <div className="mt-8 rounded-lg border border-amber-200 bg-amber-50 p-4">
        <h3 className="mb-2 text-lg font-medium text-amber-800">Potential Solutions</h3>
        <ul className="list-inside list-disc space-y-2 text-amber-700">
          <li>
            <strong>Memoize permission results</strong> - Store permission check results in localStorage or a React Context
          </li>
          <li>
            <strong>Add visibility check</strong> - Only re-fetch data when visibility changes from hidden to visible
          </li>
          <li>
            <strong>Implement SWR or React Query</strong> - These libraries handle caching and revalidation automatically
          </li>
          <li>
            <strong>Create a global state store</strong> - Using Redux, Zustand, or Jotai to persist state between renders
          </li>
        </ul>
      </div>
    </div>
  );
};

export default DiagnosticContent;
