import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "@/app/components/ui/Dialog";
import Button from "@/app/components/ui/inputs/Button";
import { AlertTriangle, Database } from "lucide-react";

interface ExistingPartModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  partNumber: string;
  isPartNumberRequired: boolean;
  existingPartDetails?: {
    groupId?: number;
    table?: string;
  } | null;
}

export const ExistingPartModal = ({
  isOpen,
  onClose,
  onConfirm,
  partNumber,
  isPartNumberRequired,
  existingPartDetails
}: ExistingPartModalProps) => {
  const getTableDisplayName = (table?: string) => {
    switch (table) {
      case 'part_to_group':
        return 'Compatibility System';
      case 'part_compatibility_groups':
        return 'Parts Catalog';
      default:
        return 'Database';
    }
  };
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-amber-100">
              <AlertTriangle className="h-5 w-5 text-amber-600" />
            </div>
            <div>
              <DialogTitle className="text-lg font-semibold">Part Number Already Exists</DialogTitle>
            </div>
          </div>
          <DialogDescription className="mt-4">
            <div className="flex items-start gap-3 p-4 bg-amber-50 rounded-lg border border-amber-200">
              <Database className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="space-y-3">
                <p className="font-medium text-amber-800">
                  Part number <span className="font-mono bg-amber-100 px-2 py-1 rounded text-sm">{partNumber}</span> already exists in our database.
                </p>

                {existingPartDetails && (
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                    <p className="text-sm font-medium text-amber-800 mb-1">Found in:</p>
                    <p className="text-sm text-amber-700">
                      📍 {getTableDisplayName(existingPartDetails.table)}
                      {existingPartDetails.groupId && (
                        <span className="ml-2 text-xs bg-amber-200 px-2 py-1 rounded">
                          Group ID: {existingPartDetails.groupId}
                        </span>
                      )}
                    </p>
                  </div>
                )}

                {isPartNumberRequired ? (
                  <p className="text-amber-700 text-sm">
                    This part number is already registered in our compatibility system.
                    You can still add this as a separate inventory item if it's a different physical part.
                  </p>
                ) : (
                  <p className="text-amber-700 text-sm">
                    This part number already exists with associated vehicle compatibility data.
                  </p>
                )}
              </div>
            </div>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="gap-2 sm:gap-0">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={onConfirm} className="bg-amber-600 hover:bg-amber-700">
            {isPartNumberRequired ? 'Add as New Part' : 'Add Missing Trim'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}; 