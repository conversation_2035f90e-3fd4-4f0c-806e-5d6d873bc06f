'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { usePermissions } from '@/app/hooks/usePermissions';
import {
  getRoles,
  getPermissions,
  getPermissionCategories,
  getRolePermissions,
  assignPermissionToRole,
  removePermissionFromRole,
  getUsers,
  getUserPermissionOverride,
  setUserPermissionOverride,
  removeUserPermissionOverride,
} from '@/app/libs/rbac/api';
import Tabs, { Tab } from '@/app/components/ui/Tabs';
import Switch from '@/app/components/ui/Switch';
import Spinner from '@/app/components/ui/Spinner';
import { Input } from '@/app/components/ui/Input';
import { Button } from '@/app/components/ui/Button';

interface Role {
  id: string;
  name: string;
  description: string | null;
}

interface Permission {
  id: string;
  name: string;
  description: string | null;
  category: string;
}

interface User {
  id: string;
  username: string | null;
  full_name: string | null;
}

interface PermissionOverride {
  id: string;
  user_id: string;
  permission_id: string;
  granted: boolean;
}

const RolePermissionsContent = () => {
  const [activeTab, setActiveTab] = useState<string | number>('role-permissions');
  const [roles, setRoles] = useState<Role[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [rolePermissions, setRolePermissions] = useState<Permission[]>([]);
  const [userPermissionOverrides, setUserPermissionOverrides] = useState<Record<string, PermissionOverride | null>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingPermissions, setIsLoadingPermissions] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const { hasPermission: canAssignPermissions, isLoading: isAssignPermissionsLoading, error: assignPermissionsError } = 
    usePermissions('admin:assign_permissions');
  const { hasPermission: canOverridePermissions, isLoading: isOverridePermissionsLoading, error: overridePermissionsError } = 
    usePermissions('admin:override_permissions');
  const router = useRouter();

  // Effect 1: Check permissions and redirect if necessary
  useEffect(() => {
    const permissionsLoading = isAssignPermissionsLoading || isOverridePermissionsLoading;
    
    if (!permissionsLoading) {
      console.log(`RolePermissionsPage: Permission check complete. canAssignPermissions: ${canAssignPermissions}, canOverridePermissions: ${canOverridePermissions}`);
      
      // Only redirect if both permission checks fail
      if (!canAssignPermissions && !canOverridePermissions) {
        console.log('RolePermissionsPage: Redirecting to /admin due to missing permissions.');
        setError('You do not have permission to manage role permissions or user permission overrides.');
        setTimeout(() => router.push('/admin'), 50);
      }
    }
  }, [canAssignPermissions, canOverridePermissions, isAssignPermissionsLoading, isOverridePermissionsLoading, router]);

  // Effect 2: Fetch initial data when permissions are checked
  useEffect(() => {
    const permissionsLoading = isAssignPermissionsLoading || isOverridePermissionsLoading;
    const hasAnyPermission = canAssignPermissions || canOverridePermissions;
    
    if (!permissionsLoading && hasAnyPermission) {
      console.log('RolePermissionsPage: At least one permission granted. Fetching initial data...');
      fetchInitialData();
      
      // If user can only manage one type of permissions, automatically select that tab
      if (canAssignPermissions && !canOverridePermissions) {
        setActiveTab('role-permissions');
      } else if (!canAssignPermissions && canOverridePermissions) {
        setActiveTab('user-permissions');
      }
    } else if (!permissionsLoading && !hasAnyPermission) {
      setIsLoading(false); // Ensure loading stops if all permissions denied
    }
  }, [canAssignPermissions, canOverridePermissions, isAssignPermissionsLoading, isOverridePermissionsLoading]);

  const fetchInitialData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const [permissionsResult, categoriesResult, rolesResult, usersResult] = await Promise.all([
        getPermissions(),
        getPermissionCategories(),
        getRoles(),
        getUsers(),
      ]);
      
      if (permissionsResult.error) throw permissionsResult.error;
      if (categoriesResult.error) throw categoriesResult.error;
      if (rolesResult.error) throw rolesResult.error;
      if (usersResult.error) throw usersResult.error;
      
      setPermissions(permissionsResult.data || []);
      setCategories(categoriesResult.data || []);
      setRoles(rolesResult.data || []);
      setUsers(usersResult.data || []);
      
      // Auto-select first role or user if available
      if (rolesResult.data && rolesResult.data.length > 0 && canAssignPermissions) {
        const firstRole = rolesResult.data[0];
        setSelectedRole(firstRole);
        fetchRolePermissions(firstRole.id);
      } else if (usersResult.data && usersResult.data.length > 0 && canOverridePermissions) {
        const firstUser = usersResult.data[0];
        setSelectedUser(firstUser);
        fetchAllUserPermissionOverrides(firstUser.id);
      }
    } catch (err) {
      setError('Failed to fetch initial data. Please try again.');
      console.error('Error fetching initial data:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRolePermissions = async (roleId: string) => {
    setIsLoadingPermissions(true);
    setError(null);
    try {
      const { data, error } = await getRolePermissions(roleId);
      if (error) throw error;
      
      setRolePermissions(data || []);
    } catch (err: any) {
      setError(`Failed to fetch role permissions: ${err.message || 'Please try again.'}`);
      console.error('Error fetching role permissions:', err);
      setRolePermissions([]);
    } finally {
      setIsLoadingPermissions(false);
    }
  };

  const fetchAllUserPermissionOverrides = async (userId: string) => {
    setIsLoadingPermissions(true);
    setError(null);
    try {
      const overrides: Record<string, PermissionOverride | null> = {};
      
      // Initialize all permissions with null (no override)
      permissions.forEach(permission => {
        overrides[permission.id] = null;
      });
      
      // Fetch each permission override
      await Promise.all(
        permissions.map(async permission => {
          const { data } = await getUserPermissionOverride(userId, permission.id);
          overrides[permission.id] = data;
        })
      );
      
      setUserPermissionOverrides(overrides);
    } catch (err: any) {
      setError(`Failed to fetch user permission overrides: ${err.message || 'Please try again.'}`);
      console.error('Error fetching user permission overrides:', err);
    } finally {
      setIsLoadingPermissions(false);
    }
  };

  const handleSelectRole = async (role: Role) => {
    setSelectedRole(role);
    await fetchRolePermissions(role.id);
  };

  const handleSelectUser = async (user: User) => {
    setSelectedUser(user);
    await fetchAllUserPermissionOverrides(user.id);
  };

  const handleToggleRolePermission = async (permissionId: string, isAssigned: boolean) => {
    if (!selectedRole) return;
    
    try {
      if (isAssigned) {
        // Remove permission from role
        const { error } = await removePermissionFromRole(selectedRole.id, permissionId);
        if (error) throw error;
      } else {
        // Assign permission to role
        const { error } = await assignPermissionToRole(selectedRole.id, permissionId);
        if (error) throw error;
      }
      
      // Refresh role permissions
      await fetchRolePermissions(selectedRole.id);
    } catch (err: any) {
      setError(`Failed to ${isAssigned ? 'remove' : 'assign'} permission: ${err.message || 'Please try again.'}`);
      console.error(`Error ${isAssigned ? 'removing' : 'assigning'} permission:`, err);
    }
  };

  const handleToggleUserPermission = async (permissionId: string, currentOverride: PermissionOverride | null) => {
    if (!selectedUser) return;
    
    try {
      if (!currentOverride) {
        // No override exists, create a new one (granted)
        const { error } = await setUserPermissionOverride(selectedUser.id, permissionId, true);
        if (error) throw error;
      } else if (currentOverride.granted) {
        // Override exists and is granted, change to denied
        const { error } = await setUserPermissionOverride(selectedUser.id, permissionId, false);
        if (error) throw error;
      } else {
        // Override exists and is denied, remove the override
        const { error } = await removeUserPermissionOverride(selectedUser.id, permissionId);
        if (error) throw error;
      }
      
      // Refresh user permission overrides
      await fetchAllUserPermissionOverrides(selectedUser.id);
    } catch (err: any) {
      setError(`Failed to update permission override: ${err.message || 'Please try again.'}`);
      console.error('Error updating permission override:', err);
    }
  };

  // Group permissions by category
  const permissionsByCategory = permissions.reduce<Record<string, Permission[]>>((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {});

  const tabs: Tab[] = [];
  
  // Only show tabs for permissions the user has
  if (canAssignPermissions) {
    tabs.push({ id: 'role-permissions', label: 'Role Permissions' });
  }
  
  if (canOverridePermissions) {
    tabs.push({ id: 'user-permissions', label: 'User Permissions' });
  }

  const filteredRoles = roles.filter(role => {
    return role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
           (role.description?.toLowerCase() || '').includes(searchQuery.toLowerCase());
  });

  const filteredUsers = users.filter(user => {
    const fullName = user.full_name?.toLowerCase() || '';
    const username = user.username?.toLowerCase() || '';
    return fullName.includes(searchQuery.toLowerCase()) || username.includes(searchQuery.toLowerCase());
  });

  if (isLoading) {
    return (
      <div className="flex min-h-[300px] items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  if (!canAssignPermissions && !canOverridePermissions) {
    return (
      <div className="rounded-md border border-red-200 bg-red-50 p-6 text-center shadow-sm">
        <h2 className="mb-2 text-xl font-semibold text-red-700">Access Denied</h2>
        <p className="mb-4 text-red-600">
          {error || 'You do not have the required permissions to manage role permissions or user permission overrides.'}
        </p>
        {(assignPermissionsError || overridePermissionsError) && (
          <p className="mb-4 text-sm text-red-500">
            Details: {assignPermissionsError?.message || overridePermissionsError?.message}
          </p>
        )}
        <Button
          onClick={() => router.push('/admin')}
          variant="destructive"
          className="px-4 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        >
          Return to Admin Home
        </Button>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-xl font-semibold">Role & User Permissions</h2>
        <p className="mt-2 text-gray-600">
          Manage permissions for roles and set user-specific permission overrides.
        </p>
      </div>

      {error && (
        <div className="mb-4 rounded-md bg-red-50 p-4 text-red-800">
          <p>{error}</p>
          <button
            onClick={() => setError(null)}
            className="ml-2 text-sm font-medium text-red-600 hover:text-red-800"
          >
            Dismiss
          </button>
        </div>
      )}

      {tabs.length > 1 && (
        <Tabs
          tabs={tabs}
          activeTabId={activeTab}
          onTabChange={setActiveTab}
          className="mb-6"
        />
      )}

      {activeTab === 'role-permissions' && canAssignPermissions && (
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Role Selection */}
          <div className="col-span-1 rounded-lg border border-gray-200 bg-white shadow-sm">
            <div className="p-4">
              <h3 className="mb-4 text-lg font-medium">Roles</h3>
              <div className="mb-4">
                <Input
                  type="text"
                  placeholder="Search roles..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="max-h-96 overflow-y-auto">
                {filteredRoles.length === 0 ? (
                  <p className="py-4 text-center text-sm text-gray-500">
                    {searchQuery ? 'No roles match your search.' : 'No roles found.'}
                  </p>
                ) : (
                  <ul className="divide-y divide-gray-200">
                    {filteredRoles.map((role) => (
                      <li
                        key={role.id}
                        className={`cursor-pointer py-3 hover:bg-gray-50 ${
                          selectedRole?.id === role.id ? 'bg-blue-50' : ''
                        }`}
                        onClick={() => handleSelectRole(role)}
                      >
                        <div className="px-2">
                          <p className="font-medium text-gray-900">{role.name}</p>
                          {role.description && (
                            <p className="text-sm text-gray-500">{role.description}</p>
                          )}
                        </div>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
          </div>

          {/* Permission Assignment */}
          <div className="col-span-1 lg:col-span-2">
            {selectedRole ? (
              <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                <h3 className="mb-4 text-lg font-medium">
                  Permissions for {selectedRole.name}
                </h3>
                
                {isLoadingPermissions ? (
                  <div className="flex justify-center py-10">
                    <Spinner size="md" />
                  </div>
                ) : (
                  <>
                    <p className="mb-6 text-sm text-gray-600">
                      Toggle permissions to assign or remove them from this role.
                    </p>
                    
                    {categories.map((category) => {
                      const categoryPermissions = permissionsByCategory[category] || [];
                      
                      if (categoryPermissions.length === 0) return null;
                      
                      return (
                        <div key={category} className="mb-6">
                          <h4 className="mb-3 text-md font-medium text-gray-900">{category}</h4>
                          <div className="space-y-3">
                            {categoryPermissions.map((permission) => {
                              const isAssigned = rolePermissions.some(p => p.id === permission.id);
                              
                              return (
                                <div key={permission.id} className="flex items-center justify-between">
                                  <div>
                                    <p className="text-sm font-medium text-gray-900">{permission.name}</p>
                                    {permission.description && (
                                      <p className="text-xs text-gray-500">{permission.description}</p>
                                    )}
                                  </div>
                                  <Switch
                                    checked={isAssigned}
                                    onChange={() => handleToggleRolePermission(permission.id, isAssigned)}
                                    aria-label={permission.name}
                                  />
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      );
                    })}
                  </>
                )}
              </div>
            ) : (
              <div className="flex h-full items-center justify-center rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                <p className="text-gray-500">Select a role to manage its permissions</p>
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'user-permissions' && canOverridePermissions && (
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* User Selection */}
          <div className="col-span-1 rounded-lg border border-gray-200 bg-white shadow-sm">
            <div className="p-4">
              <h3 className="mb-4 text-lg font-medium">Users</h3>
              <div className="mb-4">
                <Input
                  type="text"
                  placeholder="Search users..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="max-h-96 overflow-y-auto">
                {filteredUsers.length === 0 ? (
                  <p className="py-4 text-center text-sm text-gray-500">
                    {searchQuery ? 'No users match your search.' : 'No users found.'}
                  </p>
                ) : (
                  <ul className="divide-y divide-gray-200">
                    {filteredUsers.map((user) => (
                      <li
                        key={user.id}
                        className={`cursor-pointer py-3 hover:bg-gray-50 ${
                          selectedUser?.id === user.id ? 'bg-blue-50' : ''
                        }`}
                        onClick={() => handleSelectUser(user)}
                      >
                        <div className="px-2">
                          <p className="font-medium text-gray-900">
                            {user.full_name || user.username || 'Unnamed User'}
                          </p>
                          {user.username && user.full_name && (
                            <p className="text-sm text-gray-500">@{user.username}</p>
                          )}
                        </div>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
          </div>

          {/* Permission Override Assignment */}
          <div className="col-span-1 lg:col-span-2">
            {selectedUser ? (
              <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                <h3 className="mb-4 text-lg font-medium">
                  Permission Overrides for {selectedUser.full_name || selectedUser.username || 'User'}
                </h3>
                
                {isLoadingPermissions ? (
                  <div className="flex justify-center py-10">
                    <Spinner size="md" />
                  </div>
                ) : (
                  <>
                    <p className="mb-6 text-sm text-gray-600">
                      Set user-specific permission overrides. These will take precedence over role-based permissions.
                    </p>
                    
                    {categories.map((category) => {
                      const categoryPermissions = permissionsByCategory[category] || [];
                      
                      if (categoryPermissions.length === 0) return null;
                      
                      return (
                        <div key={category} className="mb-6">
                          <h4 className="mb-3 text-md font-medium text-gray-900">{category}</h4>
                          <div className="space-y-3">
                            {categoryPermissions.map((permission) => {
                              const override = userPermissionOverrides[permission.id];
                              
                              // Determine the state: null (no override), true (granted), false (denied)
                              let state = 'default';
                              if (override) {
                                state = override.granted ? 'granted' : 'denied';
                              }
                              
                              return (
                                <div key={permission.id} className="flex items-center justify-between">
                                  <div>
                                    <p className="text-sm font-medium text-gray-900">{permission.name}</p>
                                    {permission.description && (
                                      <p className="text-xs text-gray-500">{permission.description}</p>
                                    )}
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <span className={`text-xs ${
                                      state === 'granted' ? 'text-green-600' : 
                                      state === 'denied' ? 'text-red-600' : 'text-gray-500'
                                    }`}>
                                      {state === 'granted' ? 'Granted' : 
                                       state === 'denied' ? 'Denied' : 'Default'}
                                    </span>
                                    <button
                                      onClick={() => handleToggleUserPermission(permission.id, override)}
                                      className={`rounded-md px-3 py-1 text-xs font-medium ${
                                        state === 'granted' ? 'bg-green-100 text-green-800 hover:bg-green-200' : 
                                        state === 'denied' ? 'bg-red-100 text-red-800 hover:bg-red-200' : 
                                        'bg-gray-100 text-gray-800 hover:bg-gray-200'
                                      }`}
                                    >
                                      {state === 'granted' ? 'Deny' : 
                                       state === 'denied' ? 'Reset' : 'Grant'}
                                    </button>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      );
                    })}
                  </>
                )}
              </div>
            ) : (
              <div className="flex h-full items-center justify-center rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                <p className="text-gray-500">Select a user to manage their permission overrides</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default RolePermissionsContent;
