# AutoFlow Mobile App Setup Guide

This guide will help you set up the React Native mobile app for the AutoFlow platform.

## Prerequisites

Before you begin, ensure you have the following installed:

### Required Software

1. **Node.js** (version 16 or higher)
   - Download from [nodejs.org](https://nodejs.org/)
   - Verify installation: `node --version`

2. **React Native CLI**
   ```bash
   npm install -g react-native-cli
   ```

3. **Git**
   - Download from [git-scm.com](https://git-scm.com/)

### Platform-Specific Requirements

#### For Android Development

1. **Android Studio**
   - Download from [developer.android.com](https://developer.android.com/studio)
   - Install Android SDK (API level 31 or higher)
   - Configure Android SDK path in environment variables

2. **Java Development Kit (JDK)**
   - Install JDK 11 or higher
   - Set JAVA_HOME environment variable

#### For iOS Development (macOS only)

1. **Xcode**
   - Install from Mac App Store
   - Install Xcode Command Line Tools: `xcode-select --install`

2. **CocoaPods**
   ```bash
   sudo gem install cocoapods
   ```

## Step-by-Step Setup

### 1. Clone and Navigate to Mobile Directory

```bash
# If you haven't cloned the main repository yet
git clone https://github.com/autoflowke/autoflow_beta.git
cd autoflow_beta/mobile

# Or if you're already in the main directory
cd mobile
```

### 2. Install Dependencies

```bash
# Install Node.js dependencies
npm install

# For iOS only - install CocoaPods dependencies
cd ios && pod install && cd ..
```

### 3. Configure Environment Variables

1. **Create environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Update `.env` with your configuration:**
   ```env
   # Supabase Configuration (from your main .env.local)
   SUPABASE_URL=https://excgraelqcvcdsnlvrtv.supabase.co
   SUPABASE_ANON_KEY=your_supabase_anon_key

   # API Configuration
   API_BASE_URL=https://autoflow.parts
   API_BASE_URL_DEV=http://localhost:3000

   # M-PESA Configuration (from your main .env.local)
   MPESA_CONSUMER_KEY=your_mpesa_consumer_key
   MPESA_CONSUMER_SECRET=your_mpesa_consumer_secret
   MPESA_SHORT_CODE=your_mpesa_short_code
   MPESA_ENVIRONMENT=sandbox

   # App Configuration
   APP_NAME=AutoFlow
   APP_VERSION=1.0.0
   ```

### 4. Platform-Specific Configuration

#### Android Configuration

1. **Update package name** (optional):
   - Edit `android/app/build.gradle`
   - Change `applicationId` to your desired package name

2. **Configure signing** (for release builds):
   - Generate keystore: 
     ```bash
     keytool -genkeypair -v -storetype PKCS12 -keystore my-upload-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000
     ```
   - Update `android/gradle.properties` with keystore details

#### iOS Configuration

1. **Update bundle identifier** (optional):
   - Open `ios/AutoflowMobile.xcworkspace` in Xcode
   - Select project → Target → General → Bundle Identifier

2. **Configure signing**:
   - In Xcode, select your development team
   - Ensure provisioning profiles are set up

### 5. Test the Setup

#### Start Metro Bundler

```bash
npm start
```

#### Run on Android

```bash
# Make sure you have an Android device connected or emulator running
npm run android
```

#### Run on iOS

```bash
# Make sure you have an iOS simulator running or device connected
npm run ios
```

## Verification Checklist

After setup, verify the following:

- [ ] App launches successfully
- [ ] Login screen appears
- [ ] Can navigate between screens
- [ ] API calls work (check network tab in debugger)
- [ ] Authentication flow works
- [ ] No console errors in development

## Common Setup Issues

### Metro Bundler Issues

**Problem**: Metro bundler fails to start or shows caching issues

**Solution**:
```bash
npx react-native start --reset-cache
```

### Android Build Issues

**Problem**: Android build fails with Gradle errors

**Solutions**:
```bash
# Clean and rebuild
cd android && ./gradlew clean && cd ..
npm run android

# If still failing, check:
# 1. Android SDK path is correct
# 2. Java version is compatible
# 3. Gradle version is compatible
```

### iOS Build Issues

**Problem**: iOS build fails with CocoaPods or Xcode errors

**Solutions**:
```bash
# Reinstall pods
cd ios && pod deintegrate && pod install && cd ..

# Clean build folder in Xcode
# Product → Clean Build Folder

# Check:
# 1. Xcode version is compatible
# 2. iOS deployment target matches
# 3. Provisioning profiles are valid
```

### Environment Variable Issues

**Problem**: Environment variables not loading

**Solutions**:
1. Ensure `.env` file is in the `mobile/` directory
2. Restart Metro bundler after changing `.env`
3. Check that `react-native-dotenv` is properly configured in `babel.config.js`

### Network Issues

**Problem**: API calls fail or timeout

**Solutions**:
1. Check that your backend is running
2. Verify API_BASE_URL in `.env`
3. For Android emulator, use `********` instead of `localhost`
4. Check network permissions in `AndroidManifest.xml`

## Development Workflow

### 1. Start Development Server

```bash
# Terminal 1: Start Metro bundler
npm start

# Terminal 2: Run on platform
npm run android  # or npm run ios
```

### 2. Enable Debug Mode

- **Android**: Shake device or press `Cmd+M`
- **iOS**: Shake device or press `Cmd+D`
- Enable "Debug JS Remotely" for Chrome debugging

### 3. Hot Reloading

- Enable "Fast Refresh" in debug menu
- Changes will automatically reload
- State is preserved during reloads

## Next Steps

After successful setup:

1. **Explore the codebase**: Start with `src/App.tsx`
2. **Test authentication**: Try logging in with your backend credentials
3. **Check API integration**: Verify parts data loads correctly
4. **Customize branding**: Update colors, logos, and app name
5. **Add features**: Implement additional screens and functionality

## Getting Help

If you encounter issues:

1. Check this setup guide thoroughly
2. Review the main README.md for project overview
3. Check React Native documentation
4. Search for similar issues online
5. Contact the development team

## Production Deployment

For production deployment:

1. **Android**: Generate signed APK/AAB
2. **iOS**: Archive and upload to App Store
3. **Testing**: Test on real devices
4. **Store Listing**: Prepare app store metadata

See the main README.md for detailed deployment instructions.
