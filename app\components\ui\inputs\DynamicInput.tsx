import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, X, Minus } from 'lucide-react';
import Popover from '../utils/Popover';

interface DynamicInputProps {
  label: string;
  values: string[];
  onChange: (values: string[]) => void;
  placeholder?: string;
  className?: string;
  textLink?: string;
  onClose?: () => void;
}

const DynamicInput: React.FC<DynamicInputProps> = ({
  label,
  values,
  onChange,
  placeholder = 'Enter text...',
  className = '',
  textLink,
  onClose
}) => {
  const [focusedIndex, setFocusedIndex] = useState<number | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const handleAdd = () => {
    onChange([...values, '']);
  };

  const handleRemove = (index: number) => {
    const newValues = values.filter((_, i) => i !== index);
    onChange(newValues);
    if (index === 0 && textLink) {
      setIsExpanded(false);
    }
  };

  const handleChange = (index: number, value: string) => {
    const newValues = [...values];
    newValues[index] = value;
    onChange(newValues);
  };

  const handleTextLinkClick = () => {
    setIsExpanded(true);
    if (values.length === 0) {
      onChange(['']);
    }
  };

  const handleClose = () => {
    setIsExpanded(false);
    onChange([]); // Reset the values
    if (onClose) {
      onClose();
    }
  };

  if (textLink && !isExpanded) {
    return (
      <div className={className}>
        <button
          type="button"
          onClick={handleTextLinkClick}
          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium"
        >
          {textLink}
        </button>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-200">{label}</span>
        <div className="flex">
          <motion.button
            type="button"
            onClick={handleAdd}
            whileTap={{ scale: 0.98 }}
            className="h-8 px-3 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-l-lg"
          >
            <div className="flex items-center">
              <Plus className="w-4 h-4 mr-1" />
              Add
            </div>
          </motion.button>
          <Popover content="Close">
            <motion.button
              type="button"
              onClick={handleClose}
              whileTap={{ scale: 0.98 }}
              className="h-8 px-3 text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded-r-lg border-l border-red-500"
            >
              <div className="flex items-center">
                <X className="w-4 h-4 mr-1" />
                Close
              </div>
            </motion.button>
          </Popover>
        </div>
      </div>

      <div className="space-y-2">
        {values.map((value, index) => (
          <div key={index} className="relative flex">
            <div className="relative flex-[4]">
              <input
                ref={(el) => {
                  inputRefs.current[index] = el;
                }}
                type="text"
                value={value}
                onChange={(e) => handleChange(index, e.target.value)}
                onFocus={() => setFocusedIndex(index)}
                onBlur={() => setFocusedIndex(null)}
                placeholder=" "
                className={`
                  w-full h-12 pl-4 pr-2 text-gray-900 border rounded-l-lg
                  focus:outline-none focus:ring-0
                  transition-colors duration-200
                  ${focusedIndex === index || value ? 'pt-4 pb-2' : 'py-3'}
                  ${
                    focusedIndex === index
                      ? 'border-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:border-blue-500'
                      : 'border-gray-300 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400'
                  }
                  dark:text-white
                `}
              />
              <AnimatePresence>
                <motion.label
                  initial={false}
                  animate={{
                    opacity: 1,
                    scale: focusedIndex === index || value ? 0.75 : 1,
                    y: focusedIndex === index || value ? -8 : 0,
                  }}
                  transition={{ duration: 0.2 }}
                  className={`absolute transition-all duration-300 ease-in-out ${
                    focusedIndex === index || value
                      ? 'text-sm -top-0 left-0 px-1 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800'
                      : 'text-m top-1/2 -translate-y-1/2 left-4 text-gray-500 dark:text-gray-400'
                  } pointer-events-none truncate max-w-[calc(100%-2rem)]`}
                >
                  {placeholder}
                </motion.label>
              </AnimatePresence>
            </div>
            <Popover content="Remove">
              <motion.button
                type="button"
                onClick={() => handleRemove(index)}
                whileTap={{ scale: 0.98 }}
                className="flex-[1] h-12 flex items-center justify-center text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded-r-lg border-l border-red-500"
              >
                <Minus className="w-5 h-5" />
              </motion.button>
            </Popover>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DynamicInput; 