import { MD3LightTheme as DefaultTheme } from 'react-native-paper';
import { CONFIG } from '@/constants/config';

export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: CONFIG.COLORS.PRIMARY,
    secondary: CONFIG.COLORS.SECONDARY,
    tertiary: CONFIG.COLORS.INFO,
    surface: CONFIG.COLORS.SURFACE,
    background: CONFIG.COLORS.BACKGROUND,
    error: CONFIG.COLORS.ERROR,
    onSurface: CONFIG.COLORS.TEXT_PRIMARY,
    onBackground: CONFIG.COLORS.TEXT_PRIMARY,
    outline: CONFIG.COLORS.BORDER,
    success: CONFIG.COLORS.SUCCESS,
    warning: CONFIG.COLORS.WARNING,
    info: CONFIG.COLORS.INFO,
  },
  fonts: {
    ...DefaultTheme.fonts,
    default: {
      fontFamily: 'System',
    },
  },
};

export type AppTheme = typeof theme;
