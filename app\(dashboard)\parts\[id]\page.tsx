'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Tag, Wrench, CheckCircle, Truck, Star, Phone, Mail, Settings, Package } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { useParams } from 'next/navigation';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

// Icon mapping for specs/features
const specIcons: { [key: string]: React.ElementType } = {
  condition: CheckCircle,
  brand: Tag,
  compatibility: Settings,
  stock: Package,
};

// Spec Item Component - Reusable for key details
interface SpecItemProps {
  icon: React.ElementType;
  label: string;
  value: string | number;
  colorClass?: string;
}

const SpecItem: React.FC<SpecItemProps> = ({ icon: Icon, label, value, colorClass = 'bg-teal-50 text-teal-800' }) => (
  <motion.div
    className={`flex items-center space-x-2 ${colorClass} px-3 py-1.5 rounded-lg text-sm shadow-sm`}
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3, delay: 0.2 }}
  >
    <Icon className="w-4 h-4" />
    <span className="font-medium">{label}:</span>
    <span>{value}</span>
  </motion.div>
);

// Main Component
const PartDetailsPage: React.FC = () => {
  const params = useParams();
  const partId = params.id as string;

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [partData, setPartData] = useState<any>(null);
  const [mainImage, setMainImage] = useState('');
  const [showMore, setShowMore] = useState(false);

  useEffect(() => {
    const fetchPartDetails = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const supabase = createClient();

        // Fetch part details
        const { data: part, error: partError } = await supabase
          .from('parts')
          .select('*')
          .eq('part_id', partId)
          .single();

        if (partError) throw partError;

        // Fetch part images
        const { data: images, error: imagesError } = await supabase
          .from('part_images')
          .select('*')
          .eq('part_id', partId);

        if (imagesError) throw imagesError;

        // Fetch part conditions
        const { data: conditions, error: conditionsError } = await supabase
          .from('parts_condition')
          .select('*')
          .eq('part_id', partId);

        if (conditionsError) throw conditionsError;

        // Calculate total stock
        const totalStock = conditions?.reduce((sum, condition) => sum + (condition.stock || 0), 0) || 0;

        // Prepare part data
        const processedPartData = {
          id: part.id,
          name: part.title || 'Unnamed Part',
          partNumber: part.partnumber_group?.toString() || 'N/A',
          price: conditions?.[0]?.price || 0,
          condition: conditions?.[0]?.condition || 'Unknown',
          brand: 'Autoflow',
          compatibility: 'Check compatibility details',
          stock: totalStock,
          description: part.description || 'No description available',
          specifications: {
            material: 'Not specified',
            diameter: 'Not specified',
            weight: 'Not specified',
            finish: 'Not specified',
          },
          seller: {
            name: 'Autoflow',
            rating: 4.8,
            reviews: 120,
            location: 'Nairobi, Kenya',
            imageUrl: 'https://placehold.co/100x100/E2E8F0/4A5568?text=Seller',
          },
          images: images?.length > 0
            ? images.map(img => img.image_url)
            : ['https://placehold.co/600x400/cccccc/666666?text=No+Image'],
        };

        setPartData(processedPartData);
        if (processedPartData.images.length > 0) {
          setMainImage(processedPartData.images[0]);
        }
      } catch (err) {
        console.error('Error fetching part details:', err);
        setError('Failed to load part details. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    if (partId) {
      fetchPartDetails();
    }
  }, [partId]);

  // Animation variants for sections
  const sectionVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
      },
    }),
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <LoadingSpinner size={40} />
      </div>
    );
  }

  if (error || !partData) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="bg-white p-6 rounded-lg shadow-md text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">Error</h2>
          <p className="text-gray-700">{error || 'Failed to load part details'}</p>
          <button
            onClick={() => window.history.back()}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const descriptionToShow = showMore
    ? partData.description
    : `${partData.description.substring(0, 120)}${partData.description.length > 120 ? '...' : ''}`;

  return (
    <div className="min-h-screen bg-gray-100 font-sans">
      <div className="max-w-4xl mx-auto p-4 md:p-8">
        {/* --- Image Gallery --- */}
        <motion.div
          className="mb-6"
          custom={0}
          initial="hidden"
          animate="visible"
          variants={sectionVariants}
        >
          {/* Main Image */}
          <div className="relative mb-3 rounded-xl overflow-hidden shadow-lg bg-gray-300 h-64 md:h-80">
             <AnimatePresence mode="wait">
               <motion.img
                 key={mainImage}
                 src={mainImage}
                 alt={`${partData.name} - Main View`}
                 className="w-full h-full object-contain p-2"
                 initial={{ opacity: 0, scale: 0.95 }}
                 animate={{ opacity: 1, scale: 1 }}
                 exit={{ opacity: 0, scale: 0.95 }}
                 transition={{ duration: 0.4 }}
                 onError={(e) => (e.currentTarget.src = 'https://placehold.co/600x400/E2E8F0/4A5568?text=Image+Error')}
               />
             </AnimatePresence>
             {/* Image overlay buttons (optional) */}
             <div className="absolute top-4 right-4 flex flex-col space-y-2">
                 {partData.images.length > 1 && (
                     <button className="bg-black bg-opacity-60 text-white p-2 rounded-full hover:bg-opacity-75 transition-colors text-xs font-semibold shadow">
                         +{partData.images.length - 1} more
                     </button>
                 )}
             </div>
          </div>

          {/* Thumbnails */}
          {partData.images.length > 1 && (
            <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-7 gap-2">
              {partData.images.map((img: string, index: number) => (
                <motion.div
                  key={index}
                  className={`relative h-16 rounded-md overflow-hidden cursor-pointer border-2 ${
                    mainImage === img ? 'border-orange-600' : 'border-gray-300'
                  } hover:border-orange-400 transition-all bg-white shadow-sm`}
                  onClick={() => setMainImage(img)}
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: 'spring', stiffness: 300 }}
                >
                  <img
                    src={img}
                    alt={`${partData.name} - View ${index + 1}`}
                    className="w-full h-full object-contain p-1"
                    loading="lazy"
                    onError={(e) => (e.currentTarget.src = 'https://placehold.co/150x100/E2E8F0/4A5568?text=Error')}
                  />
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>

        {/* --- Part Info --- */}
        <motion.div
          className="flex flex-col md:flex-row justify-between items-start mb-4 bg-white p-4 rounded-lg shadow"
          custom={1}
          initial="hidden"
          animate="visible"
          variants={sectionVariants}
        >
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-1">{partData.name}</h1>
            <div className="flex items-center text-gray-500 text-sm mb-2">
              <Tag className="w-4 h-4 mr-2 text-teal-700" />
              <span>Part #: {partData.partNumber}</span>
            </div>
             <div className="flex items-center text-gray-600 text-sm mb-2">
              <Wrench className="w-4 h-4 mr-2 text-teal-700" />
              <span>Brand: {partData.brand}</span>
            </div>
          </div>
          <div className="mt-3 md:mt-0 text-right">
             <div className="text-2xl md:text-3xl font-bold text-orange-700 mb-1">
                ${typeof partData.price === 'number' ? partData.price.toFixed(2) : '0.00'}
             </div>
             <div className={`text-sm font-semibold ${partData.stock > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {partData.stock > 0 ? `${partData.stock} in Stock` : 'Out of Stock'}
             </div>
          </div>
        </motion.div>

        {/* --- Key Specs --- */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 mb-6"
          custom={2}
          initial="hidden"
          animate="visible"
          variants={sectionVariants}
        >
          <SpecItem icon={specIcons.condition} label="Condition" value={partData.condition} colorClass="bg-blue-50 text-blue-800" />
          <SpecItem icon={specIcons.compatibility} label="Compatibility" value="View Details" colorClass="bg-yellow-50 text-yellow-800 cursor-pointer hover:bg-yellow-100" />
          <SpecItem icon={specIcons.stock} label="Stock" value={partData.stock > 0 ? 'Available' : 'Unavailable'} colorClass={partData.stock > 0 ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'} />
          <SpecItem icon={Wrench} label="Material" value={partData.specifications.material} />
          <SpecItem icon={Wrench} label="Diameter" value={partData.specifications.diameter} />
          <SpecItem icon={Truck} label="Weight" value={partData.specifications.weight} />
        </motion.div>

        {/* --- Description --- */}
        <motion.div
          className="mb-6 bg-white p-4 rounded-lg shadow"
          custom={3}
          initial="hidden"
          animate="visible"
          variants={sectionVariants}
        >
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Part Details</h2>
          <p className="text-gray-600 leading-relaxed text-sm">
            {descriptionToShow}
          </p>
          {partData.description.length > 120 && (
            <button
              onClick={() => setShowMore(!showMore)}
              className="text-orange-600 hover:text-orange-700 font-semibold mt-2 text-sm"
            >
              {showMore ? 'Read less' : 'Read more'}
            </button>
          )}
        </motion.div>

        {/* --- Seller Info & Contact --- */}
        <motion.div
          className="bg-white p-4 rounded-lg shadow flex flex-col md:flex-row items-center justify-between gap-4"
          custom={4}
          initial="hidden"
          animate="visible"
          variants={sectionVariants}
        >
          <div className="flex items-center gap-4">
            <img
              src={partData.seller.imageUrl}
              alt={`${partData.seller.name} logo`}
              className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
              onError={(e) => (e.currentTarget.src = 'https://placehold.co/100x100/E2E8F0/4A5568?text=Seller')}
            />
            <div>
              <h3 className="text-lg font-semibold text-gray-800">{partData.seller.name}</h3>
              <div className="flex items-center text-sm text-gray-500 mt-1">
                <Star className="w-4 h-4 text-yellow-500 mr-1" fill="currentColor" />
                <span className="font-semibold mr-1">{partData.seller.rating}</span>
                ({partData.seller.reviews} reviews)
              </div>
               <div className="text-sm text-gray-500 mt-1">{partData.seller.location}</div>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 w-full md:w-auto mt-4 md:mt-0">
             {/* Example Contact Buttons */}
             <motion.button
                className="flex items-center justify-center gap-2 w-full sm:w-auto bg-teal-700 hover:bg-teal-800 text-white font-semibold py-2 px-5 rounded-lg shadow transition-colors duration-200"
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.98 }}
             >
                <Phone className="w-4 h-4" />
                Call Seller
             </motion.button>
              <motion.button
                className="flex items-center justify-center gap-2 w-full sm:w-auto bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2 px-5 rounded-lg shadow transition-colors duration-200"
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.98 }}
              >
                <Mail className="w-4 h-4" />
                Message
              </motion.button>
          </div>
        </motion.div>

        {/* Back Button */}
        <motion.div
          className="mt-6 text-center"
          custom={5}
          initial="hidden"
          animate="visible"
          variants={sectionVariants}
        >
          <button
            onClick={() => window.history.back()}
            className="bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2 px-6 rounded-lg shadow transition-colors duration-200"
          >
            Back to Parts
          </button>
        </motion.div>
      </div>
    </div>
  );
};

export default PartDetailsPage;
