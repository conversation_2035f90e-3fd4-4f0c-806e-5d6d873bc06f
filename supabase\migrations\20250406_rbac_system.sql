-- Ensure the pgcrypto extension is enabled for gen_random_uuid()
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Table: roles
-- Stores the different roles users can have (e.g., Employee, Supplier, Super Admin).
CREATE TABLE IF NOT EXISTS public.roles (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text UNIQUE NOT NULL,
    description text,
    created_at timestamptz DEFAULT now() NOT NULL,
    updated_at timestamptz DEFAULT now() NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_roles_name ON public.roles(name);
COMMENT ON TABLE public.roles IS 'Stores the different roles users can have (e.g., Employee, Supplier, Super Admin).';

-- Table: permissions
-- Stores the granular permissions available in the system (e.g., parts:create, users:read).
CREATE TABLE IF NOT EXISTS public.permissions (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text UNIQUE NOT NULL, -- Format: category:action (e.g., parts:create)
    description text,
    category text NOT NULL, -- For grouping in UI (e.g., Parts Management)
    created_at timestamptz DEFAULT now() NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_permissions_name ON public.permissions(name);
CREATE INDEX IF NOT EXISTS idx_permissions_category ON public.permissions(category);
COMMENT ON TABLE public.permissions IS 'Stores the granular permissions available in the system (e.g., parts:create, users:read).';
COMMENT ON COLUMN public.permissions.name IS 'Unique permission identifier, recommended format: category:action (e.g., parts:create)';
COMMENT ON COLUMN public.permissions.category IS 'Logical grouping for UI presentation (e.g., Parts Management)';

-- Table: user_roles
-- Many-to-many relationship linking users (profiles) to roles.
CREATE TABLE IF NOT EXISTS public.user_roles (
    user_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    role_id uuid NOT NULL REFERENCES public.roles(id) ON DELETE CASCADE,
    assigned_at timestamptz DEFAULT now() NOT NULL,
    PRIMARY KEY (user_id, role_id) -- Composite primary key
);
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON public.user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON public.user_roles(role_id);
COMMENT ON TABLE public.user_roles IS 'Links users (from profiles table) to their assigned roles.';

-- Table: role_permissions
-- Many-to-many relationship linking roles to permissions.
CREATE TABLE IF NOT EXISTS public.role_permissions (
    role_id uuid NOT NULL REFERENCES public.roles(id) ON DELETE CASCADE,
    permission_id uuid NOT NULL REFERENCES public.permissions(id) ON DELETE CASCADE,
    assigned_at timestamptz DEFAULT now() NOT NULL,
    PRIMARY KEY (role_id, permission_id) -- Composite primary key
);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON public.role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON public.role_permissions(permission_id);
COMMENT ON TABLE public.role_permissions IS 'Links roles to the permissions granted by that role.';

-- Table: user_permissions
-- Stores user-specific permission overrides (explicit grants or denies).
CREATE TABLE IF NOT EXISTS public.user_permissions (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    permission_id uuid NOT NULL REFERENCES public.permissions(id) ON DELETE CASCADE,
    has_permission boolean NOT NULL, -- TRUE for explicit grant, FALSE for explicit deny
    created_at timestamptz DEFAULT now() NOT NULL,
    updated_at timestamptz DEFAULT now() NOT NULL,
    UNIQUE (user_id, permission_id) -- Ensure only one override per user/permission
);
CREATE INDEX IF NOT EXISTS idx_user_permissions_user_id ON public.user_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_permissions_permission_id ON public.user_permissions(permission_id);
COMMENT ON TABLE public.user_permissions IS 'Stores user-specific permission overrides (explicit grants or denies).';
COMMENT ON COLUMN public.user_permissions.has_permission IS 'TRUE indicates an explicit grant, FALSE indicates an explicit denial, overriding role permissions.';

-- Table: audit_log
-- Logs changes made to roles, permissions, and assignments.
CREATE TABLE IF NOT EXISTS public.audit_log (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    timestamp timestamptz DEFAULT now() NOT NULL,
    user_id uuid REFERENCES public.profiles(id) ON DELETE SET NULL, -- User who performed the action (nullable for system actions)
    action_type text NOT NULL, -- e.g., 'CREATE_ROLE', 'ASSIGN_PERMISSION_TO_ROLE', 'GRANT_USER_PERMISSION'
    target_table text, -- e.g., 'roles', 'user_permissions'
    target_record_id text, -- Can store uuid or composite keys as text
    change_details jsonb, -- Stores details of the change (e.g., old/new values)
    ip_address inet -- Optional: IP address of the user performing the action
);
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON public.audit_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON public.audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_action_type ON public.audit_log(action_type);
CREATE INDEX IF NOT EXISTS idx_audit_log_target ON public.audit_log(target_table, target_record_id);
COMMENT ON TABLE public.audit_log IS 'Logs changes to permissions, roles, and assignments for auditing purposes.';
COMMENT ON COLUMN public.audit_log.user_id IS 'The user (from profiles table) who performed the action. NULL for system actions.';
COMMENT ON COLUMN public.audit_log.change_details IS 'JSONB field to store specifics about the change, like old and new values.';

-- Function to automatically update 'updated_at' timestamp
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers to update 'updated_at' timestamps
DROP TRIGGER IF EXISTS set_timestamp_roles ON public.roles;
CREATE TRIGGER set_timestamp_roles
BEFORE UPDATE ON public.roles
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

DROP TRIGGER IF EXISTS set_timestamp_user_permissions ON public.user_permissions;
CREATE TRIGGER set_timestamp_user_permissions
BEFORE UPDATE ON public.user_permissions
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Create the crucial permission check function
CREATE OR REPLACE FUNCTION check_user_permission(p_user_id uuid, p_permission_name text)
RETURNS boolean AS $$
DECLARE
    v_permission_id uuid;
    v_has_override boolean;
    v_override_value boolean;
    v_has_role_permission boolean;
BEGIN
    -- Get the permission ID from the name
    SELECT id INTO v_permission_id
    FROM public.permissions
    WHERE name = p_permission_name;
    
    -- If permission doesn't exist, return false
    IF v_permission_id IS NULL THEN
        RETURN false;
    END IF;
    
    -- Check for user-specific override
    SELECT EXISTS (
        SELECT 1
        FROM public.user_permissions
        WHERE user_id = p_user_id AND permission_id = v_permission_id
    ) INTO v_has_override;
    
    -- If override exists, return its value
    IF v_has_override THEN
        SELECT has_permission INTO v_override_value
        FROM public.user_permissions
        WHERE user_id = p_user_id AND permission_id = v_permission_id;
        
        RETURN v_override_value;
    END IF;
    
    -- Check if any of the user's roles grant this permission
    SELECT EXISTS (
        SELECT 1
        FROM public.user_roles ur
        JOIN public.role_permissions rp ON ur.role_id = rp.role_id
        WHERE ur.user_id = p_user_id AND rp.permission_id = v_permission_id
    ) INTO v_has_role_permission;
    
    RETURN v_has_role_permission;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS Policies

-- Enable RLS on all tables
ALTER TABLE public.roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_log ENABLE ROW LEVEL SECURITY;

-- Helper function to check if a user is a super admin
CREATE OR REPLACE FUNCTION is_super_admin(p_user_id uuid)
RETURNS boolean AS $$
DECLARE
    v_super_admin_role_id uuid;
BEGIN
    -- Get the super admin role ID
    SELECT id INTO v_super_admin_role_id
    FROM public.roles
    WHERE name = 'Super Admin';
    
    -- If super admin role doesn't exist, return false
    IF v_super_admin_role_id IS NULL THEN
        RETURN false;
    END IF;
    
    -- Check if the user has the super admin role
    RETURN EXISTS (
        SELECT 1
        FROM public.user_roles
        WHERE user_id = p_user_id AND role_id = v_super_admin_role_id
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Super Admin policies (full access)
CREATE POLICY super_admin_roles_policy ON public.roles
    USING (is_super_admin(auth.uid()) OR check_user_permission(auth.uid(), 'admin:manage_roles'));

CREATE POLICY super_admin_permissions_policy ON public.permissions
    USING (is_super_admin(auth.uid()) OR check_user_permission(auth.uid(), 'admin:manage_permissions'));

CREATE POLICY super_admin_user_roles_policy ON public.user_roles
    USING (is_super_admin(auth.uid()) OR check_user_permission(auth.uid(), 'admin:assign_roles'));

CREATE POLICY super_admin_role_permissions_policy ON public.role_permissions
    USING (is_super_admin(auth.uid()) OR check_user_permission(auth.uid(), 'admin:assign_permissions'));

CREATE POLICY super_admin_user_permissions_policy ON public.user_permissions
    USING (is_super_admin(auth.uid()) OR check_user_permission(auth.uid(), 'admin:override_permissions'));

CREATE POLICY super_admin_audit_log_policy ON public.audit_log
    USING (is_super_admin(auth.uid()) OR check_user_permission(auth.uid(), 'admin:view_audit_log'));

-- Regular user policies (read-only for their own data)
CREATE POLICY user_view_own_roles_policy ON public.user_roles
    FOR SELECT
    USING (user_id = auth.uid());

-- Seed initial data (Super Admin role and basic permissions)
INSERT INTO public.roles (name, description)
VALUES 
    ('Super Admin', 'Has full access to all system features'),
    ('Admin', 'Has administrative access with some restrictions'),
    ('Employee', 'Regular employee with standard access'),
    ('Supplier', 'External supplier with limited access')
ON CONFLICT (name) DO NOTHING;

-- Insert basic permissions
INSERT INTO public.permissions (name, description, category)
VALUES
    -- Admin permissions
    ('admin:access_panel', 'Access the admin panel', 'Administration'),
    ('admin:manage_roles', 'Create, update, and delete roles', 'Administration'),
    ('admin:manage_permissions', 'Manage system permissions', 'Administration'),
    ('admin:assign_roles', 'Assign roles to users', 'Administration'),
    ('admin:assign_permissions', 'Assign permissions to roles', 'Administration'),
    ('admin:override_permissions', 'Create user-specific permission overrides', 'Administration'),
    ('admin:view_audit_log', 'View the system audit log', 'Administration'),
    
    -- User management
    ('users:view', 'View user profiles', 'User Management'),
    ('users:create', 'Create new users', 'User Management'),
    ('users:edit', 'Edit user profiles', 'User Management'),
    ('users:delete', 'Delete users', 'User Management'),
    ('users:edit_own_profile', 'Edit own profile', 'User Management'),
    
    -- Parts management
    ('parts:view', 'View parts', 'Parts Management'),
    ('parts:create', 'Create new parts', 'Parts Management'),
    ('parts:edit', 'Edit parts', 'Parts Management'),
    ('parts:delete', 'Delete parts', 'Parts Management'),
    
    -- Products management
    ('products:view', 'View products', 'Products Management'),
    ('products:create', 'Create new products', 'Products Management'),
    ('products:edit', 'Edit products', 'Products Management'),
    ('products:delete', 'Delete products', 'Products Management')
ON CONFLICT (name) DO NOTHING;

-- Assign all permissions to Super Admin role
WITH super_admin AS (
    SELECT id FROM public.roles WHERE name = 'Super Admin'
),
all_permissions AS (
    SELECT id FROM public.permissions
)
INSERT INTO public.role_permissions (role_id, permission_id)
SELECT super_admin.id, all_permissions.id
FROM super_admin, all_permissions
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Assign basic permissions to Admin role
WITH admin_role AS (
    SELECT id FROM public.roles WHERE name = 'Admin'
),
admin_permissions AS (
    SELECT id FROM public.permissions 
    WHERE name IN (
        'admin:access_panel',
        'users:view', 'users:create', 'users:edit',
        'parts:view', 'parts:create', 'parts:edit', 'parts:delete',
        'products:view', 'products:create', 'products:edit', 'products:delete'
    )
)
INSERT INTO public.role_permissions (role_id, permission_id)
SELECT admin_role.id, admin_permissions.id
FROM admin_role, admin_permissions
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Assign basic permissions to Employee role
WITH employee_role AS (
    SELECT id FROM public.roles WHERE name = 'Employee'
),
employee_permissions AS (
    SELECT id FROM public.permissions 
    WHERE name IN (
        'users:edit_own_profile',
        'parts:view', 'parts:create', 'parts:edit',
        'products:view'
    )
)
INSERT INTO public.role_permissions (role_id, permission_id)
SELECT employee_role.id, employee_permissions.id
FROM employee_role, employee_permissions
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Assign basic permissions to Supplier role
WITH supplier_role AS (
    SELECT id FROM public.roles WHERE name = 'Supplier'
),
supplier_permissions AS (
    SELECT id FROM public.permissions 
    WHERE name IN (
        'users:edit_own_profile',
        'parts:view',
        'products:view'
    )
)
INSERT INTO public.role_permissions (role_id, permission_id)
SELECT supplier_role.id, supplier_permissions.id
FROM supplier_role, supplier_permissions
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Create audit log trigger function
CREATE OR REPLACE FUNCTION log_rbac_changes()
RETURNS TRIGGER AS $$
DECLARE
    v_action_type text;
    v_target_table text;
    v_target_record_id text;
    v_change_details jsonb;
BEGIN
    v_target_table := TG_TABLE_NAME;
    
    IF TG_OP = 'INSERT' THEN
        v_action_type := 'CREATE';
        v_change_details := jsonb_build_object('new_values', row_to_json(NEW));
        
        IF v_target_table = 'roles' THEN
            v_target_record_id := NEW.id::text;
        ELSIF v_target_table = 'permissions' THEN
            v_target_record_id := NEW.id::text;
        ELSIF v_target_table = 'user_roles' THEN
            v_target_record_id := NEW.user_id::text || '/' || NEW.role_id::text;
        ELSIF v_target_table = 'role_permissions' THEN
            v_target_record_id := NEW.role_id::text || '/' || NEW.permission_id::text;
        ELSIF v_target_table = 'user_permissions' THEN
            v_target_record_id := NEW.id::text;
        END IF;
    ELSIF TG_OP = 'UPDATE' THEN
        v_action_type := 'UPDATE';
        v_change_details := jsonb_build_object(
            'old_values', row_to_json(OLD),
            'new_values', row_to_json(NEW)
        );
        
        IF v_target_table = 'roles' THEN
            v_target_record_id := NEW.id::text;
        ELSIF v_target_table = 'permissions' THEN
            v_target_record_id := NEW.id::text;
        ELSIF v_target_table = 'user_roles' THEN
            v_target_record_id := NEW.user_id::text || '/' || NEW.role_id::text;
        ELSIF v_target_table = 'role_permissions' THEN
            v_target_record_id := NEW.role_id::text || '/' || NEW.permission_id::text;
        ELSIF v_target_table = 'user_permissions' THEN
            v_target_record_id := NEW.id::text;
        END IF;
    ELSIF TG_OP = 'DELETE' THEN
        v_action_type := 'DELETE';
        v_change_details := jsonb_build_object('old_values', row_to_json(OLD));
        
        IF v_target_table = 'roles' THEN
            v_target_record_id := OLD.id::text;
        ELSIF v_target_table = 'permissions' THEN
            v_target_record_id := OLD.id::text;
        ELSIF v_target_table = 'user_roles' THEN
            v_target_record_id := OLD.user_id::text || '/' || OLD.role_id::text;
        ELSIF v_target_table = 'role_permissions' THEN
            v_target_record_id := OLD.role_id::text || '/' || OLD.permission_id::text;
        ELSIF v_target_table = 'user_permissions' THEN
            v_target_record_id := OLD.id::text;
        END IF;
    END IF;
    
    INSERT INTO public.audit_log (
        user_id,
        action_type,
        target_table,
        target_record_id,
        change_details,
        ip_address
    ) VALUES (
        auth.uid(),
        v_action_type || '_' || upper(v_target_table),
        v_target_table,
        v_target_record_id,
        v_change_details,
        inet_client_addr()
    );
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create audit log triggers
CREATE TRIGGER roles_audit_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.roles
FOR EACH ROW EXECUTE FUNCTION log_rbac_changes();

CREATE TRIGGER permissions_audit_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.permissions
FOR EACH ROW EXECUTE FUNCTION log_rbac_changes();

CREATE TRIGGER user_roles_audit_trigger
AFTER INSERT OR DELETE ON public.user_roles
FOR EACH ROW EXECUTE FUNCTION log_rbac_changes();

CREATE TRIGGER role_permissions_audit_trigger
AFTER INSERT OR DELETE ON public.role_permissions
FOR EACH ROW EXECUTE FUNCTION log_rbac_changes();

CREATE TRIGGER user_permissions_audit_trigger
AFTER INSERT OR UPDATE OR DELETE ON public.user_permissions
FOR EACH ROW EXECUTE FUNCTION log_rbac_changes();
