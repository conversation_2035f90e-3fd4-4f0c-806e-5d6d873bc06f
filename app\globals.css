@tailwind base;
@tailwind components;
@tailwind utilities;
@import url('styles/menu.css');

:root {
  --background: #ffffff;
  --foreground: #171717;
}

/* Disable dark mode for now to ensure white background on mobile */
/*
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}
*/

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

.nested-select-item {
  transition: padding-left 0.2s ease-in-out;
}

@layer utilities {
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Sales Dashboard Custom Styles */
  /* Recharts custom tooltip styles */
  .recharts-default-tooltip {
    background-color: rgba(255, 255, 255, 0.95) !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 0.5rem !important; /* 8px */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    padding: 8px 12px !important;
  }
  .recharts-tooltip-label {
    font-weight: 600 !important; /* semibold */
    margin-bottom: 4px !important;
    color: #1f2937 !important; /* gray-800 */
  }
  .recharts-tooltip-item {
     font-size: 0.75rem !important; /* text-xs */
     color: #4b5563 !important; /* gray-600 */
     padding-top: 2px !important;
     padding-bottom: 2px !important;
  }
  .recharts-tooltip-item-list {
      padding-left: 0 !important; /* Remove default list padding */
  }

  /* Ensure ResponsiveContainer works correctly */
  .recharts-responsive-container {
      width: 100% !important;
      height: 100% !important;
  }

  /* Client Dashboard Custom Styles */
  /* Style scrollbars for a cleaner look in lists */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  ::-webkit-scrollbar-thumb {
    background: #cbd5e1; /* gray-300 */
    border-radius: 3px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: #94a3b8; /* gray-400 */
  }

  /* Toast notifications should appear above everything */
  .react-hot-toast-container {
    z-index: 99999 !important;
  }

  .react-hot-toast {
    z-index: 99999 !important;
  }
}
