'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { extractIdFromSlug } from '@/app/utils/slugify';
import Link from 'next/link';
import { getAdjustedPrice } from '@/app/utils/priceAdjustment';

// Import components
import PartPageLayout from '@/app/components/shop/part-details/PartPageLayout';
import ProductImageGallery from '@/app/components/shop/part-details/ProductImageGallery';
import ProductMainInfo from '@/app/components/shop/part-details/ProductMainInfo';
import ProductMetaSection from '@/app/components/shop/part-details/ProductMetaSection';
import RelatedPartsForCarSection from '@/app/components/shop/part-details/RelatedPartsForCarSection';

// Import types directly
export interface PartDetails {
  id: string;
  title: string;
  partnumber: string;
  price: number;
  discountedPrice?: number | null;
  stock: number;
  description: string | null;
  category: string;
  images: string[];
  attributes: PartAttributeType[];
  compatibleCars: CompatibleCar[];
  similarParts: SimilarPart[];
}

export interface PartAttributeType {
  name: string;
  icon: string;
  value: string | number;
}

export interface CompatibleCar {
  id: string;
  brand: string;
  model: string;
  year: string;
  trim?: string;
}

export interface SimilarPart {
  id: string;
  name: string;
  partNumber: string;
  price: number;
  imageSrc: string;
  attributes: {
    icon: string;
    value: string | number;
    label?: string;
  }[];
}

export interface SupabaseImage {
  url: string;
}

export interface SupabasePartAttributeRaw {
  id: string;
  attribute_id: {
    name: string;
    icon_svg?: string;
  };
  value: string;
}

export interface SimilarPartAttributeItemRaw {
  icon: string;
  value: string | number;
  label: string;
}

export interface SupabaseSimilarPartRaw {
  part_id_new: {
    id: string;
    title: string;
    partnumber: string;
    price: number;
    images_new: SupabaseImage[];
    attributes_new?: Array<SimilarPartAttributeItemRaw>;
  };
}

interface PartDetailsProps {
  params: { slug: string };
  searchParams: { [key: string]: string | string[] | undefined };
}

const PartDetailsPage = ({ params, searchParams }: PartDetailsProps) => {
  // Access the slug parameter safely
  // This approach works with both current and future versions of Next.js
  // In current versions, params is an object
  // In future versions, params will be a Promise that needs to be unwrapped
  //
  // Note: We're not using React.use() directly because it's causing TypeScript errors
  // in the current version. This approach is a temporary solution until we can
  // fully migrate to the new params handling in Next.js.
  const slug = typeof params === 'object' && params !== null && 'slug' in params
    ? params.slug
    : '';
  const partId = extractIdFromSlug(slug);

  const [part, setPart] = useState<any>(null);
  const [partDetails, setPartDetails] = useState<PartDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [isFavorite, setIsFavorite] = useState(false);
  const [isWhatsAppModalOpen, setIsWhatsAppModalOpen] = useState(false);

  useEffect(() => {
    const fetchPartDetails = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const supabase = createClient();

        // Get the basic part information
        const { data: partData, error: fetchError } = await supabase
          .from('parts')
          .select(`
            id,
            title,
            partnumber_group,
            category_id,
            description
          `)
          .eq('id', partId)
          .single();

        if (fetchError) throw fetchError;

        if (!partData) {
          setError('Part not found');
          return;
        }

        // Get part number from part_compatibility_groups
        let partNumberData = null;
        try {
          if (partData.partnumber_group) {
            const { data: pnData, error } = await supabase
              .from('part_compatibility_groups')
              .select('part_number')
              .eq('id', partData.partnumber_group)
              .single();

            if (pnData) {
              partNumberData = pnData;
            }

            if (error && error.message && !error.message.includes('No rows found')) {
              console.error('Error fetching part number:', error);
            }
          }
        } catch (partNumberError) {
          console.error('Exception fetching part number:', partNumberError);
        }

        // Get images from part_images
        let imagesData: SupabaseImage[] | undefined = [];
        try {
          const { data: imgData, error } = await supabase
            .from('part_images')
            .select('image_url, is_main_image, part_id')
            .eq('part_id', partData.id);

          if (imgData && imgData.length > 0) {
            imagesData = imgData.map((img: any) => ({ url: img.image_url }));
          }

          if (error) {
            console.error('Error fetching images:', error);
          }
        } catch (imagesError) {
          console.error('Exception fetching images:', imagesError);
        }

        // Get price from part_price via parts_condition
        let priceData = null;
        let conditionData = null;

        try {
          // First get the condition ID for this part
          const { data: condData, error } = await supabase
            .from('parts_condition')
            .select('id, condition, stock')
            .eq('part_id', partData.id)
            .single();

          if (condData) {
            conditionData = condData;

            // Now get the price using the condition ID
            try {
              const { data: priceQueryData, error: priceError } = await supabase
                .from('part_price')
                .select('price, discounted_price')
                .eq('condition_id', condData.id)
                .single();

              if (priceQueryData) {
                priceData = priceQueryData;
              }

              if (priceError && priceError.message && !priceError.message.includes('No rows found')) {
                console.error('Error fetching price:', priceError);
              }
            } catch (priceException) {
              console.error('Exception fetching price:', priceException);
            }
          }

          if (error && error.message && !error.message.includes('No rows found')) {
            console.error('Error fetching condition:', error);
          }
        } catch (conditionException) {
          console.error('Exception fetching condition:', conditionException);
        }

        // Get description from parts table or set a default
        let description = '';
        if (partData.description) {
          description = partData.description;
        } else {
          // Set a default description based on the part title
          description = `This is a ${partData.title} for your vehicle. It is designed to provide reliable performance and durability. Contact us for more information about compatibility and installation.`;
        }

        // Combine all the data
        const combinedData = {
          ...partData,
          partnumber: partNumberData?.part_number || 'Unknown',
          images: imagesData || [],
          price: priceData?.price || 0,
          discounted_price: priceData?.discounted_price || null,
          parts_condition: conditionData ? [conditionData] : [],
          description: description,
          stock: conditionData?.stock || 0
        };

        setPart(combinedData);

        // Get category name
        let categoryName = 'Auto Parts';

        // Get part attributes from the database
        let attributesData = [];

        try {
          // First, get the condition attribute
          const { data: conditionData, error: conditionError } = await supabase
            .from('parts_condition')
            .select('condition')
            .eq('part_id', partData.id)
            .single();

          if (conditionData) {
            attributesData.push({
              id: 'condition',
              attribute_id: { name: 'Condition', icon_svg: 'check-circle' },
              value: conditionData.condition
            });
          } else {
            if (conditionError) {
              console.error('Error fetching condition:', conditionError.message || conditionError);
            }
            // Fallback
            attributesData.push({
              id: 'condition',
              attribute_id: { name: 'Condition', icon_svg: 'check-circle' },
              value: 'New'
            });
          }

          // Get category attributes for this part
          const { data: attrValuesData, error: attrValuesError } = await supabase
            .from('parts_category_attribute_values')
            .select(`
              attribute_id,
              value,
              selection_value,
              parts_category_attributes(attribute, input_type)
            `)
            .eq('part_id', partData.id);

          if (attrValuesData && attrValuesData.length > 0) {
            console.log('Fetched attribute values:', attrValuesData);

            // Map the attribute values to the format we need
            const mappedAttributes = attrValuesData.map(attr => {
              // Define icon mapping for common attributes
              const getIconForAttributeName = (name: string) => {
                const lowerName = (name || '').toLowerCase();
                if (lowerName.includes('material')) return 'package';
                if (lowerName.includes('warranty')) return 'shield';
                if (lowerName.includes('weight')) return 'weight';
                if (lowerName.includes('dimension')) return 'ruler';
                if (lowerName.includes('color')) return 'palette';
                if (lowerName.includes('manufacturer')) return 'factory';
                return 'info';
              };

              // Get the attribute name
              const attrName = (attr.parts_category_attributes as any)?.attribute || 'Attribute';

              // Get the attribute value - use selection_value if available, otherwise use value
              const attrValue = attr.selection_value || attr.value || 'N/A';

              return {
                id: attr.attribute_id.toString(),
                attribute_id: {
                  name: attrName,
                  icon_svg: getIconForAttributeName(attrName)
                },
                value: attrValue
              };
            });

            // Add the mapped attributes to our array
            attributesData = [...attributesData, ...mappedAttributes];
          } else {
            console.log('No attribute values found in database');
            if (attrValuesError) {
              console.error('Error fetching attribute values:', attrValuesError.message || attrValuesError);
            }

            // Add fallback attributes if none were found
            attributesData.push(
              {
                id: 'material',
                attribute_id: { name: 'Material', icon_svg: 'package' },
                value: 'Metal/Plastic'
              },
              {
                id: 'warranty',
                attribute_id: { name: 'Warranty', icon_svg: 'shield' },
                value: '6 months'
              }
            );
          }
        } catch (error) {
          console.error('Exception fetching attributes:', error);

          // Fallback attributes in case of error
          attributesData = [
            {
              id: 'condition',
              attribute_id: { name: 'Condition', icon_svg: 'check-circle' },
              value: 'New'
            },
            {
              id: 'material',
              attribute_id: { name: 'Material', icon_svg: 'package' },
              value: 'Metal/Plastic'
            },
            {
              id: 'warranty',
              attribute_id: { name: 'Warranty', icon_svg: 'shield' },
              value: '6 months'
            }
          ];
        }

        // Get compatible cars from the database
        let compatibleCars: CompatibleCar[] = [];
        try {
          // First, get all variation_trim_ids associated with this part
          const { data: partCars, error: partCarsError } = await supabase
            .from('parts_car')
            .select('variation_trim_id')
            .eq('part_id', partData.id);

          if (partCarsError) {
            console.error('Error fetching part cars:', partCarsError);
          } else if (partCars && partCars.length > 0) {
            // Get all variation_trim_ids
            const variationTrimIds = partCars.map(pc => pc.variation_trim_id);

            // Fetch all car details using a SQL query instead of nested joins
            console.log('Variation trim IDs:', variationTrimIds);

            // Make sure we're passing an array of integers
            const trimIdsArray = variationTrimIds.map(id => parseInt(id.toString(), 10));

            const { data: carDetails, error: carDetailsError } = await supabase
              .rpc('get_compatible_cars_for_part', { trim_ids: trimIdsArray });

            console.log('Car details response:', { data: carDetails, error: carDetailsError });

            if (carDetailsError) {
              console.error('Error fetching car details:', carDetailsError);
            } else if (carDetails && carDetails.length > 0) {
              // Transform the data into the format expected by the UI
              compatibleCars = carDetails.map((car: any) => {
                // Format the year range
                const startYear = car.start_production_year || '';
                const endYear = car.end_production_year || '';
                const yearRange = endYear ? `${startYear}-${endYear}` : `${startYear}+`;

                return {
                  id: car.id.toString(),
                  brand: car.brand_name || 'Unknown',
                  model: `${car.model_name || 'Unknown'} ${car.generation_name || ''} ${car.variation || ''}`,
                  year: yearRange,
                  trim: car.trim_name || ''
                };
              });
            }
          }
        } catch (compatibleCarsError) {
          console.error('Exception fetching compatible cars:', compatibleCarsError);
        }

        // Get similar parts (parts in the same category)
        let similarPartsData = [
          {
            part_id_new: {
              id: '101',
              title: 'Front Brake Pads',
              partnumber: 'BP-1234',
              price: 4500,
              images_new: [{ url: '/images/placeholder.jpg' }],
              attributes_new: [
                { icon: 'check-circle', value: 'New', label: 'Condition' }
              ]
            }
          },
          {
            part_id_new: {
              id: '102',
              title: 'Rear Brake Pads',
              partnumber: 'BP-5678',
              price: 3800,
              images_new: [{ url: '/images/placeholder.jpg' }],
              attributes_new: [
                { icon: 'check-circle', value: 'New', label: 'Condition' }
              ]
            }
          },
          {
            part_id_new: {
              id: '103',
              title: 'Brake Discs',
              partnumber: 'BD-9012',
              price: 7500,
              images_new: [{ url: '/images/placeholder.jpg' }],
              attributes_new: [
                { icon: 'check-circle', value: 'New', label: 'Condition' }
              ]
            }
          }
        ];

        const imageUrls = imagesData?.length > 0
          ? imagesData.map((img: SupabaseImage) => img.url)
          : ['/images/placeholder.jpg'];

        // Create part details data structure
        const processedAttributes = attributesData.map((attr: SupabasePartAttributeRaw) => ({
          name: attr.attribute_id.name,
          icon: attr.attribute_id.icon_svg || 'settings',
          value: attr.value,
        }));

        const processedSimilarParts = similarPartsData.map((similarPartItem: SupabaseSimilarPartRaw) => {
          const similarPartDetail = similarPartItem.part_id_new;
          // Adjust the price for similar parts as well
          const adjustedPrice = getAdjustedPrice(similarPartDetail.price || 0);
          return {
            id: similarPartDetail.id,
            name: similarPartDetail.title,
            partNumber: similarPartDetail.partnumber,
            price: adjustedPrice, // Use the adjusted price
            imageSrc: similarPartDetail.images_new?.[0]?.url || '/images/placeholder.jpg',
            attributes: similarPartDetail.attributes_new || []
          };
        });

        // Get the actual price and discounted price
        const actualPrice = priceData?.price || 0;
        const actualDiscountedPrice = priceData?.discounted_price || null;

        // Calculate the adjusted prices for frontend display
        const adjustedPrice = getAdjustedPrice(actualPrice);
        const adjustedDiscountedPrice = actualDiscountedPrice ? getAdjustedPrice(actualDiscountedPrice) : null;

        const partDetailsData: PartDetails = {
          id: partData.id,
          title: partData.title,
          partnumber: partNumberData?.part_number || 'Unknown',
          price: adjustedPrice, // Use the adjusted price for frontend display
          discountedPrice: adjustedDiscountedPrice, // Use the adjusted discounted price
          stock: conditionData?.stock || 0,
          description: description,
          category: categoryName,
          images: imageUrls,
          attributes: processedAttributes,
          compatibleCars: compatibleCars,
          similarParts: processedSimilarParts
        };

        setPartDetails(partDetailsData);
      } catch (err) {
        console.error('Error fetching part details:', err);
        setError('Failed to load part details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPartDetails();
  }, [partId, slug]);

  // Handle quantity change
  const handleQuantityChange = (newQuantity: number) => {
    setQuantity(newQuantity);
  };

  // Handle add to cart
  const handleAddToCart = () => {
    // Implementation would go here
    console.log('Add to cart:', { partId, quantity });
  };

  // Handle toggle favorite
  const handleToggleFavorite = () => {
    setIsFavorite(!isFavorite);
  };

  // Handle open WhatsApp modal
  const handleOpenWhatsAppModal = () => {
    setIsWhatsAppModalOpen(true);
  };

  // Handle close WhatsApp modal
  const handleCloseWhatsAppModal = () => {
    setIsWhatsAppModalOpen(false);
  };

  // Get icon for attribute
  const getIconForAttribute = (attributeName: string): string => {
    const attributeIcons: Record<string, string> = {
      'Material': 'package',
      'Weight': 'scale',
      'Dimensions': 'ruler',
      'Color': 'palette',
      'Manufacturer': 'factory',
      'Country': 'globe',
      'Warranty': 'shield',
      'Installation': 'tool',
      'Compatibility': 'check-circle',
      'Condition': 'check-decagram'
    };

    return attributeIcons[attributeName] || 'info';
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 bg-white">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded mb-4"></div>
          <div className="h-32 bg-gray-200 rounded mb-4"></div>
          <div className="h-48 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error || !partDetails) {
    return (
      <div className="container mx-auto px-4 py-8 bg-white">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <p>{error || 'Failed to load part details'}</p>
          <p className="mt-2">Parameter: {slug}, Part ID: {partId}</p>
          <div className="mt-4">
            <Link
              href="/shop"
              className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
            >
              Back to Shop
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const mainContent = (
    <>
      <div className="flex flex-col md:flex-row gap-6 mb-8 w-full">
        <ProductImageGallery images={partDetails.images} productTitle={partDetails.title} />
        <ProductMainInfo
          partDetails={partDetails}
          isFavorite={isFavorite}
          onToggleFavorite={handleToggleFavorite}
          onOpenWhatsAppModal={handleOpenWhatsAppModal}
          getIconForAttribute={getIconForAttribute}
        />
      </div>
    </>
  );

  const sidebarContent = (
    <ProductMetaSection partDetails={partDetails} />
  );

  return (
    <div className="container mx-auto px-4 py-8 bg-white">
      <PartPageLayout
        mainContent={mainContent}
        sidebarContent={sidebarContent}
        similarProducts={null}
      />

      {/* Related Parts for this Car Section */}
      <RelatedPartsForCarSection partId={partDetails.id} limit={4} />
    </div>
  );
};

export default PartDetailsPage;
