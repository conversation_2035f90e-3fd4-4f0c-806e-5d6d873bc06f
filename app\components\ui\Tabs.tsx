'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/app/utils/cn';

export interface Tab {
  label: string;
  id: string | number;
}

interface TabsProps {
  tabs: Tab[];
  activeTabId: string | number;
  onTabChange: (tabId: string | number) => void;
  className?: string;
}

const Tabs: React.FC<TabsProps> = ({
  tabs,
  activeTabId,
  onTabChange,
  className,
}) => {
  return (
    <div className={cn('w-full', className)}>
      <div className="relative border-b border-gray-200">
        <nav className="flex space-x-8" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={cn(
                'whitespace-nowrap py-4 px-1 text-sm font-medium relative',
                activeTabId === tab.id
                  ? 'text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              )}
              aria-current={activeTabId === tab.id ? 'page' : undefined}
            >
              {tab.label}
              {activeTabId === tab.id && (
                <motion.div
                  layoutId="activeTab"
                  className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600"
                  initial={false}
                  transition={{
                    type: 'spring',
                    stiffness: 500,
                    damping: 30,
                  }}
                />
              )}
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
};

export default Tabs;
