const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs');

const outputDir = path.join(process.cwd(), 'public', 'screenshots');
const viewport = { width: 1280, height: 720 };

// Ensure the output directory exists
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

async function waitForNetworkIdle(page) {
  await page.waitForNetworkIdle({ idleTime: 1000, timeout: 10000 });
}

async function generateScreenshots() {
  const browser = await puppeteer.launch();
  const page = await browser.newPage();
  
  // Set viewport size
  await page.setViewport(viewport);

  try {
    // Generate home page screenshot
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' });
    await waitForNetworkIdle(page);
    await page.screenshot({
      path: path.join(outputDir, 'home.png'),
      fullPage: true
    });
    console.log('Generated home screenshot');

    // Generate shop page screenshot
    await page.goto('http://localhost:3000/shop', { waitUntil: 'networkidle0' });
    await waitForNetworkIdle(page);
    await page.screenshot({
      path: path.join(outputDir, 'shop.png'),
      fullPage: true
    });
    console.log('Generated shop screenshot');

    // Generate product page screenshot
    await page.goto('http://localhost:3000/shop/1', { waitUntil: 'networkidle0' });
    await waitForNetworkIdle(page);
    await page.screenshot({
      path: path.join(outputDir, 'product.png'),
      fullPage: true
    });
    console.log('Generated product screenshot');

    // Generate cart page screenshot
    await page.goto('http://localhost:3000/cart', { waitUntil: 'networkidle0' });
    await waitForNetworkIdle(page);
    await page.screenshot({
      path: path.join(outputDir, 'cart.png'),
      fullPage: true
    });
    console.log('Generated cart screenshot');

  } catch (error) {
    console.error('Error generating screenshots:', error);
  } finally {
    await browser.close();
  }
}

// Only run if the development server is running
const http = require('http');
const checkServer = () => {
  return new Promise((resolve) => {
    http.get('http://localhost:3000', (res) => {
      if (res.statusCode === 200) {
        resolve(true);
      } else {
        resolve(false);
      }
    }).on('error', () => {
      resolve(false);
    });
  });
};

// Check if server is running before generating screenshots
checkServer().then((isRunning) => {
  if (isRunning) {
    generateScreenshots();
  } else {
    console.error('Please start the development server first (npm run dev)');
  }
}); 