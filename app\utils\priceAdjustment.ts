/**
 * Utility function to adjust the displayed price on the frontend
 * based on a predefined mapping table.
 */

interface PriceRange {
  actualMin: number;
  actualMax: number;
  frontendPrice: number;
}

// Define the price mapping table
const PRICE_MAPPING: PriceRange[] = [
  { actualMin: 0, actualMax: 1000, frontendPrice: 1500 },
  { actualMin: 1001, actualMax: 1500, frontendPrice: 2000 },
  { actualMin: 1501, actualMax: 2000, frontendPrice: 2500 },
  { actualMin: 2001, actualMax: 2500, frontendPrice: 3000 },
  { actualMin: 2501, actualMax: 3000, frontendPrice: 4500 },
  { actualMin: 3001, actualMax: 4500, frontendPrice: 6000 },
  { actualMin: 4501, actualMax: 5000, frontendPrice: 6500 },
  { actualMin: 5001, actualMax: 7500, frontendPrice: 10000 },
  { actualMin: 7501, actualMax: 10000, frontendPrice: 12000 },
  { actualMin: 10001, actualMax: 15000, frontendPrice: 18000 },
  { actualMin: 15001, actualMax: 20000, frontendPrice: 23000 },
  { actualMin: 20001, actualMax: 25000, frontendPrice: 27500 },
  { actualMin: 25001, actualMax: 30000, frontendPrice: 33000 },
  { actualMin: 30001, actualMax: 35000, frontendPrice: 38000 },
  { actualMin: 35001, actualMax: 40000, frontendPrice: 45000 },
  { actualMin: 40001, actualMax: 50000, frontendPrice: 55000 },
  { actualMin: 50001, actualMax: 55000, frontendPrice: 60000 },
  { actualMin: 55001, actualMax: 60000, frontendPrice: 65000 },
  { actualMin: 60001, actualMax: 65000, frontendPrice: 70000 },
  { actualMin: 65001, actualMax: 70000, frontendPrice: 75000 },
  { actualMin: 70001, actualMax: 75000, frontendPrice: 80000 },
  { actualMin: 75001, actualMax: 80000, frontendPrice: 85000 },
  { actualMin: 80001, actualMax: 85000, frontendPrice: 90000 },
  { actualMin: 85001, actualMax: 90000, frontendPrice: 95000 },
  { actualMin: 90001, actualMax: 100000, frontendPrice: 110000 },
];

/**
 * Adjusts the actual price to the frontend display price based on the mapping table.
 * 
 * @param actualPrice - The actual price from the database
 * @returns The adjusted price to display on the frontend
 */
export function getAdjustedPrice(actualPrice: number): number {
  // Handle prices above the highest range in the mapping table
  if (actualPrice > 100000) {
    // For prices above 100,000, add 10% to the actual price
    return Math.round(actualPrice * 1.1);
  }

  // Find the matching price range
  const matchingRange = PRICE_MAPPING.find(
    range => actualPrice >= range.actualMin && actualPrice <= range.actualMax
  );

  // Return the frontend price if a matching range is found, otherwise return the actual price
  return matchingRange ? matchingRange.frontendPrice : actualPrice;
}

/**
 * Reverses the price adjustment to get the actual price from a frontend price.
 * This is useful for when you need to convert back to the actual price.
 * 
 * @param frontendPrice - The price displayed on the frontend
 * @returns The estimated actual price
 */
export function getActualPrice(frontendPrice: number): number {
  // Find the matching frontend price
  const matchingRange = PRICE_MAPPING.find(
    range => range.frontendPrice === frontendPrice
  );

  // Return the middle of the actual price range if a match is found, otherwise return the frontend price
  return matchingRange 
    ? Math.round((matchingRange.actualMin + matchingRange.actualMax) / 2) 
    : frontendPrice;
}
