import { useState, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';

export interface PartExistenceResult {
  exists: boolean;
  groupId: number | null;
  table: 'part_compatibility_groups' | 'part_to_group' | null;
}

export const usePartExistenceCheck = (partNumber: string | null): PartExistenceResult => {
  const [result, setResult] = useState<PartExistenceResult>({
    exists: false,
    groupId: null,
    table: null
  });
  
  useEffect(() => {
    const checkPartExistence = async () => {
      if (!partNumber) {
        setResult({ exists: false, groupId: null, table: null });
        return;
      }

      try {
        const supabase = createClient();
        
        // First check in part_to_group
        const { data: partToGroupData, error: partToGroupError } = await supabase
          .from('part_to_group')
          .select('group_id')
          .eq('partnumber', partNumber)
          .single();

        if (partToGroupError && partToGroupError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
          console.error('Error checking part_to_group:', partToGroupError);
        }

        if (partToGroupData) {
          console.log("Found part in part_to_group:", partToGroupData);
          setResult({
            exists: true,
            groupId: partToGroupData.group_id,
            table: 'part_to_group'
          });
          return;
        }

        // If not found in part_to_group, check part_compatibility_groups
        const { data: compatibilityData, error: compatibilityError } = await supabase
          .from('part_compatibility_groups')
          .select('id')
          .eq('part_number', partNumber)
          .single();

        if (compatibilityError && compatibilityError.code !== 'PGRST116') {
          console.error('Error checking part_compatibility_groups:', compatibilityError);
        }

        if (compatibilityData) {
          console.log("Found part in part_compatibility_groups:", compatibilityData);
          setResult({
            exists: true,
            groupId: compatibilityData.id,
            table: 'part_compatibility_groups'
          });
          return;
        }

        // Part not found in either table
        setResult({ exists: false, groupId: null, table: null });
      } catch (error) {
        console.error('Error checking part existence:', error);
        setResult({ exists: false, groupId: null, table: null });
      }
    };

    checkPartExistence();
  }, [partNumber]);

  return result;
}; 