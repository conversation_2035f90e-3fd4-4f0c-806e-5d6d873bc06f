'use client';

import React, { useCallback } from 'react';
import { motion } from 'framer-motion';
import { CategoryWithChildren } from '../types';
import CategoryCard from './CategoryCard';
import SubcategoryItem from './SubcategoryItem';

interface CategoryListProps {
  categories: CategoryWithChildren[];
  onRefresh: () => void;
  expandedCategories: Record<number, boolean>;
  expandedSubcategories: Record<number, Record<number, boolean>>;
  onToggleCategoryExpand: (id: number) => void;
  onToggleSubcategoryExpand: (parentId: number, childId: number) => void;
  onShowBatchRename: (categoryId: number, categoryName: string) => void;
}

const CategoryList: React.FC<CategoryListProps> = ({
  categories,
  onRefresh,
  expandedCategories,
  expandedSubcategories,
  onToggleCategoryExpand,
  onToggleSubcategoryExpand,
  onShowBatchRename
}) => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  // Memoize the toggle function for each subcategory
  const createToggleFunction = useCallback((parentId: number, subcategoryId: number) => {
    return () => onToggleSubcategoryExpand(parentId, subcategoryId);
  }, [onToggleSubcategoryExpand]);

  // Recursive function to render subcategories
  const renderSubcategory = (subcategory: CategoryWithChildren, parentId: number, level: number) => {
    const parentExpanded = expandedSubcategories?.[parentId];
    const childExpanded = parentExpanded?.[subcategory.id];
    const isExpanded = Boolean(childExpanded);
    
    return (
      <SubcategoryItem
        key={subcategory.id}
        subcategory={subcategory}
        parentId={parentId}
        level={level}
        onEdit={() => {}} // This will be handled within the component
        onRefresh={onRefresh}
        isExpanded={isExpanded}
        onToggleExpand={createToggleFunction(parentId, subcategory.id)}
        expandedSubcategories={expandedSubcategories}
        onToggleSubcategoryExpand={onToggleSubcategoryExpand}
      />
    );
  };

  return (
    <motion.div
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {categories.map((category) => (
        <CategoryCard
          key={category.id}
          category={category}
          onRefresh={onRefresh}
          isExpanded={!!expandedCategories[category.id]}
          onToggleExpand={() => onToggleCategoryExpand(category.id)}
          expandedSubcategories={expandedSubcategories}
          onToggleSubcategoryExpand={onToggleSubcategoryExpand}
          onShowBatchRename={onShowBatchRename}
        />
      ))}
    </motion.div>
  );
};

export default CategoryList;
