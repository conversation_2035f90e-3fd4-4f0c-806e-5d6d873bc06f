#!/usr/bin/env ts-node

import * as path from 'path';
import { JijiAutomation, AutomationConfig } from './jiji-automation';
import * as dotenv from 'dotenv';
import { program } from 'commander';

// Load environment variables from automation directory first, then parent
// When compiled, __dirname points to dist/, so we need to go up one level
const automationDir = __dirname.includes('dist') ? path.join(__dirname, '..') : __dirname;
dotenv.config({ path: path.join(automationDir, '.env') });
dotenv.config({ path: path.join(automationDir, '../.env.local') });

interface CliOptions {
  email?: string;
  password?: string;
  headless?: boolean;
  batchSize?: number;
  delay?: number;
  maxRetries?: number;
  skipExisting?: boolean;
  testMode?: boolean;
  configFile?: string;
}

async function loadConfig(configFile?: string): Promise<AutomationConfig> {
  const defaultConfig: AutomationConfig = {
    headless: false,
    batchSize: 5,
    delayBetweenActions: 3000,
    maxRetries: 3,
    skipExisting: true,
    testMode: false
  };

  if (configFile) {
    try {
      const fs = await import('fs');
      const configData = JSON.parse(fs.readFileSync(configFile, 'utf8'));
      return { ...defaultConfig, ...configData };
    } catch (error) {
      console.warn(`Failed to load config file ${configFile}:`, error);
      console.log('Using default configuration');
    }
  }

  return defaultConfig;
}

async function validateEnvironment(): Promise<void> {
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ];

  const missing = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missing.length > 0) {
    console.error('Missing required environment variables:');
    missing.forEach(varName => console.error(`  - ${varName}`));
    console.error('\nPlease set these variables in your .env file');
    process.exit(1);
  }
}

async function promptForCredentials(): Promise<{ email: string; password: string }> {
  const readline = await import('readline');

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const question = (prompt: string): Promise<string> => {
    return new Promise(resolve => {
      rl.question(prompt, resolve);
    });
  };

  try {
    const email = await question('Jiji.co.ke Email: ');

    // Hide password input
    process.stdout.write('Jiji.co.ke Password: ');
    process.stdin.setRawMode(true);

    let password = '';
    return new Promise((resolve) => {
      process.stdin.on('data', (data: Buffer) => {
        const char = data.toString();

        if (char === '\n' || char === '\r' || char === '\u0004') {
          process.stdin.setRawMode(false);
          process.stdout.write('\n');
          rl.close();
          resolve({ email, password });
        } else if (char === '\u0003') {
          process.exit(1);
        } else if (char === '\u007f') {
          if (password.length > 0) {
            password = password.slice(0, -1);
            process.stdout.write('\b \b');
          }
        } else {
          password += char;
          process.stdout.write('*');
        }
      });
    });
  } catch (error) {
    rl.close();
    throw error;
  }
}

async function runAutomation(options: CliOptions): Promise<void> {
  console.log('🚀 Starting Jiji.co.ke Automation Tool');
  console.log('=====================================\n');

  try {
    // Validate environment
    await validateEnvironment();

    // Load configuration
    const config = await loadConfig(options.configFile);

    // Override config with CLI options
    if (options.headless !== undefined) config.headless = options.headless;
    if (options.batchSize !== undefined) config.batchSize = options.batchSize;
    if (options.delay !== undefined) config.delayBetweenActions = options.delay;
    if (options.maxRetries !== undefined) config.maxRetries = options.maxRetries;
    if (options.skipExisting !== undefined) config.skipExisting = options.skipExisting;
    if (options.testMode !== undefined) config.testMode = options.testMode;

    console.log('Configuration:');
    console.log(`  - Headless mode: ${config.headless}`);
    console.log(`  - Batch size: ${config.batchSize}`);
    console.log(`  - Delay between actions: ${config.delayBetweenActions}ms`);
    console.log(`  - Max retries: ${config.maxRetries}`);
    console.log(`  - Skip existing: ${config.skipExisting}`);
    console.log(`  - Test mode: ${config.testMode}`);
    console.log('');

    // Get credentials
    let email = options.email || process.env.JIJI_EMAIL;
    let password = options.password || process.env.JIJI_PASSWORD;

    if (!email || !password) {
      console.log('Jiji.co.ke credentials not provided via CLI or environment variables.');
      console.log('Please enter your credentials:\n');
      const credentials = await promptForCredentials();
      email = credentials.email;
      password = credentials.password;
    }

    if (!email || !password) {
      console.error('❌ Email and password are required');
      process.exit(1);
    }

    // Initialize automation
    console.log('🔧 Initializing automation...');
    const automation = new JijiAutomation(config);

    // Setup cleanup on exit
    const cleanup = async () => {
      console.log('\n🧹 Cleaning up...');
      await automation.cleanup();
      process.exit(0);
    };

    process.on('SIGINT', cleanup);
    process.on('SIGTERM', cleanup);
    process.on('uncaughtException', async (error) => {
      console.error('❌ Uncaught exception:', error);
      await automation.cleanup();
      process.exit(1);
    });

    try {
      // Initialize browser
      await automation.initialize();
      console.log('✅ Browser initialized');

      // Login to Jiji
      console.log('🔐 Logging in to Jiji.co.ke...');
      const loginSuccess = await automation.login(email, password);

      if (!loginSuccess) {
        console.error('❌ Login failed');
        await automation.cleanup();
        process.exit(1);
      }

      console.log('✅ Login successful');

      if (config.testMode) {
        console.log('🧪 Running in test mode - no actual listings will be created');
      }

      // Run batch processing
      console.log('📦 Starting batch processing...');
      await automation.runBatchProcess();

      console.log('✅ Automation completed successfully');

    } catch (error) {
      console.error('❌ Automation failed:', error);
      throw error;
    } finally {
      await automation.cleanup();
    }

  } catch (error) {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  }
}

// CLI Setup
program
  .name('jiji-automation')
  .description('Automate posting automotive parts to Jiji.co.ke marketplace')
  .version('1.0.0');

program
  .command('run')
  .description('Run the automation process')
  .option('-e, --email <email>', 'Jiji.co.ke email address')
  .option('-p, --password <password>', 'Jiji.co.ke password')
  .option('--headless', 'Run browser in headless mode')
  .option('--no-headless', 'Run browser with GUI (default)')
  .option('-b, --batch-size <number>', 'Number of parts to process in one batch', parseInt)
  .option('-d, --delay <number>', 'Delay between actions in milliseconds', parseInt)
  .option('-r, --max-retries <number>', 'Maximum number of retries per part', parseInt)
  .option('--skip-existing', 'Skip parts that are already listed (default)')
  .option('--no-skip-existing', 'Process all parts regardless of existing listings')
  .option('--test-mode', 'Run in test mode (no actual listings created)')
  .option('-c, --config-file <path>', 'Path to JSON configuration file')
  .action(runAutomation);

program
  .command('test')
  .description('Test the automation setup without creating listings')
  .option('-e, --email <email>', 'Jiji.co.ke email address')
  .option('-p, --password <password>', 'Jiji.co.ke password')
  .option('-c, --config-file <path>', 'Path to JSON configuration file')
  .action(async (options) => {
    await runAutomation({ ...options, testMode: true, batchSize: 1 });
  });

program
  .command('config')
  .description('Generate a sample configuration file')
  .option('-o, --output <path>', 'Output file path', 'jiji-automation-config.json')
  .action(async (options) => {
    const fs = await import('fs');
    const sampleConfig = {
      headless: false,
      batchSize: 5,
      delayBetweenActions: 3000,
      maxRetries: 3,
      skipExisting: true,
      testMode: false
    };

    fs.writeFileSync(options.output, JSON.stringify(sampleConfig, null, 2));
    console.log(`✅ Sample configuration file created: ${options.output}`);
  });

// Parse CLI arguments
if (require.main === module) {
  program.parse();
}
