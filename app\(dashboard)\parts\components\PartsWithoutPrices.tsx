'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { motion, AnimatePresence } from 'framer-motion';
import { DollarSign, ArrowLeft, Save, X } from 'lucide-react';
import Button from '@/app/components/ui/inputs/Button';
import Spinner from '@/app/components/ui/Spinner';
import CurrencyInput from '@/app/components/ui/inputs/Currency';
import { useForm, Controller } from 'react-hook-form';
import toast from 'react-hot-toast';

interface Part {
  id: number;
  title: string;
  condition_id: number;
  condition: string;
  stock: number;
  thumbnail_url?: string;
}

interface PriceFormValues {
  price: number;
  discountedPrice?: number;
}

const PartsWithoutPrices = ({ onBack }: { onBack: () => void }) => {
  const [parts, setParts] = useState<Part[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedPart, setSelectedPart] = useState<Part | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const supabase = createClient();

  const { control, handleSubmit, reset } = useForm<PriceFormValues>({
    defaultValues: {
      price: 0,
      discountedPrice: undefined
    }
  });

  // Fetch parts without prices
  const fetchPartsWithoutPrices = async () => {
    setIsLoading(true);
    try {
      console.log('Fetching parts without prices...');

      // First, get all parts conditions with stock
      const { data: conditions, error: conditionsError } = await supabase
        .from('parts_condition')
        .select(`
          id,
          part_id,
          condition,
          stock
        `)
        .gt('stock', 0); // Only include parts with stock

      if (conditionsError) {
        console.error('Error fetching conditions:', JSON.stringify(conditionsError));
        throw conditionsError;
      }

      console.log(`Found ${conditions?.length || 0} conditions with stock`);

      if (!conditions || conditions.length === 0) {
        setParts([]);
        return;
      }

      // Get all condition IDs
      const conditionIds = conditions.map(c => c.id);
      console.log(`Checking prices for ${conditionIds.length} conditions`);

      // Find which conditions have prices
      const { data: prices, error: pricesError } = await supabase
        .from('part_price')
        .select('condition_id')
        .in('condition_id', conditionIds);

      if (pricesError) {
        console.error('Error fetching prices:', JSON.stringify(pricesError));
        throw pricesError;
      }

      console.log(`Found ${prices?.length || 0} conditions with prices`);

      // Create a set of condition IDs that have prices
      const conditionIdsWithPrices = new Set(prices?.map(p => p.condition_id) || []);

      // Filter conditions to only include those without prices
      const conditionsWithoutPrices = conditions.filter(c => !conditionIdsWithPrices.has(c.id));
      console.log(`Found ${conditionsWithoutPrices.length} conditions without prices`);

      if (conditionsWithoutPrices.length === 0) {
        setParts([]);
        return;
      }

      // Get the part details for these conditions
      const partIds = conditionsWithoutPrices.map(c => c.part_id);
      const { data: partsData, error: partsError } = await supabase
        .from('parts')
        .select('id, title')
        .in('id', partIds);

      if (partsError) {
        console.error('Error fetching parts:', JSON.stringify(partsError));
        throw partsError;
      }

      console.log(`Found ${partsData?.length || 0} parts`);

      // Fetch part images
      const { data: partImages, error: imagesError } = await supabase
        .from('part_images')
        .select('part_id, image_url, is_main_image')
        .in('part_id', partIds);

      if (imagesError) {
        console.error('Error fetching part images:', JSON.stringify(imagesError));
        // Continue without images rather than failing
      }

      console.log(`Found ${partImages?.length || 0} part images`);

      // Create a map of part_id to main image
      const imageMap: Record<number, string> = {};
      if (partImages && partImages.length > 0) {
        // First try to find main images
        const mainImages = partImages.filter(img => img.is_main_image);
        mainImages.forEach(img => {
          imageMap[img.part_id] = img.image_url;
        });

        // For parts without main images, use the first available image
        partImages.forEach(img => {
          if (!imageMap[img.part_id]) {
            imageMap[img.part_id] = img.image_url;
          }
        });
      }

      // Create a map of part_id to part details
      const partsMap = (partsData || []).reduce((acc, part) => {
        acc[part.id] = part;
        return acc;
      }, {} as Record<number, any>);

      // Transform the data
      const partsWithoutPrices = conditionsWithoutPrices.map(condition => {
        const part = partsMap[condition.part_id];
        if (!part) return null;

        return {
          id: part.id,
          title: part.title || 'Unnamed Part',
          condition_id: condition.id,
          condition: condition.condition || 'Unknown',
          stock: condition.stock || 0,
          thumbnail_url: imageMap[part.id] || ''
        };
      }).filter(p => p !== null) as Part[];

      console.log(`Final list: ${partsWithoutPrices.length} parts without prices`);

      // Log a sample part for debugging
      if (partsWithoutPrices.length > 0) {
        console.log('Sample part:', JSON.stringify(partsWithoutPrices[0]));
      }

      setParts(partsWithoutPrices);
    } catch (error) {
      console.error('Error fetching parts without prices:', JSON.stringify(error, null, 2));
      toast.error('Failed to load parts without prices');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchPartsWithoutPrices();
  }, []);

  const handleAddPrice = (part: Part) => {
    setSelectedPart(part);
    reset({ price: 0, discountedPrice: undefined });
  };

  const handleSavePrice = async (data: PriceFormValues) => {
    if (!selectedPart) return;

    setIsSaving(true);
    try {
      // Format the price values by removing commas and converting to numbers
      const formatPrice = (price: any): number => {
        if (typeof price === 'string') {
          // Remove commas and convert to number
          return parseFloat(price.replace(/,/g, ''));
        }
        return price || 0;
      };

      const formattedPrice = formatPrice(data.price);
      const formattedDiscountedPrice = data.discountedPrice ? formatPrice(data.discountedPrice) : null;

      console.log(`Saving price for part ${selectedPart.id} (condition ${selectedPart.condition_id}):`, {
        price: formattedPrice,
        discountedPrice: formattedDiscountedPrice
      });

      // Save the price to the database
      const { error } = await supabase
        .from('part_price')
        .upsert({
          condition_id: selectedPart.condition_id,
          price: formattedPrice,
          discounted_price: formattedDiscountedPrice
        });

      if (error) {
        console.error('Error from Supabase:', JSON.stringify(error));
        throw error;
      }

      console.log('Price saved successfully');
      toast.success(`Price added for ${selectedPart.title}`);

      // Remove the part from the list
      setParts(prevParts => prevParts.filter(p => p.condition_id !== selectedPart.condition_id));

      // Close the form
      setSelectedPart(null);
    } catch (error) {
      console.error('Error saving price:', JSON.stringify(error, null, 2));
      toast.error('Failed to save price. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">Parts Without Prices</h2>
          <p className="text-gray-600">Add prices to parts that don't have them yet</p>
        </div>
        <Button onClick={onBack} variant="outline" className="flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Dashboard
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <Spinner size="lg" />
        </div>
      ) : parts.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <DollarSign className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-xl font-semibold text-gray-700 mb-2">All Parts Have Prices</h3>
          <p className="text-gray-500">There are no parts without prices in your inventory.</p>
        </div>
      ) : (
        <div>
          <div className="mb-4">
            <p className="text-gray-600">Found {parts.length} parts without prices</p>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Part</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Condition</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {parts.map((part) => (
                  <tr key={part.condition_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div
                        className="flex-shrink-0 h-12 w-12 rounded-md overflow-hidden bg-gray-100 border border-gray-200 cursor-pointer hover:opacity-80 transition-opacity"
                        onClick={() => part.thumbnail_url && setSelectedImage(part.thumbnail_url)}
                      >
                        {part.thumbnail_url ? (
                          <img
                            src={part.thumbnail_url}
                            alt={part.title}
                            className="h-full w-full object-cover"
                            onError={(e) => {
                              // Replace broken image with a placeholder
                              (e.target as HTMLImageElement).src = '/images/placeholder.jpg';
                            }}
                          />
                        ) : (
                          <div className="h-full w-full flex items-center justify-center bg-gray-100 text-gray-400">
                            <Package className="h-6 w-6" />
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{part.title}</div>
                      <div className="text-xs text-gray-500">ID: {part.id}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{part.condition}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{part.stock}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <Button
                        onClick={() => handleAddPrice(part)}
                        variant="outline"
                        size="sm"
                        className="text-blue-600 hover:text-blue-800"
                      >
                        Add Price
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Price Form Modal */}
      <AnimatePresence>
        {selectedPart && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md"
            >
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Add Price for {selectedPart.title}</h3>
                <button
                  onClick={() => setSelectedPart(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <form onSubmit={handleSubmit(handleSavePrice)} className="space-y-4">
                <div>
                  <p className="text-sm text-gray-500 mb-2">
                    Condition: <span className="font-medium">{selectedPart.condition}</span>
                  </p>
                  <p className="text-sm text-gray-500 mb-4">
                    Stock: <span className="font-medium">{selectedPart.stock}</span>
                  </p>
                </div>

                <div className="space-y-4">
                  <Controller
                    name="price"
                    control={control}
                    rules={{
                      required: "Price is required",
                      validate: {
                        positive: (value) => parseFloat(String(value).replace(/,/g, '')) > 0 || "Price must be greater than 0"
                      }
                    }}
                    render={({ field, fieldState }) => (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Price (KES)
                        </label>
                        <CurrencyInput
                          name={field.name}
                          value={field.value}
                          onChange={(value) => {
                            // Handle the value as a string to preserve formatting
                            field.onChange(value);
                          }}
                          onBlur={field.onBlur}
                          currencySymbol="KES"
                          errorMessage={fieldState.error?.message}
                        />
                        {fieldState.error && (
                          <p className="mt-1 text-sm text-red-600">{fieldState.error.message}</p>
                        )}
                      </div>
                    )}
                  />

                  <Controller
                    name="discountedPrice"
                    control={control}
                    rules={{
                      validate: {
                        validDiscount: (value, formValues) => {
                          if (!value) return true; // Optional field
                          const price = parseFloat(String(formValues.price).replace(/,/g, ''));
                          const discountPrice = parseFloat(String(value).replace(/,/g, ''));
                          return discountPrice < price || "Discounted price must be less than regular price";
                        }
                      }
                    }}
                    render={({ field, fieldState }) => (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Discounted Price (Optional)
                        </label>
                        <CurrencyInput
                          name={field.name}
                          value={field.value}
                          onChange={(value) => {
                            // Handle the value as a string to preserve formatting
                            field.onChange(value);
                          }}
                          onBlur={field.onBlur}
                          currencySymbol="KES"
                          errorMessage={fieldState.error?.message}
                        />
                        {fieldState.error && (
                          <p className="mt-1 text-sm text-red-600">{fieldState.error.message}</p>
                        )}
                      </div>
                    )}
                  />
                </div>

                <div className="flex justify-end gap-2 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setSelectedPart(null)}
                    disabled={isSaving}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSaving}
                    className="flex items-center gap-2"
                  >
                    {isSaving ? <Spinner size="sm" /> : <Save className="h-4 w-4" />}
                    Save Price
                  </Button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Image Modal */}
      <AnimatePresence>
        {selectedImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
            onClick={() => setSelectedImage(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="relative max-w-4xl max-h-[80vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={() => setSelectedImage(null)}
                className="absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-70 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
              <img
                src={selectedImage}
                alt="Part image"
                className="max-h-[80vh] max-w-full object-contain bg-white"
                onError={(e) => {
                  // Replace broken image with a placeholder
                  (e.target as HTMLImageElement).src = '/images/placeholder.jpg';
                }}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PartsWithoutPrices;
