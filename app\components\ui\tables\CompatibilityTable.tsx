'use client';

import { motion } from 'framer-motion';
import { staggerContainer, fadeIn, slideIn } from '@/app/libs/motion'; // Create these animations as needed

interface CompatibilityTableProps {
  data: Array<{
    engineModel: string;
    years: string;
    vehicleModels: string[];
    status: 'Compatible' | 'Incompatible' | 'Conditional';
  }>;
}

const CompatibilityTable = ({ data }: CompatibilityTableProps) => {
  const statusStyles = {
    Compatible: 'bg-green-100 text-green-800',
    Incompatible: 'bg-red-100 text-red-800',
    Conditional: 'bg-yellow-100 text-yellow-800',
  };

  return (
    <motion.div
      variants={staggerContainer()}
      initial="hidden"
      animate="show"
      className="rounded-xl border border-gray-200 bg-white shadow-lg"
    >
      <div className="overflow-x-auto p-4">
        <motion.table
          variants={fadeIn('up', 'spring', 0.2, 1)}
          className="w-full min-w-[600px]"
        >
          <thead>
            <tr className="border-b border-gray-200">
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                Engine Model
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                Years
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                Vehicle Models
              </th>
              <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                Status
              </th>
            </tr>
          </thead>
          
          <tbody className="divide-y divide-gray-200">
            {data.map((item, index) => (
              <motion.tr
                key={index}
                variants={slideIn('right', 'spring', index * 0.05, 1)}
                whileHover={{ scale: 1.02 }}
                className="transition-transform duration-200 ease-in-out hover:bg-gray-50"
              >
                <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                  {item.engineModel}
                </td>
                <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-600">
                  {item.years}
                </td>
                <td className="px-6 py-4 text-sm text-gray-600">
                  <div className="flex flex-wrap gap-2">
                    {item.vehicleModels.map((model, modelIndex) => (
                      <span
                        key={modelIndex}
                        className="inline-flex items-center rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800"
                      >
                        {model}
                      </span>
                    ))}
                  </div>
                </td>
                <td className="px-6 py-4 text-sm">
                  <span
                    className={`inline-flex items-center rounded-full px-3 py-1 text-xs font-medium ${
                      statusStyles[item.status]
                    }`}
                  >
                    <span className="mr-2 h-2 w-2 rounded-full bg-current"></span>
                    {item.status}
                  </span>
                </td>
              </motion.tr>
            ))}
          </tbody>
        </motion.table>
      </div>
    </motion.div>
  );
};

export default CompatibilityTable;