'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/app/libs/supabase/client';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/app/components/ui/Card';
import { Button } from "@/app/components/ui/Button";
import { Alert, AlertDescription, AlertTitle } from '@/app/components/ui/alert';
import { Badge } from '@/app/components/ui/badge';
import { Progress } from '@/app/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs-shadcn';
import {
  AlertCircle,
  CheckCircle2,
  RefreshCw,
  FileSpreadsheet,
  XCircle,
  ExternalLink,
  Info
} from 'lucide-react';
import DashboardHeader from '@/app/components/dashboard/DashboardHeader';
import { googleSheetsService } from '@/app/services/googleSheets';
import { SyncStatus } from '@/app/types/sync';
import { Skeleton } from '@/app/components/ui/skeleton';
import { Separator } from '@/app/components/ui/separator';
import { toast } from 'react-hot-toast';

interface GoogleSheetsStatus {
  authenticated: boolean;
  spreadsheetId?: string;
  syncStatus?: SyncStatus;
}

export default function GoogleSheetsPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [authenticating, setAuthenticating] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [status, setStatus] = useState<GoogleSheetsStatus>({ authenticated: false });
  const [userId, setUserId] = useState<string>('');

  // Initialize Supabase client
  const supabase = createClient();

  // Fetch user ID and status on mount
  useEffect(() => {
    // Get the current user ID
    const getCurrentUser = async () => {
      try {
        console.log('Attempting to get current user...');
        const { data, error } = await supabase.auth.getUser();

        if (error) {
          console.error('Error getting user:', error);
          toast.error('Error retrieving user session. Please log in again.');
          router.push('/login');
          return;
        }

        if (data.user) {
          setUserId(data.user.id);
          console.log('Current user ID retrieved successfully:', data.user.id);
          // Store user ID in localStorage as a fallback
          localStorage.setItem('googleSheetsUserId', data.user.id);
        } else {
          console.error('No authenticated user found in session');
          toast.error('No active session found. Please log in.');
          router.push('/login');
        }
      } catch (error) {
        console.error('Exception getting user:', error);
        toast.error('Error retrieving user session. Please log in again.');
        router.push('/login');
      }
    };

    getCurrentUser();
    fetchStatus();

    // Add event listener for messages from the auth popup window
    const handleAuthMessage = (event: MessageEvent) => {
      // Only process messages with our expected format
      if (event.data && typeof event.data === 'object') {
        if (event.data.type === 'GOOGLE_AUTH_SUCCESS') {
          console.log('Received authentication success message from popup');
          toast.success('Google authentication successful!');
          setAuthenticating(false);

          // Refresh the status to show the new authentication state
          fetchStatus();

          // If we were in the middle of syncing products, try again after a short delay
          if (syncing) {
            toast.success('Authentication successful! Attempting to sync products now...');
            setTimeout(() => {
              // Call syncProducts again now that we're authenticated
              syncProducts();
            }, 1500); // Give a moment for the status to refresh
          }
        } else if (event.data.type === 'GOOGLE_AUTH_ERROR') {
          console.error('Authentication error:', event.data.error, event.data.message);

          // Display a more user-friendly error message
          let errorMessage = 'Authentication failed';

          if (event.data.error === 'invalid_grant') {
            errorMessage = 'Authentication session expired. Please try again.';
          } else if (event.data.message) {
            errorMessage = `Authentication failed: ${event.data.message}`;
          } else if (event.data.error) {
            errorMessage = `Authentication failed: ${event.data.error}`;
          }

          toast.error(errorMessage, { duration: 5000 });
          setAuthenticating(false);
          setSyncing(false); // Reset syncing state if auth failed
        }
      }
    };

    // Add the event listener
    window.addEventListener('message', handleAuthMessage);

    // Clean up the event listener when the component unmounts
    return () => {
      window.removeEventListener('message', handleAuthMessage);
    };
  }, []);

  // Fetch Google Sheets status
  const fetchStatus = async () => {
    try {
      setLoading(true);
      console.log('Fetching authentication status from API...');
      const response = await fetch('/api/google/sheets');
      const data = await response.json();

      console.log('API status response:', data);

      setStatus(data);
    } catch (error) {
      console.error('Error fetching Google Sheets status:', error);
      toast.error('Failed to fetch Google Sheets status');
    } finally {
      setLoading(false);
    }
  };

  // Start Google authentication
  const startAuthentication = async () => {
    try {
      // Check if we have a user ID
      let currentUserId = userId;

      // If userId is not available in state, try to get it from localStorage as fallback
      if (!currentUserId) {
        console.log('User ID not available in state, checking localStorage...');
        currentUserId = localStorage.getItem('googleSheetsUserId') || '';

        if (currentUserId) {
          console.log('Found user ID in localStorage:', currentUserId);
          setUserId(currentUserId); // Update state with the ID from localStorage
        } else {
          console.error('No user ID available in state or localStorage');
          toast.error('User ID not available. Please refresh the page or log in again.');
          setAuthenticating(false);
          return;
        }
      }

      console.log('Starting authentication with user ID:', currentUserId);
      setAuthenticating(true);

      // Pass the user ID as a query parameter
      const response = await fetch(`/api/google/sheets/auth?userId=${encodeURIComponent(currentUserId)}`);
      const { url } = await response.json();

      if (url) {
        // Open Google auth URL in a new window
        const authWindow = window.open(url, 'googleAuth', 'width=600,height=600');

        // Store authentication start time in localStorage
        localStorage.setItem('googleSheetsAuthStartTime', Date.now().toString());

        // Show immediate instructions to the user
        toast.success(
          'Google authentication window opened. The page will refresh automatically after authentication.',
          { duration: 5000 }
        );

        // Set a timeout to check status after a reasonable time for auth completion
        setTimeout(async () => {
          try {
            console.log('Checking authentication status after timeout...');
            await checkAuthStatus();

            // Show refresh button toast
            toast.success(
              'If your status hasn\'t updated, click the "Refresh Status" button below.',
              { duration: 8000 }
            );
          } catch (error) {
            console.error('Error checking auth status after timeout:', error);
          }
        }, 8000); // Wait 8 seconds before first check
      }
    } catch (error) {
      console.error('Error starting Google authentication:', error);
      toast.error('Failed to start Google authentication');
      setAuthenticating(false);
    }
  };

  // Manual refresh button handler
  const refreshStatus = async () => {
    try {
      setLoading(true);
      toast.success('Refreshing authentication status...');

      // Wait a moment to ensure any pending operations complete
      await new Promise(resolve => setTimeout(resolve, 500));

      // Check auth status
      await fetchStatus();

      setLoading(false);
    } catch (error) {
      console.error('Error refreshing status:', error);
      toast.error('Failed to refresh status');
      setLoading(false);
    }
  };

  // Helper function to check authentication status
  const checkAuthStatus = async (pollInterval?: NodeJS.Timeout) => {
    try {
      const statusResponse = await fetch('/api/google/sheets');
      const statusData = await statusResponse.json();

      console.log('Auth status check result:', statusData);

      if (statusData.authenticated) {
        console.log('Authentication successful!');

        // Clear interval if provided
        if (pollInterval) {
          clearInterval(pollInterval);
        }

        setStatus(statusData);
        setAuthenticating(false);
        toast.success('Successfully authenticated with Google Sheets');
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error checking auth status:', error);
      return false;
    }
  };

  // Sync products to Google Sheets
  const syncProducts = async () => {
    try {
      // Check if we have a user ID
      let currentUserId = userId;

      // If userId is not available in state, try to get it from localStorage as fallback
      if (!currentUserId) {
        console.log('User ID not available in state for sync, checking localStorage...');
        currentUserId = localStorage.getItem('googleSheetsUserId') || '';

        if (currentUserId) {
          console.log('Found user ID in localStorage for sync:', currentUserId);
          setUserId(currentUserId); // Update state with the ID from localStorage
        } else {
          console.error('No user ID available in state or localStorage for sync');
          toast.error('User ID not available. Please refresh the page or log in again.');
          setSyncing(false);
          return;
        }
      }

      console.log('Starting product sync with user ID:', currentUserId);
      setSyncing(true);

      const response = await fetch('/api/google/sheets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'sync_products',
          userId: currentUserId // Pass the user ID directly to the API
        }),
      });

      const data = await response.json();

      if (response.status === 401 && data.requiresAuth && data.authUrl) {
        // Authentication required - open the auth window
        toast.loading('Authentication required. Opening Google authentication window...');

        // Open Google auth URL in a new window
        const authWindow = window.open(data.authUrl, 'googleAuth', 'width=600,height=600');

        toast.success(
          'Please complete the Google authentication in the popup window.',
          { duration: 5000 }
        );

        // We'll rely on the message event listener to handle the auth completion
        // The listener was set up in the useEffect hook
      } else if (data.success) {
        toast.success('Product sync initiated');
        // Refresh status after a short delay
        setTimeout(fetchStatus, 2000);
      } else {
        toast.error(data.error || 'Failed to sync products');
        setSyncing(false);
      }
    } catch (error) {
      console.error('Error syncing products:', error);
      toast.error('Failed to sync products');
    } finally {
      setSyncing(false);
    }
  };

  return (
    <div className="container mx-auto py-6">
      <DashboardHeader
        heading="Google Sheets Integration"
        text="Sync your products with Google Sheets for Google Merchant Center"
        icon={<FileSpreadsheet className="h-6 w-6" />}
      />

      <div className="grid gap-6 mt-8">
        {/* Authentication Status */}
        <Card>
          <CardHeader>
            <CardTitle>Connection Status</CardTitle>
            <CardDescription>
              Connect to your Google Sheets spreadsheet to sync products
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-2">
                <Skeleton className="h-4 w-[250px]" />
                <Skeleton className="h-4 w-[200px]" />
              </div>
            ) : status.authenticated ? (
              <Alert className="bg-green-50 border-green-200">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <AlertTitle className="text-green-800">Connected to Google Sheets</AlertTitle>
                <AlertDescription className="text-green-700">
                  Your account is connected to Google Sheets spreadsheet ID: {status.spreadsheetId || 'Not available'}
                </AlertDescription>
              </Alert>
            ) : (
              <Alert className="bg-amber-50 border-amber-200">
                <AlertCircle className="h-4 w-4 text-amber-600" />
                <AlertTitle className="text-amber-800">Not Connected</AlertTitle>
                <AlertDescription className="text-amber-700">
                  Connect your Google account to start syncing products to Google Sheets
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
          <CardFooter>
            {status.authenticated ? (
              <Button
                variant="outline"
                onClick={refreshStatus}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh Status
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button
                  onClick={startAuthentication}
                  disabled={authenticating}
                >
                  {authenticating ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Connecting...
                    </>
                  ) : (
                    <>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Connect to Google Sheets
                    </>
                  )}
                </Button>

                {/* Add refresh button for after authentication */}
                {authenticating && (
                  <Button
                    variant="outline"
                    onClick={refreshStatus}
                    disabled={loading}
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                    Refresh Status
                  </Button>
                )}
              </div>
            )}
          </CardFooter>
        </Card>

        {/* Sync Status */}
        {status.authenticated && (
          <Card>
            <CardHeader>
              <CardTitle>Product Sync</CardTitle>
              <CardDescription>
                Sync your products to Google Sheets for Google Merchant Center
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {status.syncStatus ? (
                  <>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">Last Sync</p>
                        <p className="text-sm text-gray-500">
                          {status.syncStatus.last_sync_time ? new Date(status.syncStatus.last_sync_time).toLocaleString() : 'N/A'}
                        </p>
                      </div>
                      <Badge variant={status.syncStatus.details && typeof status.syncStatus.details === 'object' && status.syncStatus.details.failed > 0 ? 'destructive' : 'default'}>
                        {status.syncStatus.details && typeof status.syncStatus.details === 'object' ? `${status.syncStatus.details.succeeded}/${status.syncStatus.details.total}` : '0/0'} Synced
                      </Badge>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progress</span>
                        <span>{status.syncStatus.details && typeof status.syncStatus.details === 'object' && status.syncStatus.details.total > 0 ? Math.round((status.syncStatus.details.processed / status.syncStatus.details.total) * 100) : 0}%</span>
                      </div>
                      <Progress value={status.syncStatus.details && typeof status.syncStatus.details === 'object' && status.syncStatus.details.total > 0 ? (status.syncStatus.details.processed / status.syncStatus.details.total) * 100 : 0} />
                    </div>

                    {status.syncStatus.details && typeof status.syncStatus.details === 'object' && Array.isArray(status.syncStatus.details.errors) && status.syncStatus.details.errors.length > 0 && (
                      <div className="mt-4">
                        <p className="text-sm font-medium text-red-600 mb-2">Errors ({status.syncStatus.details && typeof status.syncStatus.details === 'object' && Array.isArray(status.syncStatus.details.errors) ? status.syncStatus.details.errors.length : 0})</p>
                        <div className="max-h-40 overflow-y-auto bg-red-50 p-2 rounded text-sm">
                          {status.syncStatus.details && typeof status.syncStatus.details === 'object' && Array.isArray(status.syncStatus.details.errors) && status.syncStatus.details.errors.map((error: { partId: string; error: string }, index: number) => (
                            <div key={index} className="mb-1 pb-1 border-b border-red-100 last:border-0">
                              <span className="font-medium">Part ID {error.partId}:</span> {error.error}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertTitle>No sync data</AlertTitle>
                    <AlertDescription>
                      You haven't synced any products yet. Click the button below to start syncing.
                      <br /><br />
                      <strong>Note:</strong> To update the spreadsheet, you need to authenticate with Google Sheets.
                      <br /><br />
                      You can view the spreadsheet by clicking the "Open Spreadsheet" button below.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex flex-col gap-4 sm:flex-row">
              <Button
                onClick={syncProducts}
                disabled={syncing}
              >
                {syncing ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Syncing...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Sync Products to Sheets
                  </>
                )}
              </Button>

              <Button
                variant="outline"
                onClick={() => window.open(`https://docs.google.com/spreadsheets/d/${status.spreadsheetId}/edit`, '_blank')}
                disabled={!status.spreadsheetId}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Open Spreadsheet
              </Button>
            </CardFooter>
          </Card>
        )}
      </div>
    </div>
  );
}
