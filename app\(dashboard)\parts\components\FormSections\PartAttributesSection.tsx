// components/FormSections/PartAttributesSection.tsx
import React from 'react';
import { Control, FieldErrors } from 'react-hook-form';
import PartAttributes from '../AddPartModal/PartAttributes';
import { PartFormValues } from '../../types';

interface PartAttributesSectionProps {
  control: Control<PartFormValues>; // Add generic type
  errors: FieldErrors<PartFormValues>; // Add generic type
  getValues: any;
  setValue: any;
  categoryAttributes: ReadonlyArray<any>; // Change to ReadonlyArray
  requirePartNumber?: boolean;
}

export default function PartAttributesSection({
  control,
  errors,
  getValues,
  setValue,
  categoryAttributes,
  requirePartNumber = false
}: PartAttributesSectionProps) {
  return (
    <div className="space-y-4">
      <PartAttributes
        control={control}
        errors={errors}
        getValues={getValues}
        setValue={setValue}
        categoryAttributes={categoryAttributes.map(attr => ({
          ...attr,
          name: attr.attribute,
          input_type: attr.input_type,
        }))}
        requirePartNumber={requirePartNumber}
      />
    </div>
  );
}