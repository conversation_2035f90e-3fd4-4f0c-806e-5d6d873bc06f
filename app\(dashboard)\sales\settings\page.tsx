'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Settings, Save, RefreshCw, Percent } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { toast } from 'react-hot-toast';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

const SalesSettingsPage: React.FC = () => {
  const [vatRate, setVatRate] = useState<number>(16);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const supabase = createClient();

  // Fetch current VAT rate
  useEffect(() => {
    const fetchVatRate = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        const { data, error } = await supabase.rpc('get_vat_rate');
        
        if (error) {
          console.error('Error fetching VAT rate:', error);
          setError('Failed to load VAT settings');
        } else if (data !== null) {
          setVatRate(data);
        }
      } catch (error) {
        console.error('Error fetching VAT rate:', error);
        setError('Failed to load VAT settings');
      } finally {
        setIsLoading(false);
      }
    };

    fetchVatRate();
  }, []);

  // Save VAT rate
  const handleSave = async () => {
    setIsSaving(true);
    setError(null);

    try {
      // Validate VAT rate
      if (vatRate < 0 || vatRate > 100) {
        throw new Error('VAT rate must be between 0 and 100 percent');
      }

      const { data, error } = await supabase.rpc('update_vat_rate', {
        p_vat_rate: vatRate
      });

      if (error) {
        console.error('Error updating VAT rate:', error);
        throw new Error(error.message || 'Failed to update VAT rate');
      }

      toast.success('VAT settings updated successfully');
    } catch (error: any) {
      console.error('Error saving VAT rate:', error);
      setError(error.message || 'Failed to save VAT settings');
      toast.error(error.message || 'Failed to save VAT settings');
    } finally {
      setIsSaving(false);
    }
  };

  // Reset to default
  const handleReset = () => {
    setVatRate(16); // Default VAT rate for Kenya
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-2">
            <Settings className="h-8 w-8 text-teal-600 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900">Sales Settings</h1>
          </div>
          <p className="text-gray-600">
            Configure VAT rates and other sales-related settings for your business.
          </p>
        </div>

        {/* Main Content */}
        <div className="bg-white rounded-lg shadow-sm">
          {/* VAT Settings Section */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center mb-4">
              <Percent className="h-6 w-6 text-teal-600 mr-2" />
              <h2 className="text-xl font-semibold text-gray-900">VAT Configuration</h2>
            </div>
            <p className="text-gray-600 mb-6">
              Set the default VAT rate that will be applied to all sales. This can be overridden for individual sales if needed.
            </p>

            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <LoadingSpinner size={32} />
                <span className="ml-3 text-gray-600">Loading settings...</span>
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="flex">
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">Error</h3>
                        <div className="mt-2 text-sm text-red-700">
                          {error}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="vatRate" className="block text-sm font-medium text-gray-700 mb-2">
                      VAT Rate (%)
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        id="vatRate"
                        value={vatRate}
                        onChange={(e) => setVatRate(parseFloat(e.target.value) || 0)}
                        min="0"
                        max="100"
                        step="0.01"
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                        placeholder="16.00"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 sm:text-sm">%</span>
                      </div>
                    </div>
                    <p className="mt-2 text-sm text-gray-500">
                      Enter the VAT rate as a percentage (e.g., 16 for 16% VAT)
                    </p>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-md">
                    <h3 className="text-sm font-medium text-gray-900 mb-2">Preview</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Sample Amount:</span>
                        <span className="font-medium">Kshs 1,000</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">VAT ({vatRate}%):</span>
                        <span className="font-medium">Kshs {(1000 * vatRate / 100).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between border-t pt-2">
                        <span className="text-gray-900 font-medium">Total with VAT:</span>
                        <span className="font-bold">Kshs {(1000 + (1000 * vatRate / 100)).toFixed(2)}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-between pt-6">
                  <button
                    type="button"
                    onClick={handleReset}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-teal-500"
                  >
                    <RefreshCw className="h-4 w-4 mr-2 inline" />
                    Reset to Default (16%)
                  </button>
                  
                  <button
                    type="button"
                    onClick={handleSave}
                    disabled={isSaving}
                    className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 disabled:opacity-50 flex items-center"
                  >
                    {isSaving ? (
                      <>
                        <LoadingSpinner size={16} />
                        <span className="ml-2">Saving...</span>
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Save Settings
                      </>
                    )}
                  </button>
                </div>
              </motion.div>
            )}
          </div>

          {/* Additional Settings Section (for future expansion) */}
          <div className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Additional Settings</h2>
            <div className="bg-gray-50 p-4 rounded-md">
              <p className="text-gray-600 text-center">
                More sales settings will be available here in future updates.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesSettingsPage;
