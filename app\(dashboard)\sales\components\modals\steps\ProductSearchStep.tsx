'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Search, X } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import Image from 'next/image';
import { SelectedProduct } from '../AddSaleModal';

interface ProductSearchStepProps {
  onProductsSelected: (products: SelectedProduct[]) => void;
  onCancel: () => void;
  initialProducts?: SelectedProduct[];
}

interface SearchResult {
  part_id: number;
  thumbnail_url: string | null;
  title: string;
  price: number;
  stock: number;
  selected?: boolean;
  quantity?: number;
  discount?: number;
  discountReason?: string;
}

const ProductSearchStep: React.FC<ProductSearchStepProps> = ({
  onProductsSelected,
  onCancel,
  initialProducts = []
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<SelectedProduct[]>(initialProducts);
  const [error, setError] = useState<string | null>(null);

  // Refs for focus management
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchButtonRef = useRef<HTMLButtonElement>(null);
  const resultsContainerRef = useRef<HTMLDivElement>(null); // Ref for the results container

  // Initialize search results with any initial products
  useEffect(() => {
    if (initialProducts.length > 0) {
      const initialSearchResults = initialProducts.map(product => ({
        part_id: product.id,
        thumbnail_url: product.thumbnailUrl || null,
        title: product.title,
        price: product.price,
        stock: product.stock,
        selected: true,
        quantity: product.quantity,
        discount: product.discount,
        discountReason: product.discountReason
      }));
      setSearchResults(initialSearchResults);
      // Initialize selected products state based on initial products
      setSelectedProducts(initialProducts);
    }
  }, [initialProducts]); // Only run when initialProducts changes

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    // Blur the input and button immediately on submission attempt
    searchInputRef.current?.blur();
    searchButtonRef.current?.blur();

    if (!searchTerm.trim()) {
      setError('Please enter a search term');
      return;
    }

    setIsSearching(true);
    setError(null);
    setSearchResults([]); // Clear previous results immediately

    let foundResults = false; // Flag to track if results were found

    try {
      const supabase = createClient();

      // --- Try RPC Search First ---
      try {
        const { data: rpcData, error: rpcError } = await supabase
          .rpc('search_products_for_sale', { p_search_term: searchTerm });

        if (rpcError) {
          console.log('RPC search error, falling back:', rpcError.message);
        } else if (rpcData && rpcData.length > 0) {
          console.log('RPC search successful:', rpcData.length);
          const mappedResults = rpcData
            .filter((result: SearchResult) => result.stock > 0) // Filter out no-stock items from RPC results
            .map((result: SearchResult) => {
                const existingProduct = selectedProducts.find(p => p.id === result.part_id);
                return {
                    ...result,
                    selected: !!existingProduct,
                    quantity: existingProduct?.quantity || 1,
                    discount: existingProduct?.discount || 0,
                    discountReason: existingProduct?.discountReason || ''
                };
            });

          if (mappedResults.length > 0) {
              setSearchResults(mappedResults);
              foundResults = true; // Mark results as found
          } else {
               // RPC succeeded but returned only out-of-stock items or empty
               setError(rpcData.length > 0 ? 'Products found, but none have stock.' : 'No products found.');
          }

          // Don't return early from RPC, let finally handle focus/state
        } else {
          console.log('RPC search returned no results, falling back.');
        }
      } catch (rpcError) {
        console.log('RPC search exception, falling back:', rpcError);
      }

      // --- Fallback Direct Query Logic (only if RPC didn't find results) ---
      if (!foundResults) {
          console.log('Performing direct query search');
          const searchTerms = searchTerm.toLowerCase().split(' ').filter(term => term.trim() !== '');

          // Combine queries using Promise.all for potential parallel execution
          const [partsTitleRes, partNumberGroupsRes, carBrandsRes, carModelsRes] = await Promise.all([
              // Query parts by title (AND logic)
              (() => {
                  let query = supabase.from('parts').select('id, title, partnumber_group');
                  searchTerms.forEach(term => { query = query.ilike('title', `%${term}%`); });
                  return query;
              })(),
              // Query part compatibility groups by part number
              supabase.from('part_compatibility_groups').select('id').ilike('part_number', `%${searchTerm}%`),
              // Query car brands (OR logic)
              supabase.from('car_brands').select('brand_name').or(searchTerms.map(term => `brand_name.ilike.%${term}%`).join(',')),
              // Query car models (OR logic)
              supabase.from('car_models').select('model_name').or(searchTerms.map(term => `model_name.ilike.%${term}%`).join(','))
          ]);

          // Process results and handle errors individually
          if (partsTitleRes.error) throw new Error(`Title search failed: ${partsTitleRes.error.message}`);
          const partsByTitle = partsTitleRes.data || [];
          console.log('Parts by title:', partsByTitle.length);

          if (partNumberGroupsRes.error) throw new Error(`Part number search failed: ${partNumberGroupsRes.error.message}`);
          const groupIds = (partNumberGroupsRes.data || []).map(g => g.id);
          let partsByNumber: any[] = [];
          if (groupIds.length > 0) {
              const { data, error } = await supabase.from('parts').select('id, title, partnumber_group').in('partnumber_group', groupIds);
              if (error) throw new Error(`Fetching parts by group failed: ${error.message}`);
              partsByNumber = data || [];
          }
          console.log('Parts by number:', partsByNumber.length);


          if (carBrandsRes.error) console.error('Car brand search error:', carBrandsRes.error);
          if (carModelsRes.error) console.error('Car model search error:', carModelsRes.error);
          const carNames = [
              ...(carBrandsRes.data?.map(b => b.brand_name) || []),
              ...(carModelsRes.data?.map(m => m.model_name) || [])
          ].filter(name => name); // Filter out any potential null/undefined names

          let partsByCar: any[] = [];
           if (carNames.length > 0) {
               // Deduplicate car names before querying
               const uniqueCarNames = Array.from(new Set(carNames));
               console.log('Unique car names found:', uniqueCarNames.length);
               const carPartsOrFilter = uniqueCarNames.map(name => `title.ilike.%${name}%`).join(',');
               const { data, error } = await supabase.from('parts').select('id, title, partnumber_group').or(carPartsOrFilter);
               if (error) console.error('Fetching parts by car name failed:', error);
               else partsByCar = data || [];
           }
          console.log('Parts by car:', partsByCar.length);


          // Combine all parts, ensuring uniqueness
          const combinedPartMap = new Map<number, any>();
          [...partsByTitle, ...partsByNumber, ...partsByCar].forEach(part => {
              if (part) combinedPartMap.set(part.id, part);
          });
          const combinedParts = Array.from(combinedPartMap.values());
          console.log('Combined unique parts:', combinedParts.length);

          if (combinedParts.length === 0) {
              setError('No products found matching your search.');
              // Let finally handle state/focus
          } else {
              // Fetch details (image, price, stock) for combined parts IN PARALLEL
              const detailedResultsPromises = combinedParts.map(async (part) => {
                  const [imageDataRes, conditionDataRes] = await Promise.all([
                      supabase.from('part_images').select('image_url').eq('part_id', part.id).eq('is_main_image', true).limit(1),
                      supabase.from('parts_condition').select('id, stock').eq('part_id', part.id)
                  ]);

                  const imageData = imageDataRes.data;
                  const conditionData = conditionDataRes.data;
                  const totalStock = conditionData?.reduce((sum, cond) => sum + (cond.stock || 0), 0) || 0;

                  // Only proceed if there's stock
                  if (totalStock > 0) {
                      let price = 0;
                      const conditionWithStock = conditionData?.find(c => c.stock > 0);
                      const conditionIdToQuery = conditionWithStock?.id || conditionData?.[0]?.id; // Use first condition with stock, or just the first condition if none have stock

                      if (conditionIdToQuery) {
                           const { data: priceData } = await supabase.from('part_price').select('price').eq('condition_id', conditionIdToQuery).limit(1);
                           if (priceData && priceData.length > 0) price = priceData[0].price;
                      }


                      const existingProduct = selectedProducts.find(p => p.id === part.id);
                      return {
                          part_id: part.id,
                          thumbnail_url: imageData?.[0]?.image_url || null,
                          title: part.title,
                          price: price || 0,
                          stock: totalStock,
                          selected: !!existingProduct,
                          quantity: existingProduct?.quantity || 1,
                          discount: existingProduct?.discount || 0,
                          discountReason: existingProduct?.discountReason || ''
                      };
                  }
                  return null; // Exclude items with no stock
              });

               // Wait for all detail fetches and filter out nulls (no stock)
               const results = (await Promise.all(detailedResultsPromises)).filter(r => r !== null) as SearchResult[];
              console.log('Final results with stock:', results.length);

              if (results.length > 0) {
                  setSearchResults(results);
                  foundResults = true; // Mark results as found
              } else {
                  // Parts were found initially, but none had stock after detail fetch
                  setError('Products found, but none have available stock.');
              }
          }
      } // End of fallback direct query logic

    } catch (error) {
      console.error('Error during search process:', error);
      setError(error instanceof Error ? `Search failed: ${error.message}` : 'An unknown error occurred during search.');
      setSearchResults([]); // Clear results on error
      foundResults = false; // Ensure flag is false on error
    } finally {
      setIsSearching(false);
      // Use setTimeout to ensure focus changes after state update/render
      setTimeout(() => {
        searchInputRef.current?.blur(); // Blur input again
        searchButtonRef.current?.blur(); // Blur button again
        if (foundResults && resultsContainerRef.current) {
          // If results were found, focus the container
          resultsContainerRef.current.focus();
        } else if (searchInputRef.current) {
           // If no results or error, focus back on input for easy correction
          // searchInputRef.current.focus(); // Optional: Re-focus input if no results
          // searchInputRef.current.select(); // Optional: Select text for easy replacement
        }
      }, 0); // Delay of 0ms pushes execution after current stack
    }
  };

  const toggleProductSelection = (productId: number) => {
    const product = searchResults.find(p => p.part_id === productId);
    if (!product) return; // Should not happen

    // Allow toggling even if stock is 0, but handle state appropriately
    // Error for trying to select 0 stock is handled elsewhere if needed (e.g., on 'Next')

    setError(null); // Clear error on valid interaction

    setSearchResults(prev =>
      prev.map(p => {
        if (p.part_id === productId) {
          const newSelected = !p.selected;
          return {
            ...p,
            selected: newSelected,
            // Set defaults only when selecting for the first time
            quantity: newSelected ? (p.quantity ?? 1) : undefined,
            discount: newSelected ? (p.discount ?? 0) : undefined,
            discountReason: newSelected ? (p.discountReason ?? '') : undefined
          };
        }
        return p;
      })
    );
  };

   // Update selectedProducts state whenever searchResults changes
   useEffect(() => {
       const currentlySelected = searchResults
           .filter(product => product.selected && product.stock > 0) // Only include selected AND in-stock items
           .map(product => ({
               id: product.part_id,
               title: product.title,
               price: product.price,
               stock: product.stock,
               quantity: product.quantity || 1, // Ensure quantity is at least 1 if selected
               discount: product.discount || 0,
               discountReason: product.discountReason || '',
               thumbnailUrl: product.thumbnail_url || undefined
           }));
       setSelectedProducts(currentlySelected);
   }, [searchResults]);

  const updateProductQuantity = (productId: number, quantity: number) => {
    const newQuantity = Math.max(1, quantity); // Floor at 1

    const product = searchResults.find(p => p.part_id === productId);
    if (!product) return;

    if (newQuantity > product.stock) {
        setError(`Quantity (${newQuantity}) cannot exceed stock (${product.stock}) for ${product.title}`);
        // Do not update state if invalid
        return;
    }

    setError(null);

    setSearchResults(prev =>
      prev.map(p => (p.part_id === productId ? { ...p, quantity: newQuantity } : p))
    );
  };

  const updateProductDiscount = (productId: number, discount: number) => {
    const newDiscount = Math.max(0, discount);

    const product = searchResults.find(p => p.part_id === productId);
    // Ensure quantity exists before checking discount validity
    if (!product || !product.quantity || product.quantity < 1) return;

    const totalPrice = product.price * product.quantity;
    if (newDiscount > totalPrice) {
      setError(`Discount (Kshs ${newDiscount.toLocaleString()}) cannot exceed total price (Kshs ${totalPrice.toLocaleString()})`);
      return;
    }

    setError(null);

    setSearchResults(prev =>
      prev.map(p => (p.part_id === productId ? { ...p, discount: newDiscount } : p))
    );
  };

  const updateDiscountReason = (productId: number, reason: string) => {
    setSearchResults(prev =>
      prev.map(p => (p.part_id === productId ? { ...p, discountReason: reason } : p))
    );
  };

  const handleNext = () => {
    // Double check selection validity before proceeding
    const validSelectedProducts = searchResults
        .filter(p => p.selected && p.stock > 0 && (p.quantity ?? 0) > 0 && (p.quantity ?? 0) <= p.stock)
        .map(product => ({
             id: product.part_id,
             title: product.title,
             price: product.price,
             stock: product.stock,
             quantity: product.quantity as number, // Should be defined and > 0 here
             discount: product.discount || 0,
             discountReason: product.discountReason || '',
             thumbnailUrl: product.thumbnail_url || undefined
         }));


    if (validSelectedProducts.length === 0) {
        setError('Please select at least one in-stock product with a valid quantity.');
        // Update the main selectedProducts state to reflect only valid ones, if desired
        // setSelectedProducts(validSelectedProducts);
        return;
    }

    // Check if any originally selected products became invalid (e.g., stock changed)
    if (validSelectedProducts.length !== selectedProducts.length) {
         setError('Some selected items are no longer valid (e.g., out of stock or invalid quantity). Please review your selection.');
         // Update the main selectedProducts state to reflect only valid ones
         setSelectedProducts(validSelectedProducts);
         return;
    }


    setError(null);
    onProductsSelected(validSelectedProducts); // Pass only the validated products
  };

  return (
    <div>
      {/* Search Form */}
      <form onSubmit={handleSearch} className="mb-4">
        <div className="flex">
          <div className="relative flex-grow">
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Search by name, part #, car..."
              value={searchTerm}
              onChange={(e) => {
                   setSearchTerm(e.target.value);
                   setError(null);
               }}
              className="w-full px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
            />
            {searchTerm && (
              <button
                type="button"
                onClick={() => {
                    setSearchTerm('');
                    setSearchResults([]);
                    setError(null);
                    searchInputRef.current?.focus();
                 }}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-1 focus:ring-gray-400 rounded-full p-0.5"
                aria-label="Clear search term"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
          <button
            ref={searchButtonRef}
            type="submit"
            className="px-4 py-2 bg-teal-600 text-white rounded-r-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-teal-500 disabled:bg-gray-400 disabled:opacity-70"
            disabled={isSearching || !searchTerm.trim()}
          >
            {isSearching ? <LoadingSpinner size={16} /> : <Search className="w-4 h-4" />}
             <span className="sr-only">Search</span>
          </button>
        </div>
      </form>

      {/* Error Message */}
      {error && (
        <div role="alert" className="bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded mb-4 text-sm">
          {error}
        </div>
      )}

      {/* Search Results Container */}
      <div
        ref={resultsContainerRef}
        tabIndex={-1}
        className="mb-4 max-h-96 overflow-y-auto border border-gray-200 rounded-md focus:outline-none focus:ring-1 focus:ring-teal-300"
        aria-live="polite"
      >
        {isSearching ? (
          <div className="flex justify-center items-center py-12 text-gray-500">
            <LoadingSpinner size={24} />
             <span className="ml-2">Searching...</span>
          </div>
        ) : searchResults.length > 0 ? (
          <table className="min-w-full divide-y divide-gray-200">
             <thead className="bg-gray-100 sticky top-0 z-10">
              <tr>
                <th scope="col" className="px-4 py-2 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Select
                </th>
                <th scope="col" className="px-4 py-2 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Product
                </th>
                <th scope="col" className="px-4 py-2 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Price
                </th>
                <th scope="col" className="px-4 py-2 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Stock
                </th>
                <th scope="col" className="px-4 py-2 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Quantity
                </th>
                <th scope="col" className="px-4 py-2 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Discount (Kshs)
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {searchResults.map((product) => (
                 <tr
                    key={product.part_id}
                    className={`${product.selected ? 'bg-teal-50' : 'hover:bg-gray-50'}`}
                 >
                  <td className="px-4 py-2 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={product.selected || false}
                      onChange={() => toggleProductSelection(product.part_id)}
                      disabled={product.stock <= 0}
                      className={`h-4 w-4 border-gray-300 rounded focus:ring-teal-500 ${
                        product.stock > 0 ? 'text-teal-600 cursor-pointer' : 'text-gray-400 cursor-not-allowed'
                      }`}
                      aria-label={`Select ${product.title}`}
                    />
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 relative bg-gray-100 rounded">
                        {product.thumbnail_url ? (
                          <Image
                            src={product.thumbnail_url}
                            alt=""
                            fill
                            sizes="40px"
                            className="object-cover rounded"
                          />
                        ) : (
                          <div className="h-full w-full flex items-center justify-center text-gray-400 text-xs">
                            No img
                          </div>
                        )}
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">{product.title}</div>
                        <div className="text-xs text-gray-500">ID: {product.part_id}</div>
                      </div>
                    </div>
                  </td>
                   <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-800 text-right">
                    {product.price.toLocaleString()}
                  </td>
                   <td className="px-4 py-2 whitespace-nowrap text-sm text-center">
                    {product.stock > 0 ? (
                       <span className={`font-medium ${product.stock < 5 ? 'text-red-600' : product.stock < 20 ? 'text-orange-600' : 'text-green-600'}`}>
                            {product.stock}
                        </span>
                    ) : (
                      <span className="text-red-600 font-semibold">Out</span>
                    )}
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-center">
                    {product.selected && product.stock > 0 ? (
                      <input
                        type="number"
                        min="1"
                        max={product.stock}
                        value={product.quantity || 1}
                        onChange={(e) => updateProductQuantity(product.part_id, parseInt(e.target.value) || 1)}
                         onClick={(e) => e.stopPropagation()}
                        className="w-16 px-2 py-1 border border-gray-300 rounded-md text-sm text-center focus:ring-teal-500 focus:border-teal-500"
                        aria-label={`Quantity for ${product.title}`}
                      />
                    ) : (
                       <span className="text-xs text-gray-400">{product.selected ? 'No stock' : '-'}</span>
                     )}
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap">
                    {product.selected && product.stock > 0 && (
                      <div className="space-y-1">
                        <input
                          type="number"
                          min="0"
                           step="0.01"
                          value={product.discount || 0}
                          onChange={(e) => updateProductDiscount(product.part_id, parseFloat(e.target.value) || 0)}
                           onClick={(e) => e.stopPropagation()}
                          className="w-24 px-2 py-1 border border-gray-300 rounded-md text-sm focus:ring-teal-500 focus:border-teal-500"
                          placeholder="0.00"
                           aria-label={`Discount amount for ${product.title}`}
                        />
                        <input
                          type="text"
                          value={product.discountReason || ''}
                          onChange={(e) => updateDiscountReason(product.part_id, e.target.value)}
                           onClick={(e) => e.stopPropagation()}
                          className="w-full px-2 py-1 border border-gray-300 rounded-md text-xs focus:ring-teal-500 focus:border-teal-500"
                          placeholder="Reason (optional)"
                           aria-label={`Discount reason for ${product.title}`}
                        />
                      </div>
                    )}
                     {!product.selected && <span className="text-xs text-gray-400">-</span>}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : !isSearching && (
           <div className="text-center py-12 px-4 text-gray-500">
             {searchTerm
                 ? 'No products found matching your search. Please check spelling or try different terms.'
                 : 'Use the search bar above to find products.'}
           </div>
        )}
      </div>

      {/* Selected Products Summary */}
      {selectedProducts.length > 0 && (
        <div className="mb-6 p-4 bg-gray-50 rounded-md border border-gray-200">
          <h4 className="font-semibold text-gray-800 mb-3 text-base">
             Order Summary ({selectedProducts.length} item{selectedProducts.length === 1 ? '' : 's'})
          </h4>
          <dl className="space-y-1 text-sm text-gray-700">
            {selectedProducts.map(product => (
              <div key={product.id} className="flex justify-between items-start mb-1">
                 <dt className="flex-1 mr-2">
                      <span>{product.title}</span>
                      <span className="text-xs text-gray-500 ml-1 block sm:inline">({product.quantity} x {product.price.toLocaleString()})</span>
                      {product.discount > 0 && product.discountReason && (
                           <span className="text-xs text-gray-500 block italic ml-1">Reason: {product.discountReason}</span>
                      )}
                 </dt>
                 <dd className="font-medium text-gray-900 text-right">
                     <span>Kshs {(product.price * product.quantity).toLocaleString()}</span>
                     {product.discount > 0 && (
                         <span className="text-red-600 block">(-{product.discount.toLocaleString()})</span>
                     )}
                 </dd>
              </div>
            ))}
          </dl>
          <hr className="my-3 border-gray-300"/>
           <div className="flex justify-between items-center font-bold text-gray-800 text-lg">
             <span>Total Amount</span>
             <span>
                 Kshs {selectedProducts.reduce((sum, product) =>
                   sum + (product.price * product.quantity) - (product.discount || 0), 0).toLocaleString()}
             </span>
           </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 mt-6">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-gray-400"
        >
          Cancel
        </button>
        <button
          type="button"
          onClick={handleNext}
          disabled={selectedProducts.length === 0 || isSearching}
          className="px-6 py-2 bg-teal-600 text-white rounded-md text-sm font-medium hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-teal-500 disabled:bg-gray-400 disabled:opacity-70 disabled:cursor-not-allowed"
        >
          Next ({selectedProducts.length})
        </button>
      </div>
    </div>
  );
};

export default ProductSearchStep;
