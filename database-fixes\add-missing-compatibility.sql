-- =====================================================
-- COMPREHENSIVE PARTS COMPATIBILITY UPDATE SCRIPT
-- =====================================================
-- This script adds missing VW/Audi cross-compatibility for parts
-- that should logically be compatible with both brands

-- Step 1: Create a temporary table to map platform compatibility
-- Based on shared platforms between VW and Audi vehicles

CREATE TEMP TABLE platform_mapping AS
WITH platform_groups AS (
  -- Group 1: PQ35 Platform (Golf Mk5/6, A3 8P)
  SELECT 'PQ35' as platform, vt.id as variation_trim_id, cb.brand_name, cm.model_name, cg.name as generation_name
  FROM variation_trim vt 
  JOIN car_variation cv ON vt.variation_id = cv.id 
  JOIN car_generation cg ON cv.generation_id = cg.id 
  JOIN car_models cm ON cg.model_id = cm.id 
  JOIN car_brands cb ON cm.brand_id = cb.brand_id 
  WHERE (cb.brand_name = 'VW' AND cm.model_name = 'Golf' AND cg.name IN ('Mk5', 'Mk6'))
     OR (cb.brand_name = 'Audi' AND cm.model_name = 'A3' AND cg.name = '8P')
  
  UNION ALL
  
  -- Group 2: MQB Platform (Golf Mk7/8, A3 8V)
  SELECT 'MQB' as platform, vt.id as variation_trim_id, cb.brand_name, cm.model_name, cg.name as generation_name
  FROM variation_trim vt 
  JOIN car_variation cv ON vt.variation_id = cv.id 
  JOIN car_generation cg ON cv.generation_id = cg.id 
  JOIN car_models cm ON cg.model_id = cm.id 
  JOIN car_brands cb ON cm.brand_id = cb.brand_id 
  WHERE (cb.brand_name = 'VW' AND cm.model_name = 'Golf' AND cg.name IN ('Mk7', 'Mk8'))
     OR (cb.brand_name = 'Audi' AND cm.model_name = 'A3' AND cg.name = '8V')
  
  UNION ALL
  
  -- Group 3: B6/B7 Platform (Passat B6/B7, A4 B7/B8)
  SELECT 'B6B7' as platform, vt.id as variation_trim_id, cb.brand_name, cm.model_name, cg.name as generation_name
  FROM variation_trim vt 
  JOIN car_variation cv ON vt.variation_id = cv.id 
  JOIN car_generation cg ON cv.generation_id = cg.id 
  JOIN car_models cm ON cg.model_id = cm.id 
  JOIN car_brands cb ON cm.brand_id = cb.brand_id 
  WHERE (cb.brand_name = 'VW' AND cm.model_name = 'Passat' AND cg.name IN ('B6', 'B7', 'B8'))
     OR (cb.brand_name = 'Audi' AND cm.model_name = 'A4' AND cg.name IN ('B7', 'B8'))
)
SELECT * FROM platform_groups;

-- Step 2: Identify parts that need cross-brand compatibility
CREATE TEMP TABLE parts_needing_update AS
WITH part_brands AS (
  SELECT pc.part_id, cb.brand_name 
  FROM parts_car pc 
  JOIN variation_trim vt ON pc.variation_trim_id = vt.id 
  JOIN car_variation cv ON vt.variation_id = cv.id 
  JOIN car_generation cg ON cv.generation_id = cg.id 
  JOIN car_models cm ON cg.model_id = cm.id 
  JOIN car_brands cb ON cm.brand_id = cb.brand_id
),
part_brand_summary AS (
  SELECT part_id, STRING_AGG(DISTINCT brand_name, ', ' ORDER BY brand_name) as brands 
  FROM part_brands 
  GROUP BY part_id
)
SELECT p.id as part_id, p.title, pbs.brands as current_brands
FROM parts p 
JOIN part_brand_summary pbs ON p.id = pbs.part_id 
WHERE (
  -- Parts with VW/Audi in title but only single brand compatibility
  ((p.title ILIKE '%volkswagen%' OR p.title ILIKE '%vw%') AND (p.title ILIKE '%audi%') AND pbs.brands != 'Audi, VW')
  OR
  -- Parts in categories that typically share between brands
  (p.category_id IN (
    SELECT id FROM car_part_categories 
    WHERE label IN ('Steering Racks', 'Radiators', 'Brake Discs', 'Brake Pads', 'Suspension', 'Engine Mounts')
  ) AND pbs.brands != 'Audi, VW')
);

-- Step 3: Get the next available ID for parts_car table
DO $$
DECLARE
    max_id INTEGER;
    current_id INTEGER;
    part_record RECORD;
    platform_record RECORD;
    existing_count INTEGER;
BEGIN
    -- Get the maximum ID
    SELECT COALESCE(MAX(id), 0) INTO max_id FROM parts_car;
    current_id := max_id + 1;
    
    -- For each part that needs updating
    FOR part_record IN 
        SELECT DISTINCT part_id FROM parts_needing_update 
        LIMIT 50 -- Process in batches to avoid timeout
    LOOP
        -- Check if this part has Audi compatibility but missing VW
        SELECT COUNT(*) INTO existing_count
        FROM parts_car pc 
        JOIN variation_trim vt ON pc.variation_trim_id = vt.id 
        JOIN car_variation cv ON vt.variation_id = cv.id 
        JOIN car_generation cg ON cv.generation_id = cg.id 
        JOIN car_models cm ON cg.model_id = cm.id 
        JOIN car_brands cb ON cm.brand_id = cb.brand_id 
        WHERE pc.part_id = part_record.part_id AND cb.brand_name = 'Audi';
        
        -- If part has Audi compatibility, add corresponding VW compatibility
        IF existing_count > 0 THEN
            -- Add VW vehicles from the same platforms
            FOR platform_record IN 
                SELECT DISTINCT pm_vw.variation_trim_id
                FROM parts_car pc 
                JOIN platform_mapping pm_audi ON pc.variation_trim_id = pm_audi.variation_trim_id 
                JOIN platform_mapping pm_vw ON pm_audi.platform = pm_vw.platform 
                WHERE pc.part_id = part_record.part_id 
                  AND pm_audi.brand_name = 'Audi' 
                  AND pm_vw.brand_name = 'VW'
                  AND NOT EXISTS (
                    SELECT 1 FROM parts_car pc2 
                    WHERE pc2.part_id = part_record.part_id 
                      AND pc2.variation_trim_id = pm_vw.variation_trim_id
                  )
                LIMIT 5 -- Limit to 5 VW variants per part
            LOOP
                INSERT INTO parts_car (id, part_id, variation_trim_id) 
                VALUES (current_id, part_record.part_id, platform_record.variation_trim_id);
                current_id := current_id + 1;
            END LOOP;
        END IF;
        
        -- Check if this part has VW compatibility but missing Audi
        SELECT COUNT(*) INTO existing_count
        FROM parts_car pc 
        JOIN variation_trim vt ON pc.variation_trim_id = vt.id 
        JOIN car_variation cv ON vt.variation_id = cv.id 
        JOIN car_generation cg ON cv.generation_id = cg.id 
        JOIN car_models cm ON cg.model_id = cm.id 
        JOIN car_brands cb ON cm.brand_id = cb.brand_id 
        WHERE pc.part_id = part_record.part_id AND cb.brand_name = 'VW';
        
        -- If part has VW compatibility, add corresponding Audi compatibility
        IF existing_count > 0 THEN
            -- Add Audi vehicles from the same platforms
            FOR platform_record IN 
                SELECT DISTINCT pm_audi.variation_trim_id
                FROM parts_car pc 
                JOIN platform_mapping pm_vw ON pc.variation_trim_id = pm_vw.variation_trim_id 
                JOIN platform_mapping pm_audi ON pm_vw.platform = pm_audi.platform 
                WHERE pc.part_id = part_record.part_id 
                  AND pm_vw.brand_name = 'VW' 
                  AND pm_audi.brand_name = 'Audi'
                  AND NOT EXISTS (
                    SELECT 1 FROM parts_car pc2 
                    WHERE pc2.part_id = part_record.part_id 
                      AND pc2.variation_trim_id = pm_audi.variation_trim_id
                  )
                LIMIT 5 -- Limit to 5 Audi variants per part
            LOOP
                INSERT INTO parts_car (id, part_id, variation_trim_id) 
                VALUES (current_id, part_record.part_id, platform_record.variation_trim_id);
                current_id := current_id + 1;
            END LOOP;
        END IF;
    END LOOP;
END $$;

-- Step 4: Show summary of changes
SELECT 
    'Summary of Compatibility Updates' as description,
    COUNT(DISTINCT part_id) as parts_updated,
    COUNT(*) as total_new_compatibility_records
FROM parts_car 
WHERE id > (SELECT COALESCE(MAX(id), 0) FROM parts_car WHERE id <= 1325);
