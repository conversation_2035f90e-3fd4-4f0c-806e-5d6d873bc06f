'use client';

import React, { useState, useEffect } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import Notification from '@/app/components/ui/Notification';
import PartCard from '@/app/components/parts/PartCard';
import Pagination from '@/app/components/parts/Pagination';
import { usePartsSearch } from '@/app/hooks/usePartsSearch';
import { useSearchParams, useRouter } from 'next/navigation';
import AddPartModal from './AddPartModal/index';
import BottomNavigation from '@/app/components/ui/menu/BottomNavigation';

const PartsContent = () => {
  const [showNotification, setShowNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();

  // Use our custom hook for parts search and pagination
  const {
    parts,
    setParts,
    totalParts,
    setTotalParts,
    currentPage,
    totalPages,
    isLoading,
    error,
    handlePageChange,
    fetchParts
  } = usePartsSearch();

  // Function to show notification
  const displayNotification = (message: string) => {
    setNotificationMessage(message);
    setShowNotification(true);

    // Auto-hide notification after 3 seconds
    setTimeout(() => {
      setShowNotification(false);
    }, 3000);
  };

  // This component doesn't need to handle search directly anymore
  // Search is handled by the SearchBar component

  // Check for openAddModal parameter when component mounts
  useEffect(() => {
    const openAddModal = searchParams.get('openAddModal');
    if (openAddModal === 'true') {
      setIsModalOpen(true);
      // Update URL without the parameter to avoid reopening on refresh
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('openAddModal');
      router.replace(newUrl.pathname + newUrl.search);
    }
  }, [searchParams, router]);

  // Handle modal open/close
  const handleOpenModal = () => {
    console.log('handleOpenModal called, setting isModalOpen to true');
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    console.log('handleCloseModal called, setting isModalOpen to false');
    setIsModalOpen(false);
  };

  // Debug modal state changes
  useEffect(() => {
    console.log('PartsContent: Modal state changed:', isModalOpen);

    // Check if modal-root exists
    if (isModalOpen && typeof document !== 'undefined') {
      const modalRoot = document.getElementById('modal-root');
      console.log('PartsContent: modal-root element exists:', !!modalRoot);
    }
  }, [isModalOpen]);

  const handleSuccess = () => {
    setIsModalOpen(false);
    displayNotification('Part has been added successfully');
    // Refresh the parts list
    fetchParts(currentPage, searchParams?.get('query') || '');
  };

  // Scroll to top when parts are loaded for a new page
  useEffect(() => {
    if (!isLoading && parts.length > 0) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [isLoading, parts]);

  return (
    <>
      {/* Main content */}
      <div className="w-full pb-24">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Parts Catalog</h1>
          <p className="text-gray-600">Browse our collection of available parts</p>
        </div>

        {/* Parts count and filters */}
        <div className="flex flex-wrap items-center justify-between mb-6">
          <div className="mb-4 md:mb-0">
            <p className="text-gray-600">
              {isLoading ? 'Loading...' : `Showing ${parts.length} of ${totalParts} parts`}
            </p>
          </div>
        </div>

        {/* Parts grid */}
        <div className={`grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 ${!isLoading && !error && parts.length > 0 ? 'relative' : ''}`}>
          {isLoading ? (
            // Loading skeletons
            Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden h-80 animate-pulse">
                <div className="bg-gray-200 h-48"></div>
                <div className="p-4">
                  <div className="bg-gray-200 h-6 rounded mb-2"></div>
                  <div className="bg-gray-200 h-4 rounded w-2/3 mb-2"></div>
                  <div className="bg-gray-200 h-4 rounded w-1/3 mt-4"></div>
                </div>
              </div>
            ))
          ) : error ? (
            <div className="col-span-full text-center py-12">
              <p className="text-red-500 mb-2">Error loading parts</p>
              <p className="text-gray-600">{error}</p>
              <button
                onClick={() => fetchParts(currentPage, searchParams?.get('query') || '')}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                Try Again
              </button>
            </div>
          ) : parts.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <p className="text-gray-600 mb-2">No parts found</p>
              <p className="text-gray-500">Try adjusting your search criteria</p>
            </div>
          ) : (
            <AnimatePresence mode="popLayout">
              {parts.map((part) => (
                <motion.div
                  key={part.id}
                  layout
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8, transition: { duration: 0.2 } }}
                  transition={{ duration: 0.3 }}
                  className="h-full"
                >
                  <PartCard
                    part={part}
                    onDelete={(deletedPartId) => {
                      // Update the parts list locally after deletion
                      const updatedParts = parts.filter(p => p.id !== deletedPartId);
                      setParts(updatedParts);

                      // Update the total parts count
                      setTotalParts(prevTotal => prevTotal - 1);

                      // Show notification
                      displayNotification(`Part "${part.title}" has been deleted`);
                    }}
                  />
                </motion.div>
              ))}
            </AnimatePresence>
          )}
        </div>

        {/* Pagination */}
        {!isLoading && !error && totalPages > 1 && (
          <div className="mt-8 mb-8">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>

      {/* Add Part Modal */}
      {console.log('Rendering AddPartModal with isOpen:', isModalOpen)}
      <AddPartModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      {/* Bottom Navigation */}
      <BottomNavigation onOpenAddModal={handleOpenModal} />

      {/* Notification */}
      <AnimatePresence>
        {showNotification && (
          <Notification
            header="Parts Notification"
            body={notificationMessage}
            type="info"
            duration={3000}
            position="bottom-middle"
            onClose={() => setShowNotification(false)}
          />
        )}
      </AnimatePresence>
    </>
    );
};

export default PartsContent;
