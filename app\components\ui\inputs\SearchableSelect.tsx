"use client";

import {
  useState,
  createContext,
  useContext,
  forwardRef,
  useEffect,
  useRef,
  Children,
  isValidElement,
} from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDown, X } from "lucide-react";
import React from "react";

interface SearchableSelectProps {
  children: React.ReactNode;
  defaultValue?: string;
  onValueChange?: (value: string) => void;
}

interface SelectContextType {
  isOpen: boolean;
  selectedValue: string;
  searchQuery: string;
  toggleOpen: () => void;
  handleSelect: (value: string) => void;
  setSearchQuery: (query: string) => void;
}

const SelectContext = createContext<SelectContextType | null>(null);

const useSelect = () => {
  const context = useContext(SelectContext);
  if (!context) {
    throw new Error("useSelect must be used within a Select provider");
  }
  return context;
};

const SSelect = ({ children, defaultValue = "", onValueChange }: SearchableSelectProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(defaultValue);
  const [searchQuery, setSearchQuery] = useState("");
  const triggerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        triggerRef.current &&
        !triggerRef.current.contains(event.target as Node) &&
        contentRef.current &&
        !contentRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const toggleOpen = () => setIsOpen(!isOpen);

  const handleSelect = (value: string) => {
    setSelectedValue(value);
    setSearchQuery("");
    setIsOpen(false);
    onValueChange?.(value);
  };

  return (
    <SelectContext.Provider
      value={{ isOpen, selectedValue, searchQuery, toggleOpen, handleSelect, setSearchQuery }}
    >
      <div className="relative w-full" ref={triggerRef}>
        {children}
      </div>
    </SelectContext.Provider>
  );
};

interface SSelectTriggerProps extends React.RefAttributes<HTMLDivElement> {
  className?: string;
  placeholder: string;
}

const SSelectTrigger = forwardRef<HTMLDivElement, SSelectTriggerProps>(
  ({ className, placeholder }, ref) => {
    const { isOpen, toggleOpen, selectedValue, searchQuery, setSearchQuery, handleSelect } = useSelect();
    const inputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
      if (isOpen && inputRef.current) {
        inputRef.current.focus();
      }
    }, [isOpen]);

    return (
      <motion.div
        ref={ref}
        onClick={toggleOpen}
        className={`relative flex items-center justify-between w-full px-4 py-3 text-sm bg-white border rounded-md shadow-sm hover:bg-gray-50 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:border-indigo-500 ${
          className || ""
        }`}
        whileTap={{ scale: 0.98 }}
      >
        <div className="flex items-center w-full relative">
          <div className="relative flex-grow">
            <input
              ref={inputRef}
              value={isOpen ? searchQuery : selectedValue}
              onChange={(e) => {
                if (isOpen) {
                  setSearchQuery(e.target.value);
                }
              }}
              placeholder= {placeholder}
              className={`w-full bg-transparent border-none outline-none placeholder:text-gray-400 transition-all duration-200 ${
                selectedValue && !isOpen ? 'pt-4 pb-0' : 'py-0' // Adjusted paddingTop and paddingBottom
              }`}
              readOnly={!isOpen}
            />
            {!isOpen && selectedValue && (
              <motion.label
                className="absolute left-0 top-0 text-[10px] text-gray-500 origin-top-left" // Adjusted top to 0
                initial={{ opacity: 0, scale: 0.8, y: 8 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                transition={{ duration: 0.15 }}
              >
                {placeholder}
              </motion.label>
            )}
          </div>
          {!isOpen && selectedValue ? (
            <X
              className="w-4 h-4 ml-2 text-gray-400 hover:text-gray-500"
              onClick={(e) => {
                e.stopPropagation();
                handleSelect("");
              }}
            />
          ) : (
            <motion.div
              animate={{ rotate: isOpen ? 180 : 0 }}
              transition={{ duration: 0.2 }}
              className="ml-2"
            >
              <ChevronDown className="w-4 h-4 text-gray-400" />
            </motion.div>
          )}
        </div>
      </motion.div>
    );
  }
);
SSelectTrigger.displayName = "SSelectTrigger";

const SSelectContent = forwardRef<HTMLDivElement, { children: React.ReactNode; className?: string }>(
  ({ children, className }, ref) => {
    const { isOpen, searchQuery } = useSelect();

    const filteredChildren = Children.map(children, (child) => {
      if (!isValidElement(child)) return null;
      const childElement = child as React.ReactElement<{ children: React.ReactNode }>;
      const childText = childElement.props.children?.toString().toLowerCase() || "";
      return childText.includes(searchQuery.toLowerCase()) ? child : null;
    });

    return (
      <AnimatePresence>
        {isOpen && (
          <motion.div
            ref={ref}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
            className={`absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg overflow-y-auto max-h-60 ${className || ""}`}
          >
            <ul className="py-1 text-sm text-gray-700">
              {filteredChildren && filteredChildren.length > 0 ? (
                filteredChildren
              ) : (
                <motion.li
                  className="px-4 py-2 text-gray-400"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  No results found
                </motion.li>
              )}
            </ul>
          </motion.div>
        )}
      </AnimatePresence>
    );
  }
);
SSelectContent.displayName = "SSelectContent";

const SSelectItem = ({
  value,
  children,
}: {
  value: string;
  children: React.ReactNode;
}) => {
  const { handleSelect, selectedValue } = useSelect();

  return (
    <motion.li
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -10 }}
      transition={{ duration: 0.2 }}
      className={`px-4 py-2 cursor-pointer hover:bg-indigo-50 hover:text-indigo-700 ${
        selectedValue === value ? "bg-indigo-50 text-indigo-700" : ""
      }`}
      onClick={() => handleSelect(value)}
    >
      {children}
    </motion.li>
  );
};

export { SSelect, SSelectTrigger, SSelectContent, SSelectItem };