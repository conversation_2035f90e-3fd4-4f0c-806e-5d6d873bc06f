'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Trash2, User, Phone, Mail, Briefcase, Star, Edit2, X } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import PhoneInput from '@/app/components/ui/inputs/PhoneInput';
import toast from 'react-hot-toast';

interface ContactPerson {
  id?: string;
  name: string;
  phone_number: string;
  email: string;
  position: string;
  is_primary: boolean;
  is_active: boolean;
  notes: string;
}

interface ContactPersonsManagerProps {
  clientId?: string;
  categoryName?: string;
  contacts: ContactPerson[];
  onChange: (contacts: ContactPerson[]) => void;
  isEditing?: boolean;
  showSaveButton?: boolean; // Whether to show the separate save button
}

const ContactPersonsManager: React.FC<ContactPersonsManagerProps> = ({
  clientId,
  categoryName,
  contacts,
  onChange,
  isEditing = true,
  showSaveButton = false
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [formData, setFormData] = useState<ContactPerson>({
    name: '',
    phone_number: '',
    email: '',
    position: '',
    is_primary: false,
    is_active: true,
    notes: ''
  });

  // Check if this client category should show contact persons
  const shouldShowContacts = categoryName === 'Garage' || categoryName === 'Shop' || categoryName === 'Broker';

  // Start adding new contact person
  const startAddingContact = () => {
    setFormData({
      name: '',
      phone_number: '',
      email: '',
      position: '',
      is_primary: contacts.length === 0, // First contact is primary by default
      is_active: true,
      notes: ''
    });
    setIsAddingNew(true);
    setEditingIndex(null);
  };

  // Start editing existing contact person
  const startEditingContact = (index: number) => {
    setFormData({ ...contacts[index] });
    setEditingIndex(index);
    setIsAddingNew(false);
  };

  // Cancel form
  const cancelForm = () => {
    setIsAddingNew(false);
    setEditingIndex(null);
    setFormData({
      name: '',
      phone_number: '',
      email: '',
      position: '',
      is_primary: false,
      is_active: true,
      notes: ''
    });
  };

  // Save form (add or update)
  const saveForm = () => {
    if (!formData.name.trim() || !formData.phone_number.trim()) {
      toast.error('Name and phone number are required');
      return;
    }

    let updatedContacts = [...contacts];

    if (isAddingNew) {
      // Add new contact
      updatedContacts.push(formData);
    } else if (editingIndex !== null) {
      // Update existing contact
      updatedContacts[editingIndex] = formData;
    }

    // If setting as primary, unset others
    if (formData.is_primary) {
      updatedContacts = updatedContacts.map((contact, i) => ({
        ...contact,
        is_primary: isAddingNew ? i === updatedContacts.length - 1 : i === editingIndex
      }));
    }

    onChange(updatedContacts);
    cancelForm();
  };

  // Remove contact person
  const removeContact = (index: number) => {
    if (window.confirm('Are you sure you want to remove this contact person?')) {
      const updatedContacts = contacts.filter((_, i) => i !== index);
      // If we removed the primary contact, make the first remaining contact primary
      if (contacts[index].is_primary && updatedContacts.length > 0) {
        updatedContacts[0].is_primary = true;
      }
      onChange(updatedContacts);

      // Close form if editing the removed contact
      if (editingIndex === index) {
        cancelForm();
      }
    }
  };

  // Update form field
  const updateFormField = (field: keyof ContactPerson, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Set primary contact
  const setPrimaryContact = (index: number) => {
    const updatedContacts = contacts.map((contact, i) => ({
      ...contact,
      is_primary: i === index
    }));
    onChange(updatedContacts);
  };

  // Save contacts to database (if clientId exists)
  const saveContacts = async () => {
    if (!clientId) return;

    setIsLoading(true);
    try {
      const supabase = createClient();

      // Delete existing contacts
      await supabase
        .from('client_contacts')
        .delete()
        .eq('client_id', clientId);

      // Insert new contacts
      if (contacts.length > 0) {
        const contactsToInsert = contacts.map(contact => ({
          client_id: clientId,
          name: contact.name,
          phone_number: contact.phone_number,
          email: contact.email || null,
          position: contact.position,
          is_primary: contact.is_primary,
          is_active: contact.is_active,
          notes: contact.notes || null
        }));

        const { error } = await supabase
          .from('client_contacts')
          .insert(contactsToInsert);

        if (error) throw error;
      }

      toast.success('Contact persons saved successfully');
    } catch (error: any) {
      console.error('Error saving contacts:', error);
      toast.error('Failed to save contact persons');
    } finally {
      setIsLoading(false);
    }
  };

  if (!shouldShowContacts) {
    return null;
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Contact Persons</h3>
        {isEditing && !isAddingNew && editingIndex === null && (
          <button
            type="button"
            onClick={startAddingContact}
            className="flex items-center px-3 py-2 text-sm bg-teal-600 text-white rounded-md hover:bg-teal-700"
          >
            <Plus className="w-4 h-4 mr-1" />
            Add Contact
          </button>
        )}
      </div>

      {/* Contact Persons Table */}
      {contacts.length > 0 && !isAddingNew && editingIndex === null && (
        <div className="overflow-x-auto border border-gray-200 rounded-lg">
          <table className="w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Phone
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Position
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                {isEditing && (
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                )}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {contacts.map((contact, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-4 py-3">
                    <div className="flex items-center">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {contact.name}
                        </div>
                        {contact.is_primary && (
                          <div className="flex items-center text-xs text-yellow-600">
                            <Star className="w-3 h-3 mr-1" />
                            Primary Contact
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    <div className="flex items-center">
                      <Phone className="w-4 h-4 mr-2 text-gray-400" />
                      {contact.phone_number}
                    </div>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    {contact.email ? (
                      <div className="flex items-center">
                        <Mail className="w-4 h-4 mr-2 text-gray-400" />
                        {contact.email}
                      </div>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    {contact.position ? (
                      <div className="flex items-center">
                        <Briefcase className="w-4 h-4 mr-2 text-gray-400" />
                        {contact.position}
                      </div>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </td>
                  <td className="px-4 py-3">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      contact.is_active
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {contact.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  {isEditing && (
                    <td className="px-4 py-3 text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          type="button"
                          onClick={() => startEditingContact(index)}
                          className="text-teal-600 hover:text-teal-900"
                        >
                          <Edit2 className="w-4 h-4" />
                        </button>
                        <button
                          type="button"
                          onClick={() => removeContact(index)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Empty State */}
      {contacts.length === 0 && !isAddingNew && (
        <div className="text-center py-8 text-gray-500 border border-gray-200 rounded-lg">
          <User className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p className="text-lg font-medium">No contact persons added yet</p>
          <p className="text-sm">Click "Add Contact" to add the first contact person.</p>
        </div>
      )}

      {/* Contact Form (Add/Edit) */}
      {(isAddingNew || editingIndex !== null) && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="border border-gray-200 rounded-lg p-6 bg-gray-50"
        >
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-medium text-gray-900">
              {isAddingNew ? 'Add New Contact Person' : 'Edit Contact Person'}
            </h4>
            <button
              type="button"
              onClick={cancelForm}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => updateFormField('name', e.target.value)}
                placeholder="Contact person name"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone Number <span className="text-red-500">*</span>
              </label>
              <PhoneInput
                value={formData.phone_number}
                onChange={(value) => updateFormField('phone_number', value)}
                initialCountryCode="+254"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => updateFormField('email', e.target.value)}
                placeholder="<EMAIL>"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Position/Role
              </label>
              <select
                value={formData.position}
                onChange={(e) => updateFormField('position', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              >
                <option value="">Select position</option>
                <option value="Owner">Owner</option>
                <option value="Manager">Manager</option>
                <option value="Mechanic">Mechanic</option>
                <option value="Supervisor">Supervisor</option>
                <option value="Contact Person">Contact Person</option>
                <option value="Other">Other</option>
              </select>
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => updateFormField('notes', e.target.value)}
              placeholder="Additional notes about this contact person"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
            />
          </div>

          <div className="mt-4 flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_primary}
                onChange={(e) => updateFormField('is_primary', e.target.checked)}
                className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
              />
              <span className="ml-2 text-sm text-gray-700">Primary contact person</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_active}
                onChange={(e) => updateFormField('is_active', e.target.checked)}
                className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
              />
              <span className="ml-2 text-sm text-gray-700">Active</span>
            </label>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              onClick={cancelForm}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={saveForm}
              className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
            >
              {isAddingNew ? 'Add Contact' : 'Save Changes'}
            </button>
          </div>
        </motion.div>
      )}

      {/* Save Button (if standalone) */}
      {clientId && isEditing && contacts.length > 0 && showSaveButton && !isAddingNew && editingIndex === null && (
        <div className="flex justify-end pt-4">
          <button
            type="button"
            onClick={saveContacts}
            disabled={isLoading}
            className="flex items-center px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 disabled:opacity-50"
          >
            {isLoading ? (
              <>
                <LoadingSpinner size={16} />
                <span className="ml-2">Saving...</span>
              </>
            ) : (
              'Save Contact Persons'
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default ContactPersonsManager;
