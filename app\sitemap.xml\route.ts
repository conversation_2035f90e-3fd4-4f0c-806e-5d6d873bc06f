import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://autoflow.co.ke';

    // Static pages
    const staticPages = [
      {
        url: `${baseUrl}/`,
        lastModified: new Date().toISOString(),
        changeFrequency: 'daily',
        priority: '1.0'
      },
      {
        url: `${baseUrl}/parts`,
        lastModified: new Date().toISOString(),
        changeFrequency: 'daily',
        priority: '0.9'
      },
      {
        url: `${baseUrl}/categories`,
        lastModified: new Date().toISOString(),
        changeFrequency: 'weekly',
        priority: '0.8'
      },
      {
        url: `${baseUrl}/brands`,
        lastModified: new Date().toISOString(),
        changeFrequency: 'weekly',
        priority: '0.8'
      },
      {
        url: `${baseUrl}/about`,
        lastModified: new Date().toISOString(),
        changeFrequency: 'monthly',
        priority: '0.6'
      },
      {
        url: `${baseUrl}/contact`,
        lastModified: new Date().toISOString(),
        changeFrequency: 'monthly',
        priority: '0.6'
      }
    ];

    // Get dynamic pages from database
    const dynamicPages: any[] = [];

    // Fetch categories
    try {
      const { data: categories } = await supabase
        .from('categories')
        .select('id, name, updated_at')
        .eq('is_active', true)
        .limit(1000);

      if (categories) {
        categories.forEach(category => {
          const slug = category.name.toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
          
          dynamicPages.push({
            url: `${baseUrl}/categories/${slug}`,
            lastModified: category.updated_at || new Date().toISOString(),
            changeFrequency: 'weekly',
            priority: '0.7'
          });
        });
      }
    } catch (error) {
      console.error('Error fetching categories for sitemap:', error);
    }

    // Fetch recent parts (limit to avoid huge sitemap)
    try {
      const { data: parts } = await supabase
        .from('parts')
        .select('id, title, updated_at')
        .eq('is_active', true)
        .order('updated_at', { ascending: false })
        .limit(5000); // Limit to most recent 5000 parts

      if (parts) {
        parts.forEach(part => {
          const slug = part.title.toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
          
          dynamicPages.push({
            url: `${baseUrl}/parts/${slug}-${part.id}`,
            lastModified: part.updated_at || new Date().toISOString(),
            changeFrequency: 'weekly',
            priority: '0.6'
          });
        });
      }
    } catch (error) {
      console.error('Error fetching parts for sitemap:', error);
    }

    // Combine all pages
    const allPages = [...staticPages, ...dynamicPages];

    // Generate XML sitemap
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allPages.map(page => `  <url>
    <loc>${page.url}</loc>
    <lastmod>${page.lastModified}</lastmod>
    <changefreq>${page.changeFrequency}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('\n')}
</urlset>`;

    return new NextResponse(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
      },
    });

  } catch (error) {
    console.error('Error generating sitemap:', error);
    
    // Return basic sitemap on error
    const basicSitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${process.env.NEXT_PUBLIC_BASE_URL || 'https://autoflow.co.ke'}/</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>`;

    return new NextResponse(basicSitemap, {
      headers: {
        'Content-Type': 'application/xml',
      },
    });
  }
}
