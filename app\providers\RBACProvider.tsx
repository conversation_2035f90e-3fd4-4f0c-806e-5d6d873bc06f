'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode, useEffect, useMemo } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { useAuth } from '@/app/hooks/useAuth';

// Cache expiration time (30 minutes)
const CACHE_EXPIRATION = 30 * 60 * 1000;

interface CachedPermission {
  value: boolean;
  timestamp: number;
}

interface RBACContextType {
  checkPermission: (permissionName: string) => Promise<boolean>;
  getPermissionSync: (permissionName: string) => boolean | null;
  clearCache: () => void;
  permissionCache: Record<string, CachedPermission>;
  isLoading: boolean;
}

const RBACContext = createContext<RBACContextType | undefined>(undefined);

// Try to load cached permissions from localStorage
const loadCachedPermissions = (): Record<string, CachedPermission> => {
  if (typeof window === 'undefined') return {};

  try {
    const cachedData = localStorage.getItem('rbac_permission_cache');
    if (!cachedData) return {};

    let parsed;
    try {
      parsed = JSON.parse(cachedData);
    } catch (parseError) {
      console.error('Error parsing RBAC cache:', parseError);
      // Clear the corrupt cache
      localStorage.removeItem('rbac_permission_cache');
      return {};
    }

    // Validate the structure and clean up expired entries
    const now = Date.now();
    const validEntries: [string, CachedPermission][] = [];
    
    try {
      Object.entries(parsed).forEach(([key, value]: [string, any]) => {
        if (value && 
            typeof value === 'object' && 
            typeof value.value === 'boolean' && 
            typeof value.timestamp === 'number' && 
            (now - value.timestamp) < CACHE_EXPIRATION) {
          validEntries.push([key, value as CachedPermission]);
        }
      });
    } catch (validationError) {
      console.error('Error validating RBAC cache entries:', validationError);
      return {};
    }

    return Object.fromEntries(validEntries);
  } catch (error) {
    console.error('Error in loadCachedPermissions:', error);
    return {};
  }
};

export const RBACProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [permissionCache, setPermissionCache] = useState<Record<string, CachedPermission>>(() => loadCachedPermissions());
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { user } = useAuth();
  const supabase = createClient();

  // Clear the permission cache when the user changes
  useEffect(() => {
    setPermissionCache({});
    if (typeof window !== 'undefined') {
      localStorage.removeItem('rbac_permission_cache');
    }
  }, [user?.id]);

  // Save cache to localStorage when it changes
  useEffect(() => {
    if (typeof window !== 'undefined' && Object.keys(permissionCache).length > 0) {
      try {
        const serializedCache = JSON.stringify(permissionCache);
        localStorage.setItem('rbac_permission_cache', serializedCache);
      } catch (error) {
        console.error('Error serializing RBAC cache:', error);
        // If serialization fails, clear the cache to prevent future errors
        setPermissionCache({});
        localStorage.removeItem('rbac_permission_cache');
      }
    }
  }, [permissionCache]);



  // Get a permission synchronously from cache (returns null if not cached or expired)
  const getPermissionSync = useCallback((permissionName: string): boolean | null => {
    if (!user) return false;

    const cachedPermission = permissionCache[permissionName];
    if (!cachedPermission) return null;

    const now = Date.now();
    if ((now - cachedPermission.timestamp) >= CACHE_EXPIRATION) {
      // Cache expired
      return null;
    }

    return cachedPermission.value;
  }, [permissionCache, user]);

  const checkPermission = useCallback(
    async (permissionName: string): Promise<boolean> => {
      if (!user) return false;

      // Check cache first
      const cachedResult = getPermissionSync(permissionName);
      if (cachedResult !== null) {

        return cachedResult;
      }

      try {
        setIsLoading(true);


        // Make a fresh request to the server
        const response = await fetch(`/api/rbac/check-permission?permission=${encodeURIComponent(permissionName)}`);

        if (!response.ok) {

          return false;
        }

        const data = await response.json();
        const hasPermission = !!data.hasPermission;

        // Update the cache with the fresh result and timestamp
        setPermissionCache((prev) => {
          try {
            const newCache = {
              ...prev,
              [permissionName]: {
                value: hasPermission,
                timestamp: Date.now()
              },
            };
            // Verify the object can be safely serialized
            JSON.stringify(newCache);
            return newCache;
          } catch (error) {
            console.error('Error updating permission cache:', error);
            // Return previous state if there's an error
            return prev;
          }
        });

        return hasPermission;
      } catch (error) {

        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [user, getPermissionSync]
  );

  const clearCache = useCallback(() => {
    setPermissionCache({});
    if (typeof window !== 'undefined') {
      localStorage.removeItem('rbac_permission_cache');
    }
  }, []);

  const contextValue = useMemo(() => ({
    checkPermission,
    getPermissionSync,
    clearCache,
    permissionCache,
    isLoading
  }), [checkPermission, getPermissionSync, clearCache, permissionCache, isLoading]);

  return (
    <RBACContext.Provider value={contextValue}>
      {children}
    </RBACContext.Provider>
  );
};

export const useRBAC = (): RBACContextType => {
  const context = useContext(RBACContext);
  if (context === undefined) {
    throw new Error('useRBAC must be used within a RBACProvider');
  }
  return context;
};
