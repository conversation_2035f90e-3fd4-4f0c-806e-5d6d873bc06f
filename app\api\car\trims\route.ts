import { NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const variationId = searchParams.get('variationId');
    
    if (!variationId) {
      return NextResponse.json({ error: 'variationId is required' }, { status: 400 });
    }
    
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from('variation_trim')
      .select('*')
      .eq('variation_id', variationId)
      .order('trim');
      
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    const trims = data.map((t: any) => ({
      id: t.id,
      name: t.trim,
      variation_id: t.variation_id,
      trim: t.trim
    }));
    
    return NextResponse.json(trims);
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}