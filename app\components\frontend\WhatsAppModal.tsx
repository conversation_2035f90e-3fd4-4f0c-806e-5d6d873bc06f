'use client';

import React, { useState } from 'react';
import { X, Send } from 'lucide-react';
import Image from 'next/image';

interface WhatsAppModalProps {
  onClose: () => void;
  partName: string;
  partId: string;
}

const WhatsAppModal: React.FC<WhatsAppModalProps> = ({
  onClose,
  partName,
  partId
}) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validate phone number
    if (!phoneNumber.trim()) {
      setError('Please enter your phone number');
      return;
    }

    // Format phone number (remove spaces, ensure it starts with +)
    let formattedNumber = phoneNumber.trim().replace(/\s+/g, '');
    if (!formattedNumber.startsWith('+')) {
      // If number starts with 0, replace with +254
      if (formattedNumber.startsWith('0')) {
        formattedNumber = '+254' + formattedNumber.substring(1);
      }
      // If number starts with 7 or 1, add +254
      else if (formattedNumber.startsWith('7') || formattedNumber.startsWith('1')) {
        formattedNumber = '+254' + formattedNumber;
      }
    }

    // Create the WhatsApp message
    const message = `Hello, I'm interested in this part: ${partName}. My phone number is ${formattedNumber}. Here's the link: ${window.location.origin}/shop/${partId}`;

    // Create the WhatsApp URL
    const whatsappNumber = '+254724288400';
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;

    // Open WhatsApp in a new tab
    window.open(whatsappUrl, '_blank');

    // Close the modal
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div className="flex justify-between items-center p-4 border-b">
          <h3 className="text-lg font-semibold">Contact via WhatsApp</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-4">
          <div className="mb-4">
            <div>
              <h4 className="font-medium">{partName}</h4>
              <p className="text-sm text-gray-500">Send inquiry via WhatsApp</p>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 text-red-500 p-2 rounded mb-4 text-sm">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                Your Phone Number
              </label>
              <input
                type="tel"
                id="phoneNumber"
                placeholder="e.g., +254712345678"
                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-teal-500"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
              />
              <p className="text-xs text-gray-500 mt-1">
                Enter your phone number with country code (e.g., +254) or start with 0/7
              </p>
            </div>

            <div className="flex justify-end">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 mr-2 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center"
                disabled={isLoading}
              >
                {isLoading ? 'Sending...' : (
                  <>
                    <Send size={16} className="mr-2" />
                    Send WhatsApp
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default WhatsAppModal;
