'use client';

import { useEffect, useState } from 'react';
import { registerServiceWorker } from '@/app/utils/serviceWorker';
import { toast } from 'react-hot-toast';

export default function ServiceWorkerProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const initServiceWorker = async () => {
      try {
        if ('serviceWorker' in navigator) {
          await registerServiceWorker();
          toast.success('Offline features enabled');
        } else {
          console.warn('Service workers are not supported');
        }
      } catch (err) {
        console.error('Service worker registration failed:', err);
        setError(err instanceof Error ? err : new Error('Failed to register service worker'));
        toast.error('Failed to enable offline features');
      } finally {
        setIsLoading(false);
      }
    };

    initServiceWorker();
  }, []);

  if (error) {
    throw error; // This will be caught by the error boundary
  }

  if (isLoading) {
    return (
      <div className="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg p-4">
        <div className="flex items-center space-x-3">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-teal-500"></div>
          <span className="text-sm text-gray-600">Enabling offline features...</span>
        </div>
      </div>
    );
  }

  return <>{children}</>;
} 