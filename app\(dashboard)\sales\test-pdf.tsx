'use client';

import React from 'react';
import { generatePDFReceipt } from './utils/pdfGenerator';
import { FileText } from 'lucide-react';

// Sample sale data for testing
const sampleSaleData = {
  id: 'test-sale-123',
  sale_timestamp: new Date().toISOString(),
  sale_type: 'cash' as const,
  payment_method: 'cash',
  total_amount: 15000,
  discount_total: 500,
  total_before_vat: 14500,
  vat_amount: 2320,
  vat_rate: 16,
  total_with_vat: 16820,
  one_off_client_name: '<PERSON>',
  one_off_client_phone: '+254712345678',
  profiles: {
    full_name: 'Sales Staff'
  },
  sale_items: [
    {
      id: 'item-1',
      quantity: 2,
      price_at_sale: 5000,
      discount_amount: 200,
      discount_reason: 'Bulk discount',
      parts: {
        title: 'Volkswagen(VW) Audi Front Brake Pads VW123456 1.8L Petrol TSI'
      },
      part_id: 123
    },
    {
      id: 'item-2',
      quantity: 1,
      price_at_sale: 5500,
      discount_amount: 300,
      discount_reason: 'Customer loyalty',
      parts: {
        title: 'Audi A4 Headlight Assembly LED Right Side AU789012 2.0L Diesel TDI'
      },
      part_id: 456
    }
  ],
  mpesa_payments: []
};

export default function TestPDFPage() {
  const handleGenerateTestPDF = async () => {
    try {
      await generatePDFReceipt(sampleSaleData);
      alert('Test PDF generated successfully! Check your downloads folder.');
    } catch (error) {
      console.error('Error generating test PDF:', error);
      alert('Failed to generate test PDF. Check console for details.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <div className="text-center">
          <FileText className="mx-auto h-12 w-12 text-orange-600" />
          <h2 className="mt-2 text-lg font-medium text-gray-900">
            PDF Receipt Generator Test
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Click the button below to generate a sample PDF receipt
          </p>
        </div>
        
        <div className="mt-6">
          <button
            onClick={handleGenerateTestPDF}
            className="w-full flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            <FileText className="h-4 w-4 mr-2" />
            Generate Test PDF Receipt
          </button>
        </div>

        <div className="mt-6 text-xs text-gray-500">
          <h3 className="font-medium text-gray-700 mb-2">Sample Data:</h3>
          <ul className="space-y-1">
            <li>• Client: John Doe (+254712345678)</li>
            <li>• Items: 2 auto parts</li>
            <li>• Subtotal: Kshs 15,000</li>
            <li>• Discount: Kshs 500</li>
            <li>• VAT (16%): Kshs 2,320</li>
            <li>• Total: Kshs 16,820</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
