'use client';

import React from 'react';
import { Inter } from 'next/font/google';

// Initialize the Inter font
const inter = Inter({ subsets: ['latin'] });

interface LandingLayoutProps {
  children: React.ReactNode;
}

const LandingLayout: React.FC<LandingLayoutProps> = ({ children }) => {
  return (
    <div className={`landing-layout ${inter.className} min-h-screen bg-gray-900`}>
      {children}
    </div>
  );
};

export default LandingLayout;
