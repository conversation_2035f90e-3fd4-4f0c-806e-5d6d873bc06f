{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "declaration": false, "outDir": "./dist", "rootDir": "./", "baseUrl": "./", "paths": {"@/*": ["./*"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "dist", "logs", "temp"], "ts-node": {"esm": false, "experimentalSpecifierResolution": "node"}}