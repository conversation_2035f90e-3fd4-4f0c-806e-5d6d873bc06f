"use client"; // Required for client-side interactivity (hooks, animations)

import React from 'react';
import { motion } from 'framer-motion';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, RadialBarChart, RadialBar, PolarAngleAxis } from 'recharts';
import { ArrowUpRight, ArrowDownRight, TrendingUp, TrendingDown, Users, DollarSign, ShoppingCart, Package, MoreVertical, ChevronRight } from 'lucide-react';

// --- Mock Data ---

const totalSalesData = {
  value: 4910.90,
  change: 5,
  changeType: 'increase',
  period: 'last month',
};

const totalOrdersData = {
  value: 8321,
  change: 6,
  changeType: 'increase',
  period: 'last month',
};

const customerData = {
  value: 64253,
  change: 1,
  changeType: 'decrease',
  period: 'last month',
};

const newCustomersData = {
  value: 3421,
  description: 'new customer be a new member',
};

const salesReportData = [
  { name: 'Q1', Malang: 4000, Surabaya: 2400, Gresik: 1800 },
  { name: 'Q2', Malang: 3000, Surabaya: 1398, Gresik: 2210 },
  { name: 'Q3', Malang: 2000, Surabaya: 9800, Gresik: 2290 },
  { name: 'Q4', Malang: 2780, Surabaya: 3908, Gresik: 2000 }, // Added Q4 for completeness
];

const bestSellersData = [
  { id: 1, name: 'Jasmine Rice Rojo Lele', price: 24, statsChange: 15, statsType: 'increase', amount: 5524, total: 3423.00, image: 'https://placehold.co/40x40/e2e8f0/64748b?text=P1' },
  { id: 2, name: 'Indomie Goreng', price: 24, statsChange: 5, statsType: 'increase', amount: 4352, total: 1653.00, image: 'https://placehold.co/40x40/e2e8f0/64748b?text=P2' },
  { id: 3, name: 'Product C', price: 50, statsChange: 8, statsType: 'decrease', amount: 2100, total: 950.50, image: 'https://placehold.co/40x40/e2e8f0/64748b?text=P3' }, // Added more items
];

const revenueData = {
  total: 102000,
  percentage: 28,
};

// --- Reusable Card Component ---

interface CardProps {
  children: React.ReactNode;
  className?: string;
  initial?: object;
  animate?: object;
  transition?: object;
}

const Card: React.FC<CardProps> = ({ children, className = '', initial, animate, transition }) => (
  <motion.div
    className={`bg-white dark:bg-gray-800 p-5 md:p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}
    initial={initial}
    animate={animate}
    transition={transition}
  >
    {children}
  </motion.div>
);

// --- Stat Card Component ---

interface StatCardProps {
  title: string;
  value: number | string;
  change: number;
  changeType: 'increase' | 'decrease';
  period: string;
  icon: React.ElementType;
  formatAsCurrency?: boolean;
  delay?: number;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, change, changeType, period, icon: Icon, formatAsCurrency = false, delay = 0 }) => {
  const isIncrease = changeType === 'increase';
  const changeColor = isIncrease ? 'text-teal-500' : 'text-orange-500';
  const ChangeIcon = isIncrease ? TrendingUp : TrendingDown;

  const formattedValue = formatAsCurrency
    ? `$${Number(value).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    : Number(value).toLocaleString('en-US');

  return (
    <Card
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay }}
    >
      <div className="flex justify-between items-start mb-3">
        <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">{title}</h3>
        <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
          <MoreVertical size={18} />
        </button>
      </div>
      <div className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-1">{formattedValue}</div>
      <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mb-4">
        <ChangeIcon size={14} className={`mr-1 ${changeColor}`} />
        <span className={`${changeColor} font-medium`}>{change}%</span>
        <span className="ml-1">vs {period}</span>
      </div>
      <a href="#" className="flex items-center text-xs font-medium text-teal-600 hover:text-teal-700 dark:text-teal-400 dark:hover:text-teal-300">
        View More
        <ChevronRight size={14} className="ml-1" />
      </a>
    </Card>
  );
};


// --- Sales Report Bar Chart Card ---

const SalesReportCard: React.FC<{ delay?: number }> = ({ delay = 0 }) => (
  <Card
    className="col-span-1 md:col-span-2"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5, delay }}
  >
    <div className="flex justify-between items-start mb-4">
      <div>
        <h3 className="text-base font-semibold text-gray-800 dark:text-gray-100">Sales Report Area</h3>
        <p className="text-3xl font-bold text-gray-800 dark:text-gray-100 mt-1">9,321</p> {/* Example value */}
         <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1">
            <TrendingUp size={14} className="mr-1 text-teal-500" />
            <span className="text-teal-500 font-medium">12%</span>
            <span className="ml-1">vs last years</span>
         </div>
      </div>
       <div className="flex items-center space-x-3 text-xs">
            <div className="flex items-center">
                <span className="w-3 h-3 rounded-full bg-teal-500 mr-1.5"></span>
                <span>Malang</span>
            </div>
            <div className="flex items-center">
                <span className="w-3 h-3 rounded-full bg-orange-400 mr-1.5"></span>
                <span>Surabaya</span>
            </div>
             <div className="flex items-center">
                <span className="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-600 mr-1.5"></span>
                <span>Gresik</span>
            </div>
        </div>
    </div>
    <div className="h-60 md:h-72 w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={salesReportData} margin={{ top: 5, right: 0, left: -20, bottom: 5 }}> {/* Adjusted margins */}
          <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="rgba(128, 128, 128, 0.2)" />
          <XAxis dataKey="name" axisLine={false} tickLine={false} fontSize={12} dy={10} />
          <YAxis axisLine={false} tickLine={false} fontSize={12} tickFormatter={(value) => `${value}%`} />
          <Tooltip
            cursor={{ fill: 'rgba(229, 231, 235, 0.5)' }} // Light grey hover
            contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', border: '1px solid #e5e7eb', borderRadius: '8px', padding: '8px 12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}
            labelStyle={{ fontWeight: 'bold', marginBottom: '4px', color: '#374151' }}
            itemStyle={{ fontSize: '12px', color: '#4b5563' }}
          />
          {/* <Legend wrapperStyle={{ fontSize: '12px', paddingTop: '10px' }} /> */}
          <Bar dataKey="Malang" fill="#14b8a6" radius={[4, 4, 0, 0]} barSize={15} /> {/* Teal */}
          <Bar dataKey="Surabaya" fill="#fb923c" radius={[4, 4, 0, 0]} barSize={15} /> {/* Orange */}
           <Bar dataKey="Gresik" fill="#d1d5db" radius={[4, 4, 0, 0]} barSize={15} /> {/* Grey */}
        </BarChart>
      </ResponsiveContainer>
    </div>
  </Card>
);

// --- Customer Card ---
const CustomerCard: React.FC<{ delay?: number }> = ({ delay = 0 }) => (
    <Card
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay }}
    >
      <div className="flex justify-between items-start mb-3">
        <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Customer</h3>
        <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
          <MoreVertical size={18} />
        </button>
      </div>
       <div className="flex items-end justify-between gap-4 mb-4">
            <div>
                 <div className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-1">{customerData.value.toLocaleString('en-US')}</div>
                <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                    <TrendingDown size={14} className="mr-1 text-orange-500" />
                    <span className="text-orange-500 font-medium">{customerData.change}%</span>
                    <span className="ml-1">vs {customerData.period}</span>
                </div>
            </div>
             <div className="flex items-center bg-teal-50 dark:bg-teal-900/50 p-2 rounded-lg">
                <div className="bg-teal-100 dark:bg-teal-800 p-1.5 rounded-md mr-2">
                     <Users size={18} className="text-teal-600 dark:text-teal-300" />
                </div>
                <div>
                    <div className="text-lg font-semibold text-teal-800 dark:text-teal-200">{newCustomersData.value.toLocaleString('en-US')}</div>
                    <div className="text-xs text-teal-600 dark:text-teal-400">{newCustomersData.description}</div>
                </div>
            </div>
       </div>

      <a href="#" className="flex items-center text-xs font-medium text-teal-600 hover:text-teal-700 dark:text-teal-400 dark:hover:text-teal-300">
        View More
        <ChevronRight size={14} className="ml-1" />
      </a>
    </Card>
);


// --- Best Sellers Card ---

const BestSellersCard: React.FC<{ delay?: number }> = ({ delay = 0 }) => (
  <Card
    className="col-span-1 md:col-span-2"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5, delay }}
  >
    <div className="flex justify-between items-center mb-4">
      <h3 className="text-base font-semibold text-gray-800 dark:text-gray-100">Best Sellers</h3>
      <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
        <MoreVertical size={18} />
      </button>
    </div>
    <div className="overflow-x-auto">
      <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
          <tr>
            <th scope="col" className="px-4 py-3">Products</th>
            <th scope="col" className="px-4 py-3 text-center">Stats</th>
            <th scope="col" className="px-4 py-3 text-right">Amount</th>
            <th scope="col" className="px-4 py-3 text-right">Total</th>
          </tr>
        </thead>
        <tbody>
          {bestSellersData.map((item) => {
            const isIncrease = item.statsType === 'increase';
            const StatIcon = isIncrease ? ArrowUpRight : ArrowDownRight;
            const statColor = isIncrease ? 'text-teal-500 bg-teal-100 dark:bg-teal-900 dark:text-teal-300' : 'text-orange-500 bg-orange-100 dark:bg-orange-900 dark:text-orange-300';

            return (
              <motion.tr
                key={item.id}
                className="bg-white dark:bg-gray-800 border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, delay: delay + item.id * 0.1 }} // Stagger row animation
              >
                <td className="px-4 py-3 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                  <div className="flex items-center">
                    <img src={item.image} alt={item.name} className="w-8 h-8 rounded-md mr-3 object-cover" onError={(e) => (e.currentTarget.src = 'https://placehold.co/40x40/e2e8f0/64748b?text=Err')} />
                    <div>
                      <div>{item.name}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">${item.price}</div>
                    </div>
                  </div>
                </td>
                <td className="px-4 py-3 text-center">
                  <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${statColor}`}>
                    <StatIcon size={12} className="mr-0.5" /> {item.statsChange}%
                  </span>
                </td>
                <td className="px-4 py-3 text-right">{item.amount.toLocaleString('en-US')}</td>
                <td className="px-4 py-3 text-right font-semibold text-gray-800 dark:text-gray-200">
                  ${item.total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </td>
              </motion.tr>
            );
          })}
        </tbody>
      </table>
    </div>
  </Card>
);

// --- Revenue Card ---

const RevenueCard: React.FC<{ delay?: number }> = ({ delay = 0 }) => {
  const data = [{ name: 'Revenue', value: revenueData.percentage, fill: '#14b8a6' }]; // Teal color for the bar
  const endAngle = 90 + (revenueData.percentage / 100) * 360; // Calculate end angle based on percentage

  return (
    <Card
      className="relative flex flex-col items-center justify-center"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
    >
       <div className="absolute top-5 right-5">
         <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <MoreVertical size={18} />
         </button>
       </div>
       <h3 className="absolute top-5 left-5 text-base font-semibold text-gray-800 dark:text-gray-100">Revenue</h3>

      <div className="w-48 h-48 md:w-56 md:h-56"> {/* Adjust size as needed */}
        <ResponsiveContainer width="100%" height="100%">
          <RadialBarChart
            cx="50%"
            cy="50%"
            innerRadius="70%" // Adjust inner radius for thickness
            outerRadius="90%" // Adjust outer radius for thickness
            barSize={20} // Adjust bar size
            data={data}
            startAngle={90} // Start from the top
            endAngle={-270} // Go full circle counter-clockwise
          >
            <PolarAngleAxis
              type="number"
              domain={[0, 100]}
              angleAxisId={0}
              tick={false}
            />
             {/* Background Circle */}
            <RadialBar
                background={{ fill: '#e5e7eb' }} // Light grey background
                dataKey="value"
                angleAxisId={0}
                data={[{ value: 100 }]} // Full circle data for background
                cornerRadius={10} // Rounded corners for background
                />
            {/* Foreground Circle (Animated) */}
            <RadialBar
              minAngle={15}
              // label={{ position: 'insideStart', fill: '#fff' }}
              background={false} // No background for the main bar itself
              clockWise
              dataKey="value"
              cornerRadius={10} // Rounded corners for the bar
              // fill="#14b8a6" // Defined in data array
            />
            {/* <Tooltip /> */}
          </RadialBarChart>
        </ResponsiveContainer>
      </div>
      <div className="absolute flex flex-col items-center justify-center inset-0">
         <motion.div
            initial={{ scale: 0.5, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5, delay: delay + 0.2 }}
         >
            <span className="text-3xl md:text-4xl font-bold text-teal-600 dark:text-teal-400">
                {revenueData.percentage}%
            </span>
         </motion.div>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Total</p>
        <p className="text-lg md:text-xl font-semibold text-gray-800 dark:text-gray-100">
          ${revenueData.total.toLocaleString('en-US')}
        </p>
      </div>
    </Card>
  );
};


// --- Main Dashboard Component ---

export default function SalesDashboard() {
  return (
    <div className="p-4 md:p-6 lg:p-8 bg-gray-100 dark:bg-gray-900 min-h-screen font-sans">
      <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6">Sales Overview</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        {/* Row 1 */}
        <StatCard
          title="Total Sales"
          value={totalSalesData.value}
          change={totalSalesData.change}
          changeType={totalSalesData.changeType}
          period={totalSalesData.period}
          icon={DollarSign}
          formatAsCurrency={true}
          delay={0.1}
        />
        <StatCard
          title="Total Orders"
          value={totalOrdersData.value}
          change={totalOrdersData.change}
          changeType={totalOrdersData.changeType}
          period={totalOrdersData.period}
          icon={ShoppingCart}
          delay={0.2}
        />
         <SalesReportCard delay={0.3} />

        {/* Row 2 */}
         <CustomerCard delay={0.4} />
         <BestSellersCard delay={0.5} />
         <RevenueCard delay={0.6} />

      </div>
    </div>
  );
}
