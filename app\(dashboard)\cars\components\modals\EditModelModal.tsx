'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Upload } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { createClient } from '@/app/libs/supabase/client';
import { Model, ModelFormData, Brand } from '../../types';

interface EditModelModalProps {
  isOpen: boolean;
  onClose: () => void;
  model: Model;
  brands: Brand[];
  onSuccess: () => void;
}

const EditModelModal: React.FC<EditModelModalProps> = ({
  isOpen,
  onClose,
  model,
  brands,
  onSuccess
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(model.model_image || null);
  
  const { register, handleSubmit, reset, formState: { errors } } = useForm<ModelFormData>({
    defaultValues: {
      brand_id: model.brand_id,
      model_name: model.model_name
    }
  });
  
  const supabase = createClient();

  // Reset form when model changes
  useEffect(() => {
    if (isOpen) {
      reset({
        brand_id: model.brand_id,
        model_name: model.model_name
      });
      setImagePreview(model.model_image || null);
    }
  }, [model, isOpen, reset]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadImage = async (file: File): Promise<string> => {
    const fileName = `${Date.now()}-${file.name}`;
    const { data, error } = await supabase.storage
      .from('car-models')
      .upload(fileName, file);
      
    if (error) throw error;
    
    const { data: urlData } = supabase.storage
      .from('car-models')
      .getPublicUrl(fileName);
      
    return urlData.publicUrl;
  };

  const onSubmit = async (data: ModelFormData) => {
    setIsSubmitting(true);
    setError(null);
    
    try {
      // Upload image if provided
      let imageUrl = model.model_image;
      if (imageFile) {
        imageUrl = await uploadImage(imageFile);
      }
      
      // Update the model
      const { error: updateError } = await supabase
        .from('car_models')
        .update({
          brand_id: data.brand_id,
          model_name: data.model_name,
          model_image: imageUrl
        })
        .eq('id', model.id);
        
      if (updateError) throw updateError;
      
      onSuccess();
      onClose();
    } catch (err: any) {
      console.error('Error updating model:', err);
      setError(err.message || 'Failed to update model. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.2 }}
          className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex justify-between items-center p-6 border-b border-gray-200">
            <h3 className="text-xl font-semibold text-gray-800">Edit Model</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              disabled={isSubmitting}
            >
              <X size={24} />
            </button>
          </div>
          
          <form onSubmit={handleSubmit(onSubmit)} className="p-6">
            <div className="mb-6">
              <label htmlFor="brand_id" className="block text-sm font-medium text-gray-700 mb-2">
                Brand
              </label>
              <select
                id="brand_id"
                className={`w-full px-4 py-2 border ${errors.brand_id ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500`}
                {...register('brand_id', { 
                  required: 'Brand is required',
                  valueAsNumber: true
                })}
              >
                <option value="">Select a brand</option>
                {brands.map((brand) => (
                  <option key={brand.brand_id} value={brand.brand_id}>
                    {brand.brand_name}
                  </option>
                ))}
              </select>
              {errors.brand_id && (
                <p className="mt-1 text-sm text-red-600">{errors.brand_id.message}</p>
              )}
            </div>
            
            <div className="mb-6">
              <label htmlFor="model_name" className="block text-sm font-medium text-gray-700 mb-2">
                Model Name
              </label>
              <input
                id="model_name"
                type="text"
                className={`w-full px-4 py-2 border ${errors.model_name ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500`}
                placeholder="Enter model name"
                {...register('model_name', { required: 'Model name is required' })}
              />
              {errors.model_name && (
                <p className="mt-1 text-sm text-red-600">{errors.model_name.message}</p>
              )}
            </div>
            
            <div className="mb-6">
              <label htmlFor="model_image" className="block text-sm font-medium text-gray-700 mb-2">
                Model Image (Optional)
              </label>
              <div className="flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-6 cursor-pointer hover:bg-gray-50 transition-colors">
                <input
                  id="model_image"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageChange}
                />
                <label htmlFor="model_image" className="cursor-pointer text-center">
                  {imagePreview ? (
                    <div className="flex flex-col items-center">
                      <img
                        src={imagePreview}
                        alt="Preview"
                        className="w-32 h-32 object-cover rounded-md mb-2"
                      />
                      <span className="text-sm text-teal-600">Change image</span>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <Upload className="text-gray-400 mb-2" size={32} />
                      <span className="text-gray-500">Click to upload an image</span>
                    </div>
                  )}
                </label>
              </div>
            </div>
            
            {error && (
              <div className="bg-red-50 text-red-600 p-4 rounded-md mb-6">
                {error}
              </div>
            )}
            
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 transition-colors"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default EditModelModal;
