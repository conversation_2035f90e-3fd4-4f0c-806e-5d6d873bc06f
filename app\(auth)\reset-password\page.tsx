// app/(auth)/reset-password/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/app/libs/supabase/client';
import Password from '@/app/components/ui/inputs/Password';
import Button from '@/app/components/ui/inputs/Button';
import Notification from '@/app/components/ui/Notification';
import AuthLayout from '@/app/layouts/AuthLayout';
import { Check, X } from 'lucide-react';

export default function ResetPasswordPage() {
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [sessionChecked, setSessionChecked] = useState(false);
  const router = useRouter();
  const supabase = createClient();

  // Password strength indicators
  const [passwordStrength, setPasswordStrength] = useState({
    hasMinLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false,
    hasSpecialChar: false,
  });

  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'PASSWORD_RECOVERY') {
          setSessionChecked(true);
        }
      }
    );

    const checkRecoveryParams = async () => {
      // Check URL hash parameters (Supabase often puts tokens in hash)
      const hash = window.location.hash.substring(1);
      const params = new URLSearchParams(hash);
      const type = params.get('type');

      if (type === 'recovery') {
        try {
          const access_token = params.get('access_token');
          const refresh_token = params.get('refresh_token');

          if (!access_token) {
            throw new Error('Invalid reset link');
          }

          const { error } = await supabase.auth.setSession({
            access_token,
            refresh_token: refresh_token || ''
          });

          if (error) throw error;
          setSessionChecked(true);
        } catch (error: any) {
          console.error('Error setting session:', error);
          setErrorMessage(error.message || 'Invalid or expired reset link');
          setSessionChecked(true);
        }
      } else {
        // Check if we have a user already
        const { data: { user } } = await supabase.auth.getUser();

        if (user) {
          setSessionChecked(true);
        } else {
          setErrorMessage('Invalid or missing reset link parameters');
          setSessionChecked(true);
        }
      }
    };

    if (!sessionChecked) {
      checkRecoveryParams();
    }

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, [supabase.auth, sessionChecked]);

  // Update password strength when password changes
  useEffect(() => {
    setPasswordStrength({
      hasMinLength: newPassword.length >= 8,
      hasUppercase: /[A-Z]/.test(newPassword),
      hasLowercase: /[a-z]/.test(newPassword),
      hasNumber: /[0-9]/.test(newPassword),
      hasSpecialChar: /[^A-Za-z0-9]/.test(newPassword),
    });
  }, [newPassword]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (newPassword !== confirmPassword) {
      setErrorMessage('Passwords do not match');
      return;
    }

    // Check password strength
    const isStrongPassword =
      passwordStrength.hasMinLength &&
      passwordStrength.hasUppercase &&
      passwordStrength.hasLowercase &&
      passwordStrength.hasNumber &&
      passwordStrength.hasSpecialChar;

    if (!isStrongPassword) {
      setErrorMessage('Please create a stronger password that meets all requirements');
      return;
    }

    setLoading(true);
    setErrorMessage('');

    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) throw error;

      setSuccessMessage('Password updated successfully! Redirecting...');
      setTimeout(() => router.push('/login'), 2000);
    } catch (error: any) {
      setErrorMessage(error.message || 'Password update failed');
    } finally {
      setLoading(false);
    }
  };

  if (!sessionChecked) {
    return (
      <AuthLayout>
        <div className="w-full max-w-md text-center">
          <h2 className="text-2xl font-bold mb-4">Reset Password</h2>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4">Verifying reset link...</p>
        </div>
      </AuthLayout>
    );
  }

  return (
    <AuthLayout>
      <div className="w-full max-w-md space-y-6">
        <h2 className="text-2xl font-bold">Reset Password</h2>

        {errorMessage && (
          <Notification type="error" header="Error" body={errorMessage} />
        )}

        {successMessage && (
          <Notification type="success" header="Success" body={successMessage} />
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Password
              label="New Password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              required
              disabled={loading}
              minLength={8}
            />

            {/* Password strength indicators */}
            <div className="mt-2 space-y-1 text-xs">
              <p className="font-medium text-gray-700">Password must contain:</p>
              <div className="flex items-center">
                {passwordStrength.hasMinLength ?
                  <Check size={12} className="text-green-500 mr-1" /> :
                  <X size={12} className="text-red-500 mr-1" />}
                <span className={passwordStrength.hasMinLength ? 'text-green-600' : 'text-gray-500'}>At least 8 characters</span>
              </div>
              <div className="flex items-center">
                {passwordStrength.hasUppercase ?
                  <Check size={12} className="text-green-500 mr-1" /> :
                  <X size={12} className="text-red-500 mr-1" />}
                <span className={passwordStrength.hasUppercase ? 'text-green-600' : 'text-gray-500'}>At least one uppercase letter</span>
              </div>
              <div className="flex items-center">
                {passwordStrength.hasLowercase ?
                  <Check size={12} className="text-green-500 mr-1" /> :
                  <X size={12} className="text-red-500 mr-1" />}
                <span className={passwordStrength.hasLowercase ? 'text-green-600' : 'text-gray-500'}>At least one lowercase letter</span>
              </div>
              <div className="flex items-center">
                {passwordStrength.hasNumber ?
                  <Check size={12} className="text-green-500 mr-1" /> :
                  <X size={12} className="text-red-500 mr-1" />}
                <span className={passwordStrength.hasNumber ? 'text-green-600' : 'text-gray-500'}>At least one number</span>
              </div>
              <div className="flex items-center">
                {passwordStrength.hasSpecialChar ?
                  <Check size={12} className="text-green-500 mr-1" /> :
                  <X size={12} className="text-red-500 mr-1" />}
                <span className={passwordStrength.hasSpecialChar ? 'text-green-600' : 'text-gray-500'}>At least one special character</span>
              </div>
            </div>
          </div>

          <Password
            label="Confirm New Password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            required
            disabled={loading}
            minLength={8}
          />
          {confirmPassword && newPassword !== confirmPassword && (
            <p className="text-xs text-red-500">Passwords do not match</p>
          )}

          <Button
            type="submit"
            disabled={loading}
            className="w-full"
            variant="primary"
          >
            {loading ? 'Updating...' : 'Update Password'}
          </Button>
        </form>

        <div className="text-center text-sm text-muted-foreground">
          <Button
            variant="link"
            className="text-primary underline"
            onClick={() => router.push('/login')}
          >
            Return to Login
          </Button>
        </div>
      </div>
    </AuthLayout>
  );
}