-- Create table for storing Google Merchant API tokens
CREATE TABLE IF NOT EXISTS google_merchant_tokens (
  id SERIAL PRIMARY KEY,
  access_token TEXT NOT NULL,
  refresh_token TEXT NOT NULL,
  expiry_date BIGINT NOT NULL,
  merchant_id TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create table for storing Google Merchant sync status
CREATE TABLE IF NOT EXISTS google_merchant_sync_status (
  id SERIAL PRIMARY KEY,
  total INTEGER NOT NULL,
  processed INTEGER NOT NULL,
  succeeded INTEGER NOT NULL,
  failed INTEGER NOT NULL,
  errors JSONB DEFAULT '[]'::jsonb,
  synced_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create table for storing Google Merchant product mappings
CREATE TABLE IF NOT EXISTS google_merchant_products (
  id SERIAL PRIMARY KEY,
  part_id INTEGER NOT NULL REFERENCES parts(id) ON DELETE CASCADE,
  google_product_id TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending',
  last_synced_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(part_id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_google_merchant_products_part_id ON google_merchant_products(part_id);
CREATE INDEX IF NOT EXISTS idx_google_merchant_products_status ON google_merchant_products(status);

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updating the updated_at column
CREATE TRIGGER update_google_merchant_tokens_updated_at
BEFORE UPDATE ON google_merchant_tokens
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_google_merchant_products_updated_at
BEFORE UPDATE ON google_merchant_products
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
