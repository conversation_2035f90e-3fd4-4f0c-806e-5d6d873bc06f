// M-PESA API Configuration
export const MPESA_CONFIG = {
  // API Credentials
  CONSUMER_KEY: process.env.MPESA_CONSUMER_KEY || '',
  CONSUMER_SECRET: process.env.MPESA_CONSUMER_SECRET || '',
  PASSKEY: process.env.MPESA_PASSKEY || '',
  SHORT_CODE: process.env.MPESA_SHORT_CODE || '',
  USE_HARDCODED_TOKEN: process.env.MPESA_USE_HARDCODED_TOKEN === 'true',

  // Callback URL
  CALLBACK_URL: process.env.NEXT_PUBLIC_BASE_URL
    ? `${process.env.NEXT_PUBLIC_BASE_URL}/api/mpesa/callback`
    : 'https://your-domain.com/api/mpesa/callback',

  // Transaction details
  TRANSACTION_TYPE: 'CustomerPayBillOnline',
  ACCOUNT_REFERENCE: 'AUTOFLOW',
  TRANSACTION_DESCRIPTION: 'Payment for parts purchase',

  // Environment
  ENVIRONMENT: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',

  // Development mode flag
  get IS_DEV_MODE() {
    return process.env.NODE_ENV !== 'production';
  },

  // Check if M-PESA is properly configured
  get IS_CONFIGURED() {
    const hasCredentials = !!(this.CONSUMER_KEY && this.CONSUMER_SECRET && this.PASSKEY && this.SHORT_CODE);

    if (!hasCredentials && this.IS_DEV_MODE) {
      console.log('M-PESA is not fully configured. Using development mode fallbacks.');
    }

    return hasCredentials;
  },

  // API endpoints
  get BASE_URL() {
    return this.ENVIRONMENT === 'production'
      ? 'https://api.safaricom.co.ke'
      : 'https://sandbox.safaricom.co.ke';
  },

  get AUTH_URL() {
    return `${this.BASE_URL}/oauth/v1/generate?grant_type=client_credentials`;
  },

  get STK_PUSH_URL() {
    return `${this.BASE_URL}/mpesa/stkpush/v1/processrequest`;
  },

  get QUERY_URL() {
    return `${this.BASE_URL}/mpesa/stkpushquery/v1/query`;
  },

  // Log configuration (excluding sensitive data)
  logConfig() {
    if (this.IS_DEV_MODE) {
      console.log('M-PESA Configuration:', {
        ENVIRONMENT: this.ENVIRONMENT,
        IS_CONFIGURED: this.IS_CONFIGURED,
        HAS_CONSUMER_KEY: !!this.CONSUMER_KEY,
        HAS_CONSUMER_SECRET: !!this.CONSUMER_SECRET,
        HAS_PASSKEY: !!this.PASSKEY,
        HAS_SHORT_CODE: !!this.SHORT_CODE,
        CALLBACK_URL: this.CALLBACK_URL,
        BASE_URL: this.BASE_URL,
        AUTH_URL: this.AUTH_URL,
        STK_PUSH_URL: this.STK_PUSH_URL,
        QUERY_URL: this.QUERY_URL,
      });
    }
  }
};
