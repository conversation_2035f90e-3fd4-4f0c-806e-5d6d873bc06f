import React from 'react';
import { Icon as MdiIcon } from '@mdi/react'; // Rename the imported Icon to MdiIcon
import * as mdiIcons from '@mdi/js';
import { icons as lucideIcons, CheckCircle, Package } from 'lucide-react';

// Define our own type for Lucide Icon props
type LucideIconProps = React.SVGProps<SVGSVGElement>;

interface IconProps extends LucideIconProps {
  name: string;
  size?: number;
  library?: 'mdi' | 'lucide';
}

const Icon: React.FC<IconProps> = ({ name, size = 24, color = 'currentColor', library, ...props }) => {
  // Special cases for specific icons
  if (name === 'check-circle' && (library === 'lucide' || !library)) {
    return <CheckCircle size={size} color={color} {...props} />;
  }

  if (name === 'package' && (library === 'lucide' || !library)) {
    return <Package size={size} color={color} {...props} />;
  }

  const lucideIconKey = name.split('-').map(part => part.charAt(0).toUpperCase() + part.slice(1)).join('') as keyof typeof lucideIcons;

  if ((library === 'lucide' || !library) && lucideIcons[lucideIconKey]) {
    const LucideIconComponent = lucideIcons[lucideIconKey];
    return <LucideIconComponent size={size} color={color} {...props} />;
  } else if (library === 'mdi' || (library !== 'lucide' && !lucideIcons[lucideIconKey])) {
    const mdiIconKey = `mdi${name.split('-').map(part => part.charAt(0).toUpperCase() + part.slice(1)).join('')}` as keyof typeof mdiIcons;
    const path = mdiIcons[mdiIconKey];

    if (!path) {
      // Try alternative MDI icon names
      const alternativeMdiIcons: Record<string, keyof typeof mdiIcons> = {
        'check-circle': 'mdiCheckCircleOutline',
        'package': 'mdiPackageVariant'
      };

      const alternativePath = alternativeMdiIcons[name] ? mdiIcons[alternativeMdiIcons[name]] : null;

      if (!alternativePath) {
        console.warn(`Icon "${name}" not found in either library`);
        return null;
      }

      const { path: _path, ...restProps } = props as React.ComponentProps<typeof MdiIcon>;
      return <MdiIcon path={alternativePath} size={size / 24} color={color} {...restProps} />;
    }

    // Correctly type and spread props for the @mdi/react Icon component
    const { path: _path, ...restProps } = props as React.ComponentProps<typeof MdiIcon>; // Use MdiIcon here
    return <MdiIcon path={path} size={size / 24} color={color} {...restProps} />;
  }

  console.warn(`Icon "${name}" not found in either library`);
  return null;
};

export default Icon; // Now you can export Icon

/*
  Sample Usage:

  // Lucide icons (default library):
  <Icon name="home" />                       // Lucide home icon
  <Icon name="arrow-up" size={32} color="blue" />  // Lucide arrow-up icon, size 32, blue color
  <Icon name="feather" strokeWidth={3} />         // Lucide feather icon, strokeWidth 3

  // MDI icons (explicitly specify library):
  <Icon name="home" library="mdi" />              // MDI home icon
  <Icon name="alert-circle-outline" library="mdi" size={48} color="red" /> // MDI alert-circle-outline icon, size 48, red color

  // If both libraries have the same icon name (e.g., "home"), Lucide will be used by default:
  <Icon name="home" />                          // This will use the Lucide "home" icon

  // To use the MDI "home" icon in this case, be explicit:
  <Icon name="home" library="mdi" />              // This will use the MDI "home" icon
*/