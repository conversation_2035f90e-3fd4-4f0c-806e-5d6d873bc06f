import { useState, useEffect } from 'react';
import { getUserCookie } from '@/app/utils/cookies';

interface UserCookieData {
  id: string;
  name: string;
  roleId: string;
  roleName: string;
  isSuperAdmin: boolean;
  isLoading: boolean;
  error: Error | null;
}

export function useUserCookie(): UserCookieData {
  const [userData, setUserData] = useState<Omit<UserCookieData, 'isLoading' | 'error'>>({
    id: '',
    name: '',
    roleId: '',
    roleName: '',
    isSuperAdmin: false
  });
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoading(true);
        const cookieData = await getUserCookie();

        if (cookieData) {
          const roleName = cookieData.roleName || '';
          const isSuperAdmin = roleName === 'Super Admin';
          console.log('[useUserCookie] Cookie data:', cookieData);
          console.log('[useUserCookie] Role name:', roleName);
          console.log('[useUserCookie] Is Super Admin:', isSuperAdmin);

          setUserData({
            id: cookieData.id || '',
            name: cookieData.name || '',
            roleId: cookieData.roleId || '',
            roleName: roleName,
            isSuperAdmin: isSuperAdmin
          });
        }
        setError(null);
      } catch (err) {
        console.error('Error fetching user cookie:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch user data'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, []);

  return {
    ...userData,
    isLoading,
    error
  };
}
