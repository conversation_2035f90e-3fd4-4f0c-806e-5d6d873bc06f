import { z } from 'zod';

// Define the compatibility data schema
const engineCompatibilitySchema = z.array(z.object({
  engineCode: z.string(),
  engineCapacity: z.union([z.string(), z.number()]),
  fuelType: z.string(),
  engineType: z.string()
})).optional();

const vehicleCompatibilitySchema = z.array(z.object({
  brand: z.string().optional(),
  model: z.string(),
  generation: z.string(),
  trims: z.array(z.string()).optional()
})).optional();

const compatibilityDataSchema = z.object({
  partName: z.string().optional(),
  compatiblePartNumbers: z.array(z.string()).optional(),
  isEnginePart: z.boolean().optional(),
  engineCompatibility: engineCompatibilitySchema,
  vehicleCompatibility: vehicleCompatibilitySchema
}).optional();

export const partFormSchema = z.object({
  partNumber: z.string().min(1, 'Part number is required'),
  stock: z.number().min(0, 'Stock must be 0 or greater').optional(),
  price: z.number().min(0, 'Price must be 0 or greater').optional(),
  discountPrice: z.number().min(0, 'Discount price must be 0 or greater').optional(),
  condition: z.enum(['New', 'Used', 'Both']).optional(),
  imageUrl: z.string(),
  imageType: z.string(),
  categoryId: z.string(),
  trimId: z.string(),
  generationId: z.string(),
  variationId: z.string(),
  brandId: z.string(),
  modelId: z.string(),
  attributes: z.record(z.any()),
  categoryAttributes: z.array(z.object({
    id: z.number(),
    value: z.string()
  })),
  selectedCategory: z.string(),
  isCheckingPartNumber: z.boolean(),
  showVehicleSelection: z.boolean(),
  requirePartNumber: z.boolean(),
  additionalEngineCodes: z.array(z.string()),
  // Add compatibilityData field with proper schema
  compatibilityData: compatibilityDataSchema,
  // New fields for both conditions
  newStock: z.number().min(0, 'New stock must be 0 or greater').optional(),
  newPrice: z.number().min(0, 'New price must be 0 or greater').optional(),
  usedStock: z.number().min(0, 'Used stock must be 0 or greater').optional(),
  usedPrice: z.number().min(0, 'Used price must be 0 or greater').optional(),
  newDiscountPrice: z.number().min(0, 'New discount price must be 0 or greater').optional(),
  usedDiscountPrice: z.number().min(0, 'Used discount price must be 0 or greater').optional(),
  userId: z.string(),
}).refine((data) => {
  // Only apply validation if a condition is selected
  if (!data.condition) {
    return true;
  }
  
  // Validate that stock and price are provided when condition is not 'Both'
  if (data.condition !== 'Both') {
    return data.stock !== undefined && data.price !== undefined;
  }
  // Validate that both new and used stock/price are provided when condition is 'Both'
  if (data.condition === 'Both') {
    return data.newStock !== undefined && data.newPrice !== undefined && 
           data.usedStock !== undefined && data.usedPrice !== undefined;
  }
  return true;
}, {
  message: "Stock and price are required for the selected condition",
  path: ["condition"]
});