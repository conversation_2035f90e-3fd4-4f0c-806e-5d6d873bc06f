import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/server'; // Ensure this path is correct

export async function POST(request: NextRequest) {
  try {
    const { email, roleId } = await request.json();

    // --- Input Validation ---
    if (!email || typeof email !== 'string' || !email.includes('@')) { // Basic email format check
      return NextResponse.json({ error: 'A valid email address is required' }, { status: 400 });
    }

    if (!roleId || typeof roleId !== 'string') { // Assuming roleId is a string (like UUID)
      return NextResponse.json({ error: 'Role ID is required' }, { status: 400 });
    }

    // --- Initialize Supabase Client ---
    // Creates a Supabase client configured for server-side operations (admin privileges)
    const supabase = createClient();

    // --- Determine Base URL for Redirect ---
    // Get the origin URL from the request headers. This reflects the domain
    // from which the frontend request was made (e.g., https://xyz.com or http://localhost:3000).
    const origin = request.headers.get('origin');

    // Determine the base URL for the redirect link in the invitation email.
    // 1. Prioritize the 'origin' header: This ensures the user is redirected
    //    back to the specific domain (e.g., xyz.com, staging.xyz.com, localhost)
    //    from where the invitation request originated. This is the key for dynamic redirection.
    // 2. Fallback to the environment variable `NEXT_PUBLIC_APP_URL` if origin is not available.
    // 3. Default to 'http://localhost:3000' for local development if neither is set.
    const baseUrl = origin ||
                    process.env.NEXT_PUBLIC_APP_URL ||
                    'http://localhost:3000';

    console.log(`Determined base URL for redirect: ${baseUrl} (Origin: ${origin || 'Not Provided'}, Env Var: ${process.env.NEXT_PUBLIC_APP_URL || 'Not Set'})`);

    // Construct the full redirect URL pointing to your registration/setup page
    const redirectUrl = `${baseUrl}/register?invitation=true`;
    console.log(`Generating Supabase invite link redirecting to: ${redirectUrl}`);


    // --- Send Supabase Invitation ---
    // Use Supabase Auth Admin API to send an invitation email.
    // This creates a user record with status 'invited' and sends an email containing a magic link.
    const { data, error: inviteError } = await supabase.auth.admin.inviteUserByEmail(email, {
      redirectTo: redirectUrl, // Use the dynamically determined redirect URL
      data: { // Optional metadata stored in auth.users.raw_app_meta_data
        invited_by: 'admin', // Consider fetching the actual admin user ID if relevant
        invited_at: new Date().toISOString(),
        is_invitation: true, // Flag to potentially identify invited users
        registration_completed: false, // Flag to track if user completed setup
        role_id: roleId // Store intended role ID in metadata
      }
    });

    if (inviteError) {
      console.error('Supabase invitation error:', inviteError);
      // Provide a more specific error message for common cases like existing users
      let errorMessage = inviteError.message;
      if (inviteError.message.includes('User already exists')) {
          errorMessage = 'This email address is already registered or has a pending invitation.';
      } else if (inviteError.status === 429) {
          errorMessage = 'Too many requests. Please wait a moment and try again.';
      }
      // Return a 4xx status for client-side errors (like existing user), 5xx for server issues
      const status = inviteError.status && inviteError.status >= 400 && inviteError.status < 500 ? inviteError.status : 400;
      return NextResponse.json({ error: errorMessage }, { status });
    }

    // --- Assign Role (Optional but recommended here) ---
    // If the invitation was successful (inviteError is null), Supabase returns user data.
    // Attempt to create the user_roles entry immediately using the user ID from the response.
    if (data?.user?.id) {
      const userId = data.user.id;

      console.log(`Invitation sent successfully for ${email}. User ID: ${userId}. Attempting to assign role ${roleId}.`);

      const { error: roleAssignmentError } = await supabase
        .from('user_roles') // Make sure 'user_roles' is your correct table name
        .insert({
          user_id: userId, // Ensure this matches your column name
          role_id: roleId, // Ensure this matches your column name
          assigned_at: new Date().toISOString() // Optional: track when the role was assigned
        });

      if (roleAssignmentError) {
        // Log the error but don't fail the entire operation since the invite email was sent.
        // The role assignment might need manual intervention or a retry mechanism.
        // Consider adding more robust error handling/logging here (e.g., send to monitoring service)
        console.warn(`Warning: Invitation sent for ${email} (User ID: ${userId}), but failed to assign role ${roleId} immediately. Error: ${roleAssignmentError.message}`);
        // You could potentially return a specific status or message indicating partial success if the frontend needs to know.
      } else {
         console.log(`Successfully assigned role ${roleId} to invited user ${userId} (${email}) in user_roles table.`);
      }
    } else {
        // This case should be rare if inviteError was null, but good to log.
        console.warn(`Warning: Invitation sent for ${email}, but user data (including ID) was not returned in the success response. Cannot assign role automatically.`);
    }


    // --- Return Success Response ---
    return NextResponse.json({
      success: true,
      message: 'Invitation sent successfully.',
      // Avoid sending back the full `data` object unless necessary,
      // as it might contain sensitive info like the user object.
      // Only include what the client strictly needs, e.g.:
      // invitedEmail: email
    });

  } catch (error: any) {
    // Catch unexpected errors during request processing
    console.error('Error in POST /api/invite handler:', error);
    return NextResponse.json({
      error: error.message || 'An unexpected server error occurred while processing the invitation.'
    }, { status: 500 });
  }
}

// Note: Supabase handles the token validation in the magic link and user creation/update upon click.
// The user lands on your `/register?invitation=true` page after clicking the link.
// Your `/register` page component should handle the user session (Supabase client auth handles this)
// and guide the user through any necessary steps like setting a password or completing their profile.
// You might also check the user's metadata (`is_invitation`, `registration_completed`) on that page.