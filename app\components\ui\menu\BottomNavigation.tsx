'use client';

import React, { useState } from 'react';
import { User, Plus, LogOut } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/app/utils/cn';
import { menuItems } from '@/app/config/menu';

interface BottomNavigationProps {
  onOpenAddModal?: () => void;
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({ onOpenAddModal }) => {
  console.log('BottomNavigation rendered, onOpenAddModal prop exists:', !!onOpenAddModal);
  const pathname = usePathname();
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);

  // Filter out items with children for the main navigation
  const navItems = menuItems.filter(item =>
    !item.children &&
    ['dashboard', 'parts'].includes(item.id)
  );

  const handleAddClick = () => {
    console.log('Add button clicked, current pathname:', pathname);

    if ((pathname === '/parts' || pathname.startsWith('/parts/')) && onOpenAddModal) {
      console.log('Calling onOpenAddModal function, onOpenAddModal is:', !!onOpenAddModal);
      try {
        onOpenAddModal();
        console.log('onOpenAddModal called successfully');
      } catch (error) {
        console.error('Error calling onOpenAddModal:', error);
      }
    } else {
      console.log('Redirecting to parts page with openAddModal parameter');
      console.log('Current pathname does not match /parts criteria, pathname is:', pathname);
      // If we're not on the parts page, navigate there and open the modal
      window.location.href = '/parts?openAddModal=true';
    }
  };

  return (
    <div className="fixed bottom-5 left-0 right-0 flex justify-center">
      <div className="flex items-center bg-gray-800 rounded-full pl-3 pr-1 py-2 shadow-lg">
        {navItems.map((item, index) => {
          const Icon = item.icon;
          const isActive = pathname === item.href;

          return (
            <Link
              key={index}
              href={item.href}
              className={cn(
                "flex items-center justify-center p-2 mx-2 rounded-full",
                isActive ? "text-white" : "text-gray-400 hover:text-gray-200"
              )}
            >
              <Icon size={20} />
            </Link>
          );
        })}
        <div className="relative">
          <button
            onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
            className={cn(
              "flex items-center justify-center p-2 mx-2 rounded-full",
              pathname === '/profile' ? "text-white" : "text-gray-400 hover:text-gray-200"
            )}
          >
            <User size={20} />
          </button>

          {/* Profile Popup Menu */}
          {isProfileMenuOpen && (
            <>
              <div
                className="fixed inset-0"
                onClick={() => setIsProfileMenuOpen(false)}
              />
              <div className="absolute bottom-full right-0 mb-2 w-48 bg-white rounded-lg shadow-lg py-2 border border-gray-200">
                <Link
                  href="/profile"
                  className="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100"
                  onClick={() => setIsProfileMenuOpen(false)}
                >
                  <User size={16} className="mr-2" />
                  Profile
                </Link>
                <button
                  onClick={() => {
                    setIsProfileMenuOpen(false);
                    // Add logout logic here
                  }}
                  className="flex items-center w-full px-4 py-2 text-gray-700 hover:bg-gray-100"
                >
                  <LogOut size={16} className="mr-2" />
                  Logout
                </button>
              </div>
            </>
          )}
        </div>
        <button
          onClick={handleAddClick}
          className="flex items-center justify-center p-2 ml-1 rounded-full bg-blue-600 text-white hover:bg-blue-700"
        >
          <Plus size={20} />
        </button>
      </div>
    </div>
  );
};

export default BottomNavigation;