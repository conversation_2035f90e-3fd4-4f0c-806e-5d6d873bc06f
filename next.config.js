/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['images.unsplash.com', 'source.unsplash.com', 'excgraelqcvcdsnlvrtv.supabase.co', 'placeholder-image.com'],
    // Disable image optimization for Supabase storage URLs
    unoptimized: process.env.NODE_ENV === 'production',
    // Alternative approach: only disable optimization for specific domains
    // remotePatterns: [
    //   {
    //     protocol: 'https',
    //     hostname: 'excgraelqcvcdsnlvrtv.supabase.co',
    //     pathname: '/storage/**',
    //   },
    // ],
    // dangerouslyAllowSVG: true,
  },

  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },

  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
};

module.exports = nextConfig;