import { createClient } from '@/app/libs/supabase/client';
import { AttributeValue } from '../types';

/**
 * Updates the part title based on attribute changes
 *
 * @param currentTitle The current part title
 * @param attributes The updated attributes
 * @param categoryAttributes The category attributes metadata
 * @returns The updated title
 */
export const updateTitleWithAttributes = async (
  currentTitle: string,
  attributes: Array<{
    id: string;
    name: string;
    value: string | string[] | null;
    inputType: string;
  }>,
  categoryAttributes: any[]
): Promise<string> => {
  // If no attributes or no changes, return the original title
  if (!attributes || attributes.length === 0) {
    return currentTitle;
  }

  // Get important attributes that should be included in the title
  // Focus on engine-related attributes, capacity, fuel type, etc.
  const importantAttributes = [
    'engine', 'engine code', 'engine type', 'engine capacity',
    'capacity', 'fuel type', 'fuel', 'transmission', 'year',
    'side', 'position', 'color'
  ];

  // Extract the important attribute values
  const importantValues: string[] = [];

  attributes.forEach(attr => {
    // Skip empty values
    if (!attr.value) return;

    // Find the corresponding category attribute to check if it should be included in title
    const categoryAttr = categoryAttributes.find(ca => ca.id.toString() === attr.id);
    if (!categoryAttr) return;

    // Check if this is an important attribute
    const isImportant = importantAttributes.some(ia =>
      categoryAttr.attribute.toLowerCase().includes(ia.toLowerCase())
    );

    if (isImportant) {
      // Format the value based on the attribute type
      let formattedValue = '';

      if (Array.isArray(attr.value)) {
        // Handle array values (checkboxes)
        formattedValue = attr.value.join(', ');
      } else if (typeof attr.value === 'string') {
        formattedValue = attr.value;
      }

      // Add the attribute name and value to the important values
      if (formattedValue) {
        // For side attributes, just add the value (e.g., "Right" or "Left") without the attribute name
        if (categoryAttr.attribute.toLowerCase().includes('side')) {
          importantValues.push(formattedValue);
        }
        // For position and color, include the attribute name
        else if (['position', 'color'].some(a =>
          categoryAttr.attribute.toLowerCase().includes(a.toLowerCase())
        )) {
          importantValues.push(`${categoryAttr.attribute}: ${formattedValue}`);
        } else {
          // For others, just the value is enough
          importantValues.push(formattedValue);
        }
      }
    }
  });

  // If no important values found, return the original title
  if (importantValues.length === 0) {
    return currentTitle;
  }

  // Check if the title already contains any of the important values
  const containsValue = importantValues.some(value =>
    currentTitle.toLowerCase().includes(value.toLowerCase())
  );

  // If the title already contains the values, don't modify it
  if (containsValue) {
    return currentTitle;
  }

  // For tail lights and other parts where we want to completely reformat the title
  if (currentTitle.toLowerCase().includes('tail light') ||
      currentTitle.toLowerCase().includes('head light') ||
      currentTitle.toLowerCase().includes('fog light')) {

    // Extract the car details (brand, model, generation, years, body type)
    // This regex captures: Brand Model Generation (Years) Body-Type Trim
    const carDetailsRegex = /^((?:[A-Za-z]+(?:\([A-Za-z]+\))?\s+)?[A-Za-z0-9]+(?:\s+[A-Za-z0-9]+)*(?:\s+\([0-9]{4}-[0-9]{4}\))?(?:\s+[A-Za-z]+)?(?:\s+[A-Za-z]+)?)/i;
    const carDetailsMatch = currentTitle.match(carDetailsRegex);

    // Extract the car details, removing any duplicate generation/year information
    let carDetails = '';
    if (carDetailsMatch && carDetailsMatch[1]) {
      carDetails = carDetailsMatch[1].trim();

      // Remove duplicate generation/year information that might appear twice
      const generationYearRegex = /((?:Mk\d+|[A-Za-z0-9]+\s+Facelift)(?:\s+\([0-9]{4}-[0-9]{4}\))?)/gi;
      const generationMatches = Array.from(carDetails.matchAll(generationYearRegex));

      if (generationMatches.length > 1) {
        // Keep only the first occurrence of generation/year info
        const firstGeneration = generationMatches[0][0];
        for (let i = 1; i < generationMatches.length; i++) {
          carDetails = carDetails.replace(generationMatches[i][0], '');
        }
        carDetails = carDetails.replace(/\s+/g, ' ').trim();
      }
    }

    // Extract condition (Used/New)
    const conditionMatch = currentTitle.match(/(Used|New)/i);
    const condition = conditionMatch ? conditionMatch[0] : '';

    // Extract side (Left/Right) directly from the attributes
    let side = '';
    for (const attr of attributes) {
      if (attr.name && attr.name.toLowerCase().includes('side') && attr.value) {
        // Use the exact value from the attribute, not from importantValues
        side = typeof attr.value === 'string' ? attr.value : '';
        break;
      }
    }

    // If we couldn't find it in attributes, try importantValues as fallback
    if (!side) {
      const sideMatch = importantValues.find(v => v.match(/^(Left|Right)$/i));
      side = sideMatch || '';
    }

    // Extract LED or other light type
    const lightTypeMatch = currentTitle.match(/(LED|Xenon|Halogen)/i);
    const lightType = lightTypeMatch ? lightTypeMatch[0] : '';

    // Extract the part name (Tail Lights, Head Lights, Fog Lights)
    let partName = '';
    if (currentTitle.toLowerCase().includes('tail light')) {
      partName = 'Tail Lights';
    } else if (currentTitle.toLowerCase().includes('head light')) {
      partName = 'Head Lights';
    } else if (currentTitle.toLowerCase().includes('fog light')) {
      partName = 'Fog Lights';
    }

    // Debug log to see what's happening with the side attribute
    console.log('Title update debug:', {
      attributes: attributes.filter(a => a.name && a.name.toLowerCase().includes('side')),
      side,
      importantValues,
      currentTitle
    });

    // Construct the new title in the desired format
    // Format: Brand Model Generation (Years) Body-Type Trim Condition Side LED Part-Name
    return `${carDetails} ${condition} ${side} ${lightType} ${partName}`.replace(/\s+/g, ' ').trim();
  }

  // For other parts, just append the important values to the title
  return `${currentTitle} ${importantValues.join(' ')}`.trim();
};
