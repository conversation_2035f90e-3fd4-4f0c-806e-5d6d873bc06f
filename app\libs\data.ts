import { createClient } from "./supabase/client";
import {
  CategoryAttribute,
  AttributeInputOption,
  PartFormValues,
  FlatCategory
} from "../(dashboard)/parts/types";

// Define missing interfaces
interface Category {
  id?: number;
  name?: string;
  parentId?: number | null;
}

interface Brand {
  id?: number;
  name?: string;
}

interface Model {
  id?: number;
  name?: string;
  brandId?: number;
}

interface Generation {
  id?: number;
  name?: string;
  modelId?: number;
  startYear?: number;
  endYear?: number;
}

interface Variation {
  id?: number;
  name?: string;
  generationId?: number;
}

interface Trim {
  id?: number;
  name?: string;
  variationId?: number;
}

const supabase = createClient();

// Fetch functions
export async function fetchBrandName(brandId: number): Promise<string> {
  const { data } = await supabase
    .from('car_brands')
    .select('brand_name')
    .eq('brand_id', brandId)
    .single();
  return data?.brand_name || '';
}

export async function fetchModelName(modelId: number): Promise<string> {
  const { data } = await supabase
    .from('car_models')
    .select('model_name')
    .eq('id', modelId)
    .single();
  return data?.model_name || '';
}

export async function fetchGenerationDetails(generationId: number): Promise<{
  name: string;
  start_production_year: number;
  end_production_year?: number;
} | null> {
  const { data } = await supabase
    .from('car_generation')
    .select('name, start_production_year, end_production_year')
    .eq('id', generationId)
    .single();
  return data || null;
}

export async function fetchVariationName(variationId: number): Promise<string> {
  const { data } = await supabase
    .from('car_variation')
    .select('variation')
    .eq('id', variationId)
    .single();
  return data?.variation || '';
}

export async function fetchTrimName(trimId: number): Promise<string> {
  const { data } = await supabase
    .from('variation_trim')
    .select('trim')
    .eq('id', trimId)
    .single();
  return data?.trim || '';
}

export async function fetchAttributeOption(attributeId: number, optionId: number): Promise<AttributeInputOption | null> {
  const { data } = await supabase
    .from('parts_category_attribute_input_option')
    .select('*')
    .eq('id', optionId)
    .eq('attribute_id', attributeId)
    .single();
  return data || null;
}

// Create functions
export async function createPartRecord(data: PartFormValues, title: string) {
  const { data: partData, error } = await supabase
    .from('parts')
    .insert({
      partnumber: data.partNumber || null,
      category_id: parseInt(data.categoryId!),
      title,
    })
    .select('id')
    .single();

  if (error) throw error;
  return partData;
}

export async function createConditionRecord(partId: number, condition: string) {
  const { data, error } = await supabase
    .from('parts_condition')
    .insert({ part_id: partId, condition })
    .select('id')
    .single();

  if (error) throw error;
  return data;
}

export async function createPriceRecord(conditionId: number, price: number) {
  const { error } = await supabase
    .from('part_price')
    .insert({ condition_id: conditionId, price });
  if (error) throw error;
}

export async function createCarLinkRecord(partId: number, variationTrimId: string) {
  // First, get the next available ID for parts_car table
  const { data: maxIdData, error: maxIdError } = await supabase
    .from('parts_car')
    .select('id')
    .order('id', { ascending: false })
    .limit(1)
    .single();

  if (maxIdError && maxIdError.code !== 'PGRST116') {
    // PGRST116 is "Results contain 0 rows" which is fine for an empty table
    throw new Error(`Failed to get max ID: ${maxIdError.message}`);
  }

  const nextId = maxIdData ? maxIdData.id + 1 : 1;
  console.log(`Next available ID for parts_car: ${nextId}`);

  // Insert with the next available ID
  const { error } = await supabase
    .from('parts_car')
    .insert({
      id: nextId,
      part_id: partId,
      variation_trim_id: parseInt(variationTrimId)
    });

  if (error) throw error;

  return { id: nextId };
}

export async function createAttributeRecords(
  partId: number,
  attributes: CategoryAttribute[],
  getValues: any
) {
  const attributeValues = attributes.map((attr) => ({
    part_id: partId,
    attribute_id: attr.id!,
    value: String(getValues(
      attr.attribute.toLowerCase().replace(/\s+/g, '_') as keyof PartFormValues
    ))
  }));

  const { error } = await supabase
    .from('parts_category_attribute_values')
    .insert(attributeValues);
  if (error) throw error;
}

export async function createImageRecord(partId: number, imageUrl: string) {
  const { error } = await supabase
    .from('part_images')
    .insert({
      part_id: partId,
      image_url: imageUrl,
      is_main_image: true,
    });
  if (error) throw error;
}

// Validation functions
export async function checkPartNumberExists(partNumber: string) {
  if (!partNumber || typeof partNumber !== 'string' || partNumber.trim() === '') {
    return false;
  }

  const { data } = await supabase
    .from('parts')
    .select('partnumber')
    .eq('partnumber', partNumber.trim())
    .single();
  return !!data;
}

// Fetch all categories with nested children
export const fetchCategories = async (): Promise<FlatCategory[]> => {
  try {
    // Fetch only active categories
    const { data: allCategories, error } = await supabase
      .from('car_part_categories')
      .select('*')
      .eq('isActive', true)
      .order('label');

    if (error) throw error;

    if (!allCategories || allCategories.length === 0) {
      console.log('No categories found');
      return [];
    }

    // Map database fields to our expected format
    const flatCategories: FlatCategory[] = allCategories.map(category => ({
      id: category.id,
      name: category.label, // Map label to name
      parentId: category.parent_category_id,
      level: category.level || 0,
      partNumberRequired: category.requirePartNumber ?? false,
      requirePartNumber: category.requirePartNumber ?? false
    }));

    console.log('Fetched categories:', flatCategories.length);
    return flatCategories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
};

// Fetch attributes for a specific category
export const fetchCategoryAttributes = async (categoryId: number): Promise<CategoryAttribute[]> => {
  try {
    const { data, error } = await supabase
      .from('parts_category_attributes')
      .select(`
        id,
        attribute,
        input_type,
        depends_on_attribute_id,
        depends_on_option_id
      `)
      .eq('category_id', categoryId)
      .order('attribute');

    if (error) throw error;

    // Map the data to match the CategoryAttribute interface
    return (data || []).map(item => ({
      id: item.id,
      name: item.attribute,
      attribute: item.attribute,
      required: false, // Default value
      type: item.input_type,
      input_type: item.input_type,
      depends_on_attribute_id: item.depends_on_attribute_id,
      depends_on_option_id: item.depends_on_option_id
    }));
  } catch (error) {
    console.error('Error fetching category attributes:', error);
    return [];
  }
};

// Fetch input options for a specific attribute
export const fetchAttributeInputOptions = async (attributeId: number): Promise<AttributeInputOption[]> => {
  try {
    const { data, error } = await supabase
      .from('parts_category_attribute_input_option')
      .select(`
        id,
        option_value,
        attribute_id
      `)
      .eq('attribute_id', attributeId)
      .order('option_value');

    if (error) throw error;

    console.log(`Options for attribute ${attributeId}:`, data);
    return data || [];
  } catch (error) {
    console.error('Error fetching attribute input options:', error);
    return [];
  }
};

// Create operations

// Create a new brand
export const createBrand = async (brandData: Partial<Brand>) => {
  try {
    const { data, error } = await supabase
      .from('brands')
      .insert({ name: brandData.name })
      .select();

    if (error) throw error;
    return data[0];
  } catch (error) {
    console.error('Error creating brand:', error);
    throw error;
  }
};

// Create a new model
export const createModel = async (modelData: Partial<Model>) => {
  try {
    const { data, error } = await supabase
      .from('models')
      .insert({
        name: modelData.name,
        brand_id: modelData.brandId
      })
      .select();

    if (error) throw error;

    // Return data mapped to Model interface
    return data[0] ? {
      id: data[0].id,
      brandId: data[0].brand_id,
      name: data[0].name
    } : null;
  } catch (error) {
    console.error('Error creating model:', error);
    throw error;
  }
};

// Create a new category
export const createCategory = async (categoryData: Partial<Category>) => {
  try {
    const { data, error } = await supabase
      .from('parts_categories')
      .insert({
        name: categoryData.name,
        parent_category_id: categoryData.parentId
      })
      .select();

    if (error) throw error;
    return data[0];
  } catch (error) {
    console.error('Error creating category:', error);
    throw error;
  }
};

// Create a new category attribute
export const createCategoryAttribute = async (attributeData: any) => {
  try {
    const { data, error } = await supabase
      .from('parts_parts_category_attributes')
      .insert({
        category_id: attributeData.category_id,
        attribute: attributeData.attribute,
        input_type: attributeData.input_type
      })
      .select();

    if (error) throw error;
    return data[0];
  } catch (error) {
    console.error('Error creating category attribute:', error);
    throw error;
  }
};

// Create input options for an attribute
export const createAttributeInputOptions = async (attributeId: number, options: string[]) => {
  try {
    const optionsToInsert = options.map(option => ({
      attribute_id: attributeId,
      option_value: option
    }));

    const { data, error } = await supabase
      .from('parts_category_attribute_input_option')
      .insert(optionsToInsert)
      .select();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating attribute input options:', error);
    throw error;
  }
};

// Update operations

// Update an existing brand
export const updateBrand = async (brandId: number, brandData: Partial<Brand>) => {
  try {
    const { data, error } = await supabase
      .from('brands')
      .update({ name: brandData.name })
      .eq('id', brandId)
      .select();

    if (error) throw error;
    return data[0];
  } catch (error) {
    console.error('Error updating brand:', error);
    throw error;
  }
};

// Update an existing model
export const updateModel = async (modelId: number, modelData: Partial<Model>) => {
  try {
    const { data, error } = await supabase
      .from('models')
      .update({
        name: modelData.name,
        brand_id: modelData.brandId
      })
      .eq('id', modelId)
      .select();

    if (error) throw error;

    // Return data mapped to Model interface
    return data[0] ? {
      id: data[0].id,
      brandId: data[0].brand_id,
      name: data[0].name
    } : null;
  } catch (error) {
    console.error('Error updating model:', error);
    throw error;
  }
};

// Update an existing category
export const updateCategory = async (categoryId: number, categoryData: Partial<Category>) => {
  try {
    const { data, error } = await supabase
      .from('parts_categories')
      .update({
        name: categoryData.name,
        parent_category_id: categoryData.parentId
      })
      .eq('id', categoryId)
      .select();

    if (error) throw error;
    return data[0];
  } catch (error) {
    console.error('Error updating category:', error);
    throw error;
  }
};

// Update an existing category attribute
export const updateCategoryAttribute = async (attributeId: number, attributeData: Partial<CategoryAttribute>) => {
  try {
    const { data, error } = await supabase
      .from('parts_parts_category_attributes')
      .update({
        attribute: attributeData.attribute,
        input_type: attributeData.input_type
      })
      .eq('id', attributeId)
      .select();

    if (error) throw error;
    return data[0];
  } catch (error) {
    console.error('Error updating category attribute:', error);
    throw error;
  }
};

// Update input options for an attribute
export const updateAttributeInputOptions = async (attributeId: number, options: string[]) => {
  try {
    // First, delete existing options
    await supabase
      .from('parts_category_attribute_input_option')
      .delete()
      .eq('attribute_id', attributeId);

    // Then insert new options
    return await createAttributeInputOptions(attributeId, options);
  } catch (error) {
    console.error('Error updating attribute input options:', error);
    throw error;
  }
};