// app/(dashboard)/parts/add/components/AddPartModal/PartAttributes.tsx
import React, { useEffect, useState, useCallback } from 'react';
import { Controller, useWatch } from 'react-hook-form';
import Input from '@/app/components/ui/inputs/Input';
import RadioGroup from '@/app/components/ui/inputs/RadioGroup';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/inputs/Select';
import Checkbox from '@/app/components/ui/inputs/Checkbox';
import { AttributeInputOption, CategoryAttribute } from '../../types'; // Import from the centralized location
import { fetchAttributeInputOptions } from '@/app/libs/data';

// Helper function to check if a value is effectively null/undefined/empty
const isNullOrUndefined = (value: any): boolean => {
    return value === null || value === undefined || value === '';
};

// Helper function to check if an attribute has dependencies
const hasDependencies = (attribute: CategoryAttribute): boolean => {
    return !isNullOrUndefined(attribute.depends_on_attribute_id) && 
           !isNullOrUndefined(attribute.depends_on_option_id);
};

interface PartAttributesProps {
    control: any;
    errors: any;
    getValues: (name: string) => any;
    setValue: (name: string, value: any) => void;
    categoryAttributes: CategoryAttribute[];
    requirePartNumber?: boolean;
}

const PartAttributes: React.FC<PartAttributesProps> = ({ control, errors, getValues, setValue, categoryAttributes, requirePartNumber = false }) => {

    const [attributeOptions, setAttributeOptions] = useState<{ [key: number]: AttributeInputOption[] }>({});
    // Track which attributes should be visible based on dependencies
    const [visibleAttributes, setVisibleAttributes] = useState<Set<number>>(new Set());

    useEffect(() => {
        const fetchOptions = async () => {
            const optionsMap: { [key: number]: AttributeInputOption[] } = {};
            for (const attribute of categoryAttributes) {
                if (attribute.input_type === 'radio' || attribute.input_type === 'dropdown') {
                    if (attribute.id) {
                        const options = await fetchAttributeInputOptions(attribute.id);
                        optionsMap[attribute.id] = options;
                    }
                }
            }
            setAttributeOptions(optionsMap);
        };

        fetchOptions();
    }, [categoryAttributes]);

    // Watch for changes in the form values to update visible attributes
    const watchedAttributes = useWatch({
        control,
        name: 'attributes',
        defaultValue: {}
    });
    
    // Also watch category attributes for changes
    const watchedCategoryAttributes = useWatch({
        control,
        name: 'categoryAttributes',
        defaultValue: []
    });

    // Initialize visible attributes and update when dependencies change
    useEffect(() => {
        const updateVisibleAttributes = () => {
            const visibleAttributesSet = new Set<number>();
            
            // First pass: add ONLY independent attributes (no dependencies)

            // First, identify which attributes have no dependencies
            categoryAttributes.forEach(attribute => {
                const hasNoDependencies = attribute.depends_on_attribute_id === null ||
                                         attribute.depends_on_attribute_id === undefined;

                if (hasNoDependencies) {
                    visibleAttributesSet.add(attribute.id);
                }
            });
            
            // Only check for dependent attributes if we have form values
            if (watchedCategoryAttributes && watchedCategoryAttributes.length > 0) {
                // Second pass: check dependent attributes based on selected values
                let changed = true;
                // Keep checking until no new attributes become visible
                while (changed) {
                    changed = false;
                    
                    categoryAttributes.forEach(attribute => {
                        // Skip if already visible or has no dependencies
                        if (visibleAttributesSet.has(attribute.id)) return;
                        
                        // Skip if this attribute doesn't have dependencies
                        if (attribute.depends_on_attribute_id === null || 
                            attribute.depends_on_attribute_id === undefined) return;
                        
                        // Get the parent attribute ID (the one this attribute depends on)
                        const parentAttrId = attribute.depends_on_attribute_id;
                        
                        // Skip if parent attribute is not visible
                        if (!visibleAttributesSet.has(parentAttrId)) return;
                        
                        // Find the parent attribute value in the form
                        const parentCategoryAttr = watchedCategoryAttributes.find(
                            (attr: any) => attr.id === parentAttrId
                        );
                        
                        // Skip if no parent value is selected
                        if (!parentCategoryAttr || !parentCategoryAttr.value) return;
                        
                        // Get the selected option ID that this attribute depends on
                        const dependsOnOptionId = attribute.depends_on_option_id;
                        if (dependsOnOptionId === null || dependsOnOptionId === undefined) return;
                        
                        // Find the option in the parent's options that matches the selected value
                        const parentOptions = attributeOptions[parentAttrId] || [];
                        const selectedOption = parentOptions.find(
                            (opt: AttributeInputOption) => opt.option_value === parentCategoryAttr.value
                        );
                        
                        // If the selected option matches the dependency condition, make this attribute visible
                        if (selectedOption && selectedOption.id === dependsOnOptionId) {
                            visibleAttributesSet.add(attribute.id);
                            changed = true; // We found a new visible attribute, so we need another pass
                        }
                    });
                }
            }
            
            // Compare with previous visible attributes to avoid unnecessary updates
            const previousVisible = Array.from(visibleAttributes);
            const currentVisible = Array.from(visibleAttributesSet);
            
            // Only update if the visible attributes have changed
            if (JSON.stringify(previousVisible.sort()) !== JSON.stringify(currentVisible.sort())) {
                setVisibleAttributes(visibleAttributesSet);
                
                // Clear values for hidden attributes to prevent submitting them
                // But only do this once when visibility changes, not on every render
                categoryAttributes.forEach(attribute => {
                    if (!visibleAttributesSet.has(attribute.id)) {
                        // Find the index in categoryAttributes array
                        const catAttrIndex = watchedCategoryAttributes?.findIndex(
                            (attr: any) => attr.id === attribute.id
                        );
                        
                        if (catAttrIndex !== -1) {
                            // Check if the value is not already empty
                            const currentValue = watchedCategoryAttributes[catAttrIndex]?.value;
                            if (currentValue && currentValue !== '') {
                                // Create a new array to avoid mutation
                                const updatedCategoryAttrs = [...watchedCategoryAttributes];
                                // Clear the value
                                updatedCategoryAttrs[catAttrIndex] = {
                                    ...updatedCategoryAttrs[catAttrIndex],
                                    value: ''
                                };
                                // Update the form
                                setValue('categoryAttributes', updatedCategoryAttrs);
                            }
                        }
                    }
                });
            }

        };

        // Check if we need to wait for options to be loaded
        const attributesNeedingOptions = categoryAttributes.filter(
            attr => ['radio', 'dropdown', 'checkbox'].includes(attr.input_type)
        );

        // Only run if we have category attributes and either:
        // 1. No attributes need options, OR
        // 2. All attributes that need options have their options loaded
        const optionsReady = attributesNeedingOptions.length === 0 ||
                           attributesNeedingOptions.every(attr =>
                               attr.id && attributeOptions[attr.id] !== undefined
                           );

        if (categoryAttributes.length > 0 && optionsReady) {
            updateVisibleAttributes();
        }
    }, [watchedCategoryAttributes, categoryAttributes, attributeOptions, getValues, setValue, visibleAttributes]);

    // Helper function to get all direct child attributes of a parent attribute
    const getChildAttributes = (parentId: number) => {
        return categoryAttributes.filter(attr => 
            attr.depends_on_attribute_id === parentId && 
            visibleAttributes.has(attr.id)
        );
    };

    // Render an attribute and its children
    const renderAttributeWithChildren = (attribute: CategoryAttribute) => {
        // Find the corresponding categoryAttribute in the form values
        const categoryAttrIndex = watchedCategoryAttributes?.findIndex(
            (attr: any) => attr.id === attribute.id
        );
        
        // Use the categoryAttributes array field name instead of attributes object
        const fieldName = `categoryAttributes.${categoryAttrIndex}.value`;
        const errorMessage = attribute.id ? errors.categoryAttributes?.[categoryAttrIndex]?.value?.message : undefined;
                const options = attributeOptions[attribute.id || -1] || [];

        // Render the attribute input based on its type
        const renderAttributeInput = () => {
                switch (attribute.input_type) {
                    case 'radio':
                        return (
                            <Controller
                                key={attribute.id}
                                name={fieldName}
                                control={control}
                                render={({ field }) => (
                                    <RadioGroup
                                        label={attribute.attribute}
                                        options={options.map(option => ({
                                            label: option.option_value,
                                            value: option.option_value
                                        }))}
                                        value={field.value}
                                        onChange={field.onChange}
                                    />
                                )}
                            />
                        );

                    case 'dropdown':
                        return (
                            <Controller
                                key={attribute.id}
                                name={fieldName}
                                control={control}
                                render={({ field }) => (
                                    <div className="space-y-2">
                                        <label className="block text-sm font-medium text-gray-700">
                                            {attribute.attribute}
                                        </label>
                                        <Select onValueChange={field.onChange} value={field.value}>
                                            <SelectTrigger placeholder={`Select ${attribute.attribute}`}>
                                                <SelectValue>{field.value || `Select ${attribute.attribute}`}</SelectValue>
                                            </SelectTrigger>
                                            <SelectContent>
                                                {options.map(option => (
                                                    <SelectItem key={option.id} value={option.option_value}>
                                                        {option.option_value}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errorMessage && (
                                            <p className="text-sm text-red-600">{errorMessage}</p>
                                        )}
                                    </div>
                                )}
                            />
                        );

                    case 'checkbox':
                        return (
                            <Controller
                                key={attribute.id}
                                name={fieldName}
                                control={control}
                                render={({ field }) => (
                                    <Checkbox
                                        label={attribute.attribute}
                                        checked={field.value}
                                        onChange={field.onChange}  // Keep this for controlled component behavior
                                        onCheckedChange={field.onChange} // And keep this for shadcn/ui
                                    />
                                )}
                            />
                        );

                    case 'number':
                        return (
                            <Controller
                                key={attribute.id}
                                name={fieldName}
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        label={attribute.attribute}
                                        type="number"
                                        {...field}
                                        value={field.value || ''} // Ensure value is controlled
                                        errorMessage={errorMessage}
                                    />
                                )}
                            />
                        );

                    case 'date':
                        return (
                            <Controller
                                key={attribute.id}
                                name={fieldName}
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        label={attribute.attribute}
                                        type="date"
                                        {...field}
                                        value={field.value || ''} // Ensure value is controlled
                                        errorMessage={errorMessage}
                                    />
                                )}
                            />
                        );

                    default: // text and fallback
                        return (
                            <Controller
                                key={attribute.id}
                                name={fieldName}
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        label={attribute.attribute}
                                        type="text"
                                        {...field}
                                        errorMessage={errorMessage}
                                    />
                                )}
                            />
                        );
                }
        };

        // Get all child attributes that depend on this attribute
        const childAttributes = getChildAttributes(attribute.id);
        
        return (
            <div key={attribute.id} className="space-y-4">
                {renderAttributeInput()}
                
                {/* Render child attributes with indentation */}
                {childAttributes.length > 0 && (
                    <div className="ml-4 pl-2 border-l-2 border-gray-200 space-y-4">
                        {childAttributes.map(childAttr => renderAttributeWithChildren(childAttr))}
                    </div>
                )}
            </div>
        );
    };

    // Get all top-level attributes (those without dependencies)
    const topLevelAttributes = categoryAttributes.filter(attr => 
        !hasDependencies(attr) && 
        visibleAttributes.has(attr.id)
    );

    return (
        <div className="grid gap-4">
            {topLevelAttributes.map(attribute => renderAttributeWithChildren(attribute))}
        </div>
    );
};

export default PartAttributes;