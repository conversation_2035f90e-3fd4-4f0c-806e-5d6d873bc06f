// app/logout/page.tsx
'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/app/libs/supabase/client'

export default function LogoutPage() {
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const handleLogout = async () => {
      try {
        // Clear Supabase session
        const { error } = await supabase.auth.signOut()

        if (error) {
          console.error('Logout error:', error)
        }

        // Clear custom auth cookies
        const { removeUserCookie } = await import('@/app/utils/cookies');
        await removeUserCookie();

        // Clear any other auth-related data
        if (typeof window !== 'undefined') {
          localStorage.clear();
          sessionStorage.clear();
        }

        // Force a hard refresh to clear any cached session state
        window.location.href = '/login?logout=true'
      } catch (error) {
        console.error('Logout error:', error)

        // Force logout even if there's an error
        try {
          const { removeUserCookie } = await import('@/app/utils/cookies');
          await removeUserCookie();
        } catch (cookieError) {
          console.error('Error clearing cookies:', cookieError);
        }

        window.location.href = '/login?logout=true'
      }
    }

    handleLogout()
  }, [router, supabase])

  return (
    <div className="flex h-screen items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Logging out...</h1>
        <p className="text-gray-600">Please wait while we securely sign you out</p>
      </div>
    </div>
  )
}