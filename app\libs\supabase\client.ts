//@/app/libs/supabase/client.ts

import { createBrowserClient } from '@supabase/ssr'
import { Database } from '@/app/types/database'

export const createClient = () => {
    const client = createBrowserClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
            global: {
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            }
        }
    );
    
    return client;
}