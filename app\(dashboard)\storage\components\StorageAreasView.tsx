'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Search, RefreshCw, Edit, Trash2, Warehouse, MapPin, Home, ChevronDown, ChevronUp, Layers, Grid, Clipboard, Truck, Box } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { StorageAreaWithUnits, StorageUnit, StorageArea } from '../types';
import AddAreaModal from './modals/AddAreaModal';
import EditAreaModal from './modals/EditAreaModal';
import DeleteConfirmModal from './modals/DeleteConfirmModal';
import AddUnitModal from './modals/AddUnitModal';
import EditUnitModal from './modals/EditUnitModal';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

interface StorageAreasViewProps {
  onRefresh: () => void;
}

const StorageAreasView: React.FC<StorageAreasViewProps> = ({ onRefresh }) => {
  const [areasWithUnits, setAreasWithUnits] = useState<StorageAreaWithUnits[]>([]);
  const [filteredAreas, setFilteredAreas] = useState<StorageAreaWithUnits[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedAreas, setExpandedAreas] = useState<Record<number, boolean>>({});

  // Modals state
  const [isAddAreaModalOpen, setIsAddAreaModalOpen] = useState(false);
  const [isEditAreaModalOpen, setIsEditAreaModalOpen] = useState(false);
  const [isDeleteAreaModalOpen, setIsDeleteAreaModalOpen] = useState(false);
  const [selectedArea, setSelectedArea] = useState<StorageArea | null>(null);

  const [isAddUnitModalOpen, setIsAddUnitModalOpen] = useState(false);
  const [isEditUnitModalOpen, setIsEditUnitModalOpen] = useState(false);
  const [isDeleteUnitModalOpen, setIsDeleteUnitModalOpen] = useState(false);
  const [selectedUnit, setSelectedUnit] = useState<StorageUnit | null>(null);
  const [selectedAreaForUnit, setSelectedAreaForUnit] = useState<number | null>(null);

  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const supabase = createClient();

  // Fetch storage areas and units
  useEffect(() => {
    const fetchAreasAndUnits = async () => {
      setIsLoading(true);
      try {
        // Fetch areas
        const { data: areasData, error: areasError } = await supabase
          .from('storage_areas')
          .select('*')
          .order('name');

        if (areasError) throw areasError;

        // Fetch units
        const { data: unitsData, error: unitsError } = await supabase
          .from('storage_units')
          .select('*')
          .order('identifier');

        if (unitsError) throw unitsError;

        // Group units by area
        const areasWithTheirUnits: StorageAreaWithUnits[] = areasData?.map(area => {
          const areaUnits = unitsData?.filter(unit => unit.area_id === area.area_id) || [];
          return {
            ...area,
            units: areaUnits,
            isExpanded: expandedAreas[area.area_id] || false
          };
        }) || [];

        setAreasWithUnits(areasWithTheirUnits);
        setFilteredAreas(areasWithTheirUnits);
      } catch (error) {
        console.error('Error fetching storage data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAreasAndUnits();
  }, [refreshTrigger, supabase]);

  // Filter areas and units based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredAreas(areasWithUnits);
      return;
    }

    const query = searchQuery.toLowerCase();

    // Filter areas that match the query
    const areasMatching = areasWithUnits.filter(
      area =>
        area.name.toLowerCase().includes(query) ||
        area.description?.toLowerCase().includes(query) ||
        area.location_type.toLowerCase().includes(query) ||
        area.level.toLowerCase().includes(query)
    );

    // Filter areas that have units matching the query
    const areasWithMatchingUnits = areasWithUnits.filter(area => {
      const matchingUnits = area.units.filter(
        unit =>
          unit.identifier.toLowerCase().includes(query) ||
          unit.description?.toLowerCase().includes(query) ||
          unit.unit_type.toLowerCase().includes(query)
      );
      return matchingUnits.length > 0;
    });

    // Combine and deduplicate
    const combinedAreas = [...areasMatching];

    areasWithMatchingUnits.forEach(area => {
      if (!combinedAreas.some(a => a.area_id === area.area_id)) {
        // Filter units within this area
        const filteredUnits = area.units.filter(
          unit =>
            unit.identifier.toLowerCase().includes(query) ||
            unit.description?.toLowerCase().includes(query) ||
            unit.unit_type.toLowerCase().includes(query)
        );

        combinedAreas.push({
          ...area,
          units: filteredUnits,
          isExpanded: true // Auto-expand areas with matching units
        });
      }
    });

    setFilteredAreas(combinedAreas);
  }, [searchQuery, areasWithUnits]);

  // Handle refresh with optional targeted updates
  const handleRefresh = async (updatedItem?: any, itemType?: 'area' | 'unit', action?: 'update' | 'add' | 'delete') => {
    if (updatedItem && itemType) {
      // Handle targeted refresh for a specific area or unit
      if (itemType === 'area') {
        if (action === 'add') {
          // Add a new area
          const newArea: StorageAreaWithUnits = {
            ...updatedItem,
            units: [],
            isExpanded: false
          };

          setFilteredAreas(prev => [newArea, ...prev]);
          setAreasWithUnits(prev => [newArea, ...prev]);
        } else if (action === 'delete') {
          // Delete an area
          setFilteredAreas(prev => prev.filter(area => area.area_id !== updatedItem.area_id));
          setAreasWithUnits(prev => prev.filter(area => area.area_id !== updatedItem.area_id));
        } else {
          // Update a specific area
          setFilteredAreas(prev =>
            prev.map(area =>
              area.area_id === updatedItem.area_id
                ? {
                    ...area,
                    ...updatedItem,
                    // Preserve the units and expanded state
                    units: area.units,
                    isExpanded: area.isExpanded
                  }
                : area
            )
          );

          setAreasWithUnits(prev =>
            prev.map(area =>
              area.area_id === updatedItem.area_id
                ? {
                    ...area,
                    ...updatedItem,
                    // Preserve the units and expanded state
                    units: area.units,
                    isExpanded: area.isExpanded
                  }
                : area
            )
          );
        }
      } else if (itemType === 'unit') {
        if (action === 'add') {
          // Add a new unit to an area
          setFilteredAreas(prev =>
            prev.map(area => {
              if (area.area_id === updatedItem.area_id) {
                return {
                  ...area,
                  units: [...area.units, updatedItem],
                  isExpanded: true // Auto-expand the area when adding a unit
                };
              }
              return area;
            })
          );

          setAreasWithUnits(prev =>
            prev.map(area => {
              if (area.area_id === updatedItem.area_id) {
                return {
                  ...area,
                  units: [...area.units, updatedItem],
                  isExpanded: true // Auto-expand the area when adding a unit
                };
              }
              return area;
            })
          );

          // Update expanded areas state
          setExpandedAreas(prev => ({
            ...prev,
            [updatedItem.area_id]: true
          }));
        } else if (action === 'delete') {
          // Delete a unit
          setFilteredAreas(prev =>
            prev.map(area => {
              if (area.area_id === updatedItem.area_id) {
                return {
                  ...area,
                  units: area.units.filter(unit => unit.unit_id !== updatedItem.unit_id)
                };
              }
              return area;
            })
          );

          setAreasWithUnits(prev =>
            prev.map(area => {
              if (area.area_id === updatedItem.area_id) {
                return {
                  ...area,
                  units: area.units.filter(unit => unit.unit_id !== updatedItem.unit_id)
                };
              }
              return area;
            })
          );
        } else {
          // Update a specific unit
          setFilteredAreas(prev =>
            prev.map(area => {
              if (area.area_id === updatedItem.area_id) {
                return {
                  ...area,
                  units: area.units.map(unit =>
                    unit.unit_id === updatedItem.unit_id ? { ...unit, ...updatedItem } : unit
                  )
                };
              }
              return area;
            })
          );

          setAreasWithUnits(prev =>
            prev.map(area => {
              if (area.area_id === updatedItem.area_id) {
                return {
                  ...area,
                  units: area.units.map(unit =>
                    unit.unit_id === updatedItem.unit_id ? { ...unit, ...updatedItem } : unit
                  )
                };
              }
              return area;
            })
          );
        }
      }
    } else {
      // Full refresh if no specific item
      setRefreshTrigger(prev => prev + 1);
      onRefresh();
    }
  };

  // Toggle area expansion
  const toggleAreaExpanded = (e: React.MouseEvent, areaId: number) => {
    e.preventDefault();
    e.stopPropagation();

    // Update expandedAreas state
    setExpandedAreas(prev => {
      const newState = {
        ...prev,
        [areaId]: !prev[areaId]
      };

      // Update filteredAreas directly to avoid a full re-render
      setFilteredAreas(currentAreas =>
        currentAreas.map(area =>
          area.area_id === areaId
            ? { ...area, isExpanded: !prev[areaId] }
            : area
        )
      );

      return newState;
    });
  };

  // Handle area edit
  const handleEditArea = (area: StorageArea) => {
    setSelectedArea(area);
    setIsEditAreaModalOpen(true);
  };

  // Handle area delete
  const handleDeleteArea = (area: StorageArea) => {
    setSelectedArea(area);
    setIsDeleteAreaModalOpen(true);
  };

  // Handle add unit to specific area
  const handleAddUnitToArea = (areaId: number) => {
    setSelectedAreaForUnit(areaId);
    setIsAddUnitModalOpen(true);
  };

  // Handle unit edit
  const handleEditUnit = (unit: StorageUnit) => {
    setSelectedUnit(unit);
    setIsEditUnitModalOpen(true);
  };

  // Handle unit delete
  const handleDeleteUnit = (unit: StorageUnit) => {
    setSelectedUnit(unit);
    setIsDeleteUnitModalOpen(true);
  };

  // Get icon for unit type
  const getUnitTypeIcon = (unitType: string) => {
    switch (unitType) {
      case 'shelf':
        return <Layers size={20} className="text-teal-600" />;
      case 'cage':
        return <Grid size={20} className="text-orange-600" />;
      case 'hanging_line':
        return <Clipboard size={20} className="text-gray-600" />;
      case 'open_space':
        return <Truck size={20} className="text-teal-600" />;
      case 'engine_area':
        return <Box size={20} className="text-orange-600" />;
      default:
        return <Box size={20} className="text-gray-600" />;
    }
  };

  // Format unit type for display
  const formatUnitType = (unitType: string) => {
    switch (unitType) {
      case 'shelf':
        return 'Shelf';
      case 'cage':
        return 'Cage';
      case 'hanging_line':
        return 'Hanging Line';
      case 'open_space':
        return 'Open Space';
      case 'engine_area':
        return 'Engine Area';
      default:
        return unitType;
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3
      }
    },
    hover: {
      y: -5,
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      transition: {
        duration: 0.2
      }
    }
  };

  const childrenVariants = {
    hidden: {
      opacity: 0,
      height: 0,
      overflow: 'hidden',
      transition: {
        duration: 0.3,
        ease: "easeInOut"
      }
    },
    visible: {
      opacity: 1,
      height: 'auto',
      transition: {
        duration: 0.5,
        ease: "easeInOut",
        staggerChildren: 0.1
      }
    }
  };

  const unitVariants = {
    hidden: {
      opacity: 0,
      x: -20,
      scale: 0.95,
      transition: {
        duration: 0.2
      }
    },
    visible: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
        mass: 0.5
      }
    }
  };

  return (
    <div>
      {/* Search and Actions */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
        <div className="relative w-full md:w-96">
          <input
            type="text"
            placeholder="Search storage areas and units..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
        </div>

        <div className="flex gap-2 w-full md:w-auto">
          <button
            onClick={handleRefresh}
            className="flex items-center gap-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
          >
            <RefreshCw size={18} />
            <span>Refresh</span>
          </button>

          <button
            onClick={() => setIsAddAreaModalOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <Plus size={18} />
            <span>Add Area</span>
          </button>
        </div>
      </div>

      {/* Areas Grid */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size={40} />
        </div>
      ) : filteredAreas.length > 0 ? (
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {filteredAreas.map((area) => (
            <motion.div
              key={area.area_id}
              className="bg-white rounded-lg shadow-md overflow-hidden"
              variants={itemVariants}
              whileHover="hover"
            >
              <div className="p-5 border-b border-gray-100">
                <div className="flex justify-between items-start mb-3">
                  <div className="flex items-center">
                    <div className={`p-2 rounded-md mr-3 ${
                      area.location_type === 'indoor'
                        ? 'bg-teal-100 text-teal-600'
                        : 'bg-orange-100 text-orange-600'
                    }`}>
                      {area.location_type === 'indoor' ? <Home size={20} /> : <MapPin size={20} />}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-800">{area.name}</h3>
                      <p className="text-sm text-gray-500">{area.location_type} - {area.level}</p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleAddUnitToArea(area.area_id)}
                      className="p-1.5 rounded-md hover:bg-gray-100 text-gray-500 hover:text-teal-600 transition-colors"
                      aria-label="Add unit to area"
                      title="Add unit to this area"
                    >
                      <Plus size={16} />
                    </button>
                    <button
                      onClick={() => handleEditArea(area)}
                      className="p-1.5 rounded-md hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
                      aria-label="Edit area"
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={() => handleDeleteArea(area)}
                      className="p-1.5 rounded-md hover:bg-gray-100 text-gray-500 hover:text-red-500 transition-colors"
                      aria-label="Delete area"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2 mb-3">
                  <div className="bg-gray-50 p-2 rounded-md">
                    <p className="text-xs text-gray-500">Location Type</p>
                    <p className={`text-sm font-medium ${
                      area.location_type === 'indoor' ? 'text-teal-600' : 'text-orange-600'
                    }`}>
                      {area.location_type === 'indoor' ? 'Indoor' : 'Outdoor'}
                    </p>
                  </div>
                  <div className="bg-gray-50 p-2 rounded-md">
                    <p className="text-xs text-gray-500">Level</p>
                    <p className="text-sm font-medium text-gray-700">
                      {area.level === 'upstairs' ? 'Upstairs' :
                       area.level === 'downstairs' ? 'Downstairs' : 'Ground Floor'}
                    </p>
                  </div>
                </div>

                {area.description && (
                  <div className="bg-gray-50 p-2 rounded-md mb-3">
                    <p className="text-xs text-gray-500">Description</p>
                    <p className="text-sm text-gray-700 line-clamp-2">{area.description}</p>
                  </div>
                )}

                {/* Units Section */}
                <div className="bg-gray-50 px-4 py-3 rounded-md mt-2">
                  <button
                    onClick={(e) => toggleAreaExpanded(e, area.area_id)}
                    className="flex items-center justify-between w-full text-left text-sm font-medium text-gray-700 hover:text-teal-600 transition-colors"
                  >
                    <span>Storage Units ({area.units.length})</span>
                    {expandedAreas[area.area_id] ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
                  </button>

                  <AnimatePresence initial={false}>
                    {expandedAreas[area.area_id] && (
                      <motion.div
                        key={`units-${area.area_id}`}
                        variants={childrenVariants}
                        initial="hidden"
                        animate="visible"
                        exit="hidden"
                        className="mt-3 space-y-2"
                        layout
                      >
                        {area.units.length > 0 ? (
                          <motion.div className="space-y-2">
                            {area.units.map((unit) => (
                              <motion.div
                                key={unit.unit_id}
                                className="bg-white p-3 rounded-md border border-gray-200 shadow-sm"
                                variants={unitVariants}
                                initial="hidden"
                                animate="visible"
                                layout
                              >
                                <div className="flex justify-between items-start">
                                  <div className="flex items-center">
                                    <div className="p-1.5 rounded-md mr-2 bg-gray-100">
                                      {getUnitTypeIcon(unit.unit_type)}
                                    </div>
                                    <div>
                                      <h4 className="font-medium text-gray-800">{unit.identifier}</h4>
                                      <p className="text-xs text-gray-500">{formatUnitType(unit.unit_type)}</p>
                                    </div>
                                  </div>
                                  <div className="flex space-x-1">
                                    <button
                                      onClick={() => handleEditUnit(unit)}
                                      className="p-1 rounded-md hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
                                      aria-label="Edit unit"
                                    >
                                      <Edit size={14} />
                                    </button>
                                    <button
                                      onClick={() => handleDeleteUnit(unit)}
                                      className="p-1 rounded-md hover:bg-gray-100 text-gray-500 hover:text-red-500 transition-colors"
                                      aria-label="Delete unit"
                                    >
                                      <Trash2 size={14} />
                                    </button>
                                  </div>
                                </div>
                                {unit.description && (
                                  <div className="mt-1 text-xs text-gray-600">
                                    {unit.description}
                                  </div>
                                )}
                              </motion.div>
                            ))}
                          </motion.div>
                        ) : (
                          <div className="text-center py-3 text-sm text-gray-500">
                            No storage units in this area.
                            <button
                              onClick={() => handleAddUnitToArea(area.area_id)}
                              className="ml-2 text-teal-600 hover:text-teal-800 font-medium"
                            >
                              Add Unit
                            </button>
                          </div>
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      ) : (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <h3 className="text-xl font-semibold text-gray-700 mb-2">No storage areas found</h3>
          <p className="text-gray-500 mb-4">
            {searchQuery ? 'No areas match your search criteria.' : 'Start by adding a new storage area.'}
          </p>
          <button
            onClick={() => setIsAddAreaModalOpen(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <Plus size={18} />
            <span>Add Storage Area</span>
          </button>
        </div>
      )}

      {/* Add Area Modal */}
      <AddAreaModal
        isOpen={isAddAreaModalOpen}
        onClose={() => setIsAddAreaModalOpen(false)}
        onSuccess={handleRefresh}
      />

      {/* Edit Area Modal */}
      {selectedArea && (
        <EditAreaModal
          isOpen={isEditAreaModalOpen}
          onClose={() => setIsEditAreaModalOpen(false)}
          area={selectedArea}
          onSuccess={handleRefresh}
        />
      )}

      {/* Delete Area Confirmation Modal */}
      {selectedArea && (
        <DeleteConfirmModal
          isOpen={isDeleteAreaModalOpen}
          onClose={() => setIsDeleteAreaModalOpen(false)}
          itemId={selectedArea.area_id}
          itemName={selectedArea.name}
          itemType="area"
          onSuccess={handleRefresh}
        />
      )}

      {/* Add Unit Modal */}
      <AddUnitModal
        isOpen={isAddUnitModalOpen}
        onClose={() => setIsAddUnitModalOpen(false)}
        onSuccess={handleRefresh}
        areas={filteredAreas}
        preselectedAreaId={selectedAreaForUnit}
      />

      {/* Edit Unit Modal */}
      {selectedUnit && (
        <EditUnitModal
          isOpen={isEditUnitModalOpen}
          onClose={() => setIsEditUnitModalOpen(false)}
          unit={selectedUnit}
          onSuccess={handleRefresh}
          areas={filteredAreas}
        />
      )}

      {/* Delete Unit Confirmation Modal */}
      {selectedUnit && (
        <DeleteConfirmModal
          isOpen={isDeleteUnitModalOpen}
          onClose={() => setIsDeleteUnitModalOpen(false)}
          itemId={selectedUnit.unit_id}
          itemName={selectedUnit.identifier}
          itemType="unit"
          onSuccess={handleRefresh}
        />
      )}
    </div>
  );
};

export default StorageAreasView;
