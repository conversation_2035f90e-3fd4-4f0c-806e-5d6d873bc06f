'use client';

import { useCallback } from 'react';
import { backgroundSync } from '@/app/utils/backgroundSync';
import { toast } from 'react-hot-toast';

type SyncType = 'cart' | 'favorites' | 'orders';

interface UseBackgroundSyncOptions {
  type: SyncType;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export function useBackgroundSync({ type, onSuccess, onError }: UseBackgroundSyncOptions) {
  const sync = useCallback(
    async (data: any) => {
      try {
        const taskId = `${type}-${Date.now()}`;
        await backgroundSync.addSyncTask({
          id: taskId,
          type,
          data,
          timestamp: Date.now(),
        });

        onSuccess?.();
      } catch (error) {
        console.error('Background sync failed:', error);
        toast.error('Failed to sync changes');
        onError?.(error instanceof Error ? error : new Error('Sync failed'));
      }
    },
    [type, onSuccess, onError]
  );

  return { sync };
} 