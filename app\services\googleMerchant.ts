import { google } from 'googleapis';
import { OAuth2Client, Credentials } from 'google-auth-library';
import { createClient } from '@/app/libs/supabase/client';
import { createAdminClient } from '@/app/libs/supabase/admin';

/**
 * Interface for Google Merchant API product data
 * @see https://developers.google.com/shopping-content/reference/rest/v2.1/products#Product
 */
export interface ProductData {
  offerId: string;
  title: string;
  description: string;
  link: string;
  imageLink: string;
  condition: string;
  availability: string;
  price: {
    value: number;
    currency: string;
  };
  gtin?: string;
  brand?: string;
  mpn?: string;
  additionalImageLinks?: string[];
  customLabel0?: string;
  customLabel1?: string;
  customLabel2?: string;
  customLabel3?: string;
  customLabel4?: string;
  // Required fields for Google Merchant API
  channel: string;
  contentLanguage: string;
  targetCountry: string;
  // Additional fields that might be useful
  googleProductCategory?: string;
  productTypes?: string[];
  identifierExists?: boolean;
  shippingWeight?: {
    value: number;
    unit: string;
  };
}

/**
 * Interface for token storage in database
 */
interface TokenStorage {
  access_token: string;
  refresh_token: string;
  expiry_date: number;
  merchant_id: string;
}

/**
 * Interface for sync status
 */
export interface SyncStatus {
  total: number;
  processed: number;
  succeeded: number;
  failed: number;
  errors: Array<{
    partId: string;
    error: string;
  }>;
  lastSync: Date;
  syncId?: string; // Unique ID for this sync operation
}

export class GoogleMerchantService {
  private readonly auth: OAuth2Client;
  private readonly shoppingContent: any;
  private merchantId: string | null = null;
  private supabase = createClient();
  private adminSupabase = createAdminClient();

  constructor() {
    // Use a default redirect URI that will be overridden when authenticating
    // Use specific Google Merchant API environment variables
    const redirectUri = process.env.GOOGLE_MERCHANT_API_REDIRECT_URI || 'http://localhost:3000/api/google/callback';
    const clientId = process.env.GOOGLE_MERCHANT_API_CLIENT_ID;
    const clientSecret = process.env.GOOGLE_MERCHANT_API_CLIENT_SECRET;

    if (!clientId) {
      console.error('Google Merchant API Client ID not found in environment variables');
    }

    if (!clientSecret) {
      console.error('Google Merchant API Client Secret not found in environment variables');
    }

    console.log('Using Google Merchant API credentials:');
    console.log('- Client ID:', clientId ? 'Found' : 'Not found');
    console.log('- Client Secret:', clientSecret ? 'Found' : 'Not found');
    console.log('- Redirect URI:', redirectUri);

    this.auth = new OAuth2Client(
      clientId,
      clientSecret,
      redirectUri
    );

    this.shoppingContent = google.content({
      version: 'v2.1',
      auth: this.auth
    });
  }

  /**
   * Set credentials from stored tokens
   */
  async loadStoredCredentials(): Promise<boolean> {
    try {
      // Get current user if available
      let userId = null;
      try {
        const { data: userData } = await this.supabase.auth.getUser();
        if (userData?.user?.id) {
          userId = userData.user.id;
          console.log('Found user ID from session:', userId);
        }
      } catch (sessionError) {
        console.error('Error getting user session:', sessionError);
        // Continue without user ID
      }

      // First try localStorage in browser environment
      if (typeof window !== 'undefined') {
        try {
          const localStorageData = localStorage.getItem('google_merchant_tokens');
          if (localStorageData) {
            const parsedData = JSON.parse(localStorageData);
            console.log('Found tokens in localStorage:', !!parsedData);

            if (parsedData && parsedData.merchant_id) {
              // Set credentials
              this.auth.setCredentials({
                access_token: parsedData.access_token || '',
                refresh_token: parsedData.refresh_token || '',
                expiry_date: parsedData.expiry_date || 0
              });

              // Set merchant ID
              this.merchantId = parsedData.merchant_id;
              console.log('Using merchant ID from localStorage:', this.merchantId);

              // Set auth success flag if not already set
              localStorage.setItem('googleAuthSuccess', 'true');
              localStorage.setItem('googleMerchantId', this.merchantId || '');

              return true;
            }
          }
        } catch (localStorageError) {
          console.warn('Error reading from localStorage:', localStorageError);
        }
      }

      // Try to load from environment variables first
      const envMerchantId = process.env.GOOGLE_MERCHANT_ID || process.env.GOOGLE_MERCHANT_API_MERCHANT_ID;
      if (envMerchantId) {
        console.log('Using merchant ID from environment variable:', envMerchantId);
        this.merchantId = envMerchantId;

        // If we're in a browser context, store this in localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('googleMerchantId', envMerchantId);
          localStorage.setItem('googleAuthSuccess', 'true');
        }

        // If we have an access token in the auth object, consider authenticated
        if (this.auth.credentials.access_token) {
          return true;
        }
      }

      // Then try database - first try user-specific credentials
      let dbCredentials = null;

      try {
        if (userId) {
          // Try to find user-specific credentials
          const { data: userData, error: userError } = await this.supabase
            .from('google_merchant_tokens')
            .select('*')
            .eq('user_id', userId)
            .maybeSingle();

          if (!userError && userData) {
            dbCredentials = userData;
            console.log('Found user-specific credentials in database');
          }
        }

        // If no user-specific credentials, try global credentials
        if (!dbCredentials) {
          const { data: globalData, error: globalError } = await this.supabase
            .from('google_merchant_tokens')
            .select('*')
            .is('user_id', null)
            .maybeSingle();

          if (!globalError && globalData) {
            dbCredentials = globalData;
            console.log('Found global credentials in database');
          }
        }

        // If still no credentials, try any credentials
        if (!dbCredentials) {
          const { data: anyData, error: anyError } = await this.supabase
            .from('google_merchant_tokens')
            .select('*')
            .order('updated_at', { ascending: false })
            .limit(1)
            .maybeSingle();

          if (!anyError && anyData) {
            dbCredentials = anyData;
            console.log('Found most recent credentials in database');
          }
        }
      } catch (dbError) {
        console.error('Error querying database for credentials:', dbError);
        // Continue without database credentials
      }

      if (!dbCredentials) {
        console.error('No stored Google Merchant credentials found in database');
        return false;
      }

      // Set credentials
      this.auth.setCredentials({
        access_token: dbCredentials.access_token,
        refresh_token: dbCredentials.refresh_token,
        expiry_date: dbCredentials.expiry_date
      });

      // Set merchant ID
      this.merchantId = dbCredentials.merchant_id;
      console.log('Using merchant ID from database:', this.merchantId);

      // Store in localStorage for future use
      if (typeof window !== 'undefined') {
        try {
          const tokenData = {
            access_token: dbCredentials.access_token,
            refresh_token: dbCredentials.refresh_token,
            expiry_date: dbCredentials.expiry_date,
            merchant_id: dbCredentials.merchant_id,
            user_id: dbCredentials.user_id
          };

          // Convert token data to string before storing
          const tokenString = JSON.stringify(tokenData);
          localStorage.setItem('google_merchant_tokens', tokenString);
          localStorage.setItem('googleAuthSuccess', 'true');
          localStorage.setItem('googleMerchantId', this.merchantId || '');
        } catch (storageError) {
          console.error('Error storing tokens in localStorage:', storageError);
        }
      }

      return true;
    } catch (error) {
      console.error('Error loading stored credentials:', error);
      return false;
    }
  }

  /**
   * Store tokens in database
   */
  async storeCredentials(tokens: Credentials, merchantId: string): Promise<void> {
    try {
      console.log('Storing credentials for merchant ID:', merchantId);

      // Get current user session if available
      let userId = null;
      try {
        const { data: sessionData } = await this.supabase.auth.getSession();
        if (sessionData?.session?.user?.id) {
          userId = sessionData.session.user.id;
          console.log('Found user ID from session:', userId);
        }
      } catch (sessionError) {
        console.error('Error getting user session:', sessionError);
        // Continue without user ID
      }

      // Prepare token data
      const tokenData = {
        access_token: tokens.access_token || '',
        refresh_token: tokens.refresh_token || '',
        expiry_date: tokens.expiry_date || 0,
        merchant_id: merchantId,
        user_id: userId,
        updated_at: new Date().toISOString()
      };

      // Always store in localStorage first for immediate access
      if (typeof window !== 'undefined') {
        try {
          localStorage.setItem('google_merchant_tokens', JSON.stringify(tokenData));
          localStorage.setItem('googleAuthSuccess', 'true');
          localStorage.setItem('googleMerchantId', merchantId);
          console.log('Stored credentials in localStorage');
        } catch (localStorageError) {
          console.warn('Error storing in localStorage:', localStorageError);
        }
      }

      // Then try to store in database
      try {
        // First check if we already have a record for this user
        let existingRecord = null;

        if (userId) {
          // Try to find a record for this user
          const { data: userData, error: userError } = await this.supabase
            .from('google_merchant_tokens')
            .select('id')
            .eq('user_id', userId)
            .maybeSingle();

          if (!userError && userData) {
            existingRecord = userData;
          }
        }

        // If no user-specific record, try to find a global record
        if (!existingRecord) {
          const { data: globalData, error: globalError } = await this.supabase
            .from('google_merchant_tokens')
            .select('id')
            .is('user_id', null)
            .maybeSingle();

          if (!globalError && globalData) {
            existingRecord = globalData;
          }
        }

        if (!existingRecord) {
          // Insert new record
          const { error: insertError } = await this.supabase
            .from('google_merchant_tokens')
            .insert([tokenData]);

          if (insertError) {
            console.error('Error inserting token:', insertError);
          } else {
            console.log('Inserted new credentials in database');
          }
        } else {
          // Update existing record
          const { error: updateError } = await this.supabase
            .from('google_merchant_tokens')
            .update(tokenData)
            .eq('id', existingRecord.id);

          if (updateError) {
            console.error('Error updating token:', updateError);
          } else {
            console.log('Updated existing credentials in database');
          }
        }
      } catch (dbError) {
        console.error('Database operation failed:', dbError);
        // Continue anyway, we've already stored in localStorage
      }

      // Set the merchant ID in the current instance
      this.merchantId = merchantId;
    } catch (error) {
      console.error('Error storing credentials:', error);

      // Don't throw, just log the error
      console.warn('Continuing despite error storing credentials');
    }
  }

  /**
   * Authenticate with Google
   */
  async authenticate(code: string, redirectUri?: string): Promise<string> {
    try {
      // If a redirect URI is provided, create a new auth instance
      if (redirectUri) {
        // Use specific Google Merchant API environment variables
        const clientId = process.env.GOOGLE_MERCHANT_API_CLIENT_ID;
        const clientSecret = process.env.GOOGLE_MERCHANT_API_CLIENT_SECRET;

        if (!clientId || !clientSecret) {
          console.error('Google Merchant API credentials not found in environment variables');
          throw new Error('Google Merchant API credentials not configured');
        }

        // Create a new OAuth2Client instance
        const newAuth = new OAuth2Client(
          clientId,
          clientSecret,
          redirectUri
        );

        try {
          console.log('Exchanging authorization code for tokens...');
          // Use the new auth instance for this operation
          const { tokens } = await newAuth.getToken(code);

          console.log('Token exchange successful');

          // Convert tokens to a plain object
          const credentials = {
            access_token: tokens.access_token,
            refresh_token: tokens.refresh_token,
            expiry_date: tokens.expiry_date
          };

          console.log('Access token received:', tokens.access_token ? 'Yes' : 'No');
          console.log('Refresh token received:', tokens.refresh_token ? 'Yes' : 'No');
          console.log('Expiry date:', tokens.expiry_date);

          // Set credentials on both the new auth instance and our original auth
          newAuth.setCredentials(credentials);
          this.auth.setCredentials(credentials);
        } catch (tokenError) {
          console.error('Error exchanging code for tokens:', tokenError);

          if (tokenError instanceof Error && tokenError.message.includes('invalid_grant')) {
            throw new Error('Authorization code expired or invalid. Please try authenticating again.');
          }

          throw tokenError;
        }

        // We can't reassign readonly property, but we can create a new instance with the updated auth
        // The original this.shoppingContent will still work with the updated auth credentials
      }

      // Skip this section if we already exchanged the code above
      if (!this.auth.credentials.access_token) {
        console.log('Authenticating with code...');

        try {
          // Exchange code for tokens
          const { tokens } = await this.auth.getToken(code);

          console.log('Token exchange successful');

          // Convert tokens to a plain object before setting
          const credentials = {
            access_token: tokens.access_token,
            refresh_token: tokens.refresh_token,
            expiry_date: tokens.expiry_date
          };

          console.log('Access token received:', tokens.access_token ? 'Yes' : 'No');
          console.log('Refresh token received:', tokens.refresh_token ? 'Yes' : 'No');
          console.log('Expiry date:', tokens.expiry_date);

          this.auth.setCredentials(credentials);
        } catch (tokenError) {
          console.error('Error exchanging code for tokens:', tokenError);

          if (tokenError instanceof Error && tokenError.message.includes('invalid_grant')) {
            throw new Error('Authorization code expired or invalid. Please try authenticating again.');
          }

          throw tokenError;
        }
      } else {
        console.log('Skipping token exchange - already have access token');
      }

      // Get merchant ID from environment variables
      try {
        // Get the current credentials
        const currentCredentials = {
          access_token: this.auth.credentials.access_token,
          refresh_token: this.auth.credentials.refresh_token,
          expiry_date: this.auth.credentials.expiry_date
        };

        // Skip API call and use environment variable directly
        console.log('Using merchant ID from environment variable');

        // Fallback to environment variable
        const envMerchantId = process.env.GOOGLE_MERCHANT_ID || process.env.GOOGLE_MERCHANT_API_MERCHANT_ID;

        if (!envMerchantId) {
          throw new Error('No merchant ID found in environment variables');
        }

        console.log('Using merchant ID from environment variable:', envMerchantId);

        // Store tokens and merchant ID
        await this.storeCredentials(currentCredentials, envMerchantId);
        this.merchantId = envMerchantId;

        return envMerchantId;
      } catch (error) {
        console.error('Error getting merchant ID:', error);
        throw new Error('Failed to get merchant ID: ' + (error instanceof Error ? error.message : 'Unknown error'));
      }
    } catch (error) {
      console.error('Error authenticating with Google:', error);
      throw error;
    }
  }

  /**
   * Check if authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      console.log('Checking authentication status...');
      console.log('Current credentials:', {
        hasAccessToken: !!this.auth.credentials.access_token,
        hasRefreshToken: !!this.auth.credentials.refresh_token,
        expiryDate: this.auth.credentials.expiry_date,
        merchantId: this.merchantId
      });

      // First, try to get merchant ID from environment variables
      const envMerchantId = process.env.GOOGLE_MERCHANT_ID || process.env.GOOGLE_MERCHANT_API_MERCHANT_ID;
      if (envMerchantId && !this.merchantId) {
        console.log('Using merchant ID from environment variable:', envMerchantId);
        this.merchantId = envMerchantId;

        // If we're in a browser context, store this in localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('googleMerchantId', envMerchantId);
          localStorage.setItem('googleAuthSuccess', 'true');
        }
      }

      // Check localStorage for authentication data
      if (typeof window !== 'undefined') {
        try {
          const authSuccess = localStorage.getItem('googleAuthSuccess') === 'true';
          if (authSuccess) {
            console.log('Found successful authentication flag in localStorage');

            // Get merchant ID from localStorage or use default
            const storedMerchantId = localStorage.getItem('googleMerchantId');
            if (storedMerchantId) {
              this.merchantId = storedMerchantId;
              console.log('Using merchant ID from localStorage:', this.merchantId);

              // If we have tokens in localStorage, set them
              const storedTokens = localStorage.getItem('google_merchant_tokens');
              if (storedTokens) {
                try {
                  const tokenData = JSON.parse(storedTokens);
                  if (tokenData && tokenData.access_token) {
                    this.auth.setCredentials({
                      access_token: tokenData.access_token,
                      refresh_token: tokenData.refresh_token,
                      expiry_date: tokenData.expiry_date
                    });
                    console.log('Using tokens from localStorage');

                    // If we have both merchant ID and access token, consider authenticated
                    if (this.merchantId && this.auth.credentials.access_token) {
                      return true;
                    }
                  }
                } catch (parseError) {
                  console.error('Error parsing stored tokens:', parseError);
                }
              }

              // In browser context, consider authenticated if we have the flag and merchant ID
              // For development/testing purposes
              if (process.env.NODE_ENV === 'development') {
                console.log('In development mode - considering authenticated with just merchant ID');
                return true;
              }
            }
          }
        } catch (localStorageError) {
          console.warn('Error accessing localStorage:', localStorageError);
        }
      }

      // Load stored credentials if not already loaded
      if (!this.auth.credentials.access_token) {
        console.log('No access token found, loading stored credentials...');
        const loaded = await this.loadStoredCredentials();
        console.log('Credentials loaded:', loaded);
        console.log('Merchant ID after loading:', this.merchantId);

        // If we successfully loaded credentials and have a merchant ID, consider authenticated
        if (loaded && this.merchantId) {
          return true;
        }
      }

      // If we have a merchant ID at this point, consider authenticated for development
      if (this.merchantId && process.env.NODE_ENV === 'development') {
        console.log('Using existing merchant ID in development mode:', this.merchantId);
        return true;
      }

      // For production, require both merchant ID and access token
      if (this.merchantId && this.auth.credentials.access_token) {
        console.log('Using existing merchant ID and access token:', this.merchantId);
        return true;
      }

      // If we have an access token, check if it's expired and try to refresh it
      if (this.auth.credentials.access_token) {
        const isExpired = this.auth.credentials.expiry_date ? this.auth.credentials.expiry_date < Date.now() : false;
        if (isExpired) {
          console.log('Access token is expired, attempting to refresh...');
          try {
            const { credentials } = await this.auth.refreshAccessToken();
            console.log('Token refreshed successfully');
            this.auth.setCredentials(credentials);

            // Store the refreshed token
            if (this.merchantId) {
              await this.storeCredentials(credentials, this.merchantId);
            }

            return true;
          } catch (refreshError) {
            console.error('Failed to refresh token:', refreshError);
          }
        }

        // If we have a valid access token and merchant ID, try to verify with API call
        if (this.merchantId && process.env.NODE_ENV !== 'development') {
          try {
            // Verify authentication by making a simple API call
            console.log('Verifying authentication with API call...');
            const response = await this.shoppingContent.products.list({
              merchantId: this.merchantId,
              maxResults: 1
            });
            console.log('API verification successful');
            return true;
          } catch (apiError) {
            console.error('API verification failed:', {
              message: apiError instanceof Error ? apiError.message : 'Unknown error',
              code: (apiError as any)?.code || 'Unknown code',
              status: (apiError as any)?.status || 'Unknown status'
            });

            // In development mode, still consider authenticated even if API call fails
            if (process.env.NODE_ENV !== 'production') {
              console.log('In development mode - considering authenticated despite API failure');
              return true;
            }
          }
        }
      }

      console.log('Not authenticated - missing merchant ID or valid access token');
      return false;
    } catch (error) {
      console.error('Authentication check failed:', error);
      return false;
    }
  }

  /**
   * Get merchant ID
   */
  getMerchantId(): string {
    if (!this.merchantId) {
      throw new Error('Not authenticated with Google Merchant API');
    }
    return this.merchantId || '';
  }

  /**
   * Create a product
   */
  async createProduct(productData: ProductData): Promise<any> {
    try {
      console.log('Starting createProduct with data:', JSON.stringify(productData, null, 2));

      if (!this.merchantId) {
        console.log('No merchant ID found, loading stored credentials...');
        await this.loadStoredCredentials();
        console.log('Merchant ID after loading credentials:', this.merchantId);
      }

      // Ensure we have credentials set
      if (!this.auth.credentials.access_token) {
        console.log('No access token found in credentials');
        throw new Error('No valid access token found');
      }

      console.log('Making API call to create product with merchant ID:', this.merchantId);
      const response = await this.shoppingContent.products.insert({
        merchantId: this.merchantId,
        resource: productData
      });

      console.log('API Response:', JSON.stringify(response.data, null, 2));
      return response.data;
    } catch (error) {
      console.error('Error creating product:', error);
      console.error('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        code: (error as any)?.code || 'Unknown code',
        status: (error as any)?.status || 'Unknown status',
        response: (error as any)?.response?.data || 'No response data'
      });



      throw error;
    }
  }

  /**
   * Update a product
   */
  async updateProduct(productId: string, productData: ProductData): Promise<any> {
    try {
      if (!this.merchantId) {
        await this.loadStoredCredentials();
      }

      // Ensure we have credentials set
      if (!this.auth.credentials.access_token) {
        console.log('No access token found in credentials');
        throw new Error('No valid access token found');
      }

      const response = await this.shoppingContent.products.update({
        merchantId: this.merchantId,
        productId: productId,
        resource: productData
      });
      return response.data;
    } catch (error) {
      console.error('Error updating product:', error);



      throw error;
    }
  }

  /**
   * Delete a product
   */
  async deleteProduct(productId: string): Promise<void> {
    try {
      if (!this.merchantId) {
        await this.loadStoredCredentials();
      }

      await this.shoppingContent.products.delete({
        merchantId: this.merchantId,
        productId: productId
      });
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  }

  /**
   * List products
   */
  async listProducts(maxResults: number = 100): Promise<any> {
    try {
      if (!this.merchantId) {
        await this.loadStoredCredentials();
      }

      const response = await this.shoppingContent.products.list({
        merchantId: this.merchantId,
        maxResults
      });
      return response.data;
    } catch (error) {
      console.error('Error listing products:', error);
      throw error;
    }
  }

  /**
   * Sync all products (incremental sync based on timestamps)
   */
  async syncAllProducts(forceFullSync: boolean = false): Promise<SyncStatus> {
    try {
      // Create a unique sync ID for this operation
      const syncId = `sync_${Date.now()}`;
      console.log(`Starting incremental sync operation with ID: ${syncId}`);

      if (!this.merchantId) {
        await this.loadStoredCredentials();
      }

      // Ensure we're authenticated
      const isAuthenticated = await this.isAuthenticated();
      if (!isAuthenticated) {
        throw new Error('Not authenticated with Google Merchant API');
      }

      // Get the last sync time
      const lastSyncStatus = await this.getLatestSyncStatus();
      const lastSyncTime = lastSyncStatus?.lastSync;

      console.log('Last sync time:', lastSyncTime ? lastSyncTime.toISOString() : 'Never synced before');
      console.log('Force full sync:', forceFullSync);

      let partsToSync = [];

      if (!lastSyncTime || forceFullSync) {
        // First time sync or forced full sync - sync all parts
        console.log(forceFullSync ? 'Forced full sync - fetching all parts...' : 'First time sync - fetching all parts...');
        const { data: allParts, error } = await this.supabase
          .from('parts')
          .select('*');

        if (error) {
          console.error('Error fetching all parts:', error);
          throw error;
        }

        partsToSync = allParts || [];
        console.log(`First sync: Found ${partsToSync.length} parts to sync`);
      } else {
        // Incremental sync - only sync parts that were created or updated since last sync
        console.log('Incremental sync - fetching updated and new parts...');

        // Get parts updated since last sync
        const { data: updatedParts, error: updateError } = await this.supabase
          .from('parts')
          .select('*')
          .gte('updatedAt', lastSyncTime.toISOString());

        if (updateError) {
          console.error('Error fetching updated parts:', updateError);
          throw updateError;
        }

        // Get parts created since last sync (in case updatedAt is null for some reason)
        const { data: newParts, error: createError } = await this.supabase
          .from('parts')
          .select('*')
          .gte('createdAt', lastSyncTime.toISOString());

        if (createError) {
          console.error('Error fetching new parts:', createError);
          throw createError;
        }

        // Combine and deduplicate parts
        const combinedParts = [...(updatedParts || []), ...(newParts || [])];
        const uniqueParts = combinedParts.filter((part, index, self) =>
          index === self.findIndex(p => p.id === part.id)
        );

        partsToSync = uniqueParts;
        console.log(`Incremental sync: Found ${updatedParts?.length || 0} updated parts and ${newParts?.length || 0} new parts`);
        console.log(`Total unique parts to sync: ${partsToSync.length}`);
      }

      if (partsToSync.length === 0) {
        console.log('No parts to sync - everything is up to date');
        return {
          total: 0,
          processed: 0,
          succeeded: 0,
          failed: 0,
          errors: [],
          lastSync: new Date(),
          syncId
        };
      }

      console.log(`Found ${partsToSync.length} parts to sync`);

      // Get part IDs for efficient querying
      const partIds = partsToSync.map(part => part.id);

      // Fetch images for the parts to sync
      console.log('Fetching images for parts to sync...');
      const { data: allImages, error: imagesError } = await this.supabase
        .from('part_images')
        .select('part_id, image_url, is_main_image')
        .in('part_id', partIds);

      if (imagesError) {
        console.error('Error fetching images:', imagesError);
        // Continue without images
      }

      // Group images by part_id
      const imagesByPartId: Record<string, any[]> = {};
      if (allImages && allImages.length > 0) {
        for (const image of allImages) {
          if (!imagesByPartId[image.part_id]) {
            imagesByPartId[image.part_id] = [];
          }
          imagesByPartId[image.part_id].push(image);
        }
      }

      // Fetch conditions for the parts to sync
      console.log('Fetching conditions for parts to sync...');
      const { data: allConditions, error: conditionsError } = await this.supabase
        .from('parts_condition')
        .select('id, part_id, condition, stock')
        .in('part_id', partIds);

      if (conditionsError) {
        console.error('Error fetching conditions:', conditionsError);
        // Continue without conditions
      }

      // Group conditions by part_id and collect condition IDs
      const conditionsByPartId: Record<string, any[]> = {};
      const conditionIds: any[] = [];

      if (allConditions && allConditions.length > 0) {
        for (const condition of allConditions) {
          if (!conditionsByPartId[condition.part_id]) {
            conditionsByPartId[condition.part_id] = [];
          }
          conditionsByPartId[condition.part_id].push(condition);
          conditionIds.push(condition.id);
        }
      }

      // Fetch prices for all conditions
      let priceData = [];
      if (conditionIds.length > 0) {
        const { data: prices, error: priceError } = await this.supabase
          .from('part_price')
          .select('*')
          .in('condition_id', conditionIds);

        if (priceError) {
          console.error('Error fetching prices:', priceError);
        } else {
          priceData = prices || [];
          console.log(`Fetched ${priceData.length} prices for ${conditionIds.length} conditions`);
        }
      }

      // Create a map of condition IDs to prices for quick lookup
      const priceMap: Record<string, any> = {};
      for (const price of priceData) {
        priceMap[price.condition_id] = price;
      }

      // Enrich parts with conditions, prices, and images
      for (const part of partsToSync) {
        // Add conditions
        const conditions = conditionsByPartId[part.id] || [];

        // Find available condition (with stock > 0 or just the first one)
        const availableCondition = conditions.find((c: any) => c.stock > 0) || conditions[0];

        // Add price data if available
        if (availableCondition) {
          const price = priceMap[availableCondition.id];
          if (price) {
            part.price = price.price;
            part.discounted_price = price.discounted_price;
          }

          // Set condition
          part.condition = availableCondition.condition;
        }

        // Add images
        part.images = imagesByPartId[part.id] || [];
      }

      console.log('Sample part data after enrichment:', JSON.stringify(partsToSync[0], null, 2));

      // Initialize sync status
      const syncStatus: SyncStatus = {
        total: partsToSync.length,
        processed: 0,
        succeeded: 0,
        failed: 0,
        errors: [],
        lastSync: new Date(),
        syncId: syncId
      };

      // Store initial sync status in database
      await this.updateSyncStatus(syncStatus);

      // Process parts in batches to avoid rate limits
      const batchSize = 10;
      for (let i = 0; i < partsToSync.length; i += batchSize) {
        const batch = partsToSync.slice(i, i + batchSize);

        // Process batch in parallel
        const results = await Promise.allSettled(
          batch.map(part => this.syncProduct(part))
        );

        // Update sync status
        results.forEach((result, index) => {
          syncStatus.processed++;

          if (result.status === 'fulfilled') {
            syncStatus.succeeded++;
          } else {
            syncStatus.failed++;
            syncStatus.errors.push({
              partId: batch[index].id,
              error: result.reason.message
            });
          }
        });

        // Update sync status in database after each batch
        await this.updateSyncStatus(syncStatus);

        // Log progress
        console.log(`Sync progress: ${syncStatus.processed}/${syncStatus.total} (${Math.round((syncStatus.processed / syncStatus.total) * 100)}%)`);
        console.log(`Success: ${syncStatus.succeeded}, Failed: ${syncStatus.failed}`);

        // Add a small delay between batches to avoid rate limits
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Store sync status in database
      await this.storeSyncStatus(syncStatus);

      return syncStatus;
    } catch (error) {
      console.error('Error syncing all products:', error);
      throw error;
    }
  }

  /**
   * Sync a single product
   */
  private async syncProduct(part: any): Promise<any> {
    try {
      console.log('=== Starting Product Sync ===');
      console.log(`Product ID: ${part.id}`);
      console.log('Raw part data:', JSON.stringify(part, null, 2));

      // Validate required fields
      if (!part.id || !part.title) {
        throw new Error(`Missing required fields for part ${part.id}: ${!part.id ? 'id' : ''} ${!part.title ? 'title' : ''}`);
      }

      // Convert part to Google Merchant format
      console.log('Converting part to Google Merchant format...');
      const productData = (this.constructor as any).convertToMerchantFormat(part);
      console.log('Converted product data:', JSON.stringify(productData, null, 2));

      // Validate converted data
      if (!productData.offerId || !productData.title || !productData.price?.value) {
        throw new Error(`Invalid product data after conversion: ${JSON.stringify(productData)}`);
      }

      // Check if product already exists
      const productId = `online:en:KE:${part.id}`;
      console.log(`Checking if product ${productId} exists...`);

      try {
        const existingProduct = await this.shoppingContent.products.get({
          merchantId: this.merchantId,
          productId: productId
        });
        console.log('Existing product found:', JSON.stringify(existingProduct.data, null, 2));

        // Product exists, update it
        console.log(`Product ${productId} exists, updating...`);
        const result = await this.updateProduct(productId, productData);
        console.log(`Product ${productId} updated successfully:`, JSON.stringify(result, null, 2));
        return result;
      } catch (getError) {
        console.log(`Product ${productId} doesn't exist, creating new product...`);
        // Product doesn't exist, create it
        const result = await this.createProduct(productData);
        console.log(`Product ${productId} created successfully:`, JSON.stringify(result, null, 2));
        return result;
      }
    } catch (error) {
      console.error('=== Product Sync Error ===');
      console.error(`Error syncing product ${part.id}:`, error);
      console.error('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        code: (error as any)?.code || 'Unknown code',
        status: (error as any)?.status || 'Unknown status',
        response: (error as any)?.response?.data || 'No response data'
      });
      throw error;
    }
  }

  /**
   * Update sync status in database (upsert)
   */
  private async updateSyncStatus(status: SyncStatus): Promise<void> {
    try {
      // First check if a record with this syncId exists
      const { data, error: selectError } = await this.supabase
        .from('google_merchant_sync_status')
        .select('id')
        .eq('sync_id', status.syncId)
        .maybeSingle();

      if (selectError) {
        console.error('Error checking for existing sync status:', selectError);
      }

      if (data) {
        // Update existing record
        const { error: updateError } = await this.supabase
          .from('google_merchant_sync_status')
          .update({
            total: status.total,
            processed: status.processed,
            succeeded: status.succeeded,
            failed: status.failed,
            errors: status.errors,
            synced_at: new Date().toISOString()
          })
          .eq('id', data.id);

        if (updateError) {
          console.error('Error updating sync status:', updateError);
        }
      } else {
        // Insert new record
        const { error: insertError } = await this.supabase
          .from('google_merchant_sync_status')
          .insert([{
            sync_id: status.syncId,
            total: status.total,
            processed: status.processed,
            succeeded: status.succeeded,
            failed: status.failed,
            errors: status.errors,
            synced_at: new Date().toISOString()
          }]);

        if (insertError) {
          console.error('Error inserting sync status:', insertError);
        }
      }
    } catch (error) {
      console.error('Error updating sync status:', error);
    }
  }

  /**
   * Store sync status in database (legacy method)
   */
  private async storeSyncStatus(status: SyncStatus): Promise<void> {
    try {
      // Use the new updateSyncStatus method
      await this.updateSyncStatus(status);
    } catch (error) {
      console.error('Error storing sync status:', error);
    }
  }

  /**
   * Get latest sync status
   */
  async getLatestSyncStatus(): Promise<SyncStatus | null> {
    try {
      const { data, error } = await this.supabase
        .from('google_merchant_sync_status')
        .select('*')
        .order('synced_at', { ascending: false })
        .limit(1)
        .single();

      if (error || !data) {
        return null;
      }

      return {
        total: data.total,
        processed: data.processed,
        succeeded: data.succeeded,
        failed: data.failed,
        errors: data.errors,
        lastSync: new Date(data.synced_at),
        syncId: data.sync_id
      };
    } catch (error) {
      console.error('Error getting latest sync status:', error);
      return null;
    }
  }

  /**
   * Helper method to convert part data to Google Merchant format
   * @see https://developers.google.com/shopping-content/reference/rest/v2.1/products#Product
   */
  static convertToMerchantFormat(part: any): ProductData {
    console.log('=== Converting Part to Merchant Format ===');
    console.log('Input part data:', JSON.stringify(part, null, 2));

    // Validate required fields
    if (!part.id) {
      throw new Error('Part ID is required');
    }
    if (!part.title) {
      throw new Error('Part title is required');
    }

    // Get the main image URL
    const mainImage = part.images?.find((img: any) => img.is_main_image) || part.images?.[0];
    const imageUrl = mainImage?.image_url || '';
    console.log('Main image URL:', imageUrl);

    // Get additional images
    const additionalImages = part.images
      ?.filter((img: any) => img !== mainImage && img.image_url)
      .map((img: any) => img.image_url) || [];
    console.log('Additional images:', additionalImages);

    // Log price information for debugging
    console.log('Price information:');
    console.log('- Direct price:', part.price);
    console.log('- Discounted price:', part.discounted_price);

    // Get price information - handle different possible column names
    let price = 0;
    if (typeof part.price !== 'undefined' && part.price !== null) {
      price = Number(part.price);
    } else if (typeof part.selling_price !== 'undefined' && part.selling_price !== null) {
      price = Number(part.selling_price);
    } else if (typeof part.retail_price !== 'undefined' && part.retail_price !== null) {
      price = Number(part.retail_price);
    }

    // Skip parts without valid prices
    if (isNaN(price) || price <= 0) {
      console.warn(`Skipping part ${part.id}: Invalid or missing price value`);
      // Use a default price for testing/development (100 KES)
      // In production, we should skip parts without prices
      price = 100;
    }

    // Get discounted price - handle different possible column names
    let discountedPrice = null;
    if (typeof part.discounted_price !== 'undefined') {
      discountedPrice = Number(part.discounted_price);
    } else if (typeof part.sale_price !== 'undefined') {
      discountedPrice = Number(part.sale_price);
    }

    if (discountedPrice !== null && (isNaN(discountedPrice) || discountedPrice <= 0)) {
      console.warn(`Invalid discounted price: ${part.discounted_price || part.sale_price}, using regular price`);
      discountedPrice = null;
    }

    const finalPrice = discountedPrice || price;
    console.log('Final price:', finalPrice);

    // Always set availability to 'in stock' since we're only syncing available products
    const availability = 'in stock';

    // Get condition - handle different possible column names
    let condition = 'new';
    if (typeof part.condition !== 'undefined' && part.condition?.toLowerCase() === 'used') {
      condition = 'used';
    } else if (typeof part.part_condition !== 'undefined' && part.part_condition?.toLowerCase() === 'used') {
      condition = 'used';
    }
    console.log('Product condition:', condition);

    // Extract brand from title if possible
    let brand = '';
    if (part.title) {
      // Try to extract brand from title (e.g., "Volkswagen(VW) Audi + category...")
      const brandMatch = part.title.match(/^([A-Za-z]+)(?:\([A-Za-z]+\))?\s+([A-Za-z]+)?/);
      if (brandMatch) {
        brand = brandMatch[1]; // First brand mentioned
        if (brandMatch[2]) {
          brand += ' ' + brandMatch[2]; // Second brand if present
        }
      }
    } else if (part.brand) {
      // Use brand field if available
      brand = part.brand;
    }
    console.log('Extracted brand:', brand);

    // Use partnumber_group or part_number as MPN
    const mpn = part.partnumber_group || part.part_number || '';
    console.log('MPN:', mpn);

    // Build product data
    const productData: ProductData = {
      offerId: part.id.toString(),
      title: part.title.trim(),
      description: (part.description || `${part.title} - Auto part for vehicles`).trim(),
      link: `${process.env.NEXT_PUBLIC_BASE_URL}/shop/product/${part.id}-${encodeURIComponent(part.title?.replace(/\s+/g, '-').toLowerCase() || '')}`,
      imageLink: imageUrl,
      additionalImageLinks: additionalImages.length > 0 ? additionalImages : undefined,
      condition: condition,
      availability: availability,
      price: {
        value: finalPrice,
        currency: 'KES' // Kenya Shillings
      },
      brand: brand || undefined,
      mpn: mpn || undefined,
      // Use customLabel instead of customLabels (API requires this format)
      customLabel0: part.category_id?.toString() || '',
      customLabel1: brand || '',
      customLabel2: part.partnumber_group || '',
      customLabel3: '',
      customLabel4: condition || '',
      identifierExists: !!mpn,
      // Required channel parameter
      channel: 'online',
      // Required content language and target country
      contentLanguage: 'en',
      targetCountry: 'KE'
    };

    console.log('Converted product data:', JSON.stringify(productData, null, 2));
    return productData;
  }



  /**
   * Get auth object
   */
  getAuth() {
    return this.auth;
  }
}

// Export the service instance
export const googleMerchantService = new GoogleMerchantService();
