// app/types/database.ts
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      car_part_categories: {  
        Row: {
          id: number;
          label: string;
          href: string;
          parent_category_id: number | null;
          icon: string | null;
          library: string | null;
          // ... other columns
        };
        // ... rest of the table definition
      };
      profiles: {
        Row: {
          full_name: string | null;
          id: string;
          updated_at: string | null;
          username: string | null;
          website: string | null;
          idle_timeout: boolean;
          logout_only: boolean;
          phone: string | null;
        };
        Insert: {
          full_name?: string | null;
          id: string;
          updated_at?: string | null;
          username?: string | null;
          website?: string | null;
          idle_timeout?: boolean;
          logout_only?: boolean;
        };
        Update: {
          full_name?: string | null;
          id?: string;
          updated_at?: string | null;
          username?: string | null;
          website?: string | null;
          idle_timeout?: boolean;
          logout_only?: boolean;
        };
        Relationships: [
          {
            foreignKeyName: "profiles_id_fkey";
            columns: ["id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
      roles: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      permissions: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          category: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          category: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          category?: string;
          created_at?: string;
        };
        Relationships: [];
      };
      user_roles: {
        Row: {
          user_id: string;
          role_id: string;
          assigned_at: string;
        };
        Insert: {
          user_id: string;
          role_id: string;
          assigned_at?: string;
        };
        Update: {
          user_id?: string;
          role_id?: string;
          assigned_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "user_roles_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "user_roles_role_id_fkey";
            columns: ["role_id"];
            referencedRelation: "roles";
            referencedColumns: ["id"];
          }
        ];
      };
      role_permissions: {
        Row: {
          role_id: string;
          permission_id: string;
          assigned_at: string;
        };
        Insert: {
          role_id: string;
          permission_id: string;
          assigned_at?: string;
        };
        Update: {
          role_id?: string;
          permission_id?: string;
          assigned_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "role_permissions_role_id_fkey";
            columns: ["role_id"];
            referencedRelation: "roles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "role_permissions_permission_id_fkey";
            columns: ["permission_id"];
            referencedRelation: "permissions";
            referencedColumns: ["id"];
          }
        ];
      };
      user_permissions: {
        Row: {
          id: string;
          user_id: string;
          permission_id: string;
          has_permission: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          permission_id: string;
          has_permission: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          permission_id?: string;
          has_permission?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "user_permissions_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "user_permissions_permission_id_fkey";
            columns: ["permission_id"];
            referencedRelation: "permissions";
            referencedColumns: ["id"];
          }
        ];
      };
      audit_log: {
        Row: {
          id: string;
          timestamp: string;
          user_id: string | null;
          action_type: string;
          target_table: string | null;
          target_record_id: string | null;
          change_details: Json | null;
          ip_address: string | null;
        };
        Insert: {
          id?: string;
          timestamp?: string;
          user_id?: string | null;
          action_type: string;
          target_table?: string | null;
          target_record_id?: string | null;
          change_details?: Json | null;
          ip_address?: string | null;
        };
        Update: {
          id?: string;
          timestamp?: string;
          user_id?: string | null;
          action_type?: string;
          target_table?: string | null;
          target_record_id?: string | null;
          change_details?: Json | null;
          ip_address?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "audit_log_user_id_fkey";
            columns: ["user_id"];
            referencedRelation: "profiles";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    part_images: {
      Row: {
        created_at: string | null
        image_id: string
        image_url: string | null
        part_id: string | null
      }
      Insert: {
        created_at?: string | null
        image_id?: string
        image_url?: string | null
        part_id?: string | null
      }
      Update: {
        created_at?: string | null
        image_id?: string
        image_url?: string | null
        part_id?: string | null
      }
      Relationships: [
        {
          foreignKeyName: "part_images_part_id_fkey"
          columns: ["part_id"]
          isOne: false
          referencedRelation: "parts"
          referencedColumns: ["part_id"]
        }
      ]
    }
    parts: {
      Row: {
        created_at: string | null
        part_id: string
        part_name: string | null
        part_number: string | null
        price: number | null
        stock: number | null
      }
      Insert: {
        created_at?: string | null
        part_id?: string
        part_name?: string | null
        part_number?: string | null
        price?: number | null
        stock?: number | null
      }
      Update: {
        created_at?: string | null
        part_id?: string
        part_name?: string | null
        part_number?: string | null
        price?: number | null
        stock?: number | null
      }
      Relationships: []
    }
    Views: {
      [_ in never]: never;
    };
    Functions: {
      check_user_permission: {
        Args: {
          p_user_id: string;
          p_permission_name: string;
        };
        Returns: boolean;
      };
      is_super_admin: {
        Args: {
          p_user_id: string;
        };
        Returns: boolean;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}