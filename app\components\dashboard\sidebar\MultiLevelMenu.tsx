// MultiLevelMenu.tsx
'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Icon from '../../ui/Icon';
import { usePathname } from 'next/navigation';

interface MultiLevelMenuProps {
  items: {
    label: string;
    href: string;
    icon?: string;
    children?: MultiLevelMenuProps['items'];
  }[];
  level?: number;
}

const MultiLevelMenu: React.FC<MultiLevelMenuProps> = ({
  items,
  level = 0,
}) => {
  const [openItems, setOpenItems] = useState<number[]>([]);
  const pathname = usePathname();

  const toggleItem = (index: number) => {
    setOpenItems((prevOpenItems) => {
      if (prevOpenItems.includes(index)) {
        return prevOpenItems.filter((itemIndex) => itemIndex !== index);
      } else {
        return [...prevOpenItems, index];
      }
    });
  };

  const isItemSelected = (item: MultiLevelMenuProps['items'][number]) => {
    if (item.href === pathname) return true;
  
    if (item.children) {
      return item.children.some(isItemSelected);
    }
  
    return false;
  };

  return (
    <ul className={level === 0 ? 'p-2' : ''}>
      {items.map((item, index) => (
        <li key={index}>
          <div
            className={`flex items-center p-2 hover:bg-gray-700 ${
              isItemSelected(item) ? 'bg-gray-700' : ''
            }`}          
          >
            {item.icon && (
              <Icon
                name={item.icon}
                size={20}
                className="mr-2"
                library={
                  item.icon === 'tshirt-crew'
                    ? 'mdi'
                    : undefined
                }
              />
            )}
            <Link href={item.href} className="flex-grow">
              {item.label}
            </Link>
            {item.children && (
              <button onClick={() => toggleItem(index)} className='p-2'>
                <Icon
                  name={openItems.includes(index) ? 'chevron-up' : 'chevron-down'}
                  size={20}
                />
              </button>
            )}
          </div>
          {item.children && openItems.includes(index) && (
            <div className="pl-4">
              <MultiLevelMenu items={item.children} level={level + 1} />
            </div>
          )}
        </li>
      ))}
    </ul>
  );
};

export default MultiLevelMenu;