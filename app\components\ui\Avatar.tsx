import Image from 'next/image';
import React, { useState } from 'react';
import Icon from './Icon';

interface AvatarProps {
  src?: string;
  alt?: string;
}

const Avatar: React.FC<AvatarProps> = ({ src, alt = "User Avatar" }) => {
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div className="w-10 h-10 relative rounded-full overflow-hidden">
      {(!src || imageError) ? (
        <div className="flex items-center justify-center w-full h-full bg-gray-300">
          <Icon name="account-circle" library="mdi" size={50} color="gray" />
        </div>
      ) : (
        <Image
          src={src}
          alt={alt}
          layout="fill"
          objectFit="cover"
          onError={handleImageError}
        />
      )}
    </div>
  );
};

export default Avatar;