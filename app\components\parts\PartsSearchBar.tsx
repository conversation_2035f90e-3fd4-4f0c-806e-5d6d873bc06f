'use client';

import React, { useState, useEffect } from 'react';
import { Search, X } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useDebounce } from '@/app/hooks/useDebounce';

interface PartsSearchBarProps {
  className?: string;
  placeholder?: string;
}

const PartsSearchBar: React.FC<PartsSearchBarProps> = ({
  className = '',
  placeholder = 'Search parts by name or part number...'
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Initialize search input with the query from URL
  const [searchInput, setSearchInput] = useState(
    searchParams?.get('query') || ''
  );
  
  // Debounce the search input to avoid making too many requests
  const debouncedSearch = useDebounce(searchInput, 500);
  
  // Update the URL when the debounced search changes
  useEffect(() => {
    const params = new URLSearchParams(searchParams?.toString());
    
    if (debouncedSearch) {
      params.set('query', debouncedSearch);
    } else {
      params.delete('query');
    }
    
    // Reset to page 1 when search query changes
    params.set('page', '1');
    
    // Update the URL without refreshing the page
    router.push(`/parts?${params.toString()}`, { scroll: false });
  }, [debouncedSearch, router, searchParams]);
  
  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };
  
  // Clear search input
  const clearSearch = () => {
    setSearchInput('');
  };

  return (
    <div className={`relative w-full ${className}`}>
      <div className="relative flex items-center">
        <div className="absolute left-3 text-gray-400">
          <Search size={20} />
        </div>
        <input
          type="text"
          className="w-full h-12 pl-10 pr-12 rounded-lg border border-gray-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none transition-all duration-200"
          placeholder={placeholder}
          value={searchInput}
          onChange={handleSearchChange}
        />
        {searchInput && (
          <button
            onClick={clearSearch}
            className="absolute right-3 p-1 rounded-full hover:bg-gray-100 text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Clear search"
          >
            <X size={20} />
          </button>
        )}
      </div>
    </div>
  );
};

export default PartsSearchBar;
