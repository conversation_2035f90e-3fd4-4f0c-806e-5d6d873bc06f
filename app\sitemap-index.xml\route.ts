import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://autoflow.co.ke';

    console.log('Generating sitemap index...');

    // Get total parts count to determine how many parts sitemaps we need
    const { count: totalParts } = await supabase
      .from('parts')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true);

    const partsPerSitemap = 10000;
    const totalPartsSitemaps = Math.ceil((totalParts || 0) / partsPerSitemap);

    console.log(`Total parts: ${totalParts}, Parts sitemaps needed: ${totalPartsSitemaps}`);

    // Build sitemap index
    const sitemaps = [
      {
        loc: `${baseUrl}/sitemap.xml`,
        lastmod: new Date().toISOString()
      },
      {
        loc: `${baseUrl}/sitemap-categories.xml`,
        lastmod: new Date().toISOString()
      }
    ];

    // Add parts sitemaps
    for (let i = 1; i <= totalPartsSitemaps; i++) {
      sitemaps.push({
        loc: `${baseUrl}/sitemap-parts.xml?page=${i}`,
        lastmod: new Date().toISOString()
      });
    }

    // Generate XML sitemap index
    const sitemapIndex = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemaps.map(sitemap => `  <sitemap>
    <loc>${sitemap.loc}</loc>
    <lastmod>${sitemap.lastmod}</lastmod>
  </sitemap>`).join('\n')}
</sitemapindex>`;

    console.log(`Generated sitemap index with ${sitemaps.length} sitemaps`);

    return new NextResponse(sitemapIndex, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
      },
    });

  } catch (error) {
    console.error('Error generating sitemap index:', error);
    
    // Return basic sitemap index on error
    const basicSitemapIndex = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${process.env.NEXT_PUBLIC_BASE_URL || 'https://autoflow.co.ke'}/sitemap.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
</sitemapindex>`;

    return new NextResponse(basicSitemapIndex, {
      headers: {
        'Content-Type': 'application/xml',
      },
    });
  }
}
