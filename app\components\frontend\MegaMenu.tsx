'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface Category {
  id: number;
  label: string;
  href: string;
  parent_category_id: number | null;
}

interface MegaMenuProps {
  categoryId: number;
  categories: Category[];
  getSubcategories: (parentId: number) => Category[];
}

const MegaMenu: React.FC<MegaMenuProps> = ({ categoryId, categories, getSubcategories }) => {
  // Get the current category
  const currentCategory = categories.find(cat => cat.id === categoryId);
  if (!currentCategory) return null;

  // Get direct subcategories
  const subcategories = getSubcategories(categoryId);

  // Group subcategories into columns (max 4 columns)
  const groupSubcategories = () => {
    const result: Category[][] = [];
    const itemsPerColumn = Math.ceil(subcategories.length / 4);

    for (let i = 0; i < subcategories.length; i += itemsPerColumn) {
      result.push(subcategories.slice(i, i + itemsPerColumn));
    }

    return result;
  };

  const columns = groupSubcategories();

  // Get featured products for this category (placeholder)
  const featuredProduct = {
    id: 'featured-1',
    title: 'Top-Rated & Bestselling Parts',
    imageUrl: '/images/featured-part.jpg',
    href: `${currentCategory.href}&featured=true&page=1`
  };

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        {/* Category Columns */}
        {columns.map((column, colIndex) => (
          <div key={colIndex} className="space-y-4">
            {column.map(subcat => {
              // Get third-level categories
              const thirdLevel = getSubcategories(subcat.id);

              return (
                <div key={subcat.id} className="mb-6">
                  <Link
                    href={subcat.href}
                    className="font-medium text-gray-900 hover:text-teal-600 block mb-2"
                  >
                    {subcat.label}
                  </Link>

                  {thirdLevel.length > 0 && (
                    <ul className="space-y-2">
                      {thirdLevel.map(thirdCat => (
                        <li key={thirdCat.id}>
                          <Link
                            href={thirdCat.href}
                            className="text-sm text-gray-600 hover:text-teal-600 block"
                          >
                            {thirdCat.label}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              );
            })}
          </div>
        ))}

        {/* Featured Product */}
        <div className="md:col-span-2">
          <Link href={featuredProduct.href} className="block">
            <div className="relative h-64 rounded-md overflow-hidden">
              <Image
                src={featuredProduct.imageUrl}
                alt={featuredProduct.title}
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-20 flex items-end">
                <div className="p-4 text-white">
                  <h3 className="font-semibold text-lg">{featuredProduct.title}</h3>
                  <p className="text-sm mt-1">View Collection</p>
                </div>
              </div>
            </div>
          </Link>
        </div>
      </div>

      {/* Additional Links */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex flex-wrap gap-x-6 gap-y-2">
          <Link href={`${currentCategory.href}&filter=new&page=1`} className="text-sm text-teal-600 hover:text-teal-800">
            New Arrivals
          </Link>
          <Link href={`${currentCategory.href}&filter=bestsellers&page=1`} className="text-sm text-teal-600 hover:text-teal-800">
            Bestsellers
          </Link>
          <Link href={`${currentCategory.href}&filter=clearance&page=1`} className="text-sm text-teal-600 hover:text-teal-800">
            Clearance
          </Link>
          <Link href={`${currentCategory.href}`} className="text-sm text-teal-600 hover:text-teal-800">
            View All {currentCategory.label}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default MegaMenu;
