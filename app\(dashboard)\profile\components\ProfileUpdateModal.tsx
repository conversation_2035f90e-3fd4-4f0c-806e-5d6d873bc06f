// components/ProfileUpdateModal.tsx
'use client'

import React from 'react';
import Modal from '@/app/components/ui/Modal';
import { ProfileUpdateModalProps } from '@/app/types/profile';
import UpdateProfileDetailsForm from './forms/UpdateProfileDetailsForm';

const ProfileUpdateModal: React.FC<ProfileUpdateModalProps> = ({
  isOpen,
  onClose,
  missingDetails,
  onProfileUpdate
}) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Update Profile Details"
      width="w-full md:w-1/2"
    >
      <UpdateProfileDetailsForm missingDetails={missingDetails} onUpdate={onProfileUpdate} />
    </Modal>
  );
};

export default ProfileUpdateModal;