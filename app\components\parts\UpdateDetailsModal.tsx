'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { X } from 'lucide-react';
import { useForm } from 'react-hook-form';
import NumberInput from '@/app/components/ui/inputs/NumberInput';
import Input from '@/app/components/ui/inputs/Input';

interface UpdateDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  partId: string;
  partTitle: string;
  partNumber: string;
  conditions: any[];
  onDetailsUpdated: () => void;
}

interface PartDetailsFormValues {
  title: string;
  partNumber: string;
  conditions: Array<{
    id: string;
    condition: string;
    stock: number;
    price: number;
    discountedPrice: number | null;
    newCondition?: string; // New field to track condition changes
  }>;
  attributes: Array<{
    id: string;
    name: string;
    value: string;
    inputType: string;
  }>;
}

const UpdateDetailsModal: React.FC<UpdateDetailsModalProps> = ({
  isOpen,
  onClose,
  partId,
  partTitle,
  partNumber,
  conditions,
  onDetailsUpdated
}) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [actualPartNumber, setActualPartNumber] = useState<string>(partNumber);
  const [compatibilityGroupId, setCompatibilityGroupId] = useState<number | null>(null);
  const [categoryAttributes, setCategoryAttributes] = useState<any[]>([]);
  const [attributeValues, setAttributeValues] = useState<any[]>([]);
  const [attributeOptions, setAttributeOptions] = useState<{[key: string]: any[]}>({});
  const [isLoadingAttributes, setIsLoadingAttributes] = useState(false);

  const { register, control, handleSubmit, setValue, formState: { errors }, reset } = useForm<PartDetailsFormValues>({
    defaultValues: {
      title: partTitle,
      partNumber: partNumber,
      conditions: conditions.map(condition => ({
        id: condition.id,
        condition: condition.condition,
        stock: condition.stock || 0,
        price: 0,
        discountedPrice: null
      })),
      attributes: []
    }
  });

  // Fetch the actual part number and prices when the modal opens
  useEffect(() => {
    if (isOpen && partId) {
      const fetchPartDetails = async () => {
        try {
          const supabase = createClient();

          // Fetch part details with part_compatibility_groups join
          const { data: part, error: partError } = await supabase
            .from('parts')
            .select(`
              *,
              part_compatibility_groups:partnumber_group(id, part_number)
            `)
            .eq('id', partId)
            .single();

          if (partError) throw partError;

          // Set the actual part number from the joined data
          if (part.part_compatibility_groups?.part_number) {
            setActualPartNumber(part.part_compatibility_groups.part_number);
            setValue('partNumber', part.part_compatibility_groups.part_number);
          }

          // Store the compatibility group ID for later use
          if (part.partnumber_group) {
            setCompatibilityGroupId(part.partnumber_group);
          }

          // Set the title
          setValue('title', part.title || '');

          // Fetch category attributes and their values
          fetchCategoryAttributes(part.id);

          // Fetch prices for each condition
          if (conditions && conditions.length > 0) {
            const conditionIds = conditions.map(c => c.id);
            const { data: prices, error: pricesError } = await supabase
              .from('part_price')
              .select('*')
              .in('condition_id', conditionIds);

            if (pricesError) throw pricesError;

            if (prices && prices.length > 0) {
              // Update the form values with the fetched prices
              const updatedConditions = conditions.map(condition => {
                const priceData = prices.find(p => p.condition_id === parseInt(condition.id));
                return {
                  id: condition.id,
                  condition: condition.condition,
                  stock: condition.stock || 0,
                  price: priceData?.price || 0,
                  discountedPrice: priceData?.discounted_price || null
                };
              });

              setValue('conditions', updatedConditions);
            }
          }
        } catch (err) {
          console.error('Error fetching part details:', err);
          setError('Failed to load part details. Please try again later.');
        }
      };

      fetchPartDetails();
    }
  }, [isOpen, partId, conditions, setValue]);

  // Function to fetch category attributes and their values
  const fetchCategoryAttributes = async (partId: string) => {
    setIsLoadingAttributes(true);
    try {
      const supabase = createClient();

      // First, get the part's category
      const { data: part, error: partError } = await supabase
        .from('parts')
        .select('category_id')
        .eq('id', partId)
        .single();

      if (partError) throw partError;

      if (part && part.category_id) {
        // Fetch attributes for this category
        const { data: attributes, error: attributesError } = await supabase
          .from('parts_category_attributes')
          .select('*')
          .eq('category_id', part.category_id);

        if (attributesError) throw attributesError;

        if (attributes && attributes.length > 0) {
          setCategoryAttributes(attributes);

          // Fetch attribute values for this part
          const { data: values, error: valuesError } = await supabase
            .from('parts_category_attribute_values')
            .select('*')
            .eq('part_id', partId);

          if (valuesError) throw valuesError;

          setAttributeValues(values || []);

          // Fetch options for radio buttons and checkboxes
          const attributeIds = attributes
            .filter(attr => ['radio', 'checkbox'].includes(attr.input_type))
            .map(attr => attr.id);

          if (attributeIds.length > 0) {
            const { data: options, error: optionsError } = await supabase
              .from('parts_category_attribute_input_option')
              .select('*')
              .in('attribute_id', attributeIds);

            if (optionsError) throw optionsError;

            // Group options by attribute_id
            const optionsByAttribute: {[key: string]: any[]} = {};
            options?.forEach(option => {
              if (!optionsByAttribute[option.attribute_id]) {
                optionsByAttribute[option.attribute_id] = [];
              }
              optionsByAttribute[option.attribute_id].push(option);
            });

            setAttributeOptions(optionsByAttribute);
          }

          // Update form values with attribute values
          const formattedAttributes = attributes.map(attr => {
            const value = values?.find(v => v.attribute_id === attr.id);
            return {
              id: attr.id.toString(),
              name: attr.attribute,
              value: value ? (value.value || value.selection_value || '') : '',
              inputType: attr.input_type
            };
          });

          setValue('attributes', formattedAttributes);
        }
      }
    } catch (err) {
      console.error('Error fetching category attributes:', err);
    } finally {
      setIsLoadingAttributes(false);
    }
  };

  const onSubmit = async (data: PartDetailsFormValues) => {
    setIsUpdating(true);
    setError(null);

    try {
      const supabase = createClient();

      // 1. Update the part title
      const { error: titleError } = await supabase
        .from('parts')
        .update({ title: data.title })
        .eq('id', partId);

      if (titleError) throw titleError;

      // 2. Update the part number in part_compatibility_groups if it changed
      if (data.partNumber !== actualPartNumber && compatibilityGroupId) {
        const { error: partNumberError } = await supabase
          .from('part_compatibility_groups')
          .update({ part_number: data.partNumber })
          .eq('id', compatibilityGroupId);

        if (partNumberError) throw partNumberError;
      }

      // 3. Update stock for each condition
      for (const condition of data.conditions) {
        // Check if condition has changed
        const hasConditionChanged = condition.newCondition && condition.newCondition !== condition.condition;

        // Update stock and condition in parts_condition
        const { error: stockError } = await supabase
          .from('parts_condition')
          .update({
            stock: condition.stock,
            condition: hasConditionChanged ? condition.newCondition : condition.condition
          })
          .eq('id', condition.id);

        if (stockError) throw stockError;

        // Log condition change if it happened
        if (hasConditionChanged) {
          console.log(`Updated condition from ${condition.condition} to ${condition.newCondition}`);
        }

        // Check if price record exists
        const { data: existingPrice, error: priceCheckError } = await supabase
          .from('part_price')
          .select('id')
          .eq('condition_id', condition.id)
          .maybeSingle();

        if (priceCheckError) throw priceCheckError;

        if (existingPrice) {
          // Update existing price record
          const { error: updatePriceError } = await supabase
            .from('part_price')
            .update({
              price: condition.price,
              discounted_price: condition.discountedPrice
            })
            .eq('condition_id', condition.id);

          if (updatePriceError) throw updatePriceError;
        } else {
          // Create new price record
          const { error: insertPriceError } = await supabase
            .from('part_price')
            .insert({
              condition_id: parseInt(condition.id),
              price: condition.price,
              discounted_price: condition.discountedPrice
            });

          if (insertPriceError) throw insertPriceError;
        }
      }

      // 4. Update attribute values
      if (data.attributes && data.attributes.length > 0) {
        for (const attribute of data.attributes) {
          // Check if value already exists
          const { data: existingValue, error: valueCheckError } = await supabase
            .from('parts_category_attribute_values')
            .select('*')
            .eq('part_id', partId)
            .eq('attribute_id', attribute.id)
            .maybeSingle();

          if (valueCheckError) throw valueCheckError;

          if (existingValue) {
            // Update existing value
            let updateData = {};

            // Handle different input types
            if (['select', 'radio', 'checkbox'].includes(attribute.inputType)) {
              updateData = { selection_value: attribute.value, value: null };
            } else {
              updateData = { value: attribute.value, selection_value: null };
            }

            const { error: updateValueError } = await supabase
              .from('parts_category_attribute_values')
              .update(updateData)
              .eq('part_id', partId)
              .eq('attribute_id', attribute.id);

            if (updateValueError) throw updateValueError;
          } else if (attribute.value) { // Only insert if there's a value
            // Insert new value
            const insertData = {
              part_id: parseInt(partId),
              attribute_id: parseInt(attribute.id),
              value: ['select', 'radio', 'checkbox'].includes(attribute.inputType) ? null : attribute.value,
              selection_value: ['select', 'radio', 'checkbox'].includes(attribute.inputType) ? attribute.value : null
            };

            const { error: insertValueError } = await supabase
              .from('parts_category_attribute_values')
              .insert(insertData);

            if (insertValueError) throw insertValueError;
          }
        }
      }

      // Success - close modal and refresh data
      onDetailsUpdated();
      onClose();
    } catch (err) {
      console.error('Error updating part details:', err);
      setError('Failed to update part details. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onClick={(e) => {
        e.stopPropagation();
        if (!isUpdating) onClose();
      }}
    >
      <div
        className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold">Update Part Details</h3>
          <button
            className="p-1 rounded-full hover:bg-gray-100"
            onClick={() => !isUpdating && onClose()}
            disabled={isUpdating}
          >
            <X size={18} />
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Part Title */}
          <div className="mb-4">
            <label htmlFor="title" className="block text-sm font-medium mb-1">Part Title</label>
            <Input
              id="title"
              {...register('title', { required: 'Title is required' })}
              className="w-full"
            />
            {errors.title && (
              <p className="text-red-500 text-sm mt-1">{errors.title.message}</p>
            )}
          </div>

          {/* Part Number */}
          <div className="mb-4">
            <label htmlFor="partNumber" className="block text-sm font-medium mb-1">Part Number</label>
            <Input
              id="partNumber"
              {...register('partNumber', { required: 'Part number is required' })}
              className="w-full"
            />
            {errors.partNumber && (
              <p className="text-red-500 text-sm mt-1">{errors.partNumber.message}</p>
            )}
          </div>

          {/* Conditions, Stock and Prices */}
          <div className="mb-4">
            <h4 className="font-medium mb-2">Stock & Pricing</h4>
            {conditions.map((condition, index) => (
              <div key={condition.id} className="bg-gray-50 p-3 rounded-md mb-3">
                <div className="flex justify-between items-center mb-2">
                  <h5 className="font-medium">{condition.condition} Condition</h5>

                  {/* Condition selector */}
                  <select
                    {...register(`conditions.${index}.newCondition`)}
                    defaultValue={condition.condition}
                    className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="New">New</option>
                    <option value="Used">Used</option>
                  </select>
                </div>

                <div className="grid grid-cols-2 gap-3 mb-2">
                  <div>
                    <label htmlFor={`conditions.${index}.stock`} className="block text-sm font-medium mb-1">Stock</label>
                    <NumberInput
                      name={`conditions.${index}.stock`}
                      control={control}
                      className="w-full"
                    />
                  </div>
                  <div>
                    <label htmlFor={`conditions.${index}.price`} className="block text-sm font-medium mb-1">Price (Kshs)</label>
                    <NumberInput
                      name={`conditions.${index}.price`}
                      control={control}
                      className="w-full"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor={`conditions.${index}.discountedPrice`} className="block text-sm font-medium mb-1">Discounted Price (Optional)</label>
                  <NumberInput
                    name={`conditions.${index}.discountedPrice`}
                    control={control}
                    className="w-full"
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Category Attributes */}
          {categoryAttributes.length > 0 && (
            <div className="mb-4">
              <h4 className="font-medium mb-2">Category Attributes</h4>
              {isLoadingAttributes ? (
                <div className="text-center py-4">Loading attributes...</div>
              ) : (
                <div className="space-y-3">
                  {categoryAttributes.map((attribute, index) => {
                    const attributeValue = attributeValues.find(v => v.attribute_id === attribute.id);
                    const value = attributeValue ? (attributeValue.value || attributeValue.selection_value || '') : '';

                    return (
                      <div key={attribute.id} className="bg-gray-50 p-3 rounded-md">
                        <label
                          htmlFor={`attributes.${index}.value`}
                          className="block text-sm font-medium mb-1"
                        >
                          {attribute.attribute}
                        </label>

                        {attribute.input_type === 'text' && (
                          <Input
                            id={`attributes.${index}.value`}
                            {...register(`attributes.${index}.value`)}
                            defaultValue={value}
                            className="w-full"
                          />
                        )}

                        {attribute.input_type === 'number' && (
                          <NumberInput
                            name={`attributes.${index}.value`}
                            control={control}
                            defaultValue={value ? parseFloat(value) : undefined}
                            className="w-full"
                          />
                        )}

                        {attribute.input_type === 'radio' && attributeOptions[attribute.id] && (
                          <div className="flex flex-col space-y-2">
                            {attributeOptions[attribute.id].map((option) => (
                              <label key={option.id} className="inline-flex items-center">
                                <input
                                  type="radio"
                                  {...register(`attributes.${index}.value`)}
                                  value={option.option_value}
                                  defaultChecked={value === option.option_value}
                                  className="form-radio h-4 w-4 text-blue-600"
                                />
                                <span className="ml-2 text-sm text-gray-700">{option.option_value}</span>
                              </label>
                            ))}
                          </div>
                        )}

                        {attribute.input_type === 'checkbox' && attributeOptions[attribute.id] && (
                          <div className="flex flex-col space-y-2">
                            {attributeOptions[attribute.id].map((option) => (
                              <label key={option.id} className="inline-flex items-center">
                                <input
                                  type="checkbox"
                                  {...register(`attributes.${index}.value`)}
                                  value={option.option_value}
                                  defaultChecked={value?.includes(option.option_value)}
                                  className="form-checkbox h-4 w-4 text-blue-600"
                                />
                                <span className="ml-2 text-sm text-gray-700">{option.option_value}</span>
                              </label>
                            ))}
                          </div>
                        )}

                        {attribute.input_type === 'select' && (
                          <select
                            id={`attributes.${index}.value`}
                            {...register(`attributes.${index}.value`)}
                            defaultValue={value ?? ''}
                            className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="">Select an option</option>
                            {attributeOptions[attribute.id] ? (
                              attributeOptions[attribute.id].map((option) => (
                                <option key={option.id} value={option.option_value}>
                                  {option.option_value}
                                </option>
                              ))
                            ) : (
                              <option value="" disabled>No options available</option>
                            )}
                          </select>
                        )}

                        {/* Hidden fields to store attribute metadata */}
                        <input
                          type="hidden"
                          {...register(`attributes.${index}.id`)}
                          defaultValue={attribute.id}
                        />
                        <input
                          type="hidden"
                          {...register(`attributes.${index}.name`)}
                          defaultValue={attribute.attribute}
                        />
                        <input
                          type="hidden"
                          {...register(`attributes.${index}.inputType`)}
                          defaultValue={attribute.input_type}
                        />
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              className="px-4 py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50"
              onClick={onClose}
              disabled={isUpdating}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700"
              disabled={isUpdating}
            >
              {isUpdating ? 'Updating...' : 'Update Details'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UpdateDetailsModal;

