/**
 * Converts a string to a URL-friendly slug
 * @param text The text to convert to a slug
 * @returns A URL-friendly slug
 */
export function slugify(text: string): string {
  return text
    .toString()
    .normalize('NFD')                   // Normalize to decomposed form for handling accents
    .replace(/[\u0300-\u036f]/g, '')   // Remove accents
    .toLowerCase()                      // Convert to lowercase
    .replace(/\//g, '-')               // Replace slashes with hyphens (e.g., Wagon/Avant -> wagon-avant)
    .replace(/\s+/g, '-')              // Replace spaces with hyphens
    .replace(/[^\w\-]+/g, '')          // Remove all non-word characters
    .replace(/\-\-+/g, '-')            // Replace multiple hyphens with single hyphen
    .replace(/^-+/, '')                // Trim hyphens from start
    .replace(/-+$/, '');               // Trim hyphens from end
}

/**
 * Generates a slug for a product based on its title and ID
 * @param title The product title
 * @param id The product ID
 * @returns A URL-friendly slug that includes the product ID
 */
export function generateProductSlug(title: string, id: string | number): string {
  const slug = slugify(title);
  return `${slug}-${id}`;
}

/**
 * Extracts the ID from a product slug
 * @param slug The product slug
 * @returns The product ID
 */
export function extractIdFromSlug(slug: string): string {
  // The ID is the last part after the last hyphen
  const parts = slug.split('-');
  return parts[parts.length - 1];
}
