'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { createClient } from '@/app/libs/supabase/client';
import Link from 'next/link';
import {
  Search,
  Filter,
  User,
  Wrench,
  Store,
  Briefcase,
  Building,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  UserPlus,
  X,
  Check,
  AlertTriangle,
  Plus,
  ArrowUpRight
} from 'lucide-react';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import toast from 'react-hot-toast';
import ClientFormModal from './modals/ClientFormModal';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5
    }
  },
  hover: {
    y: -5,
    boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
    transition: {
      duration: 0.2
    }
  }
};

// Types
interface Client {
  id: string;
  name: string;
  category_id: string;
  category_name?: string;
  client_type: 'credit' | 'cash';
  phone_number: string;
  email: string | null;
  can_receive_credit: boolean;
  is_active: boolean;
  created_at: string;
}

interface ClientCategory {
  id: string;
  name: string;
  description: string | null;
}

// Client Card Component
interface ClientCardProps {
  client: Client;
  onDelete: (id: string) => void;
  onInvite: (id: string) => void;
  onEdit: (id: string) => void;
}

const ClientCard: React.FC<ClientCardProps> = ({ client, onDelete, onInvite, onEdit }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
      }
    };

    if (isMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuOpen]);

  // Get category icon
  const getCategoryIcon = () => {
    switch(client.category_name) {
      case 'Individual':
        return <User className="w-4 h-4" />;
      case 'Garage':
        return <Wrench className="w-4 h-4" />;
      case 'Shop':
        return <Store className="w-4 h-4" />;
      case 'Broker':
        return <Briefcase className="w-4 h-4" />;
      default:
        return <Building className="w-4 h-4" />;
    }
  };

  return (
    <motion.div
      variants={itemVariants}
      whileHover="hover"
      className="border rounded-lg p-4 bg-white relative"
    >
      <div className="flex items-center mb-3">
        <div className="w-10 h-10 rounded-full bg-gray-200 mr-3 flex items-center justify-center text-gray-600">
          {client.name?.charAt(0) || '?'}
        </div>
        <div>
          <h3 className="font-medium text-gray-900">{client.name || 'Unnamed Client'}</h3>
          <div className="text-sm text-gray-500 flex items-center">
            {getCategoryIcon()}
            <span className="ml-1">{client.category_name || 'Unknown Category'}</span>
          </div>
        </div>
      </div>

      {client.email && (
        <div className="text-sm text-gray-600 mb-2">{client.email}</div>
      )}

      <div className="text-sm text-gray-600 mb-3">{client.phone_number}</div>

      <div className="flex justify-between items-center">
        <div className="flex space-x-2">
          <span className={`px-2 py-1 text-xs rounded-full ${
            client.is_active
              ? 'bg-green-100 text-green-800 border border-green-200'
              : 'bg-gray-100 text-gray-800 border border-gray-200'
          }`}>
            {client.is_active ? 'Active' : 'Inactive'}
          </span>

          <span className={`px-2 py-1 text-xs rounded-full ${
            client.client_type === 'credit'
              ? 'bg-blue-100 text-blue-800 border border-blue-200'
              : 'bg-orange-100 text-orange-800 border border-orange-200'
          }`}>
            {client.client_type === 'credit' ? 'Credit' : 'Cash'}
          </span>
        </div>

        {/* Actions Menu */}
        <div className="relative" ref={menuRef}>
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="p-1 rounded-full hover:bg-gray-100"
          >
            <MoreVertical className="w-5 h-5 text-gray-500" />
          </button>

          {isMenuOpen && (
            <div className="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-50 border border-gray-200">
              <Link
                href={`/clients/${client.id}`}
                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                onClick={() => setIsMenuOpen(false)}
              >
                <Eye className="w-4 h-4 mr-2" />
                View Details
              </Link>

              <button
                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                onClick={() => {
                  setIsMenuOpen(false);
                  onEdit(client.id);
                }}
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit Client
              </button>

              <button
                onClick={() => {
                  onInvite(client.id);
                  setIsMenuOpen(false);
                }}
                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
              >
                <UserPlus className="w-4 h-4 mr-2" />
                Invite to System
              </button>

              <button
                onClick={() => {
                  onDelete(client.id);
                  setIsMenuOpen(false);
                }}
                className="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left border-t border-gray-200"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete Client
              </button>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

// Filter Bar Component
interface FilterBarProps {
  categories: ClientCategory[];
  onFilterChange: (filters: { category?: string; type?: string; status?: string }) => void;
  onSearch: (query: string) => void;
}

const FilterBar: React.FC<FilterBarProps> = ({ categories, onFilterChange, onSearch }) => {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchQuery);
  };

  const applyFilters = () => {
    onFilterChange({
      category: selectedCategory || undefined,
      type: selectedType || undefined,
      status: selectedStatus || undefined
    });
    setIsFilterOpen(false);
  };

  const clearFilters = () => {
    setSelectedCategory('');
    setSelectedType('');
    setSelectedStatus('');
    onFilterChange({});
    setIsFilterOpen(false);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        {/* Search Bar */}
        <form onSubmit={handleSearch} className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search clients..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            />
          </div>
        </form>

        <div className="flex space-x-2">
          {/* Filter Button */}
          <button
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
          >
            <Filter className="w-4 h-4 mr-2" />
            Filters
          </button>

          {/* Add Client Button */}
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="flex items-center px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Client
          </button>
        </div>
      </div>

      {/* Filter Panel */}
      {isFilterOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="mt-4 border-t pt-4"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.name}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Client Type Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Client Type</label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              >
                <option value="">All Types</option>
                <option value="credit">Credit</option>
                <option value="cash">Cash</option>
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          <div className="flex justify-end mt-4 space-x-2">
            <button
              onClick={clearFilters}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Clear
            </button>
            <button
              onClick={applyFilters}
              className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
            >
              Apply Filters
            </button>
          </div>
        </motion.div>
      )}
    </div>
  );
};

// Delete Confirmation Modal
interface DeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  clientName: string;
}

const DeleteModal: React.FC<DeleteModalProps> = ({ isOpen, onClose, onConfirm, clientName }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Delete Client</h3>
        <p className="text-gray-600 mb-4">
          Are you sure you want to delete <span className="font-medium">{clientName}</span>? This action cannot be undone.
        </p>

        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Delete
          </button>
        </div>
      </motion.div>
    </div>
  );
};

// Invite Modal
interface InviteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (roleId: string) => void;
  clientName: string;
  clientEmail: string | null;
}

const InviteModal: React.FC<InviteModalProps> = ({ isOpen, onClose, onConfirm, clientName, clientEmail }) => {
  const [selectedRole, setSelectedRole] = useState('');
  const [roles, setRoles] = useState<{id: string, name: string}[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchRoles = async () => {
      if (!isOpen) return;

      setIsLoading(true);
      try {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('roles')
          .select('id, name')
          .order('name');

        if (error) throw error;
        setRoles(data || []);

        // Set default role if available
        if (data && data.length > 0) {
          const clientRole = data.find(role => role.name === 'Client');
          setSelectedRole(clientRole?.id || data[0].id);
        }
      } catch (err) {
        console.error('Error fetching roles:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRoles();
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Invite Client to System</h3>

        {!clientEmail ? (
          <div className="mb-4">
            <div className="flex items-center text-amber-600 bg-amber-50 p-3 rounded-md">
              <AlertTriangle className="w-5 h-5 mr-2" />
              <p>This client doesn't have an email address. Please add an email before sending an invitation.</p>
            </div>
          </div>
        ) : (
          <>
            <p className="text-gray-600 mb-4">
              Send an invitation to <span className="font-medium">{clientName}</span> to create an account and access the system.
            </p>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Assign Role</label>
              {isLoading ? (
                <div className="flex justify-center py-2">
                  <LoadingSpinner size="sm" />
                </div>
              ) : (
                <select
                  value={selectedRole}
                  onChange={(e) => setSelectedRole(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                >
                  {roles.map((role) => (
                    <option key={role.id} value={role.id}>
                      {role.name}
                    </option>
                  ))}
                </select>
              )}
            </div>
          </>
        )}

        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          {clientEmail && (
            <button
              onClick={() => onConfirm(selectedRole)}
              className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
              disabled={isLoading || !selectedRole}
            >
              Send Invitation
            </button>
          )}
        </div>
      </motion.div>
    </div>
  );
};

// Main Client List Component
const ClientList: React.FC = () => {
  const [clients, setClients] = useState<Client[]>([]);
  const [categories, setCategories] = useState<ClientCategory[]>([]);
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Modal states
  const [deleteModal, setDeleteModal] = useState({ isOpen: false, clientId: '', clientName: '' });
  const [inviteModal, setInviteModal] = useState({ isOpen: false, clientId: '', clientName: '' });
  const [editModal, setEditModal] = useState({ isOpen: false, clientId: '', clientName: '' });
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // Fetch clients and categories
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const supabase = createClient();

        // Fetch categories
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('client_categories')
          .select('*');

        if (categoriesError) throw new Error(categoriesError.message);

        // Fetch clients with their category names
        const { data: clientsData, error: clientsError } = await supabase
          .from('clients')
          .select(`
            *,
            client_categories(name)
          `);

        if (clientsError) throw new Error(clientsError.message);

        // Process the data
        const processedClients = clientsData.map(client => ({
          ...client,
          category_name: client.client_categories?.name
        }));

        setCategories(categoriesData);
        setClients(processedClients);
        setFilteredClients(processedClients);
      } catch (err) {
        console.error('Error fetching client data:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Handle search
  const handleSearch = (query: string) => {
    if (!query.trim()) {
      setFilteredClients(clients);
      return;
    }

    const lowerQuery = query.toLowerCase();
    const filtered = clients.filter(client =>
      (client.name && client.name.toLowerCase().includes(lowerQuery)) ||
      (client.email && client.email.toLowerCase().includes(lowerQuery)) ||
      client.phone_number.toLowerCase().includes(lowerQuery)
    );

    setFilteredClients(filtered);
  };

  // Handle filters
  const handleFilterChange = (filters: { category?: string; type?: string; status?: string }) => {
    let filtered = [...clients];

    if (filters.category) {
      filtered = filtered.filter(client => client.category_name === filters.category);
    }

    if (filters.type) {
      filtered = filtered.filter(client => client.client_type === filters.type);
    }

    if (filters.status) {
      const isActive = filters.status === 'active';
      filtered = filtered.filter(client => client.is_active === isActive);
    }

    setFilteredClients(filtered);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!deleteModal.clientId) return;

    try {
      const supabase = createClient();
      const { error } = await supabase
        .from('clients')
        .delete()
        .eq('id', deleteModal.clientId);

      if (error) throw error;

      // Update state
      setClients(clients.filter(c => c.id !== deleteModal.clientId));
      setFilteredClients(filteredClients.filter(c => c.id !== deleteModal.clientId));
      setDeleteModal({ isOpen: false, clientId: '', clientName: '' });

      // Show success message (you can add toast notification here)
      console.log('Client deleted successfully');
    } catch (err) {
      console.error('Error deleting client:', err);
      // Show error message (you can add toast notification here)
    }
  };

  // Handle invite
  const handleInvite = async (roleId: string) => {
    if (!inviteModal.clientId || !roleId) return;

    try {
      const client = clients.find(c => c.id === inviteModal.clientId);
      if (!client) {
        throw new Error('Client not found');
      }

      if (!client.email) {
        throw new Error('Client has no email address');
      }

      // Show loading toast
      const loadingToast = toast.loading('Sending invitation...');

      // Call the client invitation API
      const response = await fetch('/api/clients/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: client.email,
          roleId: roleId,
          clientId: client.id
        }),
      });

      const data = await response.json();

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send invitation');
      }

      // Close the modal
      setInviteModal({ isOpen: false, clientId: '', clientName: '' });

      // Show success message
      toast.success(`Invitation sent to ${client.email}`);
    } catch (err) {
      console.error('Error sending invitation:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to send invitation');
    }
  };

  return (
    <div>
      {/* Filter Bar */}
      <FilterBar
        categories={categories}
        onFilterChange={handleFilterChange}
        onSearch={handleSearch}
      />

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <p>{error}</p>
        </div>
      )}

      {/* Client Grid */}
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <LoadingSpinner size={16} />
        </div>
      ) : filteredClients.length > 0 ? (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
        >
          {filteredClients.map((client) => (
            <ClientCard
              key={client.id}
              client={client}
              onDelete={(id) => setDeleteModal({
                isOpen: true,
                clientId: id,
                clientName: client.name || 'this client'
              })}
              onInvite={(id) => setInviteModal({
                isOpen: true,
                clientId: id,
                clientName: client.name || 'this client'
              })}
              onEdit={(id) => setEditModal({
                isOpen: true,
                clientId: id,
                clientName: client.name || 'this client'
              })}
            />
          ))}
        </motion.div>
      ) : (
        <div className="bg-white rounded-lg p-8 text-center">
          <p className="text-gray-500 mb-4">No clients found matching your criteria.</p>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="inline-flex items-center px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
          >
            <Plus className="w-4 h-4 mr-2" /> Add New Client
          </button>
        </div>
      )}

      {/* Modals */}
      <DeleteModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, clientId: '', clientName: '' })}
        onConfirm={handleDelete}
        clientName={deleteModal.clientName}
      />

      <InviteModal
        isOpen={inviteModal.isOpen}
        onClose={() => setInviteModal({ isOpen: false, clientId: '', clientName: '' })}
        onConfirm={handleInvite}
        clientName={inviteModal.clientName}
        clientEmail={clients.find(c => c.id === inviteModal.clientId)?.email || null}
      />

      {/* Edit Client Modal */}
      <ClientFormModal
        isOpen={editModal.isOpen}
        onClose={() => {
          setEditModal({ isOpen: false, clientId: '', clientName: '' });
          // Refresh the client list after editing
          const fetchData = async () => {
            try {
              const supabase = createClient();
              const { data, error } = await supabase
                .from('clients')
                .select(`
                  *,
                  client_categories(name)
                `);

              if (error) throw error;

              const processedClients = data.map(client => ({
                ...client,
                category_name: client.client_categories?.name
              }));

              setClients(processedClients);
              setFilteredClients(processedClients);
            } catch (err) {
              console.error('Error refreshing client data:', err);
            }
          };

          fetchData();
        }}
        clientId={editModal.clientId}
        title={`Edit Client: ${editModal.clientName}`}
      />

      {/* Add Client Modal */}
      <ClientFormModal
        isOpen={isAddModalOpen}
        onClose={() => {
          setIsAddModalOpen(false);
          // Refresh the client list after adding
          const fetchData = async () => {
            try {
              const supabase = createClient();
              const { data, error } = await supabase
                .from('clients')
                .select(`
                  *,
                  client_categories(name)
                `);

              if (error) throw error;

              const processedClients = data.map(client => ({
                ...client,
                category_name: client.client_categories?.name
              }));

              setClients(processedClients);
              setFilteredClients(processedClients);
            } catch (err) {
              console.error('Error refreshing client data:', err);
            }
          };

          fetchData();
        }}
        title="Add New Client"
      />
    </div>
  );
};

export default ClientList;
