// 1. Define Country Type (e.g., in @/types/index.ts or similar)
// Ensure you have this type definition accessible in your project.
/*
export interface Country {
  code: string; // e.g., '+254'
  name: string; // e.g., 'Kenya'
  flag: string; // Emoji representation
}
*/

// 2. PhoneInput Component (e.g., components/ui/inputs/PhoneInput.tsx)
"use client";

import React, { useState, useEffect, useRef, FocusEvent, ChangeEvent, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// --- Define Country Type (if not imported) ---
interface Country {
  code: string;
  name: string;
  flag: string;
}

// --- Country Data ---
const countries: Country[] = [
  { code: '+254', name: 'Kenya', flag: '🇰🇪' },
  { code: '+256', name: 'Uganda', flag: '🇺🇬' },
  { code: '+250', name: 'Rwanda', flag: '🇷🇼' },
  { code: '+255', name: 'Tanzania', flag: '🇹🇿' },
];

// --- Component Props ---
interface PhoneInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value' | 'onFocus' | 'onBlur' | 'type' | 'placeholder'> {
  label?: string; // The floating label text
  placeholder?: string; // Native input placeholder (optional, label is preferred)
  errorMessage?: string;
  helperText?: string;
  id: string; // Make id mandatory for label association & accessibility
  initialCountryCode?: string; // Optional initial country code (e.g., '+254')
  value?: string; // Controlled component value (full number e.g., '+254712345678')
  onChange?: (value: string) => void; // Callback with the full phone number string
  onCountryChange?: (country: Country) => void; // Optional: Callback when country changes
}

const PhoneInput = React.forwardRef<HTMLInputElement, PhoneInputProps>(
  (
    {
      label,
      placeholder, // Capture placeholder prop
      errorMessage,
      helperText,
      id,
      initialCountryCode = '+254', // Default to Kenya
      value = '',
      onChange,
      onCountryChange,
      disabled,
      className, // Allow passing additional classes
      ...props // Spread remaining input props (name, required, etc.)
    },
    ref
  ) => {
    // --- State Variables ---
    const [isFocused, setIsFocused] = useState(false);
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [selectedCountry, setSelectedCountry] = useState<Country>(() =>
        countries.find(c => value?.startsWith(c.code)) ||
        countries.find(c => c.code === initialCountryCode) ||
        countries[0]
    );
    const [phoneNumber, setPhoneNumber] = useState<string>(() => {
        const initialCountry = countries.find(c => value?.startsWith(c.code)) || selectedCountry;
        return value?.startsWith(initialCountry.code) ? value.substring(initialCountry.code.length) : value ?? '';
    });

    // --- Refs ---
    const dropdownRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null); // Ref for the main container

    // --- Derived State ---
    const hasError = !!errorMessage;
    // Label text prioritizes `label` prop, then `placeholder` from props, then a default
    const resolvedLabel = label || placeholder || 'Phone Number';
    // Input has value if the phone number part is non-empty
    const hasInput = phoneNumber.length > 0;
    // Determine if the label should float
    const isLabelFloated = isFocused || hasInput || !!value; // Float if focused, has input, or has a controlled value

    // --- Memoized Values ---
    // Calculate the width of the country selector dynamically based on the current code
    // This helps align the label and input correctly. Add extra space for flag, padding, dropdown arrow.
    const selectorWidth = useMemo(() => {
        const flagWidth = 24; // Approximate width for flag emoji
        const codeWidth = selectedCountry.code.length * 8; // Approximate width based on characters
        const padding = 12 + 8 + 4; // pl-3 pr-2 ml-1 = 12 + 8 + 4 = 24px approx
        const arrowWidth = 16; // Width of the dropdown arrow
        return flagWidth + codeWidth + padding + arrowWidth;
    }, [selectedCountry.code]);

    // --- Effects ---

    // Effect to handle controlled component updates
    useEffect(() => {
        const currentCountry = countries.find(c => value?.startsWith(c.code)) || selectedCountry;
        const numberPart = value?.startsWith(currentCountry.code) ? value.substring(currentCountry.code.length) : value ?? '';

        if (currentCountry.code !== selectedCountry.code) {
            setSelectedCountry(currentCountry);
        }
        if (numberPart !== phoneNumber) {
            setPhoneNumber(numberPart);
        }
    // Only run this effect if the external `value` prop changes. Avoid infinite loops.
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value]);


    // Effect to call onChange prop when selected country or phone number changes
    useEffect(() => {
      const combinedValue = `${selectedCountry.code}${phoneNumber}`;
      // Only call onChange if the combined value is different from the prop value
      // or if the component is uncontrolled (value prop is not provided).
      // This prevents unnecessary calls when the parent component updates the value prop.
      if (onChange && combinedValue !== value) {
        onChange(combinedValue);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedCountry, phoneNumber, onChange]); // Exclude `value` here intentionally

    // Effect to close dropdown on clicks outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        // Close if the click is outside the dropdown itself AND outside the main container
        if (
          dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
          containerRef.current && !containerRef.current.contains(event.target as Node)
        ) {
          setIsDropdownOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []); // Empty dependency array means this runs once on mount

    // --- Handlers ---
    const handleFocus = (event: FocusEvent<HTMLInputElement>) => {
      if (!disabled) {
        setIsFocused(true);
      }
      // Note: We don't call props.onFocus here because focus is managed by the container
    };

    const handleBlur = (event: FocusEvent<HTMLInputElement>) => {
      // Use setTimeout to allow click events on dropdown to register before blur
      setTimeout(() => {
          // Check if the new focused element is outside the main container
          if (containerRef.current && !containerRef.current.contains(document.activeElement)) {
              setIsFocused(false);
              setIsDropdownOpen(false); // Close dropdown on blur
          }
      }, 0);
      // Note: We don't call props.onBlur here because blur is managed by the container
    };

    const handleInputChange = (event: ChangeEvent<HTMLInputElement>) => {
      // Allow only digits
      const numericValue = event.target.value.replace(/\D/g, '');
      setPhoneNumber(numericValue);
    };

    const toggleDropdown = () => {
      if (!disabled) {
        setIsDropdownOpen(!isDropdownOpen);
        // Keep focus on the container when opening/closing dropdown
         if (containerRef.current) {
            containerRef.current.focus();
         }
      }
    };

    const selectCountry = (country: Country) => {
      if (selectedCountry.code !== country.code) {
          setSelectedCountry(country);
          if (onCountryChange) {
              onCountryChange(country);
          }
      }
      setIsDropdownOpen(false);
      // Focus the actual input field after selecting a country for better UX
      const inputElement = document.getElementById(id);
      inputElement?.focus();
    };

    // --- Dynamic Styles ---

    // Base classes for the container
    const containerBaseClasses = `
        relative flex items-center w-full h-14 px-0 bg-white dark:bg-gray-800
        border rounded-lg transition-colors duration-200 group
    `;
    // Dynamic classes based on state
    const containerStateClasses = disabled
      ? 'bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 cursor-not-allowed'
      : hasError
      ? 'border-red-500 dark:border-red-400'
      : isFocused
      ? 'border-blue-500 ring-1 ring-blue-500 dark:border-blue-500'
      : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500';

    // Base classes for the label
    const labelBaseClasses = `
        absolute transition-all duration-300 ease-in-out
        pointer-events-none origin-top-left z-10
    `;
    // Dynamic classes for the label based on state
    const labelStateClasses = isLabelFloated
      ? `text-xs -top-0 left-3 px-1 pt-1 ${disabled ? 'bg-gray-100 dark:bg-gray-700' : 'bg-white dark:bg-gray-800'}` // Adjust background to match input container
      : `text-base top-1/2 -translate-y-1/2`; // Start position adjusted dynamically below

    // Dynamic text color for the label
    const labelColorClasses = hasError
      ? 'text-red-700 dark:text-red-500'
      : isFocused
      ? 'text-blue-600 dark:text-blue-500'
      : 'text-gray-500 dark:text-gray-400';

    // Calculate label's left position when not floated
    const labelLeftStyle = isLabelFloated ? {} : { left: `${selectorWidth + 4}px` }; // Position after selector + small gap

    // Padding for the input element itself, adjusted when label is floated
    const inputPaddingClasses = isLabelFloated ? 'pt-5 pb-1' : 'py-3';

    // --- Accessibility ---
    const describedBy = hasError ? `${id}-error` : helperText ? `${id}-helper` : undefined;

    return (
      <div className={`w-full ${className || ''}`}>
        {/* Main Container - Handles border, focus state visual */}
        <div
          ref={containerRef}
          className={`${containerBaseClasses} ${containerStateClasses}`}
          onClick={() => {
              // Focus the hidden input when clicking the container (if not disabled)
              if (!disabled) {
                  const inputElement = document.getElementById(id);
                  inputElement?.focus();
              }
          }}
          // Add tabIndex to make the div focusable for blur detection, but hide focus ring
          tabIndex={-1}
          style={{ outline: 'none' }}
        >
          {/* --- Floating Label --- */}
          {resolvedLabel && (
            <label
              htmlFor={id}
              className={`${labelBaseClasses} ${labelStateClasses} ${labelColorClasses}`}
              style={labelLeftStyle} // Apply dynamic left position
            >
              {resolvedLabel}
            </label>
          )}

          {/* --- Country Selector Button & Dropdown --- */}
          <div className="relative h-full flex items-center" ref={dropdownRef}>
            {/* Button to trigger dropdown */}
            <button
              type="button"
              onClick={toggleDropdown}
              disabled={disabled}
              className={`flex items-center justify-center h-full pl-3 pr-2 focus:outline-none rounded-l-lg transition-colors ${
                disabled ? 'cursor-not-allowed' : 'hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
              aria-haspopup="listbox"
              aria-expanded={isDropdownOpen}
              aria-label="Select country code"
            >
              <span className="text-xl mr-1 select-none">{selectedCountry.flag}</span>
              <span className="text-sm text-gray-600 dark:text-gray-300 min-w-[35px] select-none">{selectedCountry.code}</span>
              {/* Dropdown Arrow */}
              <svg xmlns="http://www.w3.org/2000/svg" className={`h-4 w-4 ml-1 text-gray-500 transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>

            {/* Dropdown Menu (Animated) */}
            <AnimatePresence>
              {isDropdownOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10, transition: { duration: 0.15 } }}
                  transition={{ duration: 0.2, ease: 'easeOut' }}
                  className="absolute top-full left-0 mt-1 w-56 max-h-60 overflow-y-auto bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-30" // Increased z-index
                  role="listbox"
                  aria-orientation="vertical"
                >
                  {countries.map((country) => (
                    <button
                      key={country.code}
                      type="button"
                      className={`flex items-center w-full px-4 py-2 text-sm text-left transition-colors ${
                        selectedCountry.code === country.code
                          ? 'font-semibold text-blue-600 bg-blue-50 dark:bg-gray-700 dark:text-blue-300'
                          : 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                      }`}
                      onClick={() => selectCountry(country)}
                      role="option"
                      aria-selected={selectedCountry.code === country.code}
                    >
                      <span className="text-xl mr-3 select-none">{country.flag}</span>
                      <span className="flex-1 select-none">{country.name} ({country.code})</span>
                    </button>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* --- Phone Number Input --- */}
          {/* This is the actual input element */}
          <input
            ref={ref} // Forward ref to this input element
            id={id}
            type="tel" // Use "tel" type for semantic correctness and mobile keyboards
            inputMode="numeric" // Hint for numeric keyboard on mobile
            value={phoneNumber}
            onChange={handleInputChange}
            onFocus={handleFocus} // Trigger container focus state
            onBlur={handleBlur}   // Trigger container blur state
            disabled={disabled}
            aria-describedby={describedBy} // Link to error/helper text
            aria-invalid={hasError} // Indicate error state for screen readers
            placeholder={isFocused || isLabelFloated ? placeholder : ''} // Show native placeholder only when label is floated
            className={`
              flex-1 h-full px-2 bg-transparent border-none
              text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500
              focus:outline-none focus:ring-0 appearance-none peer
              ${inputPaddingClasses}
              ${disabled ? 'cursor-not-allowed text-gray-500 dark:text-gray-400' : ''}
            `}
            {...props} // Spread other native input attributes (name, required, etc.)
          />
        </div>

        {/* --- Helper and Error Text --- */}
        {/* Display error message if present */}
        {hasError && errorMessage && (
          <p id={`${id}-error`} className="mt-1 text-xs text-red-600 dark:text-red-500" role="alert">
            {errorMessage}
          </p>
        )}
        {/* Display helper text if no error and helperText is provided */}
        {!hasError && helperText && (
          <p id={`${id}-helper`} className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            {helperText}
          </p>
        )}
      </div>
    );
  }
);

PhoneInput.displayName = 'PhoneInput'; // Set display name for DevTools

export default PhoneInput;


// 3. Example Usage (e.g., in a page or another component)
/*
"use client";

import React, { useState } from 'react';
import PhoneInput from '@/components/ui/inputs/PhoneInput'; // Adjust path as needed

export default function MyForm() {
  const [phone, setPhone] = useState(''); // State to hold the full phone number
  const [phoneError, setPhoneError] = useState('');

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    console.log('Submitted Phone:', phone);
    // Add validation logic
    if (phone.length < 10) { // Example basic validation
        setPhoneError('Please enter a valid phone number.');
    } else {
        setPhoneError('');
        // Proceed with form submission...
    }
  };

  return (
    <div className="p-8 max-w-md mx-auto bg-gray-50 dark:bg-gray-900 rounded-xl shadow-md space-y-6">
       <h1 className="text-2xl font-bold text-center text-gray-800 dark:text-white">Sign Up</h1>
      <form onSubmit={handleSubmit} className="space-y-6">
        <PhoneInput
          id="phone-number"
          label="Phone Number" // Floating label
          value={phone}
          onChange={setPhone} // Update state with the full number '+254...'
          errorMessage={phoneError}
          helperText="We'll send a verification code here."
          initialCountryCode="+254" // Start with Kenya selected
          // You can add other props like 'name', 'required', etc.
          name="phoneNumber"
          required
        />

         {/* Example Disabled State *\/}
         <PhoneInput
          id="phone-number-disabled"
          label="Disabled Input"
          value="+2551234567"
          disabled
        />

        <button
          type="submit"
          className="w-full px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
        >
          Submit
        </button>
      </form>
       <div className="mt-4 p-4 bg-gray-100 dark:bg-gray-800 rounded">
            <p className="text-sm text-gray-700 dark:text-gray-300">Current Value:</p>
            <p className="font-mono text-gray-900 dark:text-white">{phone || " "}</p>
       </div>
    </div>
  );
}
*/

