'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, AlertTriangle, Trash2 } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';

interface DeleteConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemId: number;
  itemName: string;
  itemType: 'area' | 'unit';
  onSuccess: (deletedItem?: any, itemType?: 'area' | 'unit', action?: 'add' | 'update' | 'delete') => void;
}

const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
  isOpen,
  onClose,
  itemId,
  itemName,
  itemType,
  onSuccess
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const supabase = createClient();

  const handleDelete = async () => {
    setIsDeleting(true);
    setError(null);

    try {
      let error;
      let deletedItem;

      if (itemType === 'area') {
        // Get the area_id for the deleted item
        deletedItem = { area_id: itemId };

        const { error: deleteError } = await supabase
          .from('storage_areas')
          .delete()
          .eq('area_id', itemId);

        error = deleteError;
      } else {
        // For units, we need both the unit_id and area_id
        // First, get the area_id for this unit
        const { data: unitData, error: fetchError } = await supabase
          .from('storage_units')
          .select('area_id')
          .eq('unit_id', itemId)
          .single();

        if (fetchError) throw fetchError;

        deletedItem = { unit_id: itemId, area_id: unitData.area_id };

        const { error: deleteError } = await supabase
          .from('storage_units')
          .delete()
          .eq('unit_id', itemId);

        error = deleteError;
      }

      if (error) throw error;

      // Success
      onSuccess(deletedItem, itemType, 'delete');
      onClose();
    } catch (err: any) {
      console.error(`Error deleting ${itemType}:`, err);
      setError(err.message || `Failed to delete ${itemType}`);
    } finally {
      setIsDeleting(false);
    }
  };

  // Animation variants
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3 } }
  };

  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    exit: {
      opacity: 0,
      y: 50,
      scale: 0.95,
      transition: { duration: 0.2 }
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          onClick={onClose}
        >
          <motion.div
            className="bg-white rounded-lg shadow-xl w-full max-w-md overflow-hidden"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <div className="flex items-center">
                <AlertTriangle className="w-6 h-6 text-red-600 mr-3" />
                <h2 className="text-xl font-semibold text-gray-800">Confirm Deletion</h2>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {error && (
              <div className="px-6 pt-4 pb-0">
                <div className="p-3 bg-red-50 text-red-700 rounded-md text-sm">
                  {error}
                </div>
              </div>
            )}

            <div className="p-6">
              <p className="text-gray-700 mb-6">
                Are you sure you want to delete the {itemType} <span className="font-semibold">{itemName}</span>?
                {itemType === 'area' && (
                  <span className="block mt-2 text-red-600">
                    This will also delete all storage units associated with this area and remove all part locations stored in these units.
                  </span>
                )}
                {itemType === 'unit' && (
                  <span className="block mt-2 text-red-600">
                    This will also remove all part locations stored in this unit.
                  </span>
                )}
              </p>

              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 mr-2"
                  disabled={isDeleting}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleDelete}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center"
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete {itemType === 'area' ? 'Area' : 'Unit'}
                    </>
                  )}
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default DeleteConfirmModal;
