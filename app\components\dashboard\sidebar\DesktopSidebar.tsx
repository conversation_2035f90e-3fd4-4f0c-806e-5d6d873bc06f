'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Sun, Moon, Users, Lock, Shield, FileText } from 'lucide-react';
import { menuItems } from '@/app/config/menu';
import { createClient } from '@/app/libs/supabase/client';
import { useAuth } from '@/app/hooks/useAuth';
import { useUserCookie } from '@/app/hooks/useUserCookie';

export default function DesktopSidebar() {
    const pathname = usePathname();
    const [isDarkMode, setIsDarkMode] = useState(false);
    const [userPermissions, setUserPermissions] = useState<string[]>([]);
    const { user } = useAuth();
    const { roleName: userRole } = useUserCookie();

    // Define which menu items should appear at the bottom of the sidebar
    const bottomItemIds = ['settings', 'profile', 'logout'];

    // Define admin menu items that should appear in the sidebar when in admin pages
    const adminMenuItems = [
        { id: 'user-management', icon: Users, href: '/admin/users', label: 'User Management', permission: 'admin:manage_users' },
        { id: 'role-management', icon: Shield, href: '/admin/roles', label: 'Role Management', permission: 'admin:manage_roles' },
        { id: 'permission-management', icon: Lock, href: '/admin/permissions', label: 'Permission Management', permission: 'admin:manage_permissions' },
        { id: 'audit-log', icon: FileText, href: '/admin/audit', label: 'Audit Log', permission: 'admin:view_audit_log' },
    ];

    // Fetch user permissions
    useEffect(() => {
        const fetchUserPermissions = async () => {
            if (!user) return;

            // For permissions, we'll use a simplified approach
            try {
                const supabase = createClient();

                // Use a stored procedure to get the permissions
                const { data, error } = await supabase
                    .rpc('get_user_permissions', { user_id_param: user.id });

                if (error) {

                    setUserPermissions([]);
                } else if (data && Array.isArray(data)) {

                    setUserPermissions(data);
                } else {

                    setUserPermissions([]);
                }
            } catch (error) {

                setUserPermissions([]);
            }
        };

        fetchUserPermissions();
    }, [user]);

    // Filter menu items for top and bottom sections
    const topMenuItems = menuItems.filter(item => !bottomItemIds.includes(item.id));
    const bottomMenuItems = menuItems.filter(item => bottomItemIds.includes(item.id));

    // Check if current path is in admin section
    const isAdminSection = pathname?.startsWith('/admin');

    // Check if user has admin access
    const hasAdminAccess = userRole === 'Admin' || userRole === 'Super Admin' || userPermissions.includes('admin:access_panel');

    // Function to check if user has permission for a specific item
    const hasPermission = (permission?: string) => {
        if (!permission) return true; // No permission required
        if (userRole === 'Admin' || userRole === 'Super Admin') return true; // Admin and Super Admin have all permissions
        return userPermissions.includes(permission);
    };

    const toggleDarkMode = () => {
        setIsDarkMode(!isDarkMode);
        // Implement actual dark mode toggle functionality here
    };

    return (
        <aside className="hidden md:flex md:flex-col md:w-16 bg-white border-r border-gray-200 min-h-screen justify-between">
            <div>
                {/* Logo Placeholder */}
                <div className="p-3 mb-6 flex justify-center">
                    <div className="text-indigo-600 w-8 h-8">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2L4 7l8 5 8-5-8-5zm0 10L4 17l8 5 8-5-8-5z" />
                        </svg>
                    </div>
                </div>

                {/* Menu Items */}
                <nav className="flex flex-col items-center space-y-6">
                    {topMenuItems.map((item) => {
                        // Skip admin item if user doesn't have admin access
                        if (item.id === 'admin' && !hasAdminAccess) return null;

                        const Icon = item.icon;
                        const isActive = pathname === item.href ||
                                        (item.id === 'admin' && isAdminSection);

                        return (
                            <Link
                                key={item.id}
                                href={item.href}
                                className={`p-2 rounded-md transition-colors duration-200 ${isActive ? 'text-indigo-600' : 'text-gray-400 hover:text-gray-900'}`}
                                title={item.label} // Use the label for tooltip
                            >
                                <Icon size={20} />
                            </Link>
                        );
                    }).filter(Boolean)}

                    {/* Admin menu items with separator - only shown when in admin section */}
                    {isAdminSection && hasAdminAccess && (
                        <>
                            <div className="w-8 h-px bg-gray-200 my-2"></div>
                            {adminMenuItems.map((item) => {
                                // Skip items user doesn't have permission for
                                if (!hasPermission(item.permission)) return null;

                                const Icon = item.icon;
                                const isActive = pathname === item.href;

                                return (
                                    <Link
                                        key={item.id}
                                        href={item.href}
                                        className={`p-2 rounded-md transition-colors duration-200 ${isActive ? 'text-indigo-600' : 'text-gray-400 hover:text-gray-900'}`}
                                        title={item.label}
                                    >
                                        <Icon size={20} />
                                    </Link>
                                );
                            }).filter(Boolean)}
                        </>
                    )}
                </nav>
            </div>

            {/* Bottom Items */}

        </aside>
    );
}