import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Part, Category, SearchFilters, PaginatedResponse } from '@/types';
import { partsService } from '@/services/parts';
import { CONFIG } from '@/constants/config';

interface PartsState {
  // State
  parts: Part[];
  categories: Category[];
  selectedPart: Part | null;
  searchResults: Part[];
  searchFilters: SearchFilters;
  searchHistory: string[];
  favorites: number[];
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    totalPages: number;
    count: number;
  };

  // Actions
  loadParts: (page?: number, filters?: SearchFilters) => Promise<void>;
  searchParts: (query: string, filters?: SearchFilters) => Promise<void>;
  loadPartDetails: (partId: number) => Promise<void>;
  loadCategories: () => Promise<void>;
  loadPartsByCategory: (categoryId: number, page?: number) => Promise<void>;
  setSearchFilters: (filters: SearchFilters) => void;
  addToSearchHistory: (query: string) => void;
  clearSearchHistory: () => void;
  addToFavorites: (partId: number) => void;
  removeFromFavorites: (partId: number) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  reset: () => void;
}

const initialState = {
  parts: [],
  categories: [],
  selectedPart: null,
  searchResults: [],
  searchFilters: {},
  searchHistory: [],
  favorites: [],
  isLoading: false,
  error: null,
  pagination: {
    page: 1,
    limit: CONFIG.SETTINGS.DEFAULT_PAGE_SIZE,
    totalPages: 0,
    count: 0,
  },
};

export const usePartsStore = create<PartsState>()(
  persist(
    (set, get) => ({
      ...initialState,

      loadParts: async (page = 1, filters) => {
        set({ isLoading: true, error: null });
        try {
          const response = await partsService.getParts(
            page,
            get().pagination.limit,
            filters
          );

          set({
            parts: page === 1 ? response.data : [...get().parts, ...response.data],
            pagination: {
              page: response.page,
              limit: response.limit,
              totalPages: response.totalPages,
              count: response.count,
            },
            isLoading: false,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to load parts';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      searchParts: async (query: string, filters) => {
        set({ isLoading: true, error: null });
        try {
          const response = await partsService.searchParts(query, 1, get().pagination.limit, filters);
          
          set({
            searchResults: response.data,
            pagination: {
              page: response.page,
              limit: response.limit,
              totalPages: response.totalPages,
              count: response.count,
            },
            isLoading: false,
          });

          // Add to search history
          get().addToSearchHistory(query);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Search failed';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      loadPartDetails: async (partId: number) => {
        set({ isLoading: true, error: null });
        try {
          const part = await partsService.getPartDetails(partId);
          set({
            selectedPart: part,
            isLoading: false,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to load part details';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      loadCategories: async () => {
        try {
          const categories = await partsService.getCategories();
          set({ categories });
        } catch (error) {
          console.error('Failed to load categories:', error);
        }
      },

      loadPartsByCategory: async (categoryId: number, page = 1) => {
        set({ isLoading: true, error: null });
        try {
          const response = await partsService.getPartsByCategory(
            categoryId,
            page,
            get().pagination.limit
          );

          set({
            parts: page === 1 ? response.data : [...get().parts, ...response.data],
            pagination: {
              page: response.page,
              limit: response.limit,
              totalPages: response.totalPages,
              count: response.count,
            },
            isLoading: false,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to load category parts';
          set({ isLoading: false, error: errorMessage });
          throw error;
        }
      },

      setSearchFilters: (filters: SearchFilters) => {
        set({ searchFilters: filters });
      },

      addToSearchHistory: (query: string) => {
        const { searchHistory } = get();
        const trimmedQuery = query.trim();
        
        if (trimmedQuery && !searchHistory.includes(trimmedQuery)) {
          const newHistory = [trimmedQuery, ...searchHistory.slice(0, 9)]; // Keep last 10 searches
          set({ searchHistory: newHistory });
        }
      },

      clearSearchHistory: () => {
        set({ searchHistory: [] });
      },

      addToFavorites: (partId: number) => {
        const { favorites } = get();
        if (!favorites.includes(partId)) {
          set({ favorites: [...favorites, partId] });
        }
      },

      removeFromFavorites: (partId: number) => {
        const { favorites } = get();
        set({ favorites: favorites.filter(id => id !== partId) });
      },

      clearError: () => set({ error: null }),

      setLoading: (loading: boolean) => set({ isLoading: loading }),

      reset: () => set(initialState),
    }),
    {
      name: CONFIG.STORAGE_KEYS.SEARCH_HISTORY,
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        searchHistory: state.searchHistory,
        favorites: state.favorites,
        categories: state.categories,
      }),
    }
  )
);

// Helper hooks
export const useParts = () => {
  const store = usePartsStore();
  return {
    parts: store.parts,
    categories: store.categories,
    selectedPart: store.selectedPart,
    searchResults: store.searchResults,
    searchFilters: store.searchFilters,
    searchHistory: store.searchHistory,
    favorites: store.favorites,
    isLoading: store.isLoading,
    error: store.error,
    pagination: store.pagination,
    loadParts: store.loadParts,
    searchParts: store.searchParts,
    loadPartDetails: store.loadPartDetails,
    loadCategories: store.loadCategories,
    loadPartsByCategory: store.loadPartsByCategory,
    setSearchFilters: store.setSearchFilters,
    addToSearchHistory: store.addToSearchHistory,
    clearSearchHistory: store.clearSearchHistory,
    addToFavorites: store.addToFavorites,
    removeFromFavorites: store.removeFromFavorites,
    clearError: store.clearError,
  };
};

export const usePartDetails = (partId?: number) => {
  const selectedPart = usePartsStore((state) => state.selectedPart);
  const loadPartDetails = usePartsStore((state) => state.loadPartDetails);
  const isLoading = usePartsStore((state) => state.isLoading);
  const error = usePartsStore((state) => state.error);

  return {
    part: selectedPart,
    isLoading,
    error,
    loadPartDetails,
    isCurrentPart: selectedPart?.id === partId,
  };
};
