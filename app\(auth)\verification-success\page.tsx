// app/auth/verification-success/page.tsx

'use client';

import { getCookie, deleteCookie } from 'cookies-next';
import Link from 'next/link';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function VerificationSuccessPage() {
  const router = useRouter();

  useEffect(() => {
    const isSuccess = getCookie('verification-success');
    if (!isSuccess) {
      router.push('/auth/login'); // Redirect to login if no cookie
    } else {
      // Optional: Delete the cookie after it's been used
      deleteCookie('verification-success');
    }
  }, [router]);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-2">
      <main className="flex w-full flex-1 flex-col items-center justify-center px-20 text-center">
        <h1 className="text-4xl font-bold">Verification Successful!</h1>
        <p className="mt-3 text-2xl">
          Your email has been verified. You can now log in.
        </p>
        <Link href="/login" className="mt-6 text-blue-500 hover:underline">
          Go to Login
        </Link>
      </main>
    </div>
  );
}