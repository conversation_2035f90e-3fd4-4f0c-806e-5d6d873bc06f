/* Property Page Styles */

/* Responsive container */
.property-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 1rem;
}

/* Property header */
.property-header {
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  .property-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }
}

/* Property image gallery */
.image-gallery {
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
}

.image-gallery-main {
  position: relative;
  height: 400px;
  width: 100%;
  border-radius: 0.5rem;
  overflow: hidden;
}

@media (min-width: 1024px) {
  .image-gallery-main {
    height: 500px;
  }
}

.image-gallery-thumbnails {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
}

.image-gallery-thumbnail {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 0.375rem;
  overflow: hidden;
  cursor: pointer;
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.image-gallery-thumbnail.active {
  border: 2px solid #3b82f6;
}

/* Property details */
.property-details {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.property-attributes {
  display: flex;
  justify-content: space-between;
  padding-bottom: 1.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.property-description {
  margin-bottom: 1.5rem;
}

.property-location {
  margin-bottom: 1.5rem;
}

.property-seller {
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.seller-info {
  display: flex;
  align-items: flex-start;
}

.seller-avatar {
  position: relative;
  width: 3rem;
  height: 3rem;
  border-radius: 9999px;
  overflow: hidden;
  margin-right: 1rem;
}

.seller-details {
  flex: 1;
}

.contact-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-top: 1.5rem;
}

/* Similar properties */
.similar-properties {
  margin-top: 3rem;
}

.similar-properties-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .similar-properties-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 1024px) {
  .similar-properties-grid {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease forwards;
}

/* Utility classes */
.text-primary {
  color: #3b82f6;
}

.bg-primary {
  background-color: #3b82f6;
}

.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.03);
}
