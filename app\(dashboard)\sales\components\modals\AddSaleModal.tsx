'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Save, FileText } from 'lucide-react';
import ProductSearchStep from './steps/ProductSearchStep';
import ClientDetailsStep from './steps/ClientDetailsStep';
import SaleSummaryStep from './steps/SaleSummaryStep';
import DraftsModal from './DraftsModal';
import { useSalesDrafts, SalesDraft } from '../../hooks/useSalesDrafts';
import { toast } from 'react-hot-toast';

interface AddSaleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSaleAdded: () => void;
  initialDraft?: SalesDraft | null;
}

export type SelectedProduct = {
  id: number;
  title: string;
  price: number;
  stock: number;
  quantity: number;
  discount: number;
  discountReason?: string;
  thumbnailUrl?: string;
};

export type SaleFormData = {
  saleType: 'cash' | 'credit';
  cashPaymentMethod?: 'cash' | 'mpesa';
  deliveryOption?: 'at_shop' | 'delivered';
  clientType?: 'regular' | 'one_off';
  clientId?: string;
  oneOffClientName?: string;
  oneOffClientPhone?: string;
  mpesaConfirmation?: string;
};

const AddSaleModal: React.FC<AddSaleModalProps> = ({ isOpen, onClose, onSaleAdded, initialDraft }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedProducts, setSelectedProducts] = useState<SelectedProduct[]>([]);
  const [saleFormData, setSaleFormData] = useState<SaleFormData>({
    saleType: 'cash',
  });
  const [isDraftsModalOpen, setIsDraftsModalOpen] = useState(false);
  const [isAutoSaving, setIsAutoSaving] = useState(false);

  // Draft management
  const { autoSaveDraft, currentDraftId, setCurrentDraftId, clearCurrentDraft, deleteDraft } = useSalesDrafts();

  // Auto-save timer ref
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Load draft data when initialDraft is provided
  useEffect(() => {
    if (initialDraft) {
      setCurrentStep(initialDraft.current_step);
      setSaleFormData(initialDraft.form_data || { saleType: 'cash' });
      setSelectedProducts(initialDraft.selected_products || []);
      setCurrentDraftId(initialDraft.id);
    }
  }, [initialDraft, setCurrentDraftId]);

  // Auto-save function
  const performAutoSave = async () => {
    if (!isOpen) return;

    setIsAutoSaving(true);
    try {
      await autoSaveDraft({
        currentStep,
        formData: saleFormData,
        selectedProducts
      }, currentDraftId || undefined);
    } catch (error) {
      console.error('Auto-save failed:', error);
    } finally {
      setIsAutoSaving(false);
    }
  };

  // Auto-save when data changes
  useEffect(() => {
    if (!isOpen) return;

    // Clear existing timer
    if (autoSaveTimerRef.current) {
      clearTimeout(autoSaveTimerRef.current);
    }

    // Set new timer for auto-save (debounced)
    autoSaveTimerRef.current = setTimeout(() => {
      performAutoSave();
    }, 2000); // Auto-save after 2 seconds of inactivity

    // Cleanup timer on unmount
    return () => {
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
      }
    };
  }, [currentStep, saleFormData, selectedProducts, isOpen]);

  const handleProductsSelected = (products: SelectedProduct[]) => {
    setSelectedProducts(products);
    setCurrentStep(2);
  };

  const handleClientDetailsSubmit = (formData: SaleFormData) => {
    setSaleFormData(formData);
    setCurrentStep(3);
  };

  const handleSaleComplete = async () => {
    // Delete the current draft since sale is complete
    if (currentDraftId) {
      try {
        await deleteDraft(currentDraftId);
        console.log('Draft deleted successfully after sale completion');
        // Note: deleteDraft already shows a success toast, so we don't need another one
      } catch (error) {
        console.error('Failed to delete draft after sale completion:', error);
        // Don't block the sale completion if draft deletion fails
        // The user will still see the draft in their list, but the sale was successful
      }
    }

    // Clear the current draft ID from state
    clearCurrentDraft();

    // Reset form state only after a successful sale
    setSelectedProducts([]);
    setSaleFormData({ saleType: 'cash' });
    setCurrentStep(1);
    onSaleAdded();
    onClose();
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleClose = async () => {
    // Save draft before closing if there's any progress
    const hasProgress = selectedProducts.length > 0 ||
                       Object.keys(saleFormData).length > 1 ||
                       currentStep > 1;

    if (hasProgress) {
      try {
        await performAutoSave();
        toast.success('Progress saved as draft');
      } catch (error) {
        console.error('Failed to save draft on close:', error);
        toast.error('Failed to save progress');
      }
    }

    // Reset form state when closing
    setSelectedProducts([]);
    setSaleFormData({ saleType: 'cash' });
    setCurrentStep(1);
    onClose();
  };

  const handleLoadDraft = (draft: SalesDraft) => {
    setCurrentStep(draft.current_step);
    setSaleFormData(draft.form_data || { saleType: 'cash' });
    setSelectedProducts(draft.selected_products || []);
    setCurrentDraftId(draft.id);
    toast.success(`Loaded draft: ${draft.draft_name}`);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] overflow-y-auto">
      {/* Overlay and Modal Content as siblings in the same stacking context */}
      <div className="fixed inset-0 z-[9999]">
        {/* Overlay */}
        <div className="absolute inset-0 bg-gray-500 opacity-75 z-[9998]" aria-hidden="true"></div>
        {/* Modal Content */}
        <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0 relative z-[10000]">
          <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full z-[10000] max-h-[90vh] overflow-y-auto"
          >
            {/* Modal Header */}
            <div className="bg-gray-50 px-6 py-4 flex justify-between items-center">
              <div className="flex items-center">
                <h3 className="text-lg font-medium text-gray-900 mr-4">
                  {currentStep === 1 && 'Add Sale - Select Products'}
                  {currentStep === 2 && 'Add Sale - Client Details'}
                  {currentStep === 3 && 'Add Sale - Review & Confirm'}
                </h3>
                {isAutoSaving && (
                  <div className="flex items-center text-sm text-gray-500">
                    <Save className="h-4 w-4 mr-1 animate-pulse" />
                    <span>Saving...</span>
                  </div>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setIsDraftsModalOpen(true)}
                  className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
                  title="Load Draft"
                >
                  <FileText className="h-4 w-4 mr-1" />
                  <span>Drafts</span>
                </button>
                <button
                  onClick={handleClose}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="bg-white px-6 py-4">
              {/* Step Indicator */}
              <div className="mb-6">
                <div className="flex items-center justify-between">
                  {[1, 2, 3].map((step) => (
                    <React.Fragment key={step}>
                      <div className="flex flex-col items-center">
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            step === currentStep
                              ? 'bg-teal-600 text-white'
                              : step < currentStep
                              ? 'bg-teal-200 text-teal-800'
                              : 'bg-gray-200 text-gray-600'
                          }`}
                        >
                          {step}
                        </div>
                        <span className="text-xs mt-1">
                          {step === 1 && 'Products'}
                          {step === 2 && 'Client'}
                          {step === 3 && 'Confirm'}
                        </span>
                      </div>
                      {step < 3 && (
                        <div
                          className={`flex-1 h-1 mx-2 ${
                            step < currentStep ? 'bg-teal-600' : 'bg-gray-200'
                          }`}
                        ></div>
                      )}
                    </React.Fragment>
                  ))}
                </div>
              </div>

              {/* Step Content */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  {currentStep === 1 && (
                    <ProductSearchStep
                      onProductsSelected={handleProductsSelected}
                      onCancel={handleClose}
                      initialProducts={selectedProducts}
                    />
                  )}
                  {currentStep === 2 && (
                    <ClientDetailsStep
                      onSubmit={handleClientDetailsSubmit}
                      onBack={handleBack}
                      initialData={saleFormData}
                    />
                  )}
                  {currentStep === 3 && (
                    <SaleSummaryStep
                      products={selectedProducts}
                      formData={saleFormData}
                      onBack={handleBack}
                      onComplete={handleSaleComplete}
                    />
                  )}
                </motion.div>
              </AnimatePresence>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Drafts Modal */}
      <DraftsModal
        isOpen={isDraftsModalOpen}
        onClose={() => setIsDraftsModalOpen(false)}
        onLoadDraft={handleLoadDraft}
      />
    </div>
  );
};

export default AddSaleModal;
