import { NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const brandId = searchParams.get('brandId');
    
    if (!brandId) {
      return NextResponse.json({ error: 'brandId is required' }, { status: 400 });
    }
    
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from('car_models')
      .select('*')
      .eq('brand_id', brandId)
      .order('model_name');
      
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    const models = data.map((model: any) => ({
      id: model.id,
      brandId: model.brand_id,
      name: model.model_name
    }));
    
    return NextResponse.json(models);
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}