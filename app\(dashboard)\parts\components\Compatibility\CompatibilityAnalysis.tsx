// components/Compatibility/CompatibilityAnalysis.tsx
import React from 'react';

interface CompatibilityAnalysisProps {
  data: {
    partName: string;
    compatiblePartNumbers: string[];
    isEnginePart: boolean;
    engineCompatibility?: Array<{
      engineCode: string;
      engineCapacity: number;
      fuelType: string;
      engineType: string;
    }>;
    vehicleCompatibility?: Array<{
      brand: string;
      model: string;
      generation: string;
      trims: string[];
    }>;
  };
}

export default function CompatibilityAnalysis({ data }: CompatibilityAnalysisProps) {
  return (
    <div className="mt-4 p-4 bg-blue-50 rounded-lg">
      <h3 className="font-semibold mb-2">AI Analysis Results:</h3>
      
      <div className="mb-2">
        <p className="font-medium">Part Name:</p>
        <p className="text-gray-700">{data.partName}</p>
      </div>
      
      {data.compatiblePartNumbers.length > 0 && (
        <div className="mb-2">
          <p className="font-medium">Compatible Part Numbers:</p>
          <ul className="list-disc pl-4">
            {data.compatiblePartNumbers.map((pn, index) => (
              <li key={index}>{pn}</li>
            ))}
          </ul>
        </div>
      )}
      
      {data.isEnginePart && data.engineCompatibility && (
        <div className="mt-2">
          <h4 className="font-medium">Engine Compatibility:</h4>
          {data.engineCompatibility.map((engine, index) => (
            <div key={index} className="ml-4 mt-2">
              <p className="font-medium">{engine.engineCode}</p>
              <ul className="list-disc pl-4">
                <li>Capacity: {engine.engineCapacity}L</li>
                <li>Fuel Type: {engine.fuelType}</li>
                <li>Engine Type: {engine.engineType}</li>
              </ul>
            </div>
          ))}
        </div>
      )}

      {!data.isEnginePart && data.vehicleCompatibility && (
        <div className="mt-2">
          <h4 className="font-medium">Vehicle Compatibility:</h4>
          {data.vehicleCompatibility.map((vehicle, index) => (
            <div key={index} className="ml-4 mt-2">
              <p className="font-medium">{vehicle.brand} {vehicle.model}</p>
              <p className="text-gray-600">{vehicle.generation}</p>
              <p className="text-sm text-gray-500">Trims:</p>
              <ul className="list-disc pl-4">
                {vehicle.trims.map((trim, tIndex) => (
                  <li key={tIndex}>{trim}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}