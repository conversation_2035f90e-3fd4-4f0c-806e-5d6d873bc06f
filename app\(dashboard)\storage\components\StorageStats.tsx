'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Warehouse, Package, MapPin, Home, Layers, Grid, Truck, Box, Clipboard } from 'lucide-react';
import { StorageStats as StorageStatsType } from '../types';

interface StorageStatsProps {
  stats: StorageStatsType;
  isLoading: boolean;
}

const StorageStats: React.FC<StorageStatsProps> = ({ stats, isLoading }) => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  // Stat cards data
  const statCards = [
    {
      title: 'Total Areas',
      value: stats.totalAreas,
      icon: <Warehouse className="w-6 h-6 text-teal-600" />,
      color: 'bg-teal-50 border-teal-200'
    },
    {
      title: 'Total Units',
      value: stats.totalUnits,
      icon: <Box className="w-6 h-6 text-orange-600" />,
      color: 'bg-orange-50 border-orange-200'
    },
    {
      title: 'Parts Stored',
      value: stats.partsStored,
      icon: <Package className="w-6 h-6 text-gray-600" />,
      color: 'bg-gray-50 border-gray-200'
    },
    {
      title: 'Indoor Areas',
      value: stats.indoorAreas,
      icon: <Home className="w-6 h-6 text-teal-600" />,
      color: 'bg-teal-50 border-teal-200'
    },
    {
      title: 'Outdoor Areas',
      value: stats.outdoorAreas,
      icon: <MapPin className="w-6 h-6 text-orange-600" />,
      color: 'bg-orange-50 border-orange-200'
    },
    {
      title: 'Shelf Units',
      value: stats.shelfUnits,
      icon: <Layers className="w-6 h-6 text-gray-600" />,
      color: 'bg-gray-50 border-gray-200'
    },
    {
      title: 'Cage Units',
      value: stats.cageUnits,
      icon: <Grid className="w-6 h-6 text-teal-600" />,
      color: 'bg-teal-50 border-teal-200'
    },
    {
      title: 'Hanging Lines',
      value: stats.hangingLineUnits,
      icon: <Clipboard className="w-6 h-6 text-orange-600" />,
      color: 'bg-orange-50 border-orange-200'
    },
    {
      title: 'Open Spaces',
      value: stats.openSpaceUnits,
      icon: <Truck className="w-6 h-6 text-gray-600" />,
      color: 'bg-gray-50 border-gray-200'
    }
  ];

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8"
    >
      {isLoading ? (
        // Skeleton loading state
        Array.from({ length: 9 }).map((_, index) => (
          <motion.div
            key={index}
            variants={itemVariants}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 animate-pulse"
          >
            <div className="flex justify-between items-center">
              <div>
                <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-16"></div>
              </div>
              <div className="w-10 h-10 rounded-full bg-gray-200"></div>
            </div>
          </motion.div>
        ))
      ) : (
        // Actual stat cards
        statCards.map((card, index) => (
          <motion.div
            key={index}
            variants={itemVariants}
            className={`${card.color} rounded-lg shadow-sm border p-4`}
          >
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-sm font-medium text-gray-600">{card.title}</h3>
                <p className="text-2xl font-bold text-gray-800 mt-1">{card.value.toLocaleString()}</p>
              </div>
              <div className="p-2 rounded-full bg-white shadow-sm">
                {card.icon}
              </div>
            </div>
          </motion.div>
        ))
      )}
    </motion.div>
  );
};

export default StorageStats;
