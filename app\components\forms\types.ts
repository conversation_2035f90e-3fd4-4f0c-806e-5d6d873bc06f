// Storage location form types

export interface StorageArea {
  area_id: number;
  name: string;
  location_type: 'indoor' | 'outdoor';
  level: 'upstairs' | 'downstairs' | 'ground';
  description?: string;
}

export interface StorageUnit {
  unit_id: number;
  area_id: number;
  unit_type: 'shelf' | 'cage' | 'hanging_line' | 'open_space' | 'engine_area';
  identifier: string;
  description?: string;
}

export type LocationSubtype = 
  | 'crate' 
  | 'container' 
  | 'shelf_section' 
  | 'open_shelf' 
  | 'cage_section' 
  | 'hanging_point' 
  | 'open_area_spot';

export interface StorageLocationFormData {
  areaId: string;
  unitId: string;
  locationSubtype: LocationSubtype;
  quantity: number;
  notes?: string;
  details?: {
    level?: string;
    crate_code?: string;
    container_code?: string;
    section?: string;
    row?: string;
    col?: string;
    point_identifier?: string;
    spot_description?: string;
    [key: string]: string | undefined;
  };
}

export interface PartLocationDetails {
  location_id: number;
  part_id: number;
  unit_id: number;
  quantity: number;
  location_subtype: LocationSubtype;
  details: Record<string, any>;
  notes?: string;
}
