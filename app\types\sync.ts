export interface SyncStatus {
  id?: string; // UUID, optional as it's generated by the DB
  user_id?: string; // UUID, optional
  service_name: string;
  last_sync_time: string; // ISO 8601 date string
  status: 'success' | 'failed' | 'in_progress' | 'pending'; // Added 'pending'
  details?: string | Record<string, any>; // Can be a JSON string or an object
  created_at?: string; // ISO 8601 date string, optional
  updated_at?: string; // ISO 8601 date string, optional
}
