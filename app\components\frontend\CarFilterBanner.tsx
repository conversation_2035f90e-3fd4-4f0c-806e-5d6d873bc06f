'use client';

import React from 'react';
import { X } from 'lucide-react';
import { motion } from 'framer-motion';
import { CarFilter } from './CarFilterDropdown';

interface CarFilterBannerProps {
  filter: CarFilter;
  onClearFilter: () => void;
}

const CarFilterBanner: React.FC<CarFilterBannerProps> = ({ filter, onClearFilter }) => {
  // Build the car description
  const getCarDescription = () => {
    const parts = [];

    if (filter.brandName) parts.push(filter.brandName);
    if (filter.modelName) parts.push(filter.modelName);
    if (filter.generationName) parts.push(filter.generationName);
    if (filter.variationName) parts.push(filter.variationName);
    if (filter.trimName) parts.push(filter.trimName);

    return parts.join(' ');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="bg-teal-50 border border-teal-200 rounded-md p-3 mb-6 flex items-center justify-between"
    >
      <div className="flex-1">
        <p className="text-teal-800">
          <span className="font-medium">Filtered by car:</span> {getCarDescription()}
        </p>
        <p className="text-teal-600 text-sm">
          Showing only parts compatible with this vehicle
        </p>
      </div>
      <div className="flex items-center">
        <button
          onClick={onClearFilter}
          className="ml-4 px-3 py-1.5 bg-teal-100 text-teal-700 rounded-md hover:bg-teal-200 flex-shrink-0 text-sm font-medium"
        >
          Clear Filter
        </button>
      </div>
    </motion.div>
  );
};

export default CarFilterBanner;
