'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Clock,
  User,
  ShoppingCart,
  Car,
  Edit,
  LogIn,
  Mail,
  AlertCircle,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

// Types
interface ClientActivity {
  id: string;
  client_id: string;
  activity_type: string;
  description: string;
  metadata: any;
  created_at: string;
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

// Props
interface ClientActivityFeedProps {
  clientId: string;
  limit?: number;
  showPagination?: boolean;
}

// Activity Icon Component
const ActivityIcon: React.FC<{ type: string }> = ({ type }) => {
  switch (type) {
    case 'login':
      return <LogIn className="w-5 h-5 text-blue-500" />;
    case 'purchase':
      return <ShoppingCart className="w-5 h-5 text-green-500" />;
    case 'car_added':
    case 'car_updated':
    case 'car_removed':
      return <Car className="w-5 h-5 text-orange-500" />;
    case 'profile_update':
      return <Edit className="w-5 h-5 text-purple-500" />;
    case 'invitation':
      return <Mail className="w-5 h-5 text-teal-500" />;
    default:
      return <AlertCircle className="w-5 h-5 text-gray-500" />;
  }
};

// Format Date
const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true
  }).format(date);
};

// Main Component
const ClientActivityFeed: React.FC<ClientActivityFeedProps> = ({
  clientId,
  limit = 10,
  showPagination = true
}) => {
  const [activities, setActivities] = useState<ClientActivity[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit,
    pages: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch activities
  const fetchActivities = async (page: number = 1) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/clients/activity?clientId=${clientId}&limit=${limit}&page=${page}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch client activities');
      }

      setActivities(data.activities || []);
      setPagination(data.pagination || {
        total: 0,
        page: 1,
        limit,
        pages: 0
      });
    } catch (err) {
      console.error('Error fetching client activities:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchActivities();
  }, [clientId, limit]);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > pagination.pages) return;
    fetchActivities(newPage);
  };

  // Render loading state
  if (isLoading && activities.length === 0) {
    return (
      <div className="flex justify-center items-center py-8">
        <LoadingSpinner size={24} />
      </div>
    );
  }

  // Render error state
  if (error && activities.length === 0) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
        <p>{error}</p>
      </div>
    );
  }

  // Render empty state
  if (activities.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <Clock className="w-12 h-12 mx-auto mb-3 text-gray-400" />
        <p>No activities recorded yet</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Activity List */}
      <div className="space-y-3">
        {activities.map((activity) => (
          <motion.div
            key={activity.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="flex items-start p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
          >
            <div className="p-2 bg-gray-100 rounded-full mr-3">
              <ActivityIcon type={activity.activity_type} />
            </div>
            <div className="flex-1">
              <p className="text-sm text-gray-800">{activity.description}</p>
              <p className="text-xs text-gray-500 mt-1">
                <Clock className="w-3 h-3 inline-block mr-1" />
                {formatDate(activity.created_at)}
              </p>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Pagination */}
      {showPagination && pagination.pages > 1 && (
        <div className="flex justify-between items-center pt-4 border-t border-gray-200">
          <button
            onClick={() => handlePageChange(pagination.page - 1)}
            disabled={pagination.page === 1}
            className="flex items-center text-sm text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            Previous
          </button>

          <span className="text-sm text-gray-600">
            Page {pagination.page} of {pagination.pages}
          </span>

          <button
            onClick={() => handlePageChange(pagination.page + 1)}
            disabled={pagination.page === pagination.pages}
            className="flex items-center text-sm text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
            <ChevronRight className="w-4 h-4 ml-1" />
          </button>
        </div>
      )}
    </div>
  );
};

export default ClientActivityFeed;
