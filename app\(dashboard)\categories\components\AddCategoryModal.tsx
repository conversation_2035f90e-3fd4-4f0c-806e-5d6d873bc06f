'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Save, FolderTree } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { CategoryWithChildren, CategoryFormData } from '../types';
import { useForm, SubmitHandler } from 'react-hook-form';
import BatchRenameModal from './BatchRenameModal';

interface AddCategoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  categories: CategoryWithChildren[];
}

const AddCategoryModal: React.FC<AddCategoryModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  categories
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showBatchRename, setShowBatchRename] = useState(false);
  const [newCategoryId, setNewCategoryId] = useState<number | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<CategoryFormData>({
    defaultValues: {
      label: '',
      href: '',
      icon: '',
      library: '',
      parent_category_id: null,
      isActive: true,
      requirePartNumber: true,
      isEnginePart: false,
      title_template: ''
    }
  });

  const supabase = createClient();

  const onSubmit: SubmitHandler<CategoryFormData> = async (data) => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Format the href if not provided
      if (!data.href) {
        data.href = `/parts/category/${data.label.toLowerCase().replace(/\s+/g, '-')}`;
      }

      const { data: insertedData, error: insertError } = await supabase
        .from('car_part_categories')
        .insert({
          label: data.label,
          href: data.href,
          icon: data.icon || null,
          library: data.library || null,
          parent_category_id: data.parent_category_id || null,
          isActive: data.isActive,
          requirePartNumber: data.requirePartNumber,
          isEnginePart: data.isEnginePart,
          title_template: data.title_template || null
        })
        .select()
        .single();

      if (insertError) throw insertError;

      // Check if title template was provided and show batch rename modal
      if (data.title_template && insertedData) {
        setNewCategoryId(insertedData.id);
        // Small delay to prevent flickering
        setTimeout(() => {
          setShowBatchRename(true);
        }, 100);
        return; // Don't close the modal yet
      }

      // Success
      reset();
      onSuccess();
      onClose();
    } catch (err: any) {
      console.error('Error adding category:', err);
      setError(err.message || 'Failed to add category');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Animation variants
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3 } }
  };

  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    exit: {
      opacity: 0,
      y: 50,
      scale: 0.95,
      transition: { duration: 0.2 }
    }
  };

  // Flatten categories for select dropdown with improved formatting
  const flattenCategories = (cats: CategoryWithChildren[], depth = 0, path = ''): { id: number; label: string; depth: number; fullPath: string }[] => {
    let result: { id: number; label: string; depth: number; fullPath: string }[] = [];

    cats.forEach(cat => {
      const currentPath = path ? `${path} > ${cat.label}` : cat.label;
      result.push({ id: cat.id, label: cat.label, depth, fullPath: currentPath });

      if (cat.children && cat.children.length > 0) {
        result = [...result, ...flattenCategories(cat.children, depth + 1, currentPath)];
      }
    });

    return result;
  };

  const flatCategories = flattenCategories(categories);

  return (
    <>
      <AnimatePresence>
      {isOpen && !showBatchRename && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          onClick={onClose}
        >
          <motion.div
            className="bg-white rounded-lg shadow-xl w-full max-w-md overflow-hidden"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <div className="flex items-center">
                <FolderTree className="w-6 h-6 text-teal-600 mr-3" />
                <h2 className="text-xl font-semibold text-gray-800">Add New Category</h2>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="p-6">
              {error && (
                <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
                  {error}
                </div>
              )}

              <div className="space-y-4">
                <div>
                  <label htmlFor="label" className="block text-sm font-medium text-gray-700 mb-1">
                    Category Name *
                  </label>
                  <input
                    id="label"
                    type="text"
                    {...register('label', { required: 'Category name is required' })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    placeholder="e.g., Engine Parts"
                    disabled={isSubmitting}
                  />
                  {errors.label && (
                    <p className="mt-1 text-sm text-red-600">{errors.label.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="href" className="block text-sm font-medium text-gray-700 mb-1">
                    URL Path (optional)
                  </label>
                  <input
                    id="href"
                    type="text"
                    {...register('href')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    placeholder="e.g., /parts/category/engine-parts"
                    disabled={isSubmitting}
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Leave blank to auto-generate from category name
                  </p>
                </div>

                <div>
                  <label htmlFor="parent_category_id" className="block text-sm font-medium text-gray-700 mb-1">
                    Parent Category
                  </label>
                  <select
                    id="parent_category_id"
                    {...register('parent_category_id')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    disabled={isSubmitting}
                  >
                    <option value="">None (Top-level category)</option>
                    {flatCategories.map((cat) => (
                      <option key={cat.id} value={cat.id}>
                        {cat.depth > 0 ? `${Array(cat.depth).fill('—').join('')} ` : ''}{cat.label}
                        {cat.depth > 0 ? ` — ${cat.fullPath}` : ''}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="icon" className="block text-sm font-medium text-gray-700 mb-1">
                      Icon (optional)
                    </label>
                    <input
                      id="icon"
                      type="text"
                      {...register('icon')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                      placeholder="e.g., engine"
                      disabled={isSubmitting}
                    />
                  </div>

                  <div>
                    <label htmlFor="library" className="block text-sm font-medium text-gray-700 mb-1">
                      Icon Library (optional)
                    </label>
                    <input
                      id="library"
                      type="text"
                      {...register('library')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                      placeholder="e.g., lucide"
                      disabled={isSubmitting}
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="title_template" className="block text-sm font-medium text-gray-700 mb-1">
                    Title Template (optional)
                  </label>
                  <textarea
                    id="title_template"
                    {...register('title_template')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    placeholder="e.g., Volkswagen (VW) Audi {category_name} {part_number} {compatible_part_numbers} {compatible_vehicles}"
                    rows={3}
                    disabled={isSubmitting}
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Available placeholders: {'{brand_name}'}, {'{model_name}'}, {'{generation_name}'}, {'{variation}'}, {'{trim}'}, {'{category_name}'}, {'{part_number}'}, {'{compatible_part_numbers}'}, {'{compatible_vehicles}'}, {'{condition}'}, {'{attributes}'}
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center">
                    <input
                      id="isActive"
                      type="checkbox"
                      {...register('isActive')}
                      className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                      disabled={isSubmitting}
                    />
                    <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                      Active
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      id="requirePartNumber"
                      type="checkbox"
                      {...register('requirePartNumber')}
                      className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                      disabled={isSubmitting}
                    />
                    <label htmlFor="requirePartNumber" className="ml-2 block text-sm text-gray-700">
                      Require Part Number
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      id="isEnginePart"
                      type="checkbox"
                      {...register('isEnginePart')}
                      className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                      disabled={isSubmitting}
                    />
                    <label htmlFor="isEnginePart" className="ml-2 block text-sm text-gray-700">
                      Engine Part
                    </label>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 mr-2"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 flex items-center"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Save Category
                    </>
                  )}
                </button>
              </div>
            </form>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>

    {/* Batch Rename Modal - Outside AnimatePresence to avoid key conflicts */}
    {newCategoryId && (
      <BatchRenameModal
        isOpen={showBatchRename}
        onClose={() => setShowBatchRename(false)}
        categoryId={newCategoryId}
        categoryName={categories.find(c => c.id === newCategoryId)?.label || 'New Category'}
        onComplete={() => {
          setShowBatchRename(false);
          reset();
          onSuccess();
          onClose();
        }}
      />
    )}
    </>
  );
};

export default AddCategoryModal;
