// app/lib/supabase/server.ts
import {
  createServerClient,
  type CookieOptions,
  CookieOptionsWithName,
} from '@supabase/ssr';
import { type NextRequest, type NextResponse } from 'next/server';
import { type Database } from '@/app/types/database';
import { cookies } from 'next/headers'

export const createRouteHandlerClient = ({
  request,
  response,
}: {
  request: NextRequest;
  response: NextResponse;
}) => {
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          // Only set response cookies
          response.cookies.set({ name, value, ...options });
        },
        remove(name: string, options: CookieOptions) {
          // Only set response cookies to empty value
          response.cookies.set({ name, value: '', ...options });
        },
      },
    }
  );

  return supabase;
};

// Create a server client with admin privileges for operations like user invitations
export const createClient = () => {
  // Use a simpler approach without cookies for the admin client
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!, // Use service role key for admin operations
    {
      cookies: {
        get: () => undefined,
        set: () => {},
        remove: () => {},
      },
    }
  );
};

