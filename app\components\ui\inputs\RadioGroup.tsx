import React from 'react';
import { motion } from 'framer-motion';
import { Circle, CircleCheck } from 'lucide-react';
import { Control, Controller } from 'react-hook-form';
import Label from './Label';

interface RadioGroupOption {
  label: string;
  value: string;
}

interface RadioGroupProps {
  name?: string;
  control?: Control<any>;
  options: RadioGroupOption[];
  label?: string;
  errorMessage?: string;
  // Add value and onChange props
  value?: string;
  onChange?: (value: string) => void;
}

const RadioGroup: React.FC<RadioGroupProps> = ({ 
  name,
  control,
  options,
  label,
  errorMessage,
  value: externalValue,
  onChange: externalOnChange
}) => {
  const renderRadio = (field?: any) => (
    <div className="space-y-2">
      {label && <Label>{label}</Label>}
      <div className="space-y-2">
        {options.map((option) => (
          <div
            key={option.value}
            className="flex items-center space-x-2 cursor-pointer group"
            onClick={() => {
              field?.onChange(option.value);
              externalOnChange?.(option.value);
            }}
          >
            <div className="relative">
              <motion.div className="p-1 rounded-full dark:bg-gray-700 w-7 h-7 flex items-center justify-center">
                <Circle 
                  size={20}
                  className="text-gray-500 dark:text-gray-400"
                  strokeWidth={2}
                />
                <motion.div
                  className="absolute rounded-full bg-blue-500"
                  initial={{ scale: 0 }}
                  animate={{ 
                    scale: (field?.value || externalValue) === option.value ? 1 : 0 
                  }}
                  style={{ width: 16, height: 16 }}
                />
                <motion.div
                  className="absolute"
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ 
                    opacity: (field?.value || externalValue) === option.value ? 1 : 0,
                    scale: (field?.value || externalValue) === option.value ? 1 : 0.5
                  }}
                >
                  <CircleCheck 
                    size={16}
                    className="text-white"
                    strokeWidth={2}
                  />
                </motion.div>
              </motion.div>
            </div>
            <span className="text-gray-700 dark:text-gray-300">
              {option.label}
            </span>
          </div>
        ))}
      </div>
      {errorMessage && <p className="text-sm text-red-700">{errorMessage}</p>}
    </div>
  );

  return control ? (
    <Controller
      control={control}
      name={name || ''}
      render={({ field }) => renderRadio(field)}
    />
  ) : (
    renderRadio({ value: externalValue, onChange: externalOnChange })
  );
};

export default RadioGroup;