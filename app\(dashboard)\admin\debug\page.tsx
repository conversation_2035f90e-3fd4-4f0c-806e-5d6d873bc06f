export const dynamic = 'force-dynamic';

import { Suspense } from 'react';
import Spinner from '@/app/components/ui/Spinner';
import DebugContent from './components/DebugContent';

export default function DebugPage() {
    return (
        <Suspense fallback={<div className="flex min-h-[300px] items-center justify-center">
            <Spinner size="lg" />
        </div>}>
            <DebugContent />
        </Suspense>
    );
}
