'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { createClient } from '@/app/libs/supabase/client';
import { GenerationFormData, Model, Brand } from '../../types';

interface AddGenerationModalProps {
  isOpen: boolean;
  onClose: () => void;
  models: Model[];
  brands: Brand[];
  onSuccess: () => void;
}

const AddGenerationModal: React.FC<AddGenerationModalProps> = ({
  isOpen,
  onClose,
  models,
  brands,
  onSuccess
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedBrandId, setSelectedBrandId] = useState<number | ''>('');
  const [filteredModels, setFilteredModels] = useState<Model[]>([]);
  
  const { register, handleSubmit, reset, watch, setValue, formState: { errors } } = useForm<GenerationFormData>();
  
  const watchBrandId = watch('brand_id');
  const watchStartYear = watch('start_production_year');
  
  const supabase = createClient();

  // Filter models based on selected brand
  useEffect(() => {
    if (selectedBrandId) {
      const filtered = models.filter(model => model.brand_id === selectedBrandId);
      setFilteredModels(filtered);
      
      // Reset model selection if the current model doesn't belong to the selected brand
      const modelId = watch('model_id');
      if (modelId && !filtered.some(model => model.id === modelId)) {
        setValue('model_id', undefined);
      }
    } else {
      setFilteredModels(models);
    }
  }, [selectedBrandId, models, watch, setValue]);

  const onSubmit = async (data: GenerationFormData) => {
    setIsSubmitting(true);
    setError(null);
    
    try {
      // Get the next available id
      const { data: maxIdData, error: maxIdError } = await supabase
        .from('car_generation')
        .select('id')
        .order('id', { ascending: false })
        .limit(1);
        
      if (maxIdError) throw maxIdError;
      
      const nextId = maxIdData && maxIdData.length > 0 ? maxIdData[0].id + 1 : 1;
      
      // Insert the new generation
      const { error: insertError } = await supabase
        .from('car_generation')
        .insert({
          id: nextId,
          model_id: data.model_id,
          name: data.name,
          start_production_year: data.start_production_year,
          end_production_year: data.end_production_year || null
        });
        
      if (insertError) throw insertError;
      
      reset();
      setSelectedBrandId('');
      onSuccess();
      onClose();
    } catch (err: any) {
      console.error('Error adding generation:', err);
      setError(err.message || 'Failed to add generation. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.2 }}
          className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex justify-between items-center p-6 border-b border-gray-200">
            <h3 className="text-xl font-semibold text-gray-800">Add New Generation</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              disabled={isSubmitting}
            >
              <X size={24} />
            </button>
          </div>
          
          <form onSubmit={handleSubmit(onSubmit)} className="p-6">
            <div className="mb-6">
              <label htmlFor="brand_id" className="block text-sm font-medium text-gray-700 mb-2">
                Brand
              </label>
              <select
                id="brand_id"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                value={selectedBrandId}
                onChange={(e) => setSelectedBrandId(e.target.value ? Number(e.target.value) : '')}
              >
                <option value="">Select a brand</option>
                {brands.map((brand) => (
                  <option key={brand.brand_id} value={brand.brand_id}>
                    {brand.brand_name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="mb-6">
              <label htmlFor="model_id" className="block text-sm font-medium text-gray-700 mb-2">
                Model
              </label>
              <select
                id="model_id"
                className={`w-full px-4 py-2 border ${errors.model_id ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500`}
                {...register('model_id', { 
                  required: 'Model is required',
                  valueAsNumber: true
                })}
                disabled={!selectedBrandId}
              >
                <option value="">Select a model</option>
                {filteredModels.map((model) => (
                  <option key={model.id} value={model.id}>
                    {model.model_name}
                  </option>
                ))}
              </select>
              {errors.model_id && (
                <p className="mt-1 text-sm text-red-600">{errors.model_id.message}</p>
              )}
            </div>
            
            <div className="mb-6">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                Generation Name
              </label>
              <input
                id="name"
                type="text"
                className={`w-full px-4 py-2 border ${errors.name ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500`}
                placeholder="Enter generation name (e.g., MK1, First Gen)"
                {...register('name', { required: 'Generation name is required' })}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div>
                <label htmlFor="start_production_year" className="block text-sm font-medium text-gray-700 mb-2">
                  Start Year
                </label>
                <input
                  id="start_production_year"
                  type="number"
                  min="1900"
                  max={new Date().getFullYear()}
                  className={`w-full px-4 py-2 border ${errors.start_production_year ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500`}
                  placeholder="Start year"
                  {...register('start_production_year', { 
                    required: 'Start year is required',
                    valueAsNumber: true,
                    min: {
                      value: 1900,
                      message: 'Year must be 1900 or later'
                    },
                    max: {
                      value: new Date().getFullYear(),
                      message: 'Year cannot be in the future'
                    }
                  })}
                />
                {errors.start_production_year && (
                  <p className="mt-1 text-sm text-red-600">{errors.start_production_year.message}</p>
                )}
              </div>
              
              <div>
                <label htmlFor="end_production_year" className="block text-sm font-medium text-gray-700 mb-2">
                  End Year (Optional)
                </label>
                <input
                  id="end_production_year"
                  type="number"
                  min={watchStartYear || 1900}
                  max={new Date().getFullYear()}
                  className={`w-full px-4 py-2 border ${errors.end_production_year ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500`}
                  placeholder="End year (or leave blank if ongoing)"
                  {...register('end_production_year', { 
                    valueAsNumber: true,
                    min: {
                      value: watchStartYear || 1900,
                      message: 'End year must be after start year'
                    },
                    max: {
                      value: new Date().getFullYear(),
                      message: 'Year cannot be in the future'
                    }
                  })}
                />
                {errors.end_production_year && (
                  <p className="mt-1 text-sm text-red-600">{errors.end_production_year.message}</p>
                )}
              </div>
            </div>
            
            {error && (
              <div className="bg-red-50 text-red-600 p-4 rounded-md mb-6">
                {error}
              </div>
            )}
            
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => {
                  reset();
                  setSelectedBrandId('');
                  onClose();
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 transition-colors"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Adding...' : 'Add Generation'}
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default AddGenerationModal;
