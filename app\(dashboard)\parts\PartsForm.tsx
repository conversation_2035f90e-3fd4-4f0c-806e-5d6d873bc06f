// PartsForm.tsx
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { usePartSubmission } from './hooks/usePartSubmission';
import { usePartNumberCheck } from './hooks/usePartNumberCheck';
import { useVehicleSelection } from './hooks/useVehicleSelection';
import ImageUploadSection from './components/FormSections/ImageUploadSection';
import CategorySelection from './components/FormSections/CategorySelection';
import PartNumberSection from './components/FormSections/PartNumberSection';
import VehicleSelection from './components/FormSections/VehicleSelection';
import PartAttributesSection from './components/FormSections/PartAttributesSection';
import ConditionStockPrice from './components/FormSections/ConditionStockPrice';
import CompatibilityAnalysis from './components/Compatibility/CompatibilityAnalysis';
import { PartFormValues, FlatCategory, CategoryAttribute, EngineCompatibility, VehicleCompatibility } from './types';
import { NestedSelectItem } from '@/app/components/ui/inputs/NestedSelect';
import { fetchCategoryAttributes } from '@/app/libs/data';
import { Plus } from 'lucide-react';

import Button from '@/app/components/ui/inputs/Button';
import Label from '@/app/components/ui/inputs/Label';
import Input from '@/app/components/ui/inputs/Input';
import { buildCategoryTree } from './utils/categoryUtils';
import { ExistingPartModal } from './components/ExistingPartModal';
import StorageLocationModal from '@/app/components/modals/StorageLocationModal';
import { usePartForm } from './hooks/usePartForm';

interface PartsFormProps {
  onClose: () => void;
  onSuccess: () => void;
  flatCategories: FlatCategory[];
  isLoadingCategories: boolean;
  initialData: any;
}

export const PartsForm = ({
    initialData,
    onSuccess,
    flatCategories,
    isLoadingCategories,
    onClose
}: PartsFormProps) => {
  const form = useForm<PartFormValues>({
    defaultValues: {
      partNumber: '',
      stock: 1,
      price: 0,
      condition: undefined,
      imageUrl: '',
      imageType: 'upload',
      images: [],
      categoryId: '',
      trimId: '',
      generationId: '',
      variationId: '',
      brandId: '',
      modelId: '',
      attributes: {},
      categoryAttributes: [],
      selectedCategory: '',
      isCheckingPartNumber: false,
      showVehicleSelection: false,
      requirePartNumber: true,
      additionalEngineCodes: [],
      compatibilityData: undefined,
      newStock: 1,
      newPrice: 0,
      usedStock: 1,
      usedPrice: 0,
      userId: '',
      newDiscountPrice: 0,
      usedDiscountPrice: 0
    }
  });

  const {
    control,
    handleSubmit,
    formState,
    setValue,
    watch,
    getValues,
    register,
    reset
  } = form;

  const [nestedCategories, setNestedCategories] = useState<NestedSelectItem[]>([]);
  const [categoryError, setCategoryError] = useState<string | null>(null);
  const [categoryAttributes, setCategoryAttributes] = useState<CategoryAttribute[]>([]);
  const [isFetchingAttributes, setIsFetchingAttributes] = useState(false);
  const [attributesError, setAttributesError] = useState<string | null>(null);
  const [showEngineCodes, setShowEngineCodes] = useState(false);
  const [additionalEngineCodes, setAdditionalEngineCodes] = useState<string[]>(['']);
  const [showStorageLocationModal, setShowStorageLocationModal] = useState(false);
  const [newPartId, setNewPartId] = useState<number | null>(null);
  const [newPartName, setNewPartName] = useState<string>('');

  const {
    handleImageUploadSuccess,
    handleRemoveImage,
    handleSetMainImage,
    isImageUploading,
    croppedImageUrl,
    showExistingPartModal,
    setShowExistingPartModal,
    handleExistingPartConfirm,
    existingPartData,
    uploadedImages,
    handleAllImagesProcessed,
  } = usePartForm({
    setValue,
    reset,
    getValues,
    onSuccess,
    onClose
  });

  // Get the selected category details
  const selectedCategory = watch('selectedCategory');
  const categoryDetails = flatCategories.find(cat => cat.id.toString() === selectedCategory);
  const isPartNumberRequired = categoryDetails?.partNumberRequired ?? categoryDetails?.requirePartNumber ?? true;

  const selectedCategoryName = categoryDetails?.name;
  const requirePartNumber = watch('requirePartNumber');

  // Debug state changes
  useEffect(() => {
    console.log('PartsForm: croppedImageUrl changed:', croppedImageUrl);
  }, [croppedImageUrl]);

  useEffect(() => {
    console.log('PartsForm: selectedCategory changed:', selectedCategory);
  }, [selectedCategory]);



  const { errors, isSubmitting } = formState;

  // Fetch category attributes
  useEffect(() => {
    const fetchAttributes = async () => {
      if (selectedCategory) {
        setIsFetchingAttributes(true);
        setAttributesError(null);
        try {
          const attributes = await fetchCategoryAttributes(parseInt(selectedCategory));
          setCategoryAttributes(attributes);
          setValue('categoryAttributes', attributes.map(attr => ({
            id: attr.id,
            value: ''
          })));
        } catch (error) {
          console.error("Error fetching attributes:", error);
          setAttributesError('Failed to load attributes for this category.');
          setCategoryAttributes([]);
        } finally {
          setIsFetchingAttributes(false);
        }
      } else {
        setCategoryAttributes([]); // Clear attributes if no category is selected
      }
    };

    fetchAttributes();
  }, [selectedCategory, setValue]);

  // Update requirePartNumber and attributes when category changes (using the new effect)
  useEffect(() => {
    if (selectedCategory && flatCategories) {
      const category = flatCategories.find(cat => cat.id.toString() === selectedCategory);
      if (category) {
        const isRequired = category.partNumberRequired ?? category.requirePartNumber ?? true;
        setValue('requirePartNumber', isRequired, { shouldValidate: true });
        console.log('Setting requirePartNumber to:', isRequired);
      }
    }
  }, [selectedCategory, flatCategories, setValue]);

  // --- Category Change ---
  const handleCategoryChange = async (value: string) => {
    console.log('Form condition value:', watch('condition'));
    console.log('Form condition type:', typeof watch('condition'));
    console.log('Setting category ID to:', value);
    setValue('selectedCategory', value, { shouldValidate: true });
    setValue('categoryId', value, { shouldValidate: true });

    // Reset vehicle-related fields
    setValue('brandId', '');
    setValue('modelId', '');
    setValue('generationId', '');
    setValue('variationId', '');
    setValue('trimId', '');

    try {
      setIsFetchingAttributes(true);
      setAttributesError(null);

      const attributes = await fetchCategoryAttributes(parseInt(value));
      setCategoryAttributes(attributes);

      // Reset category attributes in form
      setValue('categoryAttributes', attributes.map(attr => ({
        id: attr.id,
        value: ''
      })));
    } catch (error) {
      console.error('Error fetching category attributes:', error);
      setAttributesError('Failed to load category attributes');
    } finally {
      setIsFetchingAttributes(false);
    }
  };

  const {
    partNumberExists,
    existingTrims,
    compatibilityData,
    currentTask,
    existingPartDetails,
    setPartNumberExists,
    setExistingTrims,
    setCompatibilityData,
    setCurrentTask,
    setExistingPartDetails
  } = usePartSubmissionState(); // Using a simpler state hook (defined below)

  const {
    handlePartNumberCheck,
  } = usePartNumberCheck({
    getValues,
    setValue,
    setPartNumberExists,
    setExistingTrims,
    setCompatibilityData,
    setCurrentTask,
    flatCategories,
    setShowExistingPartModal,
    setExistingPartDetails
  });

  // Add effect to update form values when compatibilityData changes
  useEffect(() => {
    if (compatibilityData) {
      console.log('Setting compatibility data in form:', compatibilityData);

      // Directly update the form values
      form.setValue('compatibilityData', compatibilityData as any, {
        shouldValidate: false,
        shouldDirty: true
      });

      // Also store the compatibility data in a ref to ensure it's available during submission
      if (compatibilityData.compatiblePartNumbers && compatibilityData.compatiblePartNumbers.length > 0) {
        console.log('Compatible part numbers being set in form:', compatibilityData.compatiblePartNumbers);
      }
    }
  }, [compatibilityData, form]);

  const {
    validateVehicleSelection,
    vehicleSelectionError,
    loadingStates,
    handleBrandModelSelectionChange
  } = useVehicleSelection({ watch, setValue });

  // Create a custom onSuccess handler that will show the storage location modal
  const handlePartSuccess = (partId: number, partName: string) => {
    // If we have a valid part ID, show the storage location modal
    if (partId && partId > 0) {
      setNewPartId(partId);
      setNewPartName(partName || 'New Part');
      setShowStorageLocationModal(true);
    } else {
      // If we don't have a valid part ID, just call the original onSuccess
      onSuccess();
    }
  };

  const { onSubmit } = usePartSubmission({
    partNumberExists,
    selectedCategory,
    validateVehicleSelection,
    getValues,
    onSuccess: (partId: number, partName: string) => handlePartSuccess(partId, partName),
    onClose: onClose,
    requirePartNumber,
    flatCategories
  });

  const handleEngineCodesChange = (newValues: string[]) => {
    setAdditionalEngineCodes(newValues);
    setValue('additionalEngineCodes', newValues.filter(code => code.trim() !== ''));
  };

  useEffect(() => {
    if (flatCategories && flatCategories.length > 0) {
      console.log('Building nested categories in PartsForm from', flatCategories.length, 'flat categories');
      const processed = buildCategoryTree(flatCategories);
      console.log('Built nested categories:', processed.length);
      setNestedCategories(processed);
    }
  }, [flatCategories]);

  // Reset all state when form is closed
  const handleClose = () => {
    // Reset form state with default values
    reset({
      partNumber: '',
      stock: 1,
      price: 0,
      condition: 'Used', // Set a default condition to ensure it's defined
      imageUrl: '',
      imageType: 'upload',
      images: [],
      categoryId: '',
      trimId: '',
      generationId: '',
      variationId: '',
      brandId: '',
      modelId: '',
      attributes: {},
      categoryAttributes: [],
      selectedCategory: '',
      isCheckingPartNumber: false,
      showVehicleSelection: false,
      requirePartNumber: true,
      additionalEngineCodes: [],
      compatibilityData: undefined,
      newStock: 1,
      newPrice: 0,
      usedStock: 1,
      usedPrice: 0,
      userId: '',
      newDiscountPrice: 0,
      usedDiscountPrice: 0
    });

    // Reset all local state
    setCategoryError(null);
    setCategoryAttributes([]);
    setAttributesError(null);
    setShowEngineCodes(false);
    setAdditionalEngineCodes(['']);

    // Call the parent's onClose
    onClose();
  };

  const onSubmitHandler = async (data: PartFormValues) => {
    console.log('Form submitted with data:', data);
    console.log('Form validation state:', formState);
    console.log('Form errors:', errors);

    // Additional validation for critical fields
    if (!data.categoryId || data.categoryId === '') {
      console.error('Category ID is missing or empty in form submission');
      alert('Please select a category before submitting');
      return;
    }

    // Only validate part number if it's required
    if (requirePartNumber && (!data.partNumber || data.partNumber === '')) {
      console.error('Part number is missing or empty in form submission');
      alert('Please enter a part number before submitting');
      return;
    }

    try {
      console.log('Submitting form with category ID:', data.categoryId);
      await onSubmit(data);
    } catch (error) {
      console.error('Error in form submission:', error);
      alert(`Error submitting form: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleVehicleSelectionChange = (field: string, value: string, name?: string, years?: string) => {
    console.log('PartsForm: Selection changed:', { field, value, name, years });
    handleBrandModelSelectionChange(field, value, name, years);
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmitHandler)} className="space-y-6">
        {categoryError && (
          <div className="p-3 bg-red-50 text-red-700 rounded-md">
            Category Error: {categoryError}
          </div>
        )}
        <input type="hidden" {...register('requirePartNumber')} />

        {/* Hidden fields for vehicle selection names */}
        <input type="hidden" {...register('modelName')} />
        <input type="hidden" {...register('generationName')} />
        <input type="hidden" {...register('generationYears')} />
        <input type="hidden" {...register('variationName')} />
        <input type="hidden" {...register('trimName')} />

        {/* Always render the ImageUploadSection, but hide it when we have an image */}
        <div className={croppedImageUrl ? 'hidden' : ''}>
          <ImageUploadSection
            control={control}
            imageType={watch('imageType')}
            handleImageUploadSuccess={(url, isMain) => handleImageUploadSuccess(url, isMain, false)}
            isImageUploading={isImageUploading}
            existingImages={watch('images')}
            onRemoveImage={handleRemoveImage}
            onSetMainImage={handleSetMainImage}
            onAllImagesProcessed={handleAllImagesProcessed}
          />
        </div>

        {/* Always render the CategorySelection, but hide it when we don't have an image */}
        <div className={!croppedImageUrl ? 'hidden' : ''}>
          <CategorySelection
            categories={nestedCategories}
            selectedCategory={selectedCategory || ''}
            handleCategoryChange={handleCategoryChange}
            selectedCategoryName={selectedCategoryName || ''}
            isLoading={isLoadingCategories}
          />
        </div>

        {croppedImageUrl && selectedCategory && requirePartNumber && (
          <PartNumberSection
            control={control}
            errors={errors}
            isChecking={watch('isCheckingPartNumber')}
            currentTask={currentTask || ''}
            onCheck={handlePartNumberCheck}
            requirePartNumber={isPartNumberRequired}
            setValue={setValue}
            partNumberExists={partNumberExists}
            existingPartDetails={existingPartDetails}
          >
            {compatibilityData && (
              <>
                <CompatibilityAnalysis data={{
                  ...compatibilityData,
                  partName: compatibilityData.partName || 'Unnamed Part',
                  compatiblePartNumbers: compatibilityData.compatiblePartNumbers || [],
                  isEnginePart: compatibilityData.isEnginePart || false,
                  engineCompatibility: compatibilityData.engineCompatibility?.map((engine: EngineCompatibility) => ({
                    ...engine,
                    engineCapacity: Number(engine.engineCapacity) || 0
                  })),
                  vehicleCompatibility: compatibilityData.vehicleCompatibility?.map((vehicle: VehicleCompatibility) => ({
                    brand: vehicle.brand || 'VW',
                    model: vehicle.model || '',
                    generation: vehicle.generation || '',
                    trims: vehicle.trims || []
                  }))
                }} />

                {!showEngineCodes && (
                  <div className="flex items-center gap-2 mt-4">
                    <Button
                      type="button"
                      variant="link"
                      className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                      onClick={() => setShowEngineCodes(true)}
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Add Missing Engine Codes
                    </Button>
                  </div>
                )}

                {/* Add Part Attributes Section after compatibility data is available */}
                <div className="mt-6">
                  <PartAttributesSection
                    control={control}
                    errors={errors}
                    getValues={getValues}
                    setValue={setValue}
                    categoryAttributes={categoryAttributes}
                    requirePartNumber={requirePartNumber}
                  />
                </div>

                {/* Add Condition, Stock, Price Section */}
                <div className="mt-6">
                  <ConditionStockPrice
                    control={control}
                    errors={errors}
                    condition={watch('condition')}
                  />
                </div>
              </>
            )}
          </PartNumberSection>
        )}

        {croppedImageUrl && selectedCategory && !requirePartNumber && (
          <div className="space-y-4">
            <VehicleSelection
              control={control}
              watch={watch}
              loadingStates={loadingStates}
              onSelectionChange={handleVehicleSelectionChange}
              error={vehicleSelectionError}
            />

            {watch('trimId') && (
              <PartAttributesSection
                control={control}
                errors={errors}
                getValues={getValues}
                setValue={setValue}
                categoryAttributes={categoryAttributes}
                requirePartNumber={requirePartNumber}
              />
            )}

            {watch('trimId') && !requirePartNumber && (
              <div className="space-y-2">
                <Label>Part Number (Optional)</Label>
                <Input
                  id="partNumber"
                  {...register('partNumber')}
                  placeholder="Enter part number (optional)"
                />
              </div>
            )}

            {/* Show condition/stock/price for both scenarios */}
            {((requirePartNumber && compatibilityData) || (!requirePartNumber && watch('trimId'))) && (
              <>
                <div className="border-t pt-6 mt-6">
                  <div className="flex items-center gap-2 mb-4">
                    <h3 className="text-lg font-medium">Step 3: Set Condition, Stock & Pricing</h3>
                    {requirePartNumber && compatibilityData && !watch('condition') && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        Required
                      </span>
                    )}
                  </div>
                  <ConditionStockPrice
                    control={control}
                    errors={errors}
                    condition={watch('condition')}
                  />
                </div>
              </>
            )}
          </div>
        )}

        {croppedImageUrl && selectedCategory && (
          <div className="flex justify-end gap-4 mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>

            {((requirePartNumber && compatibilityData && watch('condition')) ||
              (!requirePartNumber && watch('trimId') && watch('condition'))) && (
              <Button
                type="submit"
                disabled={isSubmitting}
                className="relative"
                style={{ zIndex: 'auto' }}
              >
                {isSubmitting ? 'Submitting...' : 'Submit'}
              </Button>
            )}
          </div>
        )}
      </form>

      <ExistingPartModal
        isOpen={showExistingPartModal}
        onClose={() => setShowExistingPartModal(false)}
        onConfirm={handleExistingPartConfirm}
        partNumber={form.getValues('partNumber')}
        isPartNumberRequired={isPartNumberRequired}
        existingPartDetails={existingPartDetails}
      />

      {/* Storage Location Modal */}
      {newPartId && (
        <StorageLocationModal
          isOpen={showStorageLocationModal}
          onClose={() => {
            setShowStorageLocationModal(false);
            onClose(); // Close the part modal after the storage location modal is closed
            onSuccess(); // Call the original onSuccess to refresh the parts list
          }}
          partId={newPartId}
          partName={newPartName}
        />
      )}
    </>
  );
};

// Simplified State Hook for Part Submission logic (if you still want it separate)
const usePartSubmissionState = () => {
  const [partNumberExists, setPartNumberExists] = useState<boolean>(false);
  const [existingTrims, setExistingTrims] = useState<any[]>([]);
  const [compatibilityData, setCompatibilityData] = useState<any | null>(null);
  const [currentTask, setCurrentTask] = useState<string | null>(null);
  const [existingPartDetails, setExistingPartDetails] = useState<{groupId?: number; table?: string} | null>(null);

  return {
    partNumberExists,
    existingTrims,
    compatibilityData,
    currentTask,
    existingPartDetails,
    setPartNumberExists,
    setExistingTrims,
    setCompatibilityData,
    setCurrentTask,
    setExistingPartDetails
  };
};