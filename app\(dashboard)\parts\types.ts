// app/(dashboard)/parts/types.ts

export interface PartFormValues {
  partNumber: string;
  stock?: number;
  price?: number;
  discountPrice?: number;
  condition?: 'New' | 'Used' | 'Both';
  imageUrl: string;
  imageType: string;
  images: Array<{
    url: string;
    isMain: boolean;
  }>;
  categoryId: string;
  trimId: string;
  generationId: string;
  variationId: string;
  brandId: string;
  modelId: string;
  modelName?: string;
  generationName?: string;
  generationYears?: string;
  variationName?: string;
  trimName?: string;
  attributes: Record<string, any>;
  categoryAttributes: Array<{
    id: number;
    value: string;
  }>;
  selectedCategory: string;
  isCheckingPartNumber: boolean;
  showVehicleSelection: boolean;
  requirePartNumber: boolean;
  additionalEngineCodes: string[];
  compatibilityData?: CompatibilityData;
  newStock?: number;
  newPrice?: number;
  newDiscountPrice?: number;
  usedStock?: number;
  usedPrice?: number;
  usedDiscountPrice?: number;
  userId: string;
}

export interface FlatCategory {
  id: number;
  name: string;
  parentId: number | null;
  level: number;
  partNumberRequired: boolean;
  requirePartNumber?: boolean;
}

export interface AttributeInputOption {
  id: number;
  option_value: string;
  attribute_id: number;
}

export interface CategoryAttribute {
  id: number;
  name: string;
  required: boolean;
  type: string;
  options?: string[];
  input_type: string;
  attribute: string;
  depends_on_attribute_id?: number | null;
  depends_on_option_id?: number | null;
}

export interface EngineCompatibility {
  engineCode: string;
  engineCapacity: string | number;
  fuelType: string;
  engineType: string;
}

export interface VehicleCompatibility {
  brand?: string;
  model: string;
  generation: string;
  trims?: string[];
}

export interface CompatibilityData {
  partName?: string;
  compatiblePartNumbers?: string[];
  isEnginePart?: boolean;
  engineCompatibility?: EngineCompatibility[];
  vehicleCompatibility?: VehicleCompatibility[];
}

export interface BrandModelSelectionProps {
  control: any;
  watch: any;
  loadingStates: {
    brands: boolean;
    models: boolean;
    generations: boolean;
    variations: boolean;
    trims: boolean;
  };
  onSelectionChange: (field: string, value: string, name?: string, years?: string) => void;
  error?: string | null;
}
