import React from 'react';
import { Plus } from 'lucide-react';
import ClientList from '../components/ClientList';
import DashboardHeader from '../components/DashboardHeader';

export default function ClientListPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader title="Client Management" />

      <div className="container mx-auto px-4 py-8">
        {/* Action Bar */}
        <div className="flex flex-col md:flex-row justify-between items-center mb-6">
          <div className="mb-4 md:mb-0">
            <h2 className="text-xl font-semibold text-gray-800">Client List</h2>
            <p className="text-gray-600">View and manage all your clients</p>
          </div>
        </div>

        {/* Client List Component */}
        <ClientList />
      </div>
    </div>
  );
}
