'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { createClient } from '@/app/libs/supabase/client';
import { Brand, BrandFormData } from '../../types';

interface EditBrandModalProps {
  isOpen: boolean;
  onClose: () => void;
  brand: Brand;
  onSuccess: () => void;
}

const EditBrandModal: React.FC<EditBrandModalProps> = ({
  isOpen,
  onClose,
  brand,
  onSuccess
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { register, handleSubmit, reset, formState: { errors } } = useForm<BrandFormData>({
    defaultValues: {
      brand_name: brand.brand_name
    }
  });
  
  const supabase = createClient();

  // Reset form when brand changes
  useEffect(() => {
    if (isOpen) {
      reset({
        brand_name: brand.brand_name
      });
    }
  }, [brand, isOpen, reset]);

  const onSubmit = async (data: BrandFormData) => {
    setIsSubmitting(true);
    setError(null);
    
    try {
      const { error: updateError } = await supabase
        .from('car_brands')
        .update({
          brand_name: data.brand_name
        })
        .eq('brand_id', brand.brand_id);
        
      if (updateError) throw updateError;
      
      onSuccess();
      onClose();
    } catch (err: any) {
      console.error('Error updating brand:', err);
      setError(err.message || 'Failed to update brand. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.2 }}
          className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex justify-between items-center p-6 border-b border-gray-200">
            <h3 className="text-xl font-semibold text-gray-800">Edit Brand</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              disabled={isSubmitting}
            >
              <X size={24} />
            </button>
          </div>
          
          <form onSubmit={handleSubmit(onSubmit)} className="p-6">
            <div className="mb-6">
              <label htmlFor="brand_name" className="block text-sm font-medium text-gray-700 mb-2">
                Brand Name
              </label>
              <input
                id="brand_name"
                type="text"
                className={`w-full px-4 py-2 border ${errors.brand_name ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500`}
                placeholder="Enter brand name"
                {...register('brand_name', { required: 'Brand name is required' })}
              />
              {errors.brand_name && (
                <p className="mt-1 text-sm text-red-600">{errors.brand_name.message}</p>
              )}
            </div>
            
            {error && (
              <div className="bg-red-50 text-red-600 p-4 rounded-md mb-6">
                {error}
              </div>
            )}
            
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 transition-colors"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default EditBrandModal;
