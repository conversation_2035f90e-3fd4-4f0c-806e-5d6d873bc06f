import { Metadata } from 'next';

// Base SEO configuration
export const baseSEO = {
  title: {
    default: 'AutoFlow Parts - VW & Audi Parts Kenya | Genuine & Aftermarket Parts',
    template: '%s | AutoFlow Parts Kenya'
  },
  description: 'Find genuine and aftermarket VW & Audi parts in Kenya. Quality automotive parts for Volkswagen and Audi vehicles. Fast delivery, competitive prices, expert support.',
  keywords: [
    'VW parts Kenya',
    'Audi parts Kenya',
    'Volkswagen parts Nairobi',
    'Audi spare parts',
    'automotive parts Kenya',
    'car parts Nairobi',
    'genuine VW parts',
    'aftermarket Audi parts',
    'auto parts Kenya',
    'vehicle parts'
  ],
  authors: [{ name: 'AutoFlow Parts Kenya' }],
  creator: 'AutoFlow Parts Kenya',
  publisher: 'AutoFlow Parts Kenya',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || 'https://autoflow.co.ke'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_KE',
    url: process.env.NEXT_PUBLIC_BASE_URL || 'https://autoflow.co.ke',
    siteName: 'AutoFlow Parts Kenya',
    title: 'AutoFlow Parts - VW & Audi Parts Kenya',
    description: 'Find genuine and aftermarket VW & Audi parts in Kenya. Quality automotive parts for Volkswagen and Audi vehicles.',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'AutoFlow Parts - VW & Audi Parts Kenya',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AutoFlow Parts - VW & Audi Parts Kenya',
    description: 'Find genuine and aftermarket VW & Audi parts in Kenya. Quality automotive parts for Volkswagen and Audi vehicles.',
    images: ['/images/twitter-image.jpg'],
    creator: '@autoflowke',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_VERIFICATION_CODE,
    yandex: process.env.YANDEX_VERIFICATION_CODE,
    yahoo: process.env.YAHOO_VERIFICATION_CODE,
  },
};

// Generate SEO metadata for part pages
export function generatePartSEO(part: {
  id: number;
  title: string;
  description?: string;
  partNumber?: string;
  condition?: string;
  price?: number;
  images?: string[];
  category?: string;
  brand?: string;
  model?: string;
}): Metadata {
  const slug = part.title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-+|-+$/g, '');

  const url = `${process.env.NEXT_PUBLIC_BASE_URL}/parts/${slug}-${part.id}`;
  
  const title = `${part.title} | ${part.condition || 'Used'} ${part.brand || 'VW/Audi'} Part`;
  const description = part.description || 
    `${part.condition || 'Used'} ${part.title} for ${part.brand || 'VW/Audi'} ${part.model || 'vehicles'}. Part number: ${part.partNumber || 'N/A'}. Quality automotive parts in Kenya.`;

  return {
    title,
    description,
    keywords: [
      part.partNumber,
      part.title,
      `${part.brand} parts`,
      `${part.model} parts`,
      part.category,
      'Kenya automotive parts',
      'VW Audi parts Kenya'
    ].filter(Boolean) as string[],
    alternates: {
      canonical: url,
    },
    openGraph: {
      title,
      description,
      url,
      type: 'website',
      images: part.images?.map(img => ({
        url: img,
        width: 800,
        height: 600,
        alt: part.title,
      })) || [],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: part.images?.[0] ? [part.images[0]] : [],
    },
    other: {
      'product:price:amount': part.price?.toString() || '',
      'product:price:currency': 'KES',
      'product:condition': part.condition?.toLowerCase() || '',
      'product:availability': 'in stock',
    },
  };
}

// Generate SEO metadata for category pages
export function generateCategorySEO(category: {
  name: string;
  description?: string;
  partsCount?: number;
}): Metadata {
  const slug = category.name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-+|-+$/g, '');

  const url = `${process.env.NEXT_PUBLIC_BASE_URL}/categories/${slug}`;
  
  const title = `${category.name} Parts | VW & Audi ${category.name} Parts Kenya`;
  const description = category.description || 
    `Find quality ${category.name.toLowerCase()} parts for VW and Audi vehicles in Kenya. ${category.partsCount || 'Many'} parts available. Genuine and aftermarket options.`;

  return {
    title,
    description,
    keywords: [
      `${category.name} parts`,
      `VW ${category.name}`,
      `Audi ${category.name}`,
      'automotive parts Kenya',
      'car parts Nairobi'
    ],
    alternates: {
      canonical: url,
    },
    openGraph: {
      title,
      description,
      url,
      type: 'website',
    },
    twitter: {
      card: 'summary',
      title,
      description,
    },
  };
}

// Generate SEO metadata for brand pages
export function generateBrandSEO(brand: {
  name: string;
  description?: string;
  modelsCount?: number;
  partsCount?: number;
}): Metadata {
  const slug = brand.name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-+|-+$/g, '');

  const url = `${process.env.NEXT_PUBLIC_BASE_URL}/brands/${slug}`;
  
  const title = `${brand.name} Parts Kenya | Genuine & Aftermarket ${brand.name} Parts`;
  const description = brand.description || 
    `Find genuine and aftermarket ${brand.name} parts in Kenya. ${brand.partsCount || 'Thousands of'} parts for ${brand.modelsCount || 'all'} ${brand.name} models. Quality guaranteed.`;

  return {
    title,
    description,
    keywords: [
      `${brand.name} parts Kenya`,
      `${brand.name} spare parts`,
      `genuine ${brand.name} parts`,
      `aftermarket ${brand.name} parts`,
      'automotive parts Kenya'
    ],
    alternates: {
      canonical: url,
    },
    openGraph: {
      title,
      description,
      url,
      type: 'website',
    },
    twitter: {
      card: 'summary',
      title,
      description,
    },
  };
}
