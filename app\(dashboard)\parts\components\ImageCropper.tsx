import { useState, useRef, useCallback } from 'react';
import ReactCrop, { Crop, PixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { createClient } from '@/app/libs/supabase/client';

const supabase = createClient();

interface ImageCropperProps {
  imageUrl: string;
  onCropComplete: (croppedImageUrl: string) => void;
  onClose: () => void;
}

export const ImageCropper = ({ imageUrl, onCropComplete, onClose }: ImageCropperProps) => {
  const [crop, setCrop] = useState<Crop>({
    unit: '%',
    width: 100,
    height: 100,
    x: 0,
    y: 0
  });
  const [completedCrop, setCompletedCrop] = useState<PixelCrop | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  const handleCropConfirm = async () => {
    if (!completedCrop || !imgRef.current) return;

    setIsUploading(true);

    // Create the cropped image outside the try block so it's accessible in the catch block
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      setIsUploading(false);
      console.error('No 2d context');
      return;
    }

    const image = imgRef.current;

    // Set canvas dimensions to match the cropped area
    canvas.width = completedCrop.width;
    canvas.height = completedCrop.height;

    // Draw the cropped image onto the canvas
    ctx.drawImage(
      image,
      completedCrop.x,
      completedCrop.y,
      completedCrop.width,
      completedCrop.height,
      0,
      0,
      completedCrop.width,
      completedCrop.height
    );

    const croppedImageUrl = canvas.toDataURL('image/jpeg');

    try {

      // Convert base64 to blob
      const base64Data = croppedImageUrl.split(',')[1];
      const byteCharacters = atob(base64Data);
      const byteArrays = [];

      for (let offset = 0; offset < byteCharacters.length; offset += 1024) {
        const slice = byteCharacters.slice(offset, offset + 1024);
        const byteNumbers = new Array(slice.length);

        for (let i = 0; i < slice.length; i++) {
          byteNumbers[i] = slice.charCodeAt(i);
        }

        const byteArray = new Uint8Array(byteNumbers);
        byteArrays.push(byteArray);
      }

      const blob = new Blob(byteArrays, { type: 'image/jpeg' });

      // Generate unique filename
      const timestamp = Date.now();
      const filename = `part_image_${timestamp}.jpg`;

      // Upload to Supabase storage
      console.log('Starting image upload to Supabase storage...');
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('parts-images')
        .upload(filename, blob, {
          contentType: 'image/jpeg',
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        console.error('Error uploading image:', uploadError);
        throw uploadError;
      }

      console.log('Upload successful:', uploadData);

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('parts-images')
        .getPublicUrl(filename);

      console.log('Image uploaded successfully. Public URL:', publicUrl);

      // Pass the public URL to the parent component
      onCropComplete(publicUrl);
    } catch (error) {
      console.error('Error in image upload process:', error);
      // Fallback to base64 if upload fails
      onCropComplete(croppedImageUrl);
    } finally {
      setIsUploading(false);
      onClose();
    }
  };

  const onImageLoad = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
    imgRef.current = e.currentTarget;
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-4 rounded-lg max-w-4xl w-full">
        <div className="h-[500px] overflow-auto">
          <ReactCrop
            crop={crop}
            onChange={(c) => setCrop(c)}
            onComplete={(c) => setCompletedCrop(c)}
            aspect={1}
          >
            <img
              ref={imgRef}
              src={imageUrl}
              alt="Crop me"
              onLoad={onImageLoad}
              style={{ maxHeight: '100%', maxWidth: '100%' }}
            />
          </ReactCrop>
        </div>
        <div className="mt-4 flex justify-end space-x-2">
          <button
            onClick={onClose}
            disabled={isUploading}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleCropConfirm}
            disabled={isUploading}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 flex items-center"
          >
            {isUploading ? (
              <>
                <span className="mr-2">Uploading...</span>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              </>
            ) : (
              'Confirm Crop'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};