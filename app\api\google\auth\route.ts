import { NextResponse } from 'next/server';
import { OAuth2Client } from 'google-auth-library';
import { googleMerchantService } from '@/app/services/googleMerchant';

/**
 * GET: Generate Google OAuth URL for authentication
 */
export async function GET(request: Request) {
  try {
    // Get the origin from the request to build the redirect URI
    const origin = request.headers.get('origin') || request.headers.get('host') || 'http://localhost:3000';
    const redirectUri = `${origin.startsWith('http') ? origin : `http://${origin}`}/api/google/callback`;

    console.log('Using redirect URI:', redirectUri);

    // Use specific Google Merchant API environment variables
    const clientId = process.env.GOOGLE_MERCHANT_API_CLIENT_ID;
    const clientSecret = process.env.GOOGLE_MERCHANT_API_CLIENT_SECRET;

    if (!clientId) {
      console.error('Google Merchant API Client ID not found in environment variables');
      return NextResponse.json(
        { error: 'Google Merchant API Client ID not configured' },
        { status: 500 }
      );
    }

    if (!clientSecret) {
      console.error('Google Merchant API Client Secret not found in environment variables');
      return NextResponse.json(
        { error: 'Google Merchant API Client Secret not configured' },
        { status: 500 }
      );
    }

    console.log('Using Google Merchant API credentials for auth:');
    console.log('- Client ID:', clientId ? 'Found' : 'Not found');
    console.log('- Client Secret:', clientSecret ? 'Found' : 'Not found');
    console.log('- Redirect URI:', redirectUri);

    const auth = new OAuth2Client(
      clientId,
      clientSecret,
      redirectUri // Use dynamically generated redirect URI
    );

    const url = auth.generateAuthUrl({
      access_type: 'offline',
      scope: [
        'https://www.googleapis.com/auth/content',
        'https://www.googleapis.com/auth/userinfo.email'
      ],
      // Force approval prompt to ensure we get a refresh token
      prompt: 'consent'
    });

    return NextResponse.json({ url });
  } catch (error) {
    console.error('Error generating auth URL:', error);
    return NextResponse.json(
      { error: 'Failed to generate auth URL' },
      { status: 500 }
    );
  }
}

/**
 * POST: Exchange authorization code for tokens and store them
 */
export async function POST(request: Request) {
  try {
    const { code } = await request.json();

    if (!code) {
      return NextResponse.json(
        { error: 'Authorization code is required' },
        { status: 400 }
      );
    }

    // Use the service to authenticate and store tokens
    const merchantId = await googleMerchantService.authenticate(code);

    return NextResponse.json({
      success: true,
      merchantId,
      message: 'Successfully authenticated with Google Merchant API'
    });
  } catch (error) {
    console.error('Error exchanging auth code:', error);
    return NextResponse.json(
      { error: 'Failed to exchange auth code', details: error.message },
      { status: 500 }
    );
  }
}
