import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/app/libs/supabase/server';
import { getAdjustedPrice } from '@/app/utils/priceAdjustment';

// Helper function to get all category IDs in a tree (parent + all descendants)
async function getCategoryTreeIds(supabase: any, categoryId: number): Promise<number[]> {
  try {
    // Get all categories
    const { data: allCategories, error } = await supabase
      .from('car_part_categories')
      .select('id, parent_category_id');

    if (error) {
      console.error('Error fetching categories:', error);
      return [categoryId]; // Return just the parent ID if there's an error
    }

    // Build a tree of categories to find all descendants
    const categoryIds = [categoryId];

    // Function to recursively find all child categories
    const findChildCategories = (parentId: number) => {
      const children = allCategories?.filter(c => c.parent_category_id === parentId) || [];
      children.forEach(child => {
        categoryIds.push(child.id);
        findChildCategories(child.id);
      });
    };

    // Find all descendants of the selected category
    findChildCategories(categoryId);

    return categoryIds;
  } catch (error) {
    console.error('Error in getCategoryTreeIds:', error);
    return [categoryId]; // Return just the parent ID if there's an error
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '12', 10);
    const sort = searchParams.get('sort') || 'featured';
    const categoryId = searchParams.get('category') ? parseInt(searchParams.get('category') as string) : undefined;
    const trimId = searchParams.get('trimId') ? parseInt(searchParams.get('trimId') as string) : undefined;
    const variationId = searchParams.get('variationId') ? parseInt(searchParams.get('variationId') as string) : undefined;
    const generationId = searchParams.get('generationId') ? parseInt(searchParams.get('generationId') as string) : undefined;
    const modelId = searchParams.get('modelId') ? parseInt(searchParams.get('modelId') as string) : undefined;
    const brandId = searchParams.get('brandId') ? parseInt(searchParams.get('brandId') as string) : undefined;
    const offset = (page - 1) * limit;

    console.log('Filtering parts by car with params:', {
      page,
      limit,
      offset,
      sort,
      categoryId,
      trimId,
      variationId,
      generationId,
      modelId,
      brandId
    });

    // Create a Supabase client for API route
    const response = NextResponse.next();
    const supabase = createRouteHandlerClient({ request, response });

    // Build the query to get variation_trim_ids based on the provided filters
    let variationTrimIds: number[] = [];

    if (trimId) {
      // If we have a specific trim ID, use it directly
      variationTrimIds = [trimId];
    } else if (variationId) {
      // If we have a variation ID but no trim, get all trims for this variation
      const { data: trims } = await supabase
        .from('variation_trim')
        .select('id')
        .eq('variation_id', variationId);

      if (trims && trims.length > 0) {
        variationTrimIds = trims.map(t => t.id);
      }
    } else if (generationId) {
      // If we only have a generation ID, get all trims for all variations of this generation
      const { data: variations } = await supabase
        .from('car_variation')
        .select('id')
        .eq('generation_id', generationId);

      if (variations && variations.length > 0) {
        const variationIds = variations.map(v => v.id);

        const { data: trims } = await supabase
          .from('variation_trim')
          .select('id')
          .in('variation_id', variationIds);

        if (trims && trims.length > 0) {
          variationTrimIds = trims.map(t => t.id);
        }
      }
    } else if (modelId) {
      // If we only have a model ID, get all trims for all generations and variations of this model
      const { data: generations } = await supabase
        .from('car_generation')
        .select('id')
        .eq('model_id', modelId);

      if (generations && generations.length > 0) {
        const generationIds = generations.map(g => g.id);

        const { data: variations } = await supabase
          .from('car_variation')
          .select('id')
          .in('generation_id', generationIds);

        if (variations && variations.length > 0) {
          const variationIds = variations.map(v => v.id);

          const { data: trims } = await supabase
            .from('variation_trim')
            .select('id')
            .in('variation_id', variationIds);

          if (trims && trims.length > 0) {
            variationTrimIds = trims.map(t => t.id);
          }
        }
      }
    } else if (brandId) {
      // If we only have a brand ID, get all trims for all models, generations, and variations of this brand
      const { data: models } = await supabase
        .from('car_models')
        .select('id')
        .eq('brand_id', brandId);

      if (models && models.length > 0) {
        const modelIds = models.map(m => m.id);

        const { data: generations } = await supabase
          .from('car_generation')
          .select('id')
          .in('model_id', modelIds);

        if (generations && generations.length > 0) {
          const generationIds = generations.map(g => g.id);

          const { data: variations } = await supabase
            .from('car_variation')
            .select('id')
            .in('generation_id', generationIds);

          if (variations && variations.length > 0) {
            const variationIds = variations.map(v => v.id);

            const { data: trims } = await supabase
              .from('variation_trim')
              .select('id')
              .in('variation_id', variationIds);

            if (trims && trims.length > 0) {
              variationTrimIds = trims.map(t => t.id);
            }
          }
        }
      }
    } else {
      // If we don't have any car filter, return an error
      return NextResponse.json(
        { error: 'No car filter provided' },
        { status: 400 }
      );
    }

    // If no variation trim IDs were found, return empty results with a more descriptive message
    if (variationTrimIds.length === 0) {
      console.log('No variation trims found for the provided filters');
      return NextResponse.json({
        parts: [],
        totalParts: 0,
        currentPage: page,
        totalPages: 0,
        message: 'No compatible parts found for the selected vehicle'
      });
    }

    console.log(`Found ${variationTrimIds.length} variation trims for the provided filters`);

    // Check if the parts_car table exists and has the expected structure
    try {
      const { data: tableInfo, error: tableError } = await supabase
        .from('parts_car')
        .select('*')
        .limit(1);

      if (tableError) {
        console.error('Error checking parts_car table:', tableError);
        return NextResponse.json(
          { error: `Failed to check parts_car table: ${tableError.message}` },
          { status: 500 }
        );
      }

      console.log('parts_car table structure check:', tableInfo);
    } catch (tableCheckError) {
      console.error('Exception checking parts_car table:', tableCheckError);
    }

    // Get parts that match the variation_trim_ids
    console.log(`Querying parts_car with variation_trim_ids:`, variationTrimIds.slice(0, 5), `... (${variationTrimIds.length} total)`);

    // Use a simpler query first to debug
    let partsQuery = supabase
      .from('parts_car')
      .select('part_id, variation_trim_id', { count: 'exact' })
      .in('variation_trim_id', variationTrimIds);

    // Execute the simple query to check if it works
    const { data: simplePartsData, error: simplePartsError, count: simpleCount } = await partsQuery;

    if (simplePartsError) {
      console.error('Error with simple parts_car query:', simplePartsError);
      return NextResponse.json(
        { error: `Failed with simple parts_car query: ${simplePartsError.message}` },
        { status: 500 }
      );
    }

    console.log(`Simple parts_car query returned ${simplePartsData?.length || 0} results`);

    // If no parts found for the selected car, return empty results with a descriptive message
    if (!simplePartsData || simplePartsData.length === 0) {
      console.log('No parts found for the selected car in parts_car table');
      return NextResponse.json({
        parts: [],
        totalParts: 0,
        currentPage: page,
        totalPages: 0,
        message: 'No compatible parts found for the selected vehicle'
      });
    }

    // Now try the full query with a more robust approach
    // First, get the part_ids from parts_car table
    const { data: partIds, error: partIdsError } = await supabase
      .from('parts_car')
      .select('part_id')
      .in('variation_trim_id', variationTrimIds);

    if (partIdsError) {
      console.error('Error fetching part IDs:', partIdsError);
      return NextResponse.json(
        { error: `Failed to fetch part IDs: ${partIdsError.message}` },
        { status: 500 }
      );
    }

    if (!partIds || partIds.length === 0) {
      console.log('No part IDs found for the selected car');
      return NextResponse.json({
        parts: [],
        totalParts: 0,
        currentPage: page,
        totalPages: 0,
        message: 'No compatible parts found for the selected vehicle'
      });
    }

    // Extract unique part IDs
    const uniquePartIds = [...new Set(partIds.map(p => p.part_id))];
    console.log(`Found ${uniquePartIds.length} unique part IDs`);

    // Debug: Check if we have any parts for this car
    if (uniquePartIds.length === 0) {
      console.log('No parts found for the selected car. Check the parts_car table for entries with these variation_trim_ids:',
        variationTrimIds.length > 10 ?
          `${variationTrimIds.slice(0, 10).join(', ')}... (${variationTrimIds.length} total)` :
          variationTrimIds
      );
    }

    // Special debug for VW Golf Mk7 and Electrical Parts
    if (categoryId && (modelId === 1 || brandId === 1)) {
      console.log('Debugging VW Golf Mk7 with Electrical Parts category');

      // Get the category name
      const { data: categoryData } = await supabase
        .from('car_part_categories')
        .select('label')
        .eq('id', categoryId)
        .single();

      console.log('Category:', categoryData?.label || 'Unknown');

      // Direct query to check if there are any parts in this category for VW Golf Mk7
      const { data: directQueryData, count: directQueryCount } = await supabase
        .from('parts')
        .select('id, title, category_id', { count: 'exact' })
        .eq('category_id', categoryId)
        .limit(5);

      console.log(`Direct query found ${directQueryCount} parts in category ${categoryId}`);
      if (directQueryData && directQueryData.length > 0) {
        console.log('Sample parts:', directQueryData.map(p => `${p.id}: ${p.title}`));
      }

      // Check if these parts are compatible with VW Golf Mk7
      if (directQueryData && directQueryData.length > 0) {
        const partIds = directQueryData.map(p => p.id);
        const { data: compatibilityData } = await supabase
          .from('parts_car')
          .select('part_id, variation_trim_id')
          .in('part_id', partIds)
          .in('variation_trim_id', variationTrimIds);

        console.log(`Found ${compatibilityData?.length || 0} compatible parts out of ${partIds.length} checked`);
        if (compatibilityData && compatibilityData.length > 0) {
          console.log('Compatible part IDs:', compatibilityData.map(p => p.part_id));
        }
      }
    }

    // Now query the parts table directly with these IDs
    let partsDataQuery;

    // If we have a category filter, we need a different approach
    if (categoryId) {
      console.log('Using category-first approach for better results');

      // First, get all categories in the tree
      const allCategoryIds = await getCategoryTreeIds(supabase, categoryId);
      console.log(`Category tree has ${allCategoryIds.length} categories`);

      // Check if we have any parts for this car
      if (uniquePartIds.length === 0) {
        console.log('No parts found for the selected car, checking if there are any parts in this category');

        // Query parts that are in the category tree, regardless of car compatibility
        partsDataQuery = supabase
          .from('parts')
          .select(`
            id,
            title,
            category_id,
            partnumber_group
          `, { count: 'exact' })
          .in('category_id', allCategoryIds)
          .limit(limit)
          .offset(offset);

        console.log('Querying for parts in category without car filter');
      } else {
        // Query parts that match both the category tree AND are compatible with the selected car
        partsDataQuery = supabase
          .from('parts')
          .select(`
            id,
            title,
            category_id,
            partnumber_group
          `, { count: 'exact' })
          .in('category_id', allCategoryIds)
          .in('id', uniquePartIds);
      }
    } else {
      // Standard approach - just filter by car compatibility
      partsDataQuery = supabase
        .from('parts')
        .select(`
          id,
          title,
          category_id,
          partnumber_group
        `, { count: 'exact' })
        .in('id', uniquePartIds);
    }

    // Variables to store query results
    let partsData: any[] = [];
    let count: number | null = null;

    // Add pagination
    partsDataQuery = partsDataQuery.range(offset, offset + limit - 1);

    // Execute the query
    console.log('Executing query with filters:', {
      uniquePartIds: uniquePartIds.length > 10 ?
        `${uniquePartIds.slice(0, 10).join(', ')}... (${uniquePartIds.length} total)` :
        uniquePartIds,
      categoryFilter: categoryId ? `Category ID: ${categoryId}` : 'None',
      pagination: `Offset: ${offset}, Limit: ${limit}`
    });

    const queryResult = await partsDataQuery;
    partsData = queryResult.data || [];
    count = queryResult.count;

    console.log(`Query returned ${partsData.length} parts out of ${count} total`);

    if (queryResult.error) {
      console.error('Error fetching parts:', queryResult.error);
      return NextResponse.json(
        { error: `Failed to fetch parts: ${queryResult.error.message}` },
        { status: 500 }
      );
    }

    if (!partsData || partsData.length === 0) {
      console.log('No parts found for the provided filters');

      // Special debug for VW Golf Mk7 and Electrical Parts - try a direct query
      if (categoryId) {
        console.log('Attempting direct query for parts in this category...');

        // Get all categories in the tree
        const allCategoryIds = await getCategoryTreeIds(supabase, categoryId);

        // Direct query to check if there are any parts in this category
        const { data: directCategoryParts, count: directCategoryCount } = await supabase
          .from('parts')
          .select('id, title', { count: 'exact' })
          .in('category_id', allCategoryIds)
          .limit(5);

        console.log(`Direct category query found ${directCategoryCount} parts in category tree`);

        if (directCategoryParts && directCategoryParts.length > 0) {
          // Check if any of these parts are compatible with the selected car
          const directPartIds = directCategoryParts.map(p => p.id);

          const { data: compatibleParts } = await supabase
            .from('parts_car')
            .select('part_id')
            .in('part_id', directPartIds)
            .in('variation_trim_id', variationTrimIds);

          console.log(`Found ${compatibleParts?.length || 0} compatible parts out of ${directPartIds.length} checked`);

          if (compatibleParts && compatibleParts.length > 0) {
            console.log('There ARE compatible parts, but they were not found by the main query!');
            console.log('This suggests an issue with the query logic.');
          }
        }
      }

      return NextResponse.json({
        parts: [],
        totalParts: 0,
        currentPage: page,
        totalPages: 0
      });
    }

    // We already have the part details from the query above
    const partDetails = partsData;

    // Fetch part numbers with error handling
    const partNumberIds = partDetails?.filter(p => p.partnumber_group).map(p => p.partnumber_group) || [];
    console.log(`Fetching part numbers for ${partNumberIds.length} part number groups`);

    let partNumbers: any[] = [];
    if (partNumberIds.length > 0) {
      const { data: fetchedPartNumbers, error: partNumbersError } = await supabase
        .from('part_compatibility_groups')
        .select('id, part_number')
        .in('id', partNumberIds);

      if (partNumbersError) {
        console.error('Error fetching part numbers:', partNumbersError);
        // Continue with empty part numbers rather than failing the whole request
      } else {
        partNumbers = fetchedPartNumbers || [];
      }
    }

    // Create a map of part number IDs to part numbers
    const partNumberMap = (partNumbers || []).reduce((map: Record<number, string>, pn: any) => {
      map[pn.id] = pn.part_number;
      return map;
    }, {});

    // Fetch images with error handling
    console.log(`Fetching images for ${uniquePartIds.length} parts`);
    let images = [];
    try {
      const { data: fetchedImages, error: imagesError } = await supabase
        .from('part_images')
        .select('*')
        .in('part_id', uniquePartIds);

      if (imagesError) {
        console.error('Error fetching part images:', imagesError);
        // Continue with empty images rather than failing the whole request
      } else {
        images = fetchedImages || [];
      }
    } catch (error) {
      console.error('Exception fetching part images:', error);
      // Continue with empty images
    }

    // Group images by part_id
    const partImages = (images || []).reduce((acc: Record<number, any[]>, img: any) => {
      const partId = img.part_id;
      if (!acc[partId]) {
        acc[partId] = [];
      }
      acc[partId].push(img);
      return acc;
    }, {});

    // Fetch conditions and stock with error handling
    console.log(`Fetching conditions for ${uniquePartIds.length} parts`);
    let conditions = [];
    try {
      const { data: fetchedConditions, error: conditionsError } = await supabase
        .from('parts_condition')
        .select('*')
        .in('part_id', uniquePartIds);

      if (conditionsError) {
        console.error('Error fetching part conditions:', conditionsError);
        // Continue with empty conditions rather than failing the whole request
      } else {
        conditions = fetchedConditions || [];
      }
    } catch (error) {
      console.error('Exception fetching part conditions:', error);
      // Continue with empty conditions
    }

    // Group conditions by part_id
    const partConditions = (conditions || []).reduce((acc: Record<number, any[]>, condition: any) => {
      const partId = condition.part_id;
      if (!acc[partId]) {
        acc[partId] = [];
      }
      acc[partId].push(condition);
      return acc;
    }, {});

    // Fetch prices for all conditions with error handling
    const conditionIds = conditions?.map((condition: any) => condition.id) || [];
    console.log(`Fetching prices for ${conditionIds.length} conditions`);
    let prices = [];
    if (conditionIds.length > 0) {
      try {
        const { data: fetchedPrices, error: pricesError } = await supabase
          .from('part_price')
          .select('*')
          .in('condition_id', conditionIds);

        if (pricesError) {
          console.error('Error fetching part prices:', pricesError);
          // Continue with empty prices rather than failing the whole request
        } else {
          prices = fetchedPrices || [];
        }
      } catch (error) {
        console.error('Exception fetching part prices:', error);
        // Continue with empty prices
      }
    }

    // Group prices by condition_id
    const conditionPrices = (prices || []).reduce((acc: Record<number, any>, price: any) => {
      acc[price.condition_id] = price;
      return acc;
    }, {});

    // Transform the data
    const transformedParts = partDetails?.map((part: any) => {
      // Find images for this part
      const images = partImages[part.id] || [];
      // Find the main image or use the first one
      const mainImage = images.find((img: any) => img.is_main_image === true) || images[0];
      // Get conditions for this part
      const conditions = partConditions[part.id] || [];
      // Calculate total stock
      const totalStock = conditions.reduce((total: number, condition: any) => total + (condition.stock || 0), 0);

      // Find price for this part (use the first condition with a price)
      let actualPrice = 0;
      let discountedPrice = null;

      for (const condition of conditions) {
        const price = (prices || []).find((p: any) => p.condition_id === condition.id);
        if (price && price.price > 0) {
          actualPrice = price.price;
          discountedPrice = price.discounted_price;
          break;
        }
      }

      // Calculate the adjusted price for frontend display
      const adjustedPrice = getAdjustedPrice(actualPrice);
      const adjustedDiscountedPrice = discountedPrice ? getAdjustedPrice(discountedPrice) : null;

      return {
        id: part.id.toString(),
        title: part.title || 'Unnamed Part',
        partNumber: part.partnumber_group?.toString() || 'N/A',
        actualPartNumber: partNumberMap[part.partnumber_group] || 'N/A',
        price: adjustedPrice,
        discountedPrice: adjustedDiscountedPrice,
        actualPrice: actualPrice,
        stock: totalStock,
        thumbnailUrl: mainImage?.image_url || '',
        imageUrl: mainImage?.image_url || '',
        category_id: part.category_id,
        category_name: part.car_part_categories?.label || 'Unknown'
      };
    }) || [];

    // Sort the parts based on the sort parameter
    const sortedParts = [...transformedParts].sort((a, b) => {
      if (sort === 'price-low') {
        return a.price - b.price;
      } else if (sort === 'price-high') {
        return b.price - a.price;
      } else {
        // Default sorting (featured or newest)
        return 0; // Keep original order
      }
    });

    const totalParts = count || 0;
    const totalPages = Math.ceil(totalParts / limit);

    console.log(`Calculated totalPages: ${totalPages} (totalParts: ${totalParts}, limit: ${limit})`);

    // Ensure totalPages is at least 1 if we have parts
    const finalTotalPages = sortedParts.length > 0 ? Math.max(1, totalPages) : 0;

    // Check if we're showing parts from a category without car compatibility
    const showingCategoryPartsOnly = categoryId && uniquePartIds.length === 0 && sortedParts.length > 0;

    return NextResponse.json({
      parts: sortedParts,
      totalParts,
      currentPage: page,
      totalPages: finalTotalPages,
      showingCategoryPartsOnly,
      message: showingCategoryPartsOnly ?
        'Showing parts from this category. These parts may not be compatible with your selected vehicle.' :
        undefined
    });
  } catch (error) {
    console.error('Unexpected error in filter-by-car API:', error);
    return NextResponse.json(
      {
        error: 'An unexpected error occurred while filtering parts by car',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
