// Types for the Cars Dashboard

export interface Brand {
  brand_id: number;
  brand_name: string;
}

export interface Model {
  id: number;
  brand_id: number;
  model_name: string;
  model_image?: string;
}

export interface Generation {
  id: number;
  model_id: number;
  name: string;
  start_production_year: number;
  end_production_year?: number;
}

export interface Variation {
  id: number;
  generation_id: number;
  variation: string;
}

export interface Trim {
  id: number;
  variation_id: number;
  trim: string;
}

export interface BrandFormData {
  brand_name: string;
}

export interface ModelFormData {
  brand_id: number;
  model_name: string;
  model_image?: string;
}

export interface GenerationFormData {
  model_id: number;
  name: string;
  start_production_year: number;
  end_production_year?: number;
}

export interface VariationFormData {
  generation_id: number;
  variation: string;
}

export interface TrimFormData {
  variation_id: number;
  trim: string;
}

export interface BrandWithModels extends Brand {
  models: Model[];
}

export interface ModelWithGenerations extends Model {
  generations: Generation[];
  brand_name?: string;
}

export interface GenerationWithVariations extends Generation {
  variations: Variation[];
  model_name?: string;
}

export interface VariationWithTrims extends Variation {
  trims: Trim[];
  generation_name?: string;
}

export interface TrimWithDetails extends Trim {
  variation_name?: string;
  generation_name?: string;
  model_name?: string;
  brand_name?: string;
}
