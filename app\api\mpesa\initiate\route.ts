import { NextRequest, NextResponse } from 'next/server';
import { initiateSTKPush } from '@/app/libs/mpesa/service';
import { isValidKenyanPhoneNumber } from '@/app/libs/mpesa/utils';
import { createRouteHandlerClient } from '@/app/libs/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // Create a Supabase client for API route
    const response = NextResponse.next();
    const supabase = createRouteHandlerClient({ request, response });

    // Check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized - Please log in to continue' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { phoneNumber, amount, reference, description } = body;

    // Validate required fields
    if (!phoneNumber || !amount || !reference) {
      return NextResponse.json(
        { success: false, message: 'Phone number, amount, and reference are required' },
        { status: 400 }
      );
    }

    // Validate phone number
    if (!isValidKenyanPhoneNumber(phoneNumber)) {
      return NextResponse.json(
        { success: false, message: 'Invalid phone number format. Please use a valid Kenyan phone number.' },
        { status: 400 }
      );
    }

    // Validate amount
    if (isNaN(amount) || amount <= 0) {
      return NextResponse.json(
        { success: false, message: 'Amount must be a positive number' },
        { status: 400 }
      );
    }

    // Initiate STK push
    const response = await initiateSTKPush({
      phoneNumber,
      amount,
      reference,
      description,
    });

    if (!response.success) {
      return NextResponse.json(
        { success: false, message: response.message, error: response.error },
        { status: 500 }
      );
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error initiating M-PESA payment:', error);
    return NextResponse.json(
      {
        success: false,
        message: `Error initiating M-PESA payment: ${error instanceof Error ? error.message : 'Unknown error'}`
      },
      { status: 500 }
    );
  }
}
