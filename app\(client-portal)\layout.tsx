import React from 'react';
import { Metadata } from 'next';
import Client<PERSON>ortalHeader from './components/ClientPortalHeader';
import ClientPortalSidebar from './components/ClientPortalSidebar';
import ClientPortalFooter from './components/ClientPortalFooter';
import ClientAuthProvider from './components/ClientAuthProvider';

export const metadata: Metadata = {
  title: 'Autoflow Client Portal',
  description: 'Client portal for Autoflow',
};

export default function ClientPortalLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClientAuthProvider>
      <div className="flex flex-col min-h-screen bg-gray-100">
        <ClientPortalHeader />
        
        <div className="flex flex-1">
          <ClientPortalSidebar />
          
          <main className="flex-1 p-4 md:p-6 lg:p-8">
            {children}
          </main>
        </div>
        
        <ClientPortalFooter />
      </div>
    </ClientAuthProvider>
  );
}
