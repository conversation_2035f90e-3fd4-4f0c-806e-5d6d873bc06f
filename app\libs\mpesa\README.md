# M-PESA Integration for Autoflow

This module provides integration with Safaricom's M-PESA API for payment processing in the Autoflow application.

## Features

- **STK Push**: Initiate payment requests directly to the customer's phone
- **Payment Status Checking**: Check the status of M-PESA payments
- **Callback Handling**: Process M-PESA payment callbacks
- **Transaction Tracking**: Store and track M-PESA transactions in the database

## Setup Instructions

1. **Register for M-PESA API Access**:
   - Visit the [Safaricom Developer Portal](https://developer.safaricom.co.ke/)
   - Create an account and register your application
   - Get your Consumer Key, Consumer Secret, and Passkey

2. **Set Environment Variables**:
   Add the following variables to your `.env.local` file:
   ```
   MPESA_CONSUMER_KEY=your-mpesa-consumer-key
   MPESA_CONSUMER_SECRET=your-mpesa-consumer-secret
   MPESA_PASSKEY=your-mpesa-passkey
   MPESA_SHORT_CODE=your-mpesa-shortcode
   NEXT_PUBLIC_BASE_URL=https://your-domain.com
   ```

3. **Create Database Tables**:
   Run the SQL script in `app/sql/mpesa_transactions.sql` to create the necessary database tables.

4. **Configure Callback URL**:
   - In the Safaricom Developer Portal, set your callback URL to `https://your-domain.com/api/mpesa/callback`
   - For local development, you can use a service like ngrok to expose your local server to the internet

## Usage

### Initiating an STK Push

```typescript
import { initiateSTKPush } from '@/app/libs/mpesa/service';

// Initiate an STK push request
const response = await initiateSTKPush({
  phoneNumber: '07XXXXXXXX',
  amount: 1000,
  reference: 'ORDER-123',
  description: 'Payment for order #123',
});

if (response.success) {
  // Store the checkout request ID for later use
  const checkoutRequestId = response.data.CheckoutRequestID;
}
```

### Checking Payment Status

```typescript
import { querySTKStatus } from '@/app/libs/mpesa/service';

// Check the status of an STK push request
const response = await querySTKStatus(checkoutRequestId);

if (response.success && response.data.ResultCode === '0') {
  // Payment was successful
} else {
  // Payment failed or is still pending
}
```

## Troubleshooting

- **Network Issues**: Ensure your server has internet access and can reach the Safaricom API endpoints
- **Authentication Errors**: Verify your Consumer Key and Consumer Secret are correct
- **Callback Issues**: Make sure your callback URL is accessible from the internet
- **Phone Number Format**: Ensure phone numbers are in the correct format (2547XXXXXXXX)

## Resources

- [Safaricom Developer Documentation](https://developer.safaricom.co.ke/docs)
- [M-PESA API Reference](https://developer.safaricom.co.ke/APIs/MpesaExpressSimulate)
- [Daraja API Forum](https://developer.safaricom.co.ke/forum)
