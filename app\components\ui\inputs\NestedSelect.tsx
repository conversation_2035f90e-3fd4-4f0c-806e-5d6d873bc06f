'use client'

import { useState, useMemo, createContext, useContext, useEffect } from 'react'
import { motion, AnimatePresence, LayoutGroup } from 'framer-motion'
import { ChevronDown, Search, ChevronRight } from 'lucide-react'

// Type Definitions
interface NestedSelectItem {
  label: string
  value: string
  children?: NestedSelectItem[]
}

interface NestedSelectProps {
  items: NestedSelectItem[]
  placeholder?: string
  onValueChange?: (value: string) => void
  value?: string | null
  disabled?: boolean
}

interface SelectContextType {
  selectedValue: string | null
  open: boolean
  setOpen: (open: boolean) => void
  searchQuery: string
  setSearchQuery: (query: string) => void
  onValueChange?: (value: string) => void
  disabled: boolean
}

// Context Creation
const SelectContext = createContext<SelectContextType>({
  selectedValue: null,
  open: false,
  setOpen: () => {},
  searchQuery: '',
  setSearchQuery: () => {},
  onValueChange: () => {},
  disabled: false,
})

// Main Component
const NestedSelect = ({
  items,
  placeholder,
  onValueChange,
  value,
  disabled = false,
}: NestedSelectProps) => {
  const [open, setOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  const selectedLabel = useMemo(() => {
    const findLabel = (items: NestedSelectItem[]): string | undefined => {
      // Check if 'items' is an array before iterating
      if (!Array.isArray(items)) {
        console.error("Items is not an array:", items);
        return undefined; // Or some default label
    }
      for (const item of items) {
        if (item.value === value) return item.label
        if (item.children) {
          const childLabel = findLabel(item.children)
          if (childLabel) return childLabel
        }
      }
    }
    return findLabel(items) || ''
}, [value, items]);

  return (
    <SelectContext.Provider
      value={{
        selectedValue: value || null,
        open,
        setOpen: disabled ? () => {} : setOpen,
        searchQuery,
        setSearchQuery: disabled ? () => {} : setSearchQuery,
        onValueChange,
        disabled,
      }}
    >
      <div className="relative w-full">
        <NestedSelectTrigger>
          <NestedSelectValue
            placeholder={placeholder}
            selectedLabel={selectedLabel}
          />
        </NestedSelectTrigger>
        {open && !disabled && <NestedSelectContent items={items} />}
      </div>
    </SelectContext.Provider>
  )
}

// Subcomponents
const NestedSelectTrigger = ({ children }: { children: React.ReactNode }) => {
  const { setOpen, open, disabled } = useContext(SelectContext)

  return (
    <div
      onClick={() => !disabled && setOpen(!open)}
      className={`flex h-12 w-full items-center rounded-lg border bg-white px-4 py-2 shadow-sm transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
        disabled
          ? 'border-gray-100 bg-gray-50 cursor-not-allowed'
          : 'border-gray-200 hover:border-gray-300'
      }`}
    >
      {children}
      <ChevronDown
        className={`ml-auto h-5 w-5 transition-transform ${
          open ? 'rotate-180' : ''
        } ${disabled ? 'text-gray-300' : 'text-gray-400'}`}
      />
    </div>
  )
}

const NestedSelectValue = ({
  placeholder,
  selectedLabel,
}: {
  placeholder?: string
  selectedLabel: string
}) => {
  const { selectedValue, disabled } = useContext(SelectContext)

  return (
    <div className="relative flex-1">
      <AnimatePresence>
        {!selectedValue ? (
          <motion.span
            key="placeholder"
            initial={{ opacity: 0, y: 0 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className={`absolute left-0 top-0 ${
              disabled ? 'text-gray-400' : 'text-gray-400'
            }`}
          >
            {placeholder}
          </motion.span>
        ) : (
          <motion.span
            key="selected-value"
            layoutId="selected-value"
            className={`block origin-top-left text-left ${
              disabled ? 'text-gray-400' : 'text-gray-900'
            }`}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            {selectedLabel}
          </motion.span>
        )}
      </AnimatePresence>
    </div>
  )
}

const NestedSelectContent = ({ items }: { items: NestedSelectItem[] }) => {
  const { open, searchQuery, setSearchQuery, disabled } = useContext(SelectContext)
  const filteredItems = useMemo(
    () => filterItems(items, searchQuery),
    [items, searchQuery]
  )

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="absolute z-50 mt-2 w-full rounded-lg border border-gray-200 bg-white shadow-lg"
      style={{ position: 'absolute', top: '100%' }}
    >
      <div className="p-2">
        <div className="flex items-center rounded-md bg-gray-100 px-3 py-2">
          <Search className="h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search..."
            className="ml-2 flex-1 bg-transparent outline-none"
            value={searchQuery}
            onChange={(e) => !disabled && setSearchQuery(e.target.value)}
            disabled={disabled}
          />
        </div>
      </div>

      <div className="max-h-64 overflow-y-auto p-2">
        <LayoutGroup>
          {filteredItems.map((item) => (
            <SelectItemComponent key={item.value} item={item} level={0} />
          ))}
          {filteredItems.length === 0 && (
            <div className="px-4 py-2 text-gray-500">No results found</div>
          )}
        </LayoutGroup>
      </div>
    </motion.div>
  )
}

const SelectItemComponent = ({
  item,
  level,
}: {
  item: NestedSelectItem
  level: number
}) => {
  const { setOpen, selectedValue, onValueChange, disabled } = useContext(SelectContext)

  const handleSelect = () => {
    if (!item.children?.length) {
      onValueChange?.(item.value)
      setOpen(false)
    }
  }

  return (
    <>
      <motion.div
        layout
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className={`group flex cursor-pointer items-center rounded-md px-4 py-2 transition-colors ${
          item.children?.length
            ? 'pointer-events-none font-semibold text-gray-900'
            : 'hover:bg-blue-50 text-gray-900'
        } ${
          selectedValue === item.value
            ? 'bg-blue-50 font-medium text-blue-600'
            : ''
        }`}
        style={{ paddingLeft: `${level * 1.5 + 1}rem`}}
        onClick={handleSelect}
      >
        {item.children?.length ? (
          <ChevronRight className="mr-2 h-4 w-4 text-gray-400" />
        ) : null}
        {item.label}
      </motion.div>

      {item.children?.map((child) => (
        <SelectItemComponent key={child.value} item={child} level={level + 1} />
      ))}
    </>
  )
}

// Helper Functions
function filterItems(
  items: NestedSelectItem[],
  query: string
): NestedSelectItem[] {
  return items
    .filter((item) => {
      const matches = item.label.toLowerCase().includes(query.toLowerCase())
      if (item.children) {
        const filteredChildren = filterItems(item.children, query)
        return matches || filteredChildren.length > 0
      }
      return matches
    })
    .map((item) => {
      if (item.children) {
        return {
          ...item,
          children: filterItems(item.children, query),
        }
      }
      return item
    })
}

// Exports
export { NestedSelect }
export type { NestedSelectItem }