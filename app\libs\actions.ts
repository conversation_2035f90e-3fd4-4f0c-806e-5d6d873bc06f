// app/libs/actions.ts
'use server';

import { revalidatePath } from 'next/cache';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';
import { Database } from '@/app/types/database';

interface UploadResult {
  success: boolean;
  imageUrl?: string;
  error?: string;
}

export const uploadImage = async (imageDataUrl: string): Promise<UploadResult> => {
    try {
        // Create a Supabase client
        const cookieStore = await cookies();
        const supabase = createServerClient<Database>(
            process.env.NEXT_PUBLIC_SUPABASE_URL!,
            process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
            {
                cookies: {
                    get(name: string) {
                        return cookieStore.get(name)?.value;
                    },
                    set() {
                        // Server actions don't set cookies
                    },
                    remove() {
                        // Server actions don't remove cookies
                    },
                },
            }
        );

        // Validate imageDataUrl format
        if (!imageDataUrl || !imageDataUrl.startsWith('data:image/')) {
            console.error("Invalid image data URL format");
            return { success: false, error: 'Invalid image format' };
        }

        try {
            // Generate a unique filename first
            const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.jpg`;
            const filePath = `${fileName}`;
            
            console.log(`Processing image upload to path: ${filePath}`);
            
            // Extract the base64 data - everything after the comma
            const commaIndex = imageDataUrl.indexOf(',');
            if (commaIndex === -1) {
                return { success: false, error: 'Invalid data URL format' };
            }
            
            const base64Data = imageDataUrl.substring(commaIndex + 1);
            const buffer = Buffer.from(base64Data, 'base64');
            
            // Add size logging for debugging
            console.log(`Image size: ${Math.round(buffer.length / 1024)} KB`);
            
            // If image is too large, try to handle it differently
            if (buffer.length > 5 * 1024 * 1024) { // If larger than 5MB
                console.log('Large image detected, applying alternative upload process');
                
                // Here we'll still use the same approach but with logging
                // In a production app, you might implement chunked uploads for very large files
            }

            // Upload to Supabase Storage
            const { data, error } = await supabase
                .storage
                .from('car-part-images')
                .upload(filePath, buffer, {
                    contentType: 'image/jpeg',
                    upsert: true,
                });

            if (error) {
                console.error("Supabase Storage upload error:", error);
                return { success: false, error: error.message };
            }

            // Get the public URL
            const { data: publicUrlData } = supabase
                .storage
                .from('car-part-images')
                .getPublicUrl(filePath);

            console.log("Successfully uploaded:", publicUrlData.publicUrl);
            revalidatePath('/parts/add');

            return { success: true, imageUrl: publicUrlData.publicUrl };
        } catch (conversionError: any) {
            console.error("Error processing image data:", conversionError);
            return { 
                success: false, 
                error: `Failed to process image data: ${conversionError?.message || 'Unknown error'}`
            };
        }
    } catch (error: any) {
        console.error("Error in uploadImage server action:", error);
        return { 
            success: false, 
            error: `An unexpected error occurred: ${error?.message || 'Unknown error'}`
        };
    }
};

interface PartData {
    partnumber: string | undefined | null;
    category_id: number;
    title: string | undefined | null;
    createdBy: number;
    updatedBy: number;
}

export const createPart = async (partData: PartData) => {
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set() {
          // Server actions don't set cookies
        },
        remove() {
          // Server actions don't remove cookies
        },
      },
    }
  );
  const { data, error } = await supabase
    .from('parts')
    .insert([partData])
    .select()
    .single(); // Use .single() to get a single object

  if (error) {
    console.error("Error creating part:", error);
    return null;
  }

  revalidatePath('/parts/add'); // Or the appropriate path
  return data;
};


interface ImageData {
part_id: number;
image_url: string;
is_main_image?: boolean;
alt_text?: string;
}

export const createImage = async (imageData: ImageData) => {
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set() {
          // Server actions don't set cookies
        },
        remove() {
          // Server actions don't remove cookies
        },
      },
    }
  );

const { data, error } = await supabase
  .from('part_images')
  .insert([imageData]).select().single();

if (error) {
  console.error("Error creating image:", error);
  return null;
}
revalidatePath('/parts/add');
return data;
};