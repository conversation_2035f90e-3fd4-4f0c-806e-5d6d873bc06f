'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { usePermissions } from '@/app/hooks/usePermissions';
import { useAuth } from '@/app/hooks/useAuth';
import { useUserCookie } from '@/app/hooks/useUserCookie';
import { createClient } from '@/app/libs/supabase/client';
import Spinner from '@/app/components/ui/Spinner';
import Link from 'next/link';

// Icon components
function DashboardIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <rect x="3" y="3" width="7" height="7" />
      <rect x="14" y="3" width="7" height="7" />
      <rect x="14" y="14" width="7" height="7" />
      <rect x="3" y="14" width="7" height="7" />
    </svg>
  );
}

function UsersIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" />
      <circle cx="9" cy="7" r="4" />
      <path d="M23 21v-2a4 4 0 0 0-3-3.87" />
      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
    </svg>
  );
}

function RolesIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
      <circle cx="12" cy="7" r="4" />
      <path d="M18 2h2v2" />
      <path d="M20 4v2" />
      <path d="M18 6h2" />
    </svg>
  );
}

function PermissionsIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
      <path d="M7 11V7a5 5 0 0 1 10 0v4" />
    </svg>
  );
}

function AuditIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
      <polyline points="14 2 14 8 20 8" />
      <line x1="16" y1="13" x2="8" y2="13" />
      <line x1="16" y1="17" x2="8" y2="17" />
      <polyline points="10 9 9 9 8 9" />
    </svg>
  );
}

export default function AdminLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const { hasPermission: originalHasPermission, isLoading: permissionLoading, error } = usePermissions('admin:access_panel');
  const { roleName, isSuperAdmin, isLoading: cookieLoading } = useUserCookie();
  const [isClient, setIsClient] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [isDebugMode, setIsDebugMode] = useState(true); // Changed from false to true
  const { user } = useAuth();
  const supabase = createClient();
  const [actualSuperAdmin, setActualSuperAdmin] = useState(false);

  // Force hasPermission to be true if isSuperAdmin is true, regardless of what usePermissions returns
  const hasPermission = isSuperAdmin ? true : originalHasPermission;

  // User has access if they have the permission OR they are a Super Admin
  const hasAccess = hasPermission || isSuperAdmin || actualSuperAdmin;

  // Debug logging
  console.log('[AdminLayout] Original hasPermission:', originalHasPermission);
  console.log('[AdminLayout] Overridden hasPermission:', hasPermission);
  console.log('[AdminLayout] Role name:', roleName);
  console.log('[AdminLayout] Role name lowercase:', roleName?.toLowerCase());
  console.log('[AdminLayout] Is Super Admin from cookie:', isSuperAdmin);
  console.log('[AdminLayout] Has Access:', hasAccess);

  // Combined loading state
  const isLoading = permissionLoading || cookieLoading;

  useEffect(() => {
    setIsClient(true);

    // Fetch debug info
    const fetchDebugInfo = async () => {
      try {
        if (user) {
          // Direct check using RPC
          const { data: directCheck, error: directError } = await supabase.rpc('check_user_permission', {
            p_user_id: user.id,
            p_permission_name: 'admin:access_panel'
          });

          // Get user roles directly from database
          const { data: userRoles, error: rolesError } = await supabase
            .from('user_roles')
            .select('role_id, roles(name, description)')
            .eq('user_id', user.id);

          // Check if user is actually a Super Admin by checking role names
          const isActualSuperAdmin = userRoles?.some((ur: any) => {
            const roleName = ur?.roles?.name;
            return roleName && 
              (roleName === 'Super Admin' || 
               roleName.toLowerCase() === 'super admin' || 
               roleName.toLowerCase() === 'superadmin');
          }) || false;

          setActualSuperAdmin(isActualSuperAdmin);
          console.log('[AdminLayout] User Roles from DB:', userRoles);
          console.log('[AdminLayout] Is Actual Super Admin:', isActualSuperAdmin);

          // API check
          const response = await fetch('/api/rbac/check-permission');
          const apiData = await response.json();

          // Log detailed permission info
          console.log('[AdminLayout] Direct Permission Check:', directCheck);
          console.log('[AdminLayout] API Permission Check:', apiData);
          console.log('[AdminLayout] Current User Cookie Role:', roleName);
          console.log('[AdminLayout] Current User ID:', user.id);

          setDebugInfo({
            directCheck: {
              result: directCheck,
              error: directError ? directError.message : null
            },
            apiCheck: apiData,
            user: {
              id: user.id,
              email: user.email
            },
            role: {
              name: roleName,
              isSuperAdmin,
              actualSuperAdmin: isActualSuperAdmin
            },
            userRoles,
            access: {
              hasPermission,
              isSuperAdmin,
              actualSuperAdmin: isActualSuperAdmin,
              hasAccess: hasPermission || isSuperAdmin || isActualSuperAdmin
            }
          });
        }
      } catch (err) {
        console.error('Error fetching debug info:', err);
      }
    };

    if (isClient && user) {
      fetchDebugInfo();
    }
  }, [isClient, user, supabase, roleName, isSuperAdmin, hasPermission]);

  // Recalculate hasAccess whenever relevant dependencies change
  const effectiveHasAccess = hasPermission || isSuperAdmin || actualSuperAdmin;

  useEffect(() => {
    // Only redirect if debug mode is disabled
    if (isClient && !isLoading) {
      console.log('[AdminRedirect] Checking access with these values:');
      console.log('[AdminRedirect] hasPermission =', hasPermission);
      console.log('[AdminRedirect] isSuperAdmin =', isSuperAdmin);
      console.log('[AdminRedirect] actualSuperAdmin =', actualSuperAdmin);
      console.log('[AdminRedirect] effectiveHasAccess =', effectiveHasAccess);
      console.log('[AdminRedirect] debugMode =', isDebugMode);
      
      // Add slight delay to ensure all state values are properly updated
      const redirectTimer = setTimeout(() => {
        if (!effectiveHasAccess && !isDebugMode) {
          console.log('[AdminRedirect] REDIRECTING: No admin access detected after verification');
          router.push('/dashboard');
        } else {
          console.log('[AdminRedirect] Access granted - staying on admin page');
        }
      }, 500); // 500ms delay to ensure all state is updated
      
      return () => clearTimeout(redirectTimer);
    }
  }, [isClient, isLoading, hasPermission, isSuperAdmin, actualSuperAdmin, effectiveHasAccess, router, isDebugMode]);

  if (isLoading || !isClient) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  // In debug mode, show the admin panel even without access
  if (!hasAccess && !isDebugMode) {
    return (
      <div className="flex h-screen flex-col items-center justify-center p-4 text-center">
        <h1 className="mb-4 text-2xl font-bold text-red-600">Access Denied</h1>
        <p className="mb-2">You don't have permission to access the admin panel.</p>
        <p className="mb-4 text-sm text-gray-500">
          Permission check result: {hasPermission ? 'Granted' : 'Denied'}<br/>
          Role: {roleName || 'Unknown'}<br/>
          Super Admin: {isSuperAdmin ? 'Yes' : 'No'}<br/>
          Access: {hasAccess ? 'Granted' : 'Denied'}
        </p>
        {error && (
          <div className="mb-4 rounded-md bg-red-50 p-4 text-red-800">
            <p>Error: {error.message}</p>
          </div>
        )}
        <div className="mb-4 rounded-md bg-yellow-50 p-4 text-yellow-800">
          <h3 className="mb-2 font-semibold">Debug Information</h3>
          <pre className="max-h-60 overflow-auto text-xs">{JSON.stringify(debugInfo, null, 2)}</pre>
        </div>
        <div className="flex space-x-4">
          <button
            onClick={() => setIsDebugMode(true)}
            className="rounded-md bg-green-600 px-4 py-2 text-white hover:bg-green-700"
          >
            Enter Debug Mode
          </button>
          <button
            onClick={() => router.push('/dashboard')}
            className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    );
  }

  // Admin menu items
  const adminMenuItems = [
    { id: 'dashboard', label: 'Dashboard', href: '/admin', icon: DashboardIcon },
    { id: 'users', label: 'User Management', href: '/admin/users', icon: UsersIcon },
    { id: 'roles', label: 'Role Management', href: '/admin/roles', icon: RolesIcon },
    { id: 'permissions', label: 'Permission Management', href: '/admin/permissions', icon: PermissionsIcon },
    { id: 'audit', label: 'Audit Log', href: '/admin/audit', icon: AuditIcon },
  ];

  return (
    <>
      {isDebugMode && !hasAccess && (
        <div className="mb-6 rounded-md bg-yellow-50 p-4 text-yellow-800">
          <h3 className="mb-2 font-semibold">⚠️ Debug Mode Enabled</h3>
          <p className="mb-2">You are viewing the admin panel in debug mode without the required access.</p>
          <p className="mb-4">
            Permission check result: {hasPermission ? 'Granted' : 'Denied'}<br/>
            Role: {roleName || 'Unknown'}<br/>
            Super Admin: {isSuperAdmin ? 'Yes' : 'No'}<br/>
            Access: {hasAccess ? 'Granted' : 'Denied'}
          </p>
          <div className="mb-4">
            <h4 className="mb-1 font-medium">Debug Information:</h4>
            <pre className="max-h-60 overflow-auto rounded-md bg-gray-100 p-2 text-xs">{JSON.stringify(debugInfo, null, 2)}</pre>
          </div>
          <div className="flex space-x-4">
            <button
              onClick={() => setIsDebugMode(false)}
              className="rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
            >
              Exit Debug Mode
            </button>
            <a
              href="/admin-direct"
              className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
            >
              Go to Admin Direct Access
            </a>
          </div>
        </div>
      )}

      {children}
    </>
  );
}
