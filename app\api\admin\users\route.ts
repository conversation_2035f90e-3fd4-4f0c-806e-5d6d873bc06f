// app/api/admin/users/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { checkSuperAdminAuth } from '@/app/utils/apiAuth';

export async function GET(request: NextRequest) {
  // Check Super Admin authentication
  const { authenticated, user, supabase, isSuperAdmin, errorResponse } = await checkSuperAdminAuth(request);
  
  // If not authenticated or not Super Admin, return the error response
  if (!authenticated || !user || !supabase || !isSuperAdmin) {
    return errorResponse;
  }
  
  try {
    // Get all users from the database
    const { data: users, error } = await supabase
      .from('profiles')
      .select(`
        id,
        full_name,
        email,
        phone,
        created_at,
        updated_at,
        user_roles:user_roles(
          role_id,
          roles:roles(
            id,
            name,
            description
          )
        )
      `)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching users:', error);
      return NextResponse.json(
        { error: 'Failed to fetch users', details: error.message },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      users
    });
  } catch (error: any) {
    console.error('Unexpected error in admin users API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
