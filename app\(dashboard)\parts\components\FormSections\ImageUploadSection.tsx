// app/(dashboard)/parts/add/components/AddPartModal/ImageUploadSection.tsx
'use client';

import React, { useCallback, useState } from 'react';
import { Controller } from 'react-hook-form';
import RadioGroup from '@/app/components/ui/inputs/RadioGroup';
import Upload from '@/app/components/ui/inputs/Upload';
import dynamic from 'next/dynamic';
import 'react-image-crop/dist/ReactCrop.css';
import { Crop, PixelCrop } from 'react-image-crop';
import AnimatedEllipsisLoader from '@/app/components/ui/AnimatedEllipsisLoader';
import { CheckCircle2, Star, Trash2, Image as ImageIcon } from 'lucide-react';
import Button from '@/app/components/ui/inputs/Button';

const ReactCrop = dynamic(() => import('react-image-crop'), { ssr: false });

// Constants for image compression
const MAX_IMAGE_SIZE = 800; // max dimension (width or height) in pixels
const JPEG_QUALITY = 0.85; // quality setting for JPEG compression (0-1)

interface ImageUploadSectionProps {
  control: any;
  imageType: string | undefined;
  handleImageUploadSuccess: (url: string | null, isMain: boolean, isLastImage: boolean) => Promise<string | null>;
  isImageUploading: boolean;
  existingImages?: Array<{ url: string; isMain: boolean }>;
  onRemoveImage?: (url: string) => void;
  onSetMainImage?: (url: string) => void;
  onAllImagesProcessed?: () => void; // Add new callback for when all images are done
}

const ImageUploadSection: React.FC<ImageUploadSectionProps> = ({
  control,
  imageType,
  handleImageUploadSuccess,
  isImageUploading,
  existingImages = [],
  onRemoveImage,
  onSetMainImage,
  onAllImagesProcessed,
}) => {
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [crop, setCrop] = useState<Crop>({
    x: 0,
    y: 0,
    width: 100,
    height: 100,
    unit: '%',
  });
  const [croppedImageUrl, setCroppedImageUrl] = useState<string | null>(null);
  const [imageQueue, setImageQueue] = useState<File[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState<number>(0);
  const [processingQueue, setProcessingQueue] = useState<boolean>(false);
  const [uploadStats, setUploadStats] = useState({ processed: 0, total: 0 });

  const handleFilesAdded = useCallback(
    (file: File) => {
      console.log('handleFilesAdded called with single file');
      if (imageQueue.length > 0 || processingQueue) {
        setImageQueue((prev) => [...prev, file]);
        setUploadStats((prev) => ({ ...prev, total: prev.total + 1 }));
        return;
      }

      // This is a single file upload
      console.log('Single file upload detected');
      setImageQueue([file]);
      setCurrentImageIndex(0);
      setProcessingQueue(true);
      setUploadStats({ processed: 0, total: 1 });

      setTimeout(() => {
        setImageSrc(URL.createObjectURL(file));
      }, 0);
    },
    [imageQueue, processingQueue]
  );

  const handleMultipleFilesAdded = useCallback(
    (files: File[]) => {
      const maxAdditionalFiles = 5 - existingImages.length;
      const filesToProcess = files.slice(0, maxAdditionalFiles);

      if (filesToProcess.length === 0) return;

      if (processingQueue) {
        setImageQueue((prev) => [...prev, ...filesToProcess]);
        setUploadStats((prev) => ({ ...prev, total: prev.total + filesToProcess.length }));
        return;
      }

      setImageQueue(filesToProcess);
      setCurrentImageIndex(0);
      setProcessingQueue(true);
      setUploadStats({ processed: 0, total: filesToProcess.length });

      setTimeout(() => {
        setImageSrc(URL.createObjectURL(filesToProcess[0]));
      }, 0);
    },
    [processingQueue, existingImages.length]
  );

  // Helper function to compress image
  const compressImage = useCallback((sourceCanvas: HTMLCanvasElement, quality: number = JPEG_QUALITY): string => {
    try {
      // Return compressed data URL
      return sourceCanvas.toDataURL('image/jpeg', quality);
    } catch (err) {
      console.error('Error compressing image:', err);
      // Fallback to uncompressed if compression fails
      return sourceCanvas.toDataURL('image/jpeg');
    }
  }, []);

  // Helper function to resize image while maintaining aspect ratio
  const resizeImage = useCallback((img: HTMLImageElement, maxSize: number = MAX_IMAGE_SIZE): HTMLCanvasElement => {
    const canvas = document.createElement('canvas');
    let width = img.width;
    let height = img.height;

    // Calculate new dimensions while maintaining aspect ratio
    if (width > height) {
      if (width > maxSize) {
        height = Math.round(height * (maxSize / width));
        width = maxSize;
      }
    } else {
      if (height > maxSize) {
        width = Math.round(width * (maxSize / height));
        height = maxSize;
      }
    }

    canvas.width = width;
    canvas.height = height;

    const ctx = canvas.getContext('2d');
    ctx?.drawImage(img, 0, 0, width, height);

    return canvas;
  }, []);

  const processCurrentImage = useCallback(async () => {
    // Check if this is a single image upload
    const isSingleImageUpload = imageQueue.length === 1 && existingImages.length === 0;
    if (!imageSrc) return;
    setIsUploading(true);
    try {
      let imageDataUrl: string | null = null;
      if (!croppedImageUrl && imageSrc) {
        const image = new Image();
        image.src = imageSrc;
        imageDataUrl = await new Promise<string>((resolve, reject) => {
          image.onload = () => {
            try {
              // First resize the image to manageable dimensions
              const resizedCanvas = resizeImage(image);
              // Then compress it with quality setting
              const compressedDataUrl = compressImage(resizedCanvas);
              resolve(compressedDataUrl);
            } catch (err) {
              console.error('Error processing image:', err);
              reject(new Error('Failed to process image'));
            }
          };
          image.onerror = () => reject(new Error('Failed to load image'));
        });
      } else if (croppedImageUrl) {
        // For cropped images, we still want to compress them
        const image = new Image();
        image.src = croppedImageUrl;
        imageDataUrl = await new Promise<string>((resolve, reject) => {
          image.onload = () => {
            try {
              // Create a canvas with the cropped dimensions
              const canvas = document.createElement('canvas');
              canvas.width = image.width;
              canvas.height = image.height;

              // Draw the cropped image to the canvas
              const ctx = canvas.getContext('2d');
              ctx?.drawImage(image, 0, 0);

              // Compress the result
              const compressedDataUrl = compressImage(canvas);
              resolve(compressedDataUrl);
            } catch (err) {
              console.error('Error processing cropped image:', err);
              reject(new Error('Failed to process image'));
            }
          };
          image.onerror = () => reject(new Error('Failed to load image'));
        });
      }
      if (!imageDataUrl) {
        throw new Error('Failed to get image data');
      }
      // Determine if this is the last image
      const nextIndex = currentImageIndex + 1;
      const isLastImage = nextIndex >= imageQueue.length;
      console.log(`Processing image ${currentImageIndex + 1} of ${imageQueue.length}, isLastImage: ${isLastImage}`);

      // Log if this is a single image upload
      console.log(`Is single image upload: ${isSingleImageUpload}`);

      const storageUrl = await handleImageUploadSuccess(
        imageDataUrl,
        currentImageIndex === 0 && existingImages.length === 0,
        isLastImage || isSingleImageUpload // Force isLastImage to true for single uploads
      );
      setUploadStats((prev) => ({
        ...prev,
        processed: prev.processed + 1,
      }));
      setImageSrc(null);
      setCroppedImageUrl(null);
      if (nextIndex < imageQueue.length) {
        setCurrentImageIndex(nextIndex);
        setTimeout(() => {
          setImageSrc(URL.createObjectURL(imageQueue[nextIndex]));
        }, 0);
      } else {
        setImageQueue([]);
        setCurrentImageIndex(0);
        setProcessingQueue(false);
        // Always trigger onAllImagesProcessed when we're done with the queue,
        // regardless of how many images were processed
        console.log('Image queue processing complete, calling onAllImagesProcessed');
        if (onAllImagesProcessed) {
          // Add a small delay to ensure all state updates are complete
          setTimeout(() => {
            onAllImagesProcessed();
            console.log('onAllImagesProcessed called');
          }, 100);
        } else {
          console.warn('onAllImagesProcessed callback is not defined');
        }

        // For single image uploads, make sure we call onAllImagesProcessed
        if (isSingleImageUpload && onAllImagesProcessed) {
          console.log('Single image upload complete, ensuring onAllImagesProcessed is called');
          // Call multiple times with different delays to ensure it gets through
          onAllImagesProcessed();
          console.log('onAllImagesProcessed called immediately for single image');

          setTimeout(() => {
            onAllImagesProcessed();
            console.log('onAllImagesProcessed called after 200ms for single image');
          }, 200);

          setTimeout(() => {
            onAllImagesProcessed();
            console.log('onAllImagesProcessed called after 500ms for single image');
          }, 500);
        }
      }
    } finally {
      setIsUploading(false);
    }
  }, [imageSrc, croppedImageUrl, currentImageIndex, imageQueue, existingImages, handleImageUploadSuccess, resizeImage, compressImage, onAllImagesProcessed]);

  const onImageLoaded = useCallback((image: HTMLImageElement) => {
    setCrop({
      x: 0,
      y: 0,
      width: 100,
      height: 100,
      unit: '%',
    });
  }, []);

  const onChange = useCallback(
    (newCrop: Crop) => {
      setCrop((currentCrop) => ({
        ...currentCrop,
        ...newCrop,
      }));
    },
    [setCrop]
  );

  const onCropComplete = useCallback(
    (crop: PixelCrop) => {
      if (imageSrc && crop.width && crop.height) {
        makeClientCrop(imageSrc, crop);
      }
    },
    [imageSrc]
  );

  const makeClientCrop = useCallback(async (mediaUrl: string, crop: any) => {
    const image = new Image();
    image.src = mediaUrl;

    image.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = crop.width;
      canvas.height = crop.height;

      ctx?.drawImage(image, crop.x, crop.y, crop.width, crop.height, 0, 0, crop.width, crop.height);

      // Compress the cropped image
      setCroppedImageUrl(compressImage(canvas));
    };
  }, [compressImage]);

  return (
    <div className="space-y-6">
      <div className="mb-6">
        <Controller
          name="imageType"
          control={control}
          render={({ field }) => (
            <RadioGroup
              options={[
                { label: 'Take Photo', value: 'camera' },
                { label: 'Upload Photo', value: 'upload' },
              ]}
              value={field.value ?? ''}
              onChange={field.onChange}
            />
          )}
        />
      </div>

      {/* Existing Images Gallery */}
      {existingImages.length > 0 && (
        <div className="mb-6">
          <h3 className="text-sm font-medium mb-2">Uploaded Images</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {existingImages.map((image, index) => (
              <div key={index} className="relative group">
                <div className="aspect-square relative overflow-hidden rounded-md border border-gray-200">
                  <img src={image.url} alt={`Part image ${index + 1}`} className="object-cover w-full h-full" />
                  {image.isMain && (
                    <div className="absolute top-1 left-1 bg-yellow-400 text-white p-1 rounded-full">
                      <Star size={16} />
                    </div>
                  )}
                </div>
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black bg-opacity-50 rounded-md">
                  <button
                    type="button"
                    className="p-1 bg-white rounded-full mr-2"
                    onClick={() => onSetMainImage && onSetMainImage(image.url)}
                    disabled={image.isMain}
                    title={image.isMain ? 'Main image' : 'Set as main image'}
                  >
                    {image.isMain ? <CheckCircle2 size={18} className="text-green-500" /> : <Star size={18} className="text-yellow-500" />}
                  </button>
                  <button
                    type="button"
                    className="p-1 bg-white rounded-full"
                    onClick={() => onRemoveImage && onRemoveImage(image.url)}
                    title="Remove image"
                  >
                    <Trash2 size={18} className="text-red-500" />
                  </button>
                </div>
              </div>
            ))}
          </div>
          <p className="text-sm text-gray-500 mt-2">
            {existingImages.length >= 5
              ? 'Maximum of 5 images reached'
              : `You can upload ${5 - existingImages.length} more image${5 - existingImages.length !== 1 ? 's' : ''}`}
          </p>
        </div>
      )}

      {/* Show upload component only if we haven't reached the maximum image count (5) */}
      {existingImages.length < 5 && !processingQueue && !imageSrc && (
        <div className="border-2 border-dashed border-gray-300 rounded-md p-4">
          <h3 className="text-sm font-medium mb-2">Upload Images</h3>
          <p className="text-sm text-gray-500 mb-4">
            Select up to {5 - existingImages.length} images at once - each will be processed for cropping
          </p>

          <Upload
            onFileAdded={handleFilesAdded}
            onFilesAdded={handleMultipleFilesAdded}
            onFileRemoved={(file) => {}}
            acceptedFileTypes={['image/*']}
            maxFiles={5 - existingImages.length}
            capture={imageType === 'camera' ? 'environment' : undefined}
            showPreview={true}
          />
        </div>
      )}

      {/* Cropping UI */}
      {imageSrc && (
        <div className="mt-4 space-y-4 border-2 border-dashed border-blue-300 rounded-md p-4">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium">Crop Image {currentImageIndex + 1} of {uploadStats.total}</h3>
            <span className="text-xs text-gray-500">
              {uploadStats.processed} of {uploadStats.total} uploaded
            </span>
          </div>

          <ReactCrop
            crop={crop}
            onChange={onChange}
            onComplete={onCropComplete}
            minWidth={50}
            minHeight={50}
          >
            <img
              src={imageSrc}
              onLoad={(e) => onImageLoaded(e.currentTarget)}
              alt="Part to crop"
              className="max-h-96 object-contain"
            />
          </ReactCrop>

          <div className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                const nextIndex = currentImageIndex + 1;
                if (nextIndex < imageQueue.length) {
                  setCurrentImageIndex(nextIndex);
                  setImageSrc(URL.createObjectURL(imageQueue[nextIndex]));
                  setCroppedImageUrl(null);
                } else {
                  setImageQueue([]);
                  setCurrentImageIndex(0);
                  setProcessingQueue(false);
                  setImageSrc(null);
                  setCroppedImageUrl(null);
                }
              }}
              className="px-3 py-2"
              disabled={isUploading}
            >
              Skip
            </Button>

            <Button
              type="button"
              onClick={processCurrentImage}
              className="px-3 py-2"
              disabled={isUploading || isImageUploading}
            >
              {isUploading || isImageUploading ? (
                <AnimatedEllipsisLoader text="Uploading" textColor="white" />
              ) : (
                'Confirm & Continue'
              )}
            </Button>
          </div>

          {uploadStats.processed > 0 && (
            <div className="mt-2">
              <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-blue-600 transition-all duration-300"
                  style={{ width: `${(uploadStats.processed / uploadStats.total) * 100}%` }}
                />
              </div>
            </div>
          )}
        </div>
      )}

      {/* Progress indicator when processing queue but not showing crop UI */}
      {processingQueue && !imageSrc && (
        <div className="mt-4 p-4 border border-gray-200 rounded-md">
          <div className="flex items-center justify-center">
            <AnimatedEllipsisLoader text="Processing images" textColor="gray-700" />
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageUploadSection;