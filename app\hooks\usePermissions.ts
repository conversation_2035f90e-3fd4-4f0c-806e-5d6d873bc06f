'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '@/app/hooks/useAuth';
import { useRBAC } from '@/app/providers/RBACProvider';
import { useUserCookie } from '@/app/hooks/useUserCookie';

interface UsePermissionsResult {
  hasPermission: boolean;
  isLoading: boolean;
  error: Error | null;
}

/**
 * Hook to check if the current user has a specific permission
 * @param permissionName The name of the permission to check (e.g., 'parts:create')
 * @returns Object containing hasPermission, isLoading, and error states
 */
export const usePermissions = (permissionName: string): UsePermissionsResult => {
  const [hasPermission, setHasPermission] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { user } = useAuth();
  const { roleName, isSuperAdmin, isLoading: cookieLoading } = useUserCookie();
  const { checkPermission, getPermissionSync, isLoading: rbacLoading } = useRBAC();

  const checkUserPermission = useCallback(async () => {
    if (!user) {
      setHasPermission(false);
      setIsLoading(false);
      return;
    }

    // Super Admin users automatically have all permissions
    if (isSuperAdmin) {
      console.log(`[usePermissions] Super Admin detected - automatically granting permission: ${permissionName}`);
      setHasPermission(true);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);

      // First check if we have a cached result
      const cachedResult = getPermissionSync(permissionName);

      if (cachedResult !== null) {

        setHasPermission(cachedResult);
        setIsLoading(false);
        return;
      }

      // If not cached, check permission through RBAC context

      const result = await checkPermission(permissionName);

      setHasPermission(result);
    } catch (err) {

      setError(err instanceof Error ? err : new Error(String(err)));
      setHasPermission(false);
    } finally {
      setIsLoading(false);
    }
  }, [user, permissionName, checkPermission, getPermissionSync, isSuperAdmin]);

  useEffect(() => {
    // Super Admin users automatically have all permissions
    if (user && isSuperAdmin) {
      console.log(`[usePermissions] Super Admin detected in useEffect - automatically granting permission: ${permissionName}`);
      setHasPermission(true);
      setIsLoading(false);
      return;
    }

    // Check for cached result first to avoid unnecessary re-renders
    if (user) {
      const cachedResult = getPermissionSync(permissionName);
      if (cachedResult !== null) {

        setHasPermission(cachedResult);
        setIsLoading(false);
      } else {
        checkUserPermission();
      }
    } else {
      setHasPermission(false);
      setIsLoading(false);
    }
  }, [user, permissionName, getPermissionSync, checkUserPermission, isSuperAdmin]);

  // Combine local loading state with RBAC context loading state and cookie loading state
  const combinedIsLoading = isLoading || rbacLoading || cookieLoading;

  return useMemo(
    () => ({
      hasPermission,
      isLoading: combinedIsLoading,
      error,
    }),
    [hasPermission, combinedIsLoading, error]
  );
};

/**
 * Hook to check multiple permissions at once
 * @param permissionNames Array of permission names to check
 * @returns Object where keys are permission names and values are permission check results
 */
export const useMultiplePermissions = (permissionNames: string[]) => {
  const [permissions, setPermissions] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { user } = useAuth();
  const { roleName, isSuperAdmin, isLoading: cookieLoading } = useUserCookie();
  const { checkPermission, getPermissionSync, isLoading: rbacLoading } = useRBAC();

  const checkPermissions = useCallback(async () => {
    if (!user || permissionNames.length === 0) {
      setPermissions({});
      setIsLoading(false);
      return;
    }

    // Super Admin users automatically have all permissions
    if (isSuperAdmin) {
      console.log(`[useMultiplePermissions] Super Admin detected - automatically granting all permissions`);
      const allPermissions = permissionNames.reduce((acc, name) => ({
        ...acc,
        [name]: true
      }), {});
      setPermissions(allPermissions);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);

      // First check cache for all permissions
      const cachedResults: Record<string, boolean> = {};
      const permissionsToCheck: string[] = [];

      permissionNames.forEach(name => {
        const cachedResult = getPermissionSync(name);
        if (cachedResult !== null) {
          cachedResults[name] = cachedResult;
        } else {
          permissionsToCheck.push(name);
        }
      });

      // If all permissions are cached, use those results
      if (permissionsToCheck.length === 0) {

        setPermissions(cachedResults);
        setIsLoading(false);
        return;
      }

      // Check remaining permissions

      const results = await Promise.all(
        permissionsToCheck.map(async (name) => {
          const hasPermission = await checkPermission(name);
          return { name, hasPermission };
        })
      );

      const newPermissions = results.reduce(
        (acc, { name, hasPermission }) => ({
          ...acc,
          [name]: hasPermission,
        }),
        cachedResults // Include cached results
      );

      setPermissions(newPermissions);
    } catch (err) {

      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setIsLoading(false);
    }
  }, [user, permissionNames, checkPermission, getPermissionSync, isSuperAdmin]);

  useEffect(() => {
    checkPermissions();
  }, [checkPermissions]);

  // Combine local loading state with RBAC context loading state and cookie loading state
  const combinedIsLoading = isLoading || rbacLoading || cookieLoading;

  return {
    permissions,
    isLoading: combinedIsLoading,
    error,
    hasAllPermissions: useMemo(
      () =>
        !combinedIsLoading &&
        Object.keys(permissions).length === permissionNames.length &&
        Object.values(permissions).every(Boolean),
      [permissions, combinedIsLoading, permissionNames.length]
    ),
    hasAnyPermission: useMemo(
      () =>
        !combinedIsLoading &&
        Object.values(permissions).some(Boolean),
      [permissions, combinedIsLoading]
    ),
  };
};
