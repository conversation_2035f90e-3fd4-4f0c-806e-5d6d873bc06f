-- Manual Migration: Add created_by column to sales table
-- Run this SQL directly in your Supabase SQL Editor

-- Step 1: Check if the column already exists
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'sales' 
  AND column_name = 'created_by' 
  AND table_schema = 'public';

-- Step 2: Add the created_by column if it doesn't exist
-- (Only run this if the above query returns no results)
ALTER TABLE public.sales 
ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL;

-- Step 3: Add an index for better performance
CREATE INDEX IF NOT EXISTS idx_sales_created_by ON public.sales(created_by);

-- Step 4: Add a comment to document the column
COMMENT ON COLUMN public.sales.created_by IS 'User ID of the staff member who created this sale';

-- Step 5: Verify the column was added
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'sales' 
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- Step 6: Check if there are any existing sales without created_by
SELECT COUNT(*) as sales_without_created_by
FROM public.sales 
WHERE created_by IS NULL;

-- Optional: Update existing sales to set a default created_by value
-- (Replace 'your-user-id-here' with an actual user ID from auth.users)
-- UPDATE public.sales 
-- SET created_by = 'your-user-id-here'
-- WHERE created_by IS NULL;

-- Verification: Check the table structure
\d public.sales;
