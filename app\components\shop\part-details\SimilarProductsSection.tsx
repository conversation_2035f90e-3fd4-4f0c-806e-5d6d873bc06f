'use client';

import React from 'react';
import type { PartDetails, SimilarPart } from '@/app/shop/product/[slug]/page';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/app/components/ui/Button';
import Icon from '@/app/components/ui/Icon';
import { generateProductSlug } from '@/app/utils/slugify';
import { getAdjustedPrice } from '@/app/utils/priceAdjustment';

interface SimilarProductsSectionProps {
  partDetails: PartDetails | null;
}

const SimilarProductsSection: React.FC<SimilarProductsSectionProps> = ({ partDetails }) => {
  if (!partDetails?.similarParts || partDetails.similarParts.length === 0) {
    return null;
  }

  return (
    <div className="container mx-auto px-2 sm:px-4 max-w-full xl:max-w-screen-2xl">
      <div className="bg-white p-3 md:p-4 rounded-lg shadow-sm mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-bold">Related Parts for this Car</h2>
          <Link href={`/shop?category=${partDetails.category}`} className="text-blue-600 hover:text-blue-800 flex items-center">
            View All
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-1">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </Link>
        </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {partDetails.similarParts.map((similarPart: SimilarPart) => (
          <Link href={`/shop/product/${generateProductSlug(similarPart.name, similarPart.id)}`} key={similarPart.id} className="block">
            <div className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
              <div className="relative h-48">
                <Image
                  src={similarPart.imageSrc || '/images/placeholder.jpg'}
                  alt={similarPart.name}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-lg mb-1">{similarPart.name}</h3>
                <div className="text-sm text-gray-500 mb-2">Part #: {similarPart.partNumber}</div>
                <div className="flex justify-between items-center">
                  <span className="font-bold text-lg">Kshs {similarPart.price.toLocaleString()}</span>
                  <div className="flex items-center text-sm text-gray-600">
                    {similarPart.attributes.slice(0, 1).map((attr, idx) => (
                      <span key={idx} className="flex items-center">
                        <span className="mr-1">{attr.value}</span>
                        {attr.label && <span className="text-gray-500">{attr.label}</span>}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
      </div>
    </div>
  );
};

export default SimilarProductsSection;
