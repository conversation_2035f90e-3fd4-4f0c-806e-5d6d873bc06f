-- Create sales table
CREATE TABLE IF NOT EXISTS public.sales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    part_id INTEGER NOT NULL REFERENCES public.parts(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    sale_price DECIMAL(10, 2) NOT NULL CHECK (sale_price >= 0),
    total_amount DECIMAL(10, 2) NOT NULL CHECK (total_amount >= 0),
    customer_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    sale_date DATE NOT NULL,
    payment_method TEXT NOT NULL,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Add RLS policies
ALTER TABLE public.sales ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to view all sales
CREATE POLICY "Authenticated users can view all sales"
ON public.sales
FOR SELECT
USING (auth.role() = 'authenticated');

-- Allow authenticated users to insert sales
CREATE POLICY "Authenticated users can insert sales"
ON public.sales
FOR INSERT
WITH CHECK (auth.role() = 'authenticated');

-- Allow authenticated users to update their own sales
CREATE POLICY "Authenticated users can update their own sales"
ON public.sales
FOR UPDATE
USING (auth.role() = 'authenticated');

-- Allow authenticated users to delete their own sales
CREATE POLICY "Authenticated users can delete their own sales"
ON public.sales
FOR DELETE
USING (auth.role() = 'authenticated');

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_updated_at
BEFORE UPDATE ON public.sales
FOR EACH ROW
EXECUTE FUNCTION public.handle_updated_at();

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS sales_part_id_idx ON public.sales(part_id);
CREATE INDEX IF NOT EXISTS sales_customer_id_idx ON public.sales(customer_id);
CREATE INDEX IF NOT EXISTS sales_sale_date_idx ON public.sales(sale_date);
