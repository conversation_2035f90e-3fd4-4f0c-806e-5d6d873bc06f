/**
 * Title Generator Utility
 * 
 * This utility provides functions to generate standardized titles for parts
 * based on their category, attributes, and other metadata.
 */

import { createClient } from '@/app/libs/supabase/client';

// Define interfaces for the data we'll be working with
interface PartData {
  id?: number;
  title?: string;
  category_id?: number;
  partnumber_group?: number;
  part_number?: string;
  brand_id?: number;
  model_id?: number;
  generation_id?: number;
  variation_id?: number;
  trim_id?: number;
  condition?: string;
  attributes?: Record<string, any>;
  cars?: CarData[];
}

interface CarData {
  brand_name?: string;
  model_name?: string;
  generation_name?: string;
  generation_years?: string;
  variation?: string;
  trim?: string;
}

interface CategoryRule {
  categoryId: number;
  categoryName: string;
  titleFormat: string;
  attributeMapping: Record<string, string>;
}

// Category-specific title generation rules
const categoryRules: CategoryRule[] = [
  {
    categoryId: 7, // Bumpers category ID
    categoryName: 'Bumpers',
    titleFormat: '{brand_name} {model_name} {generation_name} {generation_years} {variation} {condition} {category_name} {with_bumper_lip} {with_bumper_slides}',
    attributeMapping: {
      'bumper_lip': {
        'true': 'with Bumper Lip',
        'false': 'without Bumper Lip',
        'default': ''
      } as any,
      'bumper_slides': {
        'true': 'with Bumper Slides',
        'false': 'without Bumper Slides',
        'default': ''
      } as any
    }
  },
  {
    categoryId: 167, // Fog Covers category ID
    categoryName: 'Fog Covers',
    titleFormat: '{brand_name} {model_name} {generation_name} {generation_years} {variation} {condition} {category_name} {side}',
    attributeMapping: {
      'side': {
        'left': 'Left Side',
        'right': 'Right Side',
        'pair': 'Pair (Left & Right)',
        'default': ''
      } as any
    }
  },
  // Add more category rules as needed
];

// Default title format for categories without specific rules
const defaultTitleFormat = '{brand_name} {model_name} {generation_name} {generation_years} {variation} {condition} {category_name}';

/**
 * Get the appropriate title generation rule for a category
 */
function getCategoryRule(categoryId: number): CategoryRule | null {
  return categoryRules.find(rule => rule.categoryId === categoryId) || null;
}

/**
 * Format a value based on attribute mapping rules
 */
function formatAttributeValue(attributeName: string, value: any, rule: CategoryRule): string {
  if (!rule.attributeMapping[attributeName]) {
    return value?.toString() || '';
  }
  
  const mapping = rule.attributeMapping[attributeName];
  const valueStr = value?.toString().toLowerCase() || '';
  
  return (mapping as any)[valueStr] || (mapping as any)['default'] || '';
}

/**
 * Generate a title for a part based on its category and attributes
 */
export async function generatePartTitle(partData: PartData): Promise<string> {
  try {
    // First, try to get the title template from the database
    const supabase = createClient();
    let titleTemplate = null;

    if (partData.category_id) {
      const { data: categoryData } = await supabase
        .from('car_part_categories')
        .select('title_template')
        .eq('id', partData.category_id)
        .single();

      titleTemplate = categoryData?.title_template;
    }

    // Get category rule for fallback and category name
    const categoryRule = partData.category_id ? getCategoryRule(partData.category_id) : null;

    // If no database template, fall back to hardcoded category rules
    if (!titleTemplate) {
      titleTemplate = categoryRule?.titleFormat || defaultTitleFormat;
    }

    // Get the car data (if not provided)
    let carData = partData.cars?.[0] || {};
    if (!carData.brand_name && partData.brand_id) {
      carData = await fetchCarData(partData);
    }

    // Get the category name (if not provided)
    let categoryName = categoryRule?.categoryName || '';
    if (!categoryName && partData.category_id) {
      categoryName = await fetchCategoryName(partData.category_id);
    }
    
    // Generate compatible part numbers string
    const compatiblePartNumbers = await generateCompatiblePartNumbersString(partData.partnumber_group);

    // Generate compatible vehicles string with DISTINCT logic
    let compatibleVehiclesString = '';
    if (partData.cars && partData.cars.length > 0) {
      // Create detailed unique vehicle strings and remove duplicates
      const uniqueVehicles = [...new Set(partData.cars.map(car => {
        // Build comprehensive vehicle string with all available details
        const parts = [
          car.brand_name || '',
          car.model_name || '',
          car.generation_name || '',
          car.variation || '',
          car.trim || ''
        ].filter(part => part.trim() !== ''); // Remove empty parts

        return parts.join(' ').trim();
      }))].filter(vehicle => vehicle.length > 0); // Remove empty strings

      // Limit to reasonable number for title length (max 8 vehicles)
      const limitedVehicles = uniqueVehicles.slice(0, 8);
      compatibleVehiclesString = limitedVehicles.join(', ');
    }

    // Prepare the data for title generation
    const titleData: Record<string, string> = {
      brand_name: carData.brand_name || '',
      model_name: carData.model_name || '',
      generation_name: carData.generation_name || '',
      generation_years: carData.generation_years || '',
      variation: carData.variation || '',
      trim: carData.trim || '',
      condition: partData.condition || '',
      category_name: categoryName,
      part_number: partData.part_number || '',
      compatible_part_numbers: compatiblePartNumbers,
      compatible_vehicles: compatibleVehiclesString,
      attributes: partData.attributes ? Object.values(partData.attributes).join(' ') : ''
    };
    
    // Add attribute-specific placeholders if we have a category rule
    if (categoryRule && partData.attributes) {
      for (const [attrName, mapping] of Object.entries(categoryRule.attributeMapping)) {
        const attrValue = partData.attributes[attrName];
        titleData[`with_${attrName}`] = formatAttributeValue(attrName, attrValue, categoryRule);
      }
    }
    
    // Replace placeholders in the title template
    let title = titleTemplate;
    for (const [key, value] of Object.entries(titleData)) {
      title = title.replace(new RegExp(`{${key}}`, 'g'), value);
    }
    
    // Clean up the title (remove multiple spaces, trim)
    title = title.replace(/\s+/g, ' ').trim();
    
    return title;
  } catch (error) {
    console.error('Error generating part title:', error);
    // Fall back to a basic title if something goes wrong
    return partData.title || 'Unnamed Part';
  }
}

/**
 * Fetch car data for a part
 */
async function fetchCarData(partData: PartData): Promise<CarData> {
  try {
    const supabase = createClient();
    
    // First, get the brand name
    let brandName = '';
    if (partData.brand_id) {
      const { data: brandData } = await supabase
        .from('car_brands')
        .select('brand_name')
        .eq('brand_id', partData.brand_id)
        .single();
      
      brandName = brandData?.brand_name || '';
    }
    
    // Get model name
    let modelName = '';
    if (partData.model_id) {
      const { data: modelData } = await supabase
        .from('car_models')
        .select('model_name')
        .eq('id', partData.model_id)
        .single();
      
      modelName = modelData?.model_name || '';
    }
    
    // Get generation data
    let generationName = '';
    let generationYears = '';
    if (partData.generation_id) {
      const { data: generationData } = await supabase
        .from('car_generation')
        .select('name, start_production_year, end_production_year')
        .eq('id', partData.generation_id)
        .single();
      
      if (generationData) {
        generationName = generationData.name || '';
        const startYear = generationData.start_production_year;
        const endYear = generationData.end_production_year || 'Present';
        generationYears = `${startYear}-${endYear}`;
      }
    }
    
    // Get variation
    let variation = '';
    if (partData.variation_id) {
      const { data: variationData } = await supabase
        .from('car_variation')
        .select('variation')
        .eq('id', partData.variation_id)
        .single();
      
      variation = variationData?.variation || '';
    }
    
    // Get trim
    let trim = '';
    if (partData.trim_id) {
      const { data: trimData } = await supabase
        .from('variation_trim')
        .select('trim')
        .eq('id', partData.trim_id)
        .single();
      
      trim = trimData?.trim || '';
    }
    
    return {
      brand_name: brandName,
      model_name: modelName,
      generation_name: generationName,
      generation_years: generationYears,
      variation,
      trim
    };
  } catch (error) {
    console.error('Error fetching car data:', error);
    return {};
  }
}

/**
 * Fetch category name
 */
async function fetchCategoryName(categoryId: number): Promise<string> {
  try {
    const supabase = createClient();
    
    const { data } = await supabase
      .from('car_part_categories')
      .select('label')
      .eq('id', categoryId)
      .single();
    
    return data?.label || '';
  } catch (error) {
    console.error('Error fetching category name:', error);
    return '';
  }
}

/**
 * Generate compatible part numbers string
 */
async function generateCompatiblePartNumbersString(partnumberGroup?: number): Promise<string> {
  if (!partnumberGroup) return '';

  try {
    const supabase = createClient();
    const { data, error } = await supabase
      .from('part_to_group')
      .select('partnumber')
      .eq('group_id', partnumberGroup);
      // Removed limit - show ALL compatible part numbers

    if (error || !data) return '';

    // Get unique part numbers and filter out empty ones
    const uniquePartNumbers = [...new Set(data
      .map((item: any) => item.partnumber)
      .filter((pn: string) => pn && pn.trim() !== ''))];

    return uniquePartNumbers.join(', ');
  } catch (error) {
    console.error('Error fetching compatible part numbers:', error);
    return '';
  }
}
