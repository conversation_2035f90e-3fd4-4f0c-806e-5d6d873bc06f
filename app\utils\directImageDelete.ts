import { createClient } from '@/app/libs/supabase/client';

/**
 * Directly deletes an image from Supabase storage using the exact path
 * This function bypasses the path extraction and tries to delete the image using the exact path
 * @param imageUrl Full URL of the image to delete
 * @param bucketName Name of the bucket (default: 'car-part-images')
 */
export async function directImageDelete(
  imageUrl: string,
  bucketName: string = 'car-part-images'
): Promise<{ success: boolean; error?: any; details?: any }> {
  if (!imageUrl) {
    return { success: false, error: 'No image URL provided' };
  }

  const supabase = createClient();
  console.log('Starting direct image deletion for:', imageUrl);

  try {
    // Extract the filename from the URL
    const filename = imageUrl.split('/').pop();
    if (!filename) {
      return { success: false, error: 'Could not extract filename from URL' };
    }
    
    console.log('Extracted filename:', filename);
    
    // List all files in the bucket to find the exact path
    const { data: allFiles, error: listError } = await supabase.storage
      .from(bucketName)
      .list('', { 
        limit: 1000, 
        offset: 0,
        sortBy: { column: 'name', order: 'asc' }
      });
      
    if (listError) {
      console.error('Error listing files:', listError);
      return { success: false, error: listError };
    }
    
    console.log(`Found ${allFiles?.length || 0} files in root directory`);
    
    // Try to find the file by name
    const matchingFiles = allFiles?.filter(file => file.name === filename) || [];
    
    if (matchingFiles.length > 0) {
      console.log('Found matching file in root directory:', matchingFiles[0].name);
      
      // Delete the file
      const { error: deleteError } = await supabase.storage
        .from(bucketName)
        .remove([matchingFiles[0].name]);
        
      if (deleteError) {
        console.error('Error deleting file:', deleteError);
        return { success: false, error: deleteError };
      }
      
      console.log('Successfully deleted file from root directory');
      return { success: true };
    }
    
    // If not found in root, check all subdirectories
    const { data: directories } = await supabase.storage
      .from(bucketName)
      .list('', { 
        limit: 1000, 
        offset: 0,
        sortBy: { column: 'name', order: 'asc' }
      });
      
    const potentialDirs = directories?.filter(item => !item.name.includes('.')) || [];
    console.log(`Found ${potentialDirs.length} potential directories to check`);
    
    // Search in each directory
    for (const dir of potentialDirs) {
      console.log(`Checking directory: ${dir.name}`);
      
      const { data: dirFiles, error: dirError } = await supabase.storage
        .from(bucketName)
        .list(dir.name, { 
          limit: 1000, 
          offset: 0,
          sortBy: { column: 'name', order: 'asc' }
        });
        
      if (dirError) {
        console.error(`Error listing files in directory ${dir.name}:`, dirError);
        continue;
      }
      
      console.log(`Found ${dirFiles?.length || 0} files in directory ${dir.name}`);
      
      // Try to find the file by name
      const matchingDirFiles = dirFiles?.filter(file => file.name === filename) || [];
      
      if (matchingDirFiles.length > 0) {
        console.log(`Found matching file in directory ${dir.name}:`, matchingDirFiles[0].name);
        
        const filePath = `${dir.name}/${matchingDirFiles[0].name}`;
        
        // Delete the file
        const { error: deleteDirError } = await supabase.storage
          .from(bucketName)
          .remove([filePath]);
          
        if (deleteDirError) {
          console.error(`Error deleting file from directory ${dir.name}:`, deleteDirError);
          continue;
        }
        
        console.log(`Successfully deleted file from directory ${dir.name}`);
        return { success: true, details: { path: filePath } };
      }
    }
    
    // Try direct path from URL if extraction is possible
    if (imageUrl.includes('/storage/v1/object/public/')) {
      const pathParts = imageUrl.split('/storage/v1/object/public/')[1].split('/');
      const bucket = pathParts[0];
      const path = pathParts.slice(1).join('/');
      
      console.log('Attempting direct path deletion with:', { bucket, path });
      
      if (bucket === bucketName) {
        const { error: directDeleteError } = await supabase.storage
          .from(bucketName)
          .remove([path]);
          
        if (!directDeleteError) {
          console.log('Successfully deleted file using direct path extraction');
          return { success: true, details: { path } };
        } else {
          console.error('Error deleting with direct path:', directDeleteError);
        }
      }
    }
    
    // Try with the full path from the URL
    if (imageUrl.includes('/v1/object/public/')) {
      const path = imageUrl.split('/v1/object/public/')[1];
      console.log('Trying with full path from URL:', path);
      
      const { error: fullPathError } = await supabase.storage
        .from(bucketName)
        .remove([path]);
        
      if (!fullPathError) {
        console.log('Successfully deleted file using full URL path');
        return { success: true, details: { path } };
      } else {
        console.error('Error deleting with full URL path:', fullPathError);
      }
    }
    
    // Last resort: try direct API call to delete the file
    try {
      // Get the Supabase URL and key from the client
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
      
      if (!supabaseUrl || !supabaseKey) {
        return { success: false, error: 'Supabase URL or key not available' };
      }
      
      // Extract the path from the URL
      let storagePath = '';
      if (imageUrl.includes('/storage/v1/object/public/')) {
        storagePath = imageUrl.split('/storage/v1/object/public/')[1];
      } else if (imageUrl.includes('/v1/object/public/')) {
        storagePath = imageUrl.split('/v1/object/public/')[1];
      }
      
      if (!storagePath) {
        return { success: false, error: 'Could not extract storage path from URL' };
      }
      
      console.log('Making direct API call to delete file with path:', storagePath);
      
      // Make a direct API call to delete the file
      const response = await fetch(
        `${supabaseUrl}/storage/v1/object/${bucketName}/${storagePath}`,
        {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${supabaseKey}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      if (response.ok) {
        console.log('Successfully deleted file using direct API call');
        return { success: true, details: { path: storagePath } };
      } else {
        const errorData = await response.json();
        console.error('Error deleting with direct API call:', errorData);
        return { success: false, error: errorData };
      }
    } catch (apiError) {
      console.error('Error making direct API call:', apiError);
    }
    
    return { 
      success: false, 
      error: 'Could not find or delete the file in any location' 
    };
  } catch (error) {
    console.error('Error in direct image deletion:', error);
    return { success: false, error };
  }
}
