'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { DollarSign, Settings } from 'lucide-react';
import Link from 'next/link';

interface DashboardHeaderProps {
  title: string;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({ title }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white p-6 border-b border-gray-200 mb-6 shadow-sm"
    >
      <div className="container mx-auto flex items-center justify-between">
        <div className="flex items-center">
          <DollarSign size={32} className="text-teal-600 mr-4" />
          <div>
            <h1 className="text-2xl font-bold text-gray-800">{title}</h1>
            <p className="text-gray-600">Manage your sales transactions</p>
          </div>
        </div>

        <Link
          href="/sales/settings"
          className="flex items-center px-4 py-2 text-gray-600 hover:text-teal-600 hover:bg-gray-50 rounded-md transition-colors"
        >
          <Settings size={20} className="mr-2" />
          <span>Settings</span>
        </Link>
      </div>
    </motion.div>
  );
};

export default DashboardHeader;
