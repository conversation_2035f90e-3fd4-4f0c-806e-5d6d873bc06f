'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Tag, Wrench, CheckCircle, Truck, Star, Phone, Mail, Settings, Package, X, Banknote, Car } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import CompatibleCarsCard from './CompatibleCarsCard';

// Icon mapping for specs/features
const specIcons: { [key: string]: React.ElementType } = {
  condition: CheckCircle,
  brand: Tag,
  compatibility: Settings,
  stock: Package,
};

// Spec Item Component - Reusable for key details
interface SpecItemProps {
  icon: React.ElementType;
  label: string;
  value: string | number;
  colorClass?: string;
}

const SpecItem: React.FC<SpecItemProps> = ({ icon: Icon, label, value, colorClass = 'bg-teal-50 text-teal-800' }) => (
  <motion.div
    className={`flex items-center space-x-2 ${colorClass} px-3 py-1.5 rounded-lg text-sm shadow-sm`}
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3, delay: 0.2 }}
  >
    <Icon className="w-4 h-4" />
    <span className="font-medium">{label}:</span>
    <span>{value}</span>
  </motion.div>
);

interface PartDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  partId: string;
}

const PartDetailsModal: React.FC<PartDetailsModalProps> = ({ isOpen, onClose, partId }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [partData, setPartData] = useState<any>(null);
  const [mainImage, setMainImage] = useState('');
  const [showMore, setShowMore] = useState(false);
  // We need to keep these state variables for the existing code to work
  // but we're not displaying the data in the UI anymore
  const [compatiblePartNumbers, setCompatiblePartNumbers] = useState<string[]>([]);
  const [isLoadingCompatibility, setIsLoadingCompatibility] = useState(false);
  const [actualPartNumber, setActualPartNumber] = useState<string>('N/A');
  const [partPrice, setPartPrice] = useState<number>(0);
  const [isLoadingPrice, setIsLoadingPrice] = useState(false);
  const [showImageModal, setShowImageModal] = useState(false);
  const [partLocation, setPartLocation] = useState<string>('No location set');
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [reorderLevel, setReorderLevel] = useState<number>(0);

  useEffect(() => {
    if (isOpen && partId) {
      const fetchPartDetails = async () => {
        setIsLoading(true);
        setError(null);

        try {
          const supabase = createClient();

          // Fetch part details with part_compatibility_groups join
          const { data: part, error: partError } = await supabase
            .from('parts')
            .select(`
              *,
              part_compatibility_groups:partnumber_group(id, part_number)
            `)
            .eq('id', partId)
            .single();

          if (partError) throw partError;

          // Fetch part images
          const { data: images, error: imagesError } = await supabase
            .from('part_images')
            .select('*')
            .eq('part_id', partId);

          if (imagesError) throw imagesError;

          // Fetch part conditions
          const { data: conditions, error: conditionsError } = await supabase
            .from('parts_condition')
            .select('*')
            .eq('part_id', partId);

          if (conditionsError) throw conditionsError;

          // Get reorder level from the first condition
          if (conditions && conditions.length > 0) {
            setReorderLevel(conditions[0].reorder_level || 0);
          }

          // Fetch part location
          setIsLoadingLocation(true);
          let locationStr = 'No location set';
          try {
            // First get the location record
            const { data: locationData, error: locationError } = await supabase
              .from('part_locations')
              .select(`
                location_id,
                unit_id,
                location_subtype,
                details,
                storage_units(
                  unit_id,
                  identifier,
                  area_id,
                  storage_areas(
                    area_id,
                    name
                  )
                )
              `)
              .eq('part_id', partId)
              .maybeSingle();

            if (locationError) throw locationError;

            if (locationData) {
              // Format the location string
              // Handle the storage_units data properly with type checking
              const storageUnit = locationData.storage_units as any;
              const storageArea = storageUnit?.storage_areas as any;
              const areaName = storageArea?.name || 'Unknown Area';
              const unitIdentifier = storageUnit?.identifier || 'Unknown Unit';
              // We're not using locationType, but keep it for reference
              const locationType = locationData.location_subtype?.replace(/_/g, ' ') || 'Unknown Type';

              // Format details based on location subtype
              let detailsStr = '';
              if (locationData.details) {
                const details = locationData.details;

                switch(locationData.location_subtype) {
                  case 'crate':
                    detailsStr = `Level ${details.level || '?'} → Crate ${details.crate_code || '?'}`;
                    break;
                  case 'container':
                    detailsStr = `Level ${details.level || '?'} → ${details.container_code || '?'}`;
                    break;
                  case 'shelf_section':
                  case 'open_shelf':
                    detailsStr = `Level ${details.level || '?'}`;
                    break;
                  case 'cage_section':
                    detailsStr = `Row ${details.row || '?'} → Column ${details.col || '?'}`;
                    break;
                  default:
                    detailsStr = JSON.stringify(details);
                }
              }

              // Construct the full location path
              locationStr = `${areaName} → ${unitIdentifier}${detailsStr ? ' → ' + detailsStr : ''}`;
            }
          } catch (locErr) {
            console.error('Error fetching part location:', locErr);
            locationStr = 'Location unavailable';
          } finally {
            setPartLocation(locationStr);
            setIsLoadingLocation(false);
          }

          // Fetch part prices if conditions exist
          let price = 0;
          if (conditions && conditions.length > 0) {
            setIsLoadingPrice(true);
            try {
              const conditionIds = conditions.map(c => c.id);
              const { data: prices, error: pricesError } = await supabase
                .from('part_price')
                .select('*')
                .in('condition_id', conditionIds);

              if (pricesError) throw pricesError;

              if (prices && prices.length > 0) {
                // Use the main price (not the discounted price)
                const firstPrice = prices[0];
                price = firstPrice.price || 0;
                setPartPrice(price);
              }
            } catch (priceErr) {
              console.error('Error fetching part prices:', priceErr);
            } finally {
              setIsLoadingPrice(false);
            }
          }

          // Calculate total stock
          const totalStock = conditions?.reduce((sum, condition) => sum + (condition.stock || 0), 0) || 0;

          // Get the actual part number from the joined data
          const actualPartNumber = part.part_compatibility_groups?.part_number || 'N/A';
          setActualPartNumber(actualPartNumber);

          // Prepare part data
          const processedPartData = {
            id: part.id,
            name: part.title || 'Unnamed Part',
            partNumber: actualPartNumber,
            partNumberGroupId: part.partnumber_group?.toString() || 'N/A',
            price: price || 0, // Use the price we fetched from part_price table
            condition: conditions?.[0]?.condition || 'Unknown',
            reorderLevel: reorderLevel, // Add reorder level
            location: locationStr, // Use the location string we just created
            brand: 'Autoflow',
            compatibility: 'Check compatibility details',
            stock: totalStock,
            description: part.description || 'No description available',
            specifications: {
              material: 'Not specified',
              diameter: 'Not specified',
              weight: 'Not specified',
              finish: 'Not specified',
            },
            seller: {
              name: 'Autoflow',
              rating: 4.8,
              reviews: 120,
              location: 'Nairobi, Kenya',
              imageUrl: 'https://placehold.co/100x100/E2E8F0/4A5568?text=Seller',
            },
            images: images?.length > 0
              ? images.map(img => img.image_url)
              : ['https://placehold.co/600x400/cccccc/666666?text=No+Image'],
          };

          setPartData(processedPartData);
          if (processedPartData.images.length > 0) {
            setMainImage(processedPartData.images[0]);
          }

          // Fetch compatible part numbers
          if (part.partnumber_group) {
            fetchCompatiblePartNumbers(part.partnumber_group);
          }
        } catch (err) {
          console.error('Error fetching part details:', err);
          setError('Failed to load part details. Please try again later.');
        } finally {
          setIsLoading(false);
        }
      };

      fetchPartDetails();
    }
  }, [isOpen, partId]);

  // Function to fetch compatible part numbers
  const fetchCompatiblePartNumbers = async (partNumberGroupId: number) => {
    setIsLoadingCompatibility(true);
    try {
      const supabase = createClient();

      // First, get all parts that share the same partnumber_group
      const { data: relatedParts, error: relatedPartsError } = await supabase
        .from('parts')
        .select('id, partnumber_group')
        .eq('partnumber_group', partNumberGroupId);

      if (relatedPartsError) throw relatedPartsError;

      // Then, get the part numbers from part_compatibility_groups for these parts
      if (relatedParts && relatedParts.length > 0) {
        // Get unique partnumber_group values without using Set
        const uniqueGroupIds = relatedParts
          .map(p => p.partnumber_group)
          .filter((value, index, self) => value && self.indexOf(value) === index);

        if (uniqueGroupIds.length > 0) {
          const { data: compatibilityGroups, error: compatibilityError } = await supabase
            .from('part_compatibility_groups')
            .select('part_number')
            .in('id', uniqueGroupIds);

          if (compatibilityError) throw compatibilityError;

          if (compatibilityGroups && compatibilityGroups.length > 0) {
            // Filter out the current part number
            const compatibleNumbers = compatibilityGroups
              .map(item => item.part_number)
              .filter(num => num !== actualPartNumber);

            setCompatiblePartNumbers(compatibleNumbers);
          } else {
            setCompatiblePartNumbers([]);
          }
        } else {
          setCompatiblePartNumbers([]);
        }
      } else {
        setCompatiblePartNumbers([]);
      }
    } catch (err) {
      console.error('Error fetching compatible part numbers:', err);
      setCompatiblePartNumbers([]);
    } finally {
      setIsLoadingCompatibility(false);
    }
  };

  // Animation variants for sections
  const sectionVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
      },
    }),
  };

  // Modal animation variants
  const modalVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.3 }
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      transition: { duration: 0.2 }
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 overflow-y-auto"
      onClick={onClose}
    >
      <motion.div
        className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        variants={modalVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close button */}
        <button
          className="absolute top-4 right-4 bg-white rounded-full p-1 shadow-md z-50"
          onClick={onClose}
        >
          <X className="w-6 h-6 text-gray-600" />
        </button>

        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <LoadingSpinner size={40} />
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <h2 className="text-xl font-semibold text-red-600 mb-2">Error</h2>
            <p className="text-gray-700">{error}</p>
          </div>
        ) : partData ? (
          <div className="p-6 md:p-8">
            {/* --- Image Gallery --- */}
            <motion.div
              className="mb-6"
              custom={0}
              initial="hidden"
              animate="visible"
              variants={sectionVariants}
            >
              {/* Main Image */}
              <div className="relative mb-3 rounded-xl overflow-hidden shadow-lg bg-gray-300 h-64 md:h-80">
                <AnimatePresence mode="wait">
                  <motion.img
                    key={mainImage}
                    src={mainImage}
                    alt={`${partData.name} - Main View`}
                    className="w-full h-full object-contain p-2 cursor-pointer"
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.95 }}
                    transition={{ duration: 0.4 }}
                    onClick={() => setShowImageModal(true)}
                    onError={(e) => (e.currentTarget.src = 'https://placehold.co/600x400/E2E8F0/4A5568?text=Image+Error')}
                  />
                </AnimatePresence>
                {/* Image overlay buttons (optional) */}
                <div className="absolute top-4 right-4 flex flex-col space-y-2">
                  {partData.images.length > 1 && (
                    <button className="bg-black bg-opacity-60 text-white p-2 rounded-full hover:bg-opacity-75 transition-colors text-xs font-semibold shadow">
                      +{partData.images.length - 1} more
                    </button>
                  )}
                </div>
                {/* Zoom hint */}
                <div className="absolute bottom-2 right-2">
                  <div className="bg-black bg-opacity-60 text-white p-1.5 rounded-md text-xs flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                    </svg>
                    Click to enlarge
                  </div>
                </div>
              </div>

              {/* Thumbnails */}
              {partData.images.length > 1 && (
                <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-7 gap-2">
                  {partData.images.map((img: string, index: number) => (
                    <motion.div
                      key={index}
                      className={`relative h-16 rounded-md overflow-hidden cursor-pointer border-2 ${
                        mainImage === img ? 'border-orange-600' : 'border-gray-300'
                      } hover:border-orange-400 transition-all bg-white shadow-sm`}
                      onClick={() => setMainImage(img)}
                      whileHover={{ scale: 1.05 }}
                      transition={{ type: 'spring', stiffness: 300 }}
                    >
                      <img
                        src={img}
                        alt={`${partData.name} - View ${index + 1}`}
                        className="w-full h-full object-contain p-1"
                        loading="lazy"
                        onError={(e) => (e.currentTarget.src = 'https://placehold.co/150x100/E2E8F0/4A5568?text=Error')}
                      />
                    </motion.div>
                  ))}
                </div>
              )}
            </motion.div>

            {/* --- Part Info --- */}
            <motion.div
              className="flex flex-col md:flex-row justify-between items-start mb-4 bg-white p-4 rounded-lg shadow"
              custom={1}
              initial="hidden"
              animate="visible"
              variants={sectionVariants}
            >
              <div className="w-full">
                <h1 className="text-sm md:text-base font-bold text-gray-800 mb-2" style={{ fontSize: '12px' }}>{partData.name}</h1>
                <div className="mb-2 text-sm">
                  <div className="flex items-center text-gray-500">
                    <Tag className="w-4 h-4 mr-2 text-teal-700" />
                    <span>Part #: {partData.partNumber}</span>
                  </div>
                </div>

                {/* Icons row for condition, stock, and price */}
                <div className="flex items-center justify-between mt-3 pt-2 border-t border-gray-200 text-sm">
                  {/* Condition */}
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-5 h-5 text-blue-500" />
                    <span>{partData.condition}</span>
                  </div>

                  {/* Stock */}
                  <div className="flex items-center gap-2">
                    <Package className={`w-5 h-5 ${partData.stock > 0 ? "text-green-500" : "text-red-500"}`} />
                    <span>{partData.stock > 0 ? partData.stock : 'Out'}</span>
                  </div>

                  {/* Price */}
                  <div className="flex items-center gap-2">
                    <Banknote className="w-5 h-5 text-orange-500" />
                    <span>{isLoadingPrice ? 'Loading...' : `Kshs. ${typeof partData.price === 'number' ? partData.price.toFixed(2) : '0.00'}`}</span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* --- Location and Car Info Container --- */}
            <motion.div
              className="mb-4 grid grid-cols-1 md:grid-cols-2 gap-4"
              custom={2}
              initial="hidden"
              animate="visible"
              variants={sectionVariants}
            >
              {/* --- Location Info --- */}
              <div className="bg-white p-4 rounded-lg shadow">
                <div className="flex items-center mb-2">
                  <Package className="w-5 h-5 mr-2 text-blue-600" />
                  <h3 className="text-md font-semibold text-gray-700">Storage Location</h3>
                </div>
                <div className="bg-blue-50 p-3 rounded-md text-blue-800">
                  {isLoadingLocation ? (
                    <div className="flex items-center">
                      <div className="animate-spin mr-2 h-4 w-4 border-2 border-blue-500 rounded-full border-t-transparent"></div>
                      <span>Loading location information...</span>
                    </div>
                  ) : (
                    <p className="text-sm">{partData.location}</p>
                  )}
                </div>
              </div>

              {/* --- Compatible Cars Info --- */}
              <div>
                <CompatibleCarsCard partId={partId} />
              </div>
            </motion.div>

            {/* --- Key Specs --- */}
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 mb-6"
              custom={3}
              initial="hidden"
              animate="visible"
              variants={sectionVariants}
            >
              <SpecItem icon={specIcons.condition} label="Reorder Level" value={partData.reorderLevel} colorClass="bg-blue-50 text-blue-800" />
              <SpecItem
                icon={specIcons.compatibility}
                label="Compatibility"
                value={isLoadingCompatibility ? "Loading..." : "View Details"}
                colorClass="bg-yellow-50 text-yellow-800 cursor-pointer hover:bg-yellow-100"
              />
              <SpecItem icon={specIcons.stock} label="Stock" value={partData.stock > 0 ? 'Available' : 'Unavailable'} colorClass={partData.stock > 0 ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'} />
              <SpecItem icon={Wrench} label="Material" value={partData.specifications.material} />
              <SpecItem icon={Wrench} label="Diameter" value={partData.specifications.diameter} />
              <SpecItem icon={Truck} label="Weight" value={partData.specifications.weight} />
            </motion.div>

            {/* --- Description --- */}
            {/* <motion.div
              className="mb-6 bg-white p-4 rounded-lg shadow"
              custom={4}
              initial="hidden"
              animate="visible"
              variants={sectionVariants}
            >
              <h2 className="text-xl font-semibold text-gray-700 mb-2">Part Details</h2>
              <p className="text-gray-600 leading-relaxed text-sm">
                {showMore ? partData.description : `${partData.description.substring(0, 120)}${partData.description.length > 120 ? '...' : ''}`}
              </p>
              {partData.description.length > 120 && (
                <button
                  onClick={() => setShowMore(!showMore)}
                  className="text-orange-600 hover:text-orange-700 font-semibold mt-2 text-sm"
                >
                  {showMore ? 'Read less' : 'Read more'}
                </button>
              )}


            </motion.div> */}

            {/* --- Seller Info & Contact --- */}
            {/* <motion.div
              className="bg-white p-4 rounded-lg shadow flex flex-col md:flex-row items-center justify-between gap-4"
              custom={5}
              initial="hidden"
              animate="visible"
              variants={sectionVariants}
            >
              <div className="flex items-center gap-4">
                <img
                  src={partData.seller.imageUrl}
                  alt={`${partData.seller.name} logo`}
                  className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
                  onError={(e) => (e.currentTarget.src = 'https://placehold.co/100x100/E2E8F0/4A5568?text=Seller')}
                />
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">{partData.seller.name}</h3>
                  <div className="flex items-center text-sm text-gray-500 mt-1">
                    <Star className="w-4 h-4 text-yellow-500 mr-1" fill="currentColor" />
                    <span className="font-semibold mr-1">{partData.seller.rating}</span>
                    ({partData.seller.reviews} reviews)
                  </div>
                  <div className="text-sm text-gray-500 mt-1">{partData.seller.location}</div>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-2 w-full md:w-auto mt-4 md:mt-0">
                <motion.button
                  className="flex items-center justify-center gap-2 w-full sm:w-auto bg-teal-700 hover:bg-teal-800 text-white font-semibold py-2 px-5 rounded-lg shadow transition-colors duration-200"
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Phone className="w-4 h-4" />
                  Call Seller
                </motion.button>
                <motion.button
                  className="flex items-center justify-center gap-2 w-full sm:w-auto bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2 px-5 rounded-lg shadow transition-colors duration-200"
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Mail className="w-4 h-4" />
                  Message
                </motion.button>
              </div>
            </motion.div> */}
          </div>
        ) : null}
      </motion.div>

      {/* Full-screen Image Modal - Overlay on top of the details modal */}
      {showImageModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-90 z-[70] flex items-center justify-center p-4"
          onClick={(e) => {
            e.stopPropagation(); // Prevent closing the details modal
            setShowImageModal(false);
          }}
        >
          <div className="relative w-full max-w-5xl max-h-[90vh] flex items-center justify-center">
            <button
              className="absolute top-4 right-4 bg-white rounded-full p-2 shadow-md z-10"
              onClick={(e) => {
                e.stopPropagation(); // Prevent closing the details modal
                setShowImageModal(false);
              }}
            >
              <X className="w-6 h-6 text-gray-800" />
            </button>
            <img
              src={mainImage}
              alt={partData?.name || 'Part image'}
              className="max-w-full max-h-[85vh] object-contain"
              onClick={(e) => e.stopPropagation()}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default PartDetailsModal;
