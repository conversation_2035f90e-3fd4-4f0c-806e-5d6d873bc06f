{"name": "autoflow", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "generate-icons": "node scripts/generate-icons.js", "generate-screenshots": "node scripts/generate-screenshots.js", "generate-pwa-assets": "npm run generate-icons && npm run generate-screenshots", "mobile:init": "cd mobile && npm install", "mobile:android": "cd mobile && npm run android", "mobile:ios": "cd mobile && npm run ios", "mobile:start": "cd mobile && npm start"}, "dependencies": {"@google/generative-ai": "^0.22.0", "@hookform/resolvers": "^4.0.0", "@mdi/js": "^7.4.47", "@mdi/react": "^1.6.1", "@modelcontextprotocol/server-postgres": "^0.6.2", "@playwright/test": "^1.52.0", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.11", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.48.1", "@types/js-cookie": "^3.0.6", "@types/yup": "^0.29.14", "@upstash/redis": "^1.35.1", "@vercel/postgres": "^0.10.0", "class-variance-authority": "^0.7.1", "cloudinary": "^2.5.1", "clsx": "^2.1.1", "commander": "^13.1.0", "cookies-next": "^5.1.0", "dotenv": "^16.5.0", "framer-motion": "^12.8.0", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.475.0", "next": "15.1.6", "pg": "^8.14.1", "playwright": "^1.52.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-easy-crop": "^5.4.1", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "react-image-crop": "^11.0.7", "react-responsive": "^10.0.0", "recharts": "^2.15.3", "tailwind-merge": "^2.6.0", "ts-node": "^10.9.2", "yup": "^1.6.1", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/lodash": "^4.17.15", "@types/node": "^20.17.27", "@types/react": "^19.0.12", "@types/react-dom": "^19", "@types/react-easy-crop": "^1.16.0", "@types/react-image-crop": "^8.1.6", "@types/react-responsive": "^8.0.8", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "puppeteer": "^21.0.0", "sharp": "^0.32.1", "tailwindcss": "^3.4.1", "typescript": "^5.8.3"}}