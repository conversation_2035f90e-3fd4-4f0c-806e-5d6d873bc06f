import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/server';

/**
 * GET: Get database schema information
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Get tables
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .order('table_name');
      
    if (tablesError) {
      return NextResponse.json(
        { error: 'Failed to get tables', details: tablesError },
        { status: 500 }
      );
    }
    
    // Get columns for each table
    const tableSchemas = await Promise.all(
      tables.map(async (table) => {
        const { data: columns, error: columnsError } = await supabase
          .from('information_schema.columns')
          .select('column_name, data_type, is_nullable')
          .eq('table_schema', 'public')
          .eq('table_name', table.table_name)
          .order('ordinal_position');
          
        if (columnsError) {
          console.error(`<PERSON>rror getting columns for table ${table.table_name}:`, columnsError);
          return {
            table_name: table.table_name,
            columns: [],
            error: columnsError
          };
        }
        
        return {
          table_name: table.table_name,
          columns
        };
      })
    );
    
    return NextResponse.json({
      tables: tableSchemas
    });
  } catch (error) {
    console.error('Error getting database schema:', error);
    return NextResponse.json(
      { error: 'Failed to get database schema', details: error.message },
      { status: 500 }
    );
  }
}
