-- Get the Super Admin role ID
DO $$
DECLARE
    v_super_admin_role_id uuid;
    v_user_id uuid;
BEGIN
    -- Get the Super Admin role ID
    SELECT id INTO v_super_admin_role_id
    FROM public.roles
    WHERE name = 'Super Admin';
    
    -- If Super Admin role doesn't exist, create it
    IF v_super_admin_role_id IS NULL THEN
        INSERT INTO public.roles (name, description)
        VALUES ('Super Admin', 'Has full access to all system features')
        RETURNING id INTO v_super_admin_role_id;
        
        -- Assign all permissions to Super Admin role
        INSERT INTO public.role_permissions (role_id, permission_id)
        SELECT v_super_admin_role_id, id
        FROM public.permissions;
    END IF;
    
    -- Get the first user from the profiles table
    -- You can modify this to target a specific user by email or ID
    SELECT id INTO v_user_id
    FROM public.profiles
    LIMIT 1;
    
    -- If a user exists, assign Super Admin role
    IF v_user_id IS NOT NULL THEN
        -- Check if the user already has the Super Admin role
        IF NOT EXISTS (
            SELECT 1
            FROM public.user_roles
            WHERE user_id = v_user_id AND role_id = v_super_admin_role_id
        ) THEN
            -- Assign Super Admin role to the user
            INSERT INTO public.user_roles (user_id, role_id)
            VALUES (v_user_id, v_super_admin_role_id);
            
            RAISE NOTICE 'Super Admin role assigned to user ID: %', v_user_id;
        ELSE
            RAISE NOTICE 'User ID % already has Super Admin role', v_user_id;
        END IF;
    ELSE
        RAISE NOTICE 'No users found in the profiles table';
    END IF;
END $$;
