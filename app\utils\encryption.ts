// app/libs/encryption.ts

const ENCRYPTION_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_SECRET as string;

async function getKey(secret: string): Promise<CryptoKey> {
  const encoder = new TextEncoder();
  const keyMaterial = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'PBKDF2' },
    false,
    ['deriveBits', 'deriveKey']
  );

  return crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt: encoder.encode('salt'),
      iterations: 100000,
      hash: 'SHA-256'
    },
    keyMaterial,
    { name: 'AES-GCM', length: 256 },
    true,
    ['encrypt', 'decrypt']
  );
}

// Helper function to safely encode to base64
function safeBase64Encode(buffer: Uint8Array): string {
  // Convert Uint8Array to binary string
  let binary = '';
  const len = buffer.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(buffer[i]);
  }

  // Use standard base64 encoding
  try {
    return btoa(binary);
  } catch (e) {
    console.error('Base64 encoding error:', e);
    throw new Error('Failed to encode data to base64');
  }
}

export async function encrypt(text: string): Promise<string> {
  try {
    if (!text) {
      return '';
    }

    const encoder = new TextEncoder();
    const key = await getKey(ENCRYPTION_KEY);
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const encoded = encoder.encode(text);

    const ciphertext = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv: iv },
      key,
      encoded
    );

    const encryptedContent = new Uint8Array(ciphertext);
    const result = new Uint8Array(iv.length + encryptedContent.length);
    result.set(iv);
    result.set(encryptedContent, iv.length);

    // Use our safe base64 encode function
    return safeBase64Encode(result);
  } catch (error) {
    console.error('Encryption failed:', error);
    return '';
  }
}



// Helper function to check if a string is valid base64
function isValidBase64(str: string): boolean {
  // Check if it's a cookie value that's not base64 encoded
  if (str.includes('carFilter=')) {
    return false;
  }

  // Basic check for valid base64 characters (more lenient)
  return /^[A-Za-z0-9+/\-_]*={0,2}$/.test(str);
}

// Helper function to safely decode base64
function safeBase64Decode(str: string): Uint8Array {
  try {
    // Check if the string is empty
    if (!str || str.trim() === '') {
      console.error('Empty string provided for decoding');
      return new Uint8Array(0);
    }

    // Check if it's a cookie value that's not base64 encoded
    if (str.includes('carFilter=')) {
      console.error('String appears to be a carFilter cookie, not base64');
      return new Uint8Array(0);
    }

    // Early check for obviously invalid base64 strings
    if (!/^[A-Za-z0-9+/\-_=]*$/.test(str)) {
      console.error('String contains characters that are not valid in base64');
      return new Uint8Array(0);
    }

    // First, make sure the string is properly padded
    let padded = str;
    while (padded.length % 4 !== 0) {
      padded += '=';
    }

    // Replace URL-safe characters with standard base64 characters
    padded = padded.replace(/-/g, '+').replace(/_/g, '/');

    // Check if the string contains valid base64 characters
    if (!isValidBase64(padded)) {
      // Don't log the full string to avoid console clutter
      console.error('String contains invalid base64 characters');
      return new Uint8Array(0);
    }

    // Try to decode
    try {
      const binaryString = atob(padded);
      return Uint8Array.from(binaryString, c => c.charCodeAt(0));
    } catch (decodeError) {
      console.error('Base64 decoding failed');

      // Try an alternative approach for browsers
      if (typeof window !== 'undefined') {
        try {
          // Use the browser's built-in base64 decoding via data URLs
          const binary = window.atob(padded);
          const bytes = new Uint8Array(binary.length);
          for (let i = 0; i < binary.length; i++) {
            bytes[i] = binary.charCodeAt(i);
          }
          return bytes;
        } catch (browserError) {
          console.error('Browser-based decoding also failed');
        }
      }

      // If all decoding attempts fail, return empty array
      return new Uint8Array(0);
    }
  } catch (e) {
    console.error('Base64 decoding error');
    return new Uint8Array(0);
  }
}

export async function decrypt(encryptedText: string): Promise<string> {
  try {
    if (!ENCRYPTION_KEY) {
      console.error('ENCRYPTION_KEY is not set');
      return '{}';
    }

    if (!encryptedText || encryptedText.trim() === '') {
      console.error('Empty encrypted text');
      return '{}';
    }

    // Check if it's a carFilter cookie (not encrypted)
    if (encryptedText.includes('carFilter=') || encryptedText.startsWith('{')) {
      console.log('Text appears to be unencrypted, returning as is');
      return encryptedText;
    }

    const decoder = new TextDecoder();
    const key = await getKey(ENCRYPTION_KEY);

    // Use our safe base64 decode function - no need for try/catch as it now returns empty array on error
    const encryptedData = safeBase64Decode(encryptedText);

    // Check if we got valid data
    if (encryptedData.length === 0) {
      console.error('Base64 decoding failed, got empty array');
      return '{}';
    }

    if (encryptedData.length < 12) {
      console.error('Invalid encrypted data length:', encryptedData.length);
      return '{}';
    }

    const iv = encryptedData.slice(0, 12);
    const ciphertext = encryptedData.slice(12);

    try {
      const decrypted = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv: iv },
        key,
        ciphertext
      );

      const decryptedText = decoder.decode(decrypted);
      return decryptedText || '{}';
    } catch (decryptError) {
      console.error('Decryption operation failed:', decryptError);
      return '{}';
    }
  } catch (error) {
    console.error('Decryption process failed:', error);
    // Return empty object instead of throwing
    return '{}';
  }
}