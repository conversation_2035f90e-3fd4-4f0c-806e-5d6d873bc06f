// app/utils/apiAuth.ts
import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/app/libs/supabase/server';

/**
 * Utility function to check if a user is authenticated in API routes
 * @param request The NextRequest object
 * @returns An object containing the session, user, and supabase client if authenticated, or null and an error response if not
 */
export async function checkApiAuth(request: NextRequest) {
  try {
    const response = NextResponse.next();
    const supabase = createRouteHandlerClient({ request, response });

    // Get the current user (more secure than getSession)
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error('API Auth Error:', error);
      return {
        authenticated: false,
        session: null,
        user: null,
        supabase,
        errorResponse: NextResponse.json(
          { error: 'Authentication error', details: error.message },
          { status: 401 }
        )
      };
    }
    
    if (!user) {
      return {
        authenticated: false,
        session: null,
        user: null,
        supabase,
        errorResponse: NextResponse.json(
          { error: 'Not authenticated' },
          { status: 401 }
        )
      };
    }

    // Get session for additional data if needed
    const { data: { session } } = await supabase.auth.getSession();

    return {
      authenticated: true,
      session,
      user,
      supabase,
      errorResponse: null
    };
  } catch (error: any) {
    console.error('Unexpected error in API auth check:', error);
    return {
      authenticated: false,
      session: null,
      user: null,
      supabase: null,
      errorResponse: NextResponse.json(
        { error: 'Internal server error', details: error.message },
        { status: 500 }
      )
    };
  }
}

/**
 * Utility function to check if a user has Super Admin role
 * @param request The NextRequest object
 * @returns An object containing the session, user, supabase client, and isSuperAdmin flag if authenticated
 */
export async function checkSuperAdminAuth(request: NextRequest) {
  const authResult = await checkApiAuth(request);
  
  if (!authResult.authenticated) {
    return authResult;
  }
  
  try {
    const { user, supabase } = authResult;
    
    // Get user roles to check if user is a Super Admin
    const { data: userRoles, error: rolesError } = await supabase!
      .from('user_roles')
      .select('role_id, roles(name)')
      .eq('user_id', user!.id);
    
    if (rolesError) {
      console.error('Error fetching user roles:', rolesError);
      return {
        ...authResult,
        isSuperAdmin: false,
        errorResponse: NextResponse.json(
          { error: 'Error checking admin role', details: rolesError.message },
          { status: 500 }
        )
      };
    }
    
    const isSuperAdmin = userRoles?.some((ur: any) => 
      ur?.roles?.name === 'Super Admin'
    ) || false;
    
    if (!isSuperAdmin) {
      return {
        ...authResult,
        isSuperAdmin: false,
        errorResponse: NextResponse.json(
          { error: 'Unauthorized', details: 'Super Admin role required' },
          { status: 403 }
        )
      };
    }
    
    return {
      ...authResult,
      isSuperAdmin: true,
      errorResponse: null
    };
  } catch (error: any) {
    console.error('Error in Super Admin check:', error);
    return {
      ...authResult,
      isSuperAdmin: false,
      errorResponse: NextResponse.json(
        { error: 'Internal server error', details: error.message },
        { status: 500 }
      )
    };
  }
}
