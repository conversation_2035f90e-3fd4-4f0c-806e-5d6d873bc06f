'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/app/hooks/useAuth';
import { useRBAC } from '@/app/providers/RBACProvider';

export default function StandaloneDebugPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const { checkPermission, clearCache } = useRBAC();
  const router = useRouter();

  const fetchDebugInfo = async () => {
    setIsLoading(true);
    try {
      // Check permission directly
      const hasPermission = await checkPermission('admin:access_panel');
      
      // Fetch server-side check
      const response = await fetch('/api/rbac/check-permission');
      const data = await response.json();
      
      setDebugInfo({
        clientSideCheck: hasPermission,
        serverSideCheck: data,
        user: user ? {
          id: user.id,
          email: user.email,
        } : null,
      });
    } catch (err) {
      console.error('Error fetching debug info:', err);
      setError('Failed to fetch debug information');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDebugInfo();
  }, []);

  const handleClearCache = () => {
    clearCache();
    fetchDebugInfo();
  };

  const handleRetryAccess = () => {
    router.push('/admin');
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-10">
        <div className="animate-spin rounded-full border-t-4 border-b-4 h-12 w-12 border-blue-500 border-opacity-25"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="mb-6 text-2xl font-bold">RBAC Debug Page (Standalone)</h1>
      
      {error && (
        <div className="mb-4 rounded-md bg-red-50 p-4 text-red-800">
          <p>{error}</p>
        </div>
      )}
      
      <div className="mb-6 flex space-x-4">
        <button
          onClick={handleClearCache}
          className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
        >
          Clear Permission Cache
        </button>
        <button
          onClick={handleRetryAccess}
          className="rounded-md bg-green-600 px-4 py-2 text-white hover:bg-green-700"
        >
          Retry Admin Access
        </button>
        <button
          onClick={fetchDebugInfo}
          className="rounded-md border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50"
        >
          Refresh Debug Info
        </button>
      </div>
      
      <div className="mb-6">
        <h2 className="mb-2 text-xl font-semibold">Permission Check Results</h2>
        <div className="rounded-md bg-gray-100 p-4">
          <p className="mb-2">
            <span className="font-medium">Client-side check:</span>{' '}
            <span className={debugInfo?.clientSideCheck ? 'text-green-600' : 'text-red-600'}>
              {debugInfo?.clientSideCheck ? 'Granted' : 'Denied'}
            </span>
          </p>
          <p>
            <span className="font-medium">Server-side check:</span>{' '}
            <span className={debugInfo?.serverSideCheck?.hasPermission ? 'text-green-600' : 'text-red-600'}>
              {debugInfo?.serverSideCheck?.hasPermission ? 'Granted' : 'Denied'}
            </span>
          </p>
        </div>
      </div>
      
      <div className="mb-6">
        <h2 className="mb-2 text-xl font-semibold">User Information</h2>
        <div className="rounded-md bg-gray-100 p-4">
          {debugInfo?.user ? (
            <>
              <p className="mb-2">
                <span className="font-medium">User ID:</span> {debugInfo.user.id}
              </p>
              <p>
                <span className="font-medium">Email:</span> {debugInfo.user.email}
              </p>
            </>
          ) : (
            <p className="text-red-600">No user information available</p>
          )}
        </div>
      </div>
      
      <div className="mb-6">
        <h2 className="mb-2 text-xl font-semibold">Assigned Roles</h2>
        <div className="rounded-md bg-gray-100 p-4">
          {debugInfo?.serverSideCheck?.userRoles?.length > 0 ? (
            <ul className="list-inside list-disc">
              {debugInfo.serverSideCheck.userRoles.map((userRole: any, index: number) => (
                <li key={index}>
                  {userRole.roles?.name || 'Unknown Role'}{' '}
                  {userRole.roles?.description && (
                    <span className="text-gray-500">({userRole.roles.description})</span>
                  )}
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-red-600">No roles assigned to this user</p>
          )}
        </div>
      </div>
      
      <div>
        <h2 className="mb-2 text-xl font-semibold">Full Debug Information</h2>
        <div className="max-h-96 overflow-auto rounded-md bg-gray-100 p-4">
          <pre className="text-xs">{JSON.stringify(debugInfo, null, 2)}</pre>
        </div>
      </div>
    </div>
  );
}
