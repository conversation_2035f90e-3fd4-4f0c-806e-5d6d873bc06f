'use client';

import React, { useState } from 'react';
import PropertyHeader from '../ui/PropertyHeader';
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';
import Link from 'next/link';

interface PropertyLayoutProps {
  children: React.ReactNode;
}

const PropertyLayout: React.FC<PropertyLayoutProps> = ({ children }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <PropertyHeader onMenuClick={toggleMobileMenu} />
      
      {/* Mobile Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'tween', duration: 0.3 }}
            className="fixed inset-0 bg-white z-50 pt-4 px-4"
          >
            <div className="flex justify-end mb-8">
              <button
                onClick={toggleMobileMenu}
                className="p-2 rounded-full hover:bg-gray-100"
                aria-label="Close menu"
              >
                <X size={24} />
              </button>
            </div>
            
            <nav className="flex flex-col space-y-6 text-lg">
              <Link 
                href="/new-buildings" 
                className="py-2 border-b border-gray-100"
                onClick={toggleMobileMenu}
              >
                New buildings
              </Link>
              <Link 
                href="/secondary" 
                className="py-2 border-b border-gray-100"
                onClick={toggleMobileMenu}
              >
                Secondary
              </Link>
              <Link 
                href="/commercial" 
                className="py-2 border-b border-gray-100"
                onClick={toggleMobileMenu}
              >
                Commercial
              </Link>
              <Link 
                href="/searches" 
                className="py-2 border-b border-gray-100"
                onClick={toggleMobileMenu}
              >
                Searches
              </Link>
              <Link 
                href="/mortgage" 
                className="py-2 border-b border-gray-100"
                onClick={toggleMobileMenu}
              >
                Mortgage
              </Link>
            </nav>
          </motion.div>
        )}
      </AnimatePresence>
      
      <main className="flex-grow">
        {children}
      </main>
      
      <footer className="bg-white border-t border-gray-200 py-6">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <h2 className="text-lg font-bold">
                house<sup>2</sup>
              </h2>
              <p className="text-sm text-gray-500">Find your perfect home</p>
            </div>
            
            <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-600">
              <Link href="#" className="hover:text-blue-600">Terms of Service</Link>
              <Link href="#" className="hover:text-blue-600">Privacy Policy</Link>
              <Link href="#" className="hover:text-blue-600">Contact Us</Link>
              <Link href="#" className="hover:text-blue-600">About</Link>
            </div>
          </div>
          
          <div className="mt-6 text-center text-sm text-gray-500">
            <p>&copy; {new Date().getFullYear()} House². All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PropertyLayout;
