'use client';

import { useState } from 'react';
import { useBackgroundSync } from '@/app/hooks/useBackgroundSync';
import { toast } from 'react-hot-toast';

interface CartItem {
  id: string;
  quantity: number;
}

interface OfflineCartSyncProps {
  items: CartItem[];
  onSyncComplete?: () => void;
}

export default function OfflineCartSync({ items, onSyncComplete }: OfflineCartSyncProps) {
  const [isSyncing, setIsSyncing] = useState(false);
  const { sync } = useBackgroundSync({
    type: 'cart',
    onSuccess: () => {
      setIsSyncing(false);
      toast.success('Cart changes will be synced when online');
      onSyncComplete?.();
    },
    onError: () => {
      setIsSyncing(false);
    },
  });

  const handleSync = async () => {
    if (!navigator.onLine) {
      setIsSyncing(true);
      await sync(items);
    } else {
      toast.error('You are online. Changes will sync automatically.');
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Offline Cart</h3>
          <p className="mt-1 text-sm text-gray-500">
            {items.length} items waiting to sync
          </p>
        </div>
        <button
          onClick={handleSync}
          disabled={isSyncing || navigator.onLine}
          className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${
            isSyncing || navigator.onLine
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-teal-600 hover:bg-teal-700'
          } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500`}
        >
          {isSyncing ? (
            <>
              <svg
                className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              Syncing...
            </>
          ) : (
            'Queue for Sync'
          )}
        </button>
      </div>
      {items.length > 0 && (
        <div className="mt-4">
          <ul className="divide-y divide-gray-200">
            {items.map((item) => (
              <li key={item.id} className="py-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-900">Item {item.id}</span>
                  <span className="text-sm text-gray-500">Qty: {item.quantity}</span>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
} 