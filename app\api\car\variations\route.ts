import { NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const generationId = searchParams.get('generationId');
    
    if (!generationId) {
      return NextResponse.json({ error: 'generationId is required' }, { status: 400 });
    }
    
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from('car_variation')
      .select('*')
      .eq('generation_id', generationId)
      .order('variation');
      
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    const variations = data.map((v: any) => ({
      id: v.id,
      name: v.variation,
      generation_id: v.generation_id,
      variation: v.variation
    }));
    
    return NextResponse.json(variations);
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}