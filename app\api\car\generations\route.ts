import { NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const modelId = searchParams.get('modelId');
    
    if (!modelId) {
      return NextResponse.json({ error: 'modelId is required' }, { status: 400 });
    }
    
    const supabase = createClient();
    
    const { data, error } = await supabase
      .from('car_generation')
      .select('*')
      .eq('model_id', modelId)
      .order('name');
      
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    const generations = data.map((g: any) => ({
      id: g.id,
      name: g.name,
      model_id: g.model_id,
      start_production_year: g.start_production_year,
      end_production_year: g.end_production_year,
      displayName: `${g.name} ${g.start_production_year}${g.end_production_year ? `-${g.end_production_year}` : ''}`,
      years: `${g.start_production_year}${g.end_production_year ? `-${g.end_production_year}` : ''}`
    }));
    
    return NextResponse.json(generations);
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}