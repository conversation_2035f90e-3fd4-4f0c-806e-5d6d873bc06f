import { createClient } from '@/app/libs/supabase/client';
import { productCache } from '@/app/libs/cache/redis';

export class PartsAPI {
  private supabase = createClient();

  async getPart(id: string) {
    // Try to get from cache first
    const cachedPart = await productCache.get(`part:${id}`);
    if (cachedPart) {
      return cachedPart;
    }

    // If not in cache, fetch from database
    const { data: part, error } = await this.supabase
      .from('parts')
      .select(`
        id,
        title,
        partnumber_group,
        category_id,
        part_images(image_url),
        parts_condition(condition, stock),
        part_price(price, discounted_price),
        parts_category_attribute_values(
          attribute_id,
          value,
          selection_value,
          parts_category_attributes(attribute, input_type)
        )
      `)
      .eq('id', id)
      .single();

    if (error) throw error;

    // Transform the data
    const transformedPart = {
      id: part.id,
      title: part.title,
      partnumber: part.partnumber_group || 'Unknown',
      price: (part.part_price as any)?.price || 0,
      discountedPrice: (part.part_price as any)?.discounted_price,
      stock: (part.parts_condition as any)?.stock || 0,
      description: '', // Add if needed
      category: 'Auto Parts', // Add if needed
      images: part.part_images?.map((img: any) => img.image_url) || ['/images/placeholder.jpg'],
      attributes: this.transformAttributes(part.parts_category_attribute_values),
      compatibleCars: await this.getCompatibleCars(part.id),
      similarParts: [] // Add if needed
    };

    // Cache the result
    await productCache.set(`part:${id}`, transformedPart);

    return transformedPart;
  }

  async getParts(args: any) {
    const {
      category,
      search,
      minPrice,
      maxPrice,
      inStock,
      page = 1,
      limit = 10,
      sortBy = 'title',
      sortOrder = 'asc'
    } = args;

    // Build the query
    let query = this.supabase
      .from('parts')
      .select(`
        id,
        title,
        partnumber_group,
        category_id,
        part_images(image_url),
        parts_condition(condition, stock),
        part_price(price, discounted_price)
      `, { count: 'exact' });

    // Apply filters
    if (category) {
      query = query.eq('category_id', category);
    }

    if (search) {
      query = query.ilike('title', `%${search}%`);
    }

    if (minPrice !== undefined) {
      query = query.gte('part_price.price', minPrice);
    }

    if (maxPrice !== undefined) {
      query = query.lte('part_price.price', maxPrice);
    }

    if (inStock) {
      query = query.gt('parts_condition.stock', 0);
    }

    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    const { data: parts, error, count } = await query;

    if (error) throw error;

    // Transform the data
    return parts.map((part: any) => ({
      id: part.id,
      title: part.title,
      partnumber: part.partnumber_group || 'Unknown',
      price: part.part_price?.price || 0,
      discountedPrice: part.part_price?.discounted_price,
      stock: part.parts_condition?.stock || 0,
      images: part.part_images?.map((img: any) => img.image_url) || ['/images/placeholder.jpg']
    }));
  }

  async getCategories() {
    const { data: categories, error } = await this.supabase
      .from('categories')
      .select('name')
      .order('name');

    if (error) throw error;

    return categories.map((cat: any) => cat.name);
  }

  private transformAttributes(attributes: any[]) {
    if (!attributes) return [];

    return attributes.map(attr => ({
      name: attr.parts_category_attributes?.attribute || 'Attribute',
      icon: this.getIconForAttribute(attr.parts_category_attributes?.attribute),
      value: attr.selection_value || attr.value || 'N/A'
    }));
  }

  private getIconForAttribute(name: string): string {
    const lowerName = (name || '').toLowerCase();
    if (lowerName.includes('material')) return 'package';
    if (lowerName.includes('warranty')) return 'shield';
    if (lowerName.includes('weight')) return 'weight';
    if (lowerName.includes('dimension')) return 'ruler';
    if (lowerName.includes('color')) return 'palette';
    if (lowerName.includes('manufacturer')) return 'factory';
    return 'info';
  }

  private async getCompatibleCars(partId: string | number) {
    try {
      // First, get all variation_trim_ids associated with this part
      const { data: partCars, error: partCarsError } = await this.supabase
        .from('parts_car')
        .select('variation_trim_id')
        .eq('part_id', partId);

      if (partCarsError || !partCars || partCars.length === 0) {
        return [];
      }

      // Get all variation_trim_ids
      const variationTrimIds = partCars.map(pc => pc.variation_trim_id);

      // Fetch all car details using a SQL query instead of nested joins
      const { data: carDetails, error: carDetailsError } = await this.supabase
        .rpc('get_compatible_cars_for_part', { trim_ids: variationTrimIds });

      if (carDetailsError || !carDetails || carDetails.length === 0) {
        return [];
      }

      // Transform the data into the format expected by the UI
      return carDetails.map((car: any) => {
        // Format the year range
        const startYear = car.start_production_year || '';
        const endYear = car.end_production_year || '';
        const yearRange = endYear ? `${startYear}-${endYear}` : `${startYear}+`;

        return {
          id: car.id.toString(),
          brand: car.brand_name || 'Unknown',
          model: `${car.model_name || 'Unknown'} ${car.generation_name || ''} ${car.variation || ''}`,
          year: yearRange,
          trim: car.trim_name || ''
        };
      });
    } catch (error) {
      console.error('Error fetching compatible cars:', error);
      return [];
    }
  }
}