import { NextRequest, NextResponse } from 'next/server';
import { querySTKStatus, getTransactionByCheckoutRequestId } from '@/app/libs/mpesa/service';
import { createRouteHandlerClient } from '@/app/libs/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // Create a Supabase client for API route
    const nextResponse = NextResponse.next();
    const supabase = createRouteHandlerClient({ request, response: nextResponse });

    // Check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized - Please log in to continue' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { checkoutRequestId } = body;

    // Validate required fields
    if (!checkoutRequestId) {
      return NextResponse.json(
        { success: false, message: 'Checkout request ID is required' },
        { status: 400 }
      );
    }

    // First, check if we already have the transaction in our database
    const transaction = await getTransactionByCheckoutRequestId(checkoutRequestId);

    if (transaction) {
      // If the transaction is already completed or failed, return the status
      if (transaction.status === 'completed') {
        return NextResponse.json({
          success: true,
          message: 'Payment completed successfully',
          data: {
            status: 'completed',
            mpesaReceiptNumber: transaction.mpesa_receipt_number,
            transactionDate: transaction.updated_at,
            phoneNumber: transaction.phone_number,
            amount: transaction.amount,
          },
        });
      }

      if (transaction.status === 'failed') {
        return NextResponse.json({
          success: false,
          message: 'Payment failed',
          data: {
            status: 'failed',
            resultCode: transaction.result_code,
            resultDescription: transaction.result_description,
          },
        });
      }
    }

    // Query STK status from M-PESA
    const response = await querySTKStatus(checkoutRequestId);

    if (!response.success) {
      return NextResponse.json(
        { success: false, message: response.message, error: response.error },
        { status: 500 }
      );
    }

    // Process the response
    const { data } = response;

    // If the query was successful, return the status
    if (data?.ResultCode === '0') {
      return NextResponse.json({
        success: true,
        message: 'Payment completed successfully',
        data: {
          status: 'completed',
          resultCode: data.ResultCode,
          resultDescription: data.ResultDesc,
        },
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'Payment is still pending or has failed',
        data: {
          status: data?.ResultCode === null ? 'pending' : 'failed',
          resultCode: data?.ResultCode,
          resultDescription: data?.ResultDesc,
        },
      });
    }
  } catch (error) {
    console.error('Error querying M-PESA payment status:', error);
    return NextResponse.json(
      {
        success: false,
        message: `Error querying M-PESA payment status: ${error instanceof Error ? error.message : 'Unknown error'}`
      },
      { status: 500 }
    );
  }
}
