'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Plus, Search, RefreshCw, Edit, Trash2, Tag, Layers, Settings } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { Trim, Variation, Generation, Model, Brand } from '../types';
import AddTrimModal from './modals/AddTrimModal';
import EditTrimModal from './modals/EditTrimModal';
import DeleteConfirmModal from './modals/DeleteConfirmModal';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

interface TrimsTabProps {
  onTrimUpdated: () => void;
}

const TrimsTab: React.FC<TrimsTabProps> = ({ onTrimUpdated }) => {
  const [trims, setTrims] = useState<Trim[]>([]);
  const [filteredTrims, setFilteredTrims] = useState<Trim[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [generations, setGenerations] = useState<Generation[]>([]);
  const [variations, setVariations] = useState<Variation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedBrandId, setSelectedBrandId] = useState<number | ''>('');
  const [selectedModelId, setSelectedModelId] = useState<number | ''>('');
  const [selectedGenerationId, setSelectedGenerationId] = useState<number | ''>('');
  const [selectedVariationId, setSelectedVariationId] = useState<number | ''>('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedTrim, setSelectedTrim] = useState<Trim | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const supabase = createClient();

  // Fetch brands from Supabase
  useEffect(() => {
    const fetchBrands = async () => {
      try {
        const { data, error } = await supabase
          .from('car_brands')
          .select('*')
          .order('brand_name');

        if (error) throw error;

        setBrands(data || []);
      } catch (error) {
        console.error('Error fetching brands:', error);
      }
    };

    fetchBrands();
  }, [supabase]);

  // Fetch models based on selected brand
  useEffect(() => {
    const fetchModels = async () => {
      try {
        let query = supabase
          .from('car_models')
          .select('*')
          .order('model_name');
          
        if (selectedBrandId) {
          query = query.eq('brand_id', selectedBrandId);
        }
        
        const { data, error } = await query;

        if (error) throw error;

        setModels(data || []);
        
        // Reset selected model if it doesn't belong to the selected brand
        if (selectedBrandId && selectedModelId) {
          const modelExists = data?.some(model => model.id === selectedModelId);
          if (!modelExists) {
            setSelectedModelId('');
            setSelectedGenerationId('');
            setSelectedVariationId('');
          }
        }
      } catch (error) {
        console.error('Error fetching models:', error);
      }
    };

    fetchModels();
  }, [refreshTrigger, selectedBrandId, supabase]);

  // Fetch generations based on selected model
  useEffect(() => {
    const fetchGenerations = async () => {
      try {
        let query = supabase
          .from('car_generation')
          .select('*')
          .order('name');
          
        if (selectedModelId) {
          query = query.eq('model_id', selectedModelId);
        }
        
        const { data, error } = await query;

        if (error) throw error;

        setGenerations(data || []);
        
        // Reset selected generation if it doesn't belong to the selected model
        if (selectedModelId && selectedGenerationId) {
          const generationExists = data?.some(generation => generation.id === selectedGenerationId);
          if (!generationExists) {
            setSelectedGenerationId('');
            setSelectedVariationId('');
          }
        }
      } catch (error) {
        console.error('Error fetching generations:', error);
      }
    };

    fetchGenerations();
  }, [refreshTrigger, selectedModelId, supabase]);

  // Fetch variations based on selected generation
  useEffect(() => {
    const fetchVariations = async () => {
      try {
        let query = supabase
          .from('car_variation')
          .select('*')
          .order('variation');
          
        if (selectedGenerationId) {
          query = query.eq('generation_id', selectedGenerationId);
        }
        
        const { data, error } = await query;

        if (error) throw error;

        setVariations(data || []);
        
        // Reset selected variation if it doesn't belong to the selected generation
        if (selectedGenerationId && selectedVariationId) {
          const variationExists = data?.some(variation => variation.id === selectedVariationId);
          if (!variationExists) {
            setSelectedVariationId('');
          }
        }
      } catch (error) {
        console.error('Error fetching variations:', error);
      }
    };

    fetchVariations();
  }, [refreshTrigger, selectedGenerationId, supabase]);

  // Fetch trims from Supabase
  useEffect(() => {
    const fetchTrims = async () => {
      setIsLoading(true);
      try {
        let query = supabase
          .from('variation_trim')
          .select('*')
          .order('trim');
          
        if (selectedVariationId) {
          query = query.eq('variation_id', selectedVariationId);
        }
        
        const { data, error } = await query;

        if (error) throw error;

        setTrims(data || []);
        setFilteredTrims(data || []);
      } catch (error) {
        console.error('Error fetching trims:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTrims();
  }, [refreshTrigger, selectedVariationId, supabase]);

  // Filter trims based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredTrims(trims);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = trims.filter(trim => 
      trim.trim.toLowerCase().includes(query)
    );
    setFilteredTrims(filtered);
  }, [searchQuery, trims]);

  // Refresh trims
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Get variation name by ID
  const getVariationName = (variationId: number) => {
    const variation = variations.find(v => v.id === variationId);
    return variation ? variation.variation : 'Unknown Variation';
  };

  // Get generation name by variation ID
  const getGenerationNameByVariationId = (variationId: number) => {
    const variation = variations.find(v => v.id === variationId);
    if (!variation) return 'Unknown Generation';
    
    const generation = generations.find(g => g.id === variation.generation_id);
    return generation ? generation.name : 'Unknown Generation';
  };

  // Get model name by variation ID
  const getModelNameByVariationId = (variationId: number) => {
    const variation = variations.find(v => v.id === variationId);
    if (!variation) return 'Unknown Model';
    
    const generation = generations.find(g => g.id === variation.generation_id);
    if (!generation) return 'Unknown Model';
    
    const model = models.find(m => m.id === generation.model_id);
    return model ? model.model_name : 'Unknown Model';
  };

  // Get brand name by variation ID
  const getBrandNameByVariationId = (variationId: number) => {
    const variation = variations.find(v => v.id === variationId);
    if (!variation) return 'Unknown Brand';
    
    const generation = generations.find(g => g.id === variation.generation_id);
    if (!generation) return 'Unknown Brand';
    
    const model = models.find(m => m.id === generation.model_id);
    if (!model) return 'Unknown Brand';
    
    const brand = brands.find(b => b.brand_id === model.brand_id);
    return brand ? brand.brand_name : 'Unknown Brand';
  };

  // Open edit modal
  const handleEdit = (trim: Trim) => {
    setSelectedTrim(trim);
    setIsEditModalOpen(true);
  };

  // Open delete modal
  const handleDelete = (trim: Trim) => {
    setSelectedTrim(trim);
    setIsDeleteModalOpen(true);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.3,
        ease: "easeOut"
      }
    },
    hover: {
      y: -5,
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      transition: { 
        duration: 0.2
      }
    }
  };

  return (
    <div>
      {/* Search and Actions */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
        <div className="flex flex-col md:flex-row gap-4 w-full md:w-auto">
          <div className="relative w-full md:w-64">
            <input
              type="text"
              placeholder="Search trims..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
          </div>
          
          <div className="w-full md:w-48">
            <select
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
              value={selectedBrandId}
              onChange={(e) => {
                setSelectedBrandId(e.target.value ? Number(e.target.value) : '');
                setSelectedModelId('');
                setSelectedGenerationId('');
                setSelectedVariationId('');
              }}
            >
              <option value="">All Brands</option>
              {brands.map((brand) => (
                <option key={brand.brand_id} value={brand.brand_id}>
                  {brand.brand_name}
                </option>
              ))}
            </select>
          </div>
          
          <div className="w-full md:w-48">
            <select
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
              value={selectedModelId}
              onChange={(e) => {
                setSelectedModelId(e.target.value ? Number(e.target.value) : '');
                setSelectedGenerationId('');
                setSelectedVariationId('');
              }}
              disabled={!selectedBrandId}
            >
              <option value="">All Models</option>
              {models.map((model) => (
                <option key={model.id} value={model.id}>
                  {model.model_name}
                </option>
              ))}
            </select>
          </div>
          
          <div className="w-full md:w-48">
            <select
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
              value={selectedGenerationId}
              onChange={(e) => {
                setSelectedGenerationId(e.target.value ? Number(e.target.value) : '');
                setSelectedVariationId('');
              }}
              disabled={!selectedModelId}
            >
              <option value="">All Generations</option>
              {generations.map((generation) => (
                <option key={generation.id} value={generation.id}>
                  {generation.name}
                </option>
              ))}
            </select>
          </div>
          
          <div className="w-full md:w-48">
            <select
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
              value={selectedVariationId}
              onChange={(e) => setSelectedVariationId(e.target.value ? Number(e.target.value) : '')}
              disabled={!selectedGenerationId}
            >
              <option value="">All Variations</option>
              {variations.map((variation) => (
                <option key={variation.id} value={variation.id}>
                  {variation.variation}
                </option>
              ))}
            </select>
          </div>
        </div>
        
        <div className="flex gap-2 w-full md:w-auto">
          <button
            onClick={handleRefresh}
            className="flex items-center gap-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
          >
            <RefreshCw size={18} />
            <span>Refresh</span>
          </button>
          
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <Plus size={18} />
            <span>Add Trim</span>
          </button>
        </div>
      </div>
      
      {/* Trims Grid */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size={40} />
        </div>
      ) : filteredTrims.length > 0 ? (
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {filteredTrims.map((trim) => (
            <motion.div
              key={trim.id}
              className="bg-white rounded-lg shadow-md p-6"
              variants={itemVariants}
              whileHover="hover"
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-xl font-semibold text-gray-800">{trim.trim}</h3>
                  <div className="flex items-center mt-1">
                    <Tag size={16} className="text-teal-600 mr-2" />
                    <span className="text-sm text-gray-600">
                      {getBrandNameByVariationId(trim.variation_id)} {getModelNameByVariationId(trim.variation_id)}
                    </span>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleEdit(trim)}
                    className="p-2 rounded-md hover:bg-gray-100 text-gray-500 hover:text-teal-600 transition-colors"
                    aria-label="Edit trim"
                  >
                    <Edit size={18} />
                  </button>
                  <button
                    onClick={() => handleDelete(trim)}
                    className="p-2 rounded-md hover:bg-gray-100 text-gray-500 hover:text-red-600 transition-colors"
                    aria-label="Delete trim"
                  >
                    <Trash2 size={18} />
                  </button>
                </div>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg mb-4">
                <div className="flex items-center mb-2">
                  <Layers size={18} className="text-orange-500 mr-2" />
                  <span className="text-gray-700">
                    Generation: {getGenerationNameByVariationId(trim.variation_id)}
                  </span>
                </div>
                <div className="flex items-center">
                  <Settings size={18} className="text-orange-500 mr-2" />
                  <span className="text-gray-700">
                    Variation: {getVariationName(trim.variation_id)}
                  </span>
                </div>
              </div>
              
              <div className="mt-4 pt-4 border-t border-gray-100">
                <p className="text-sm text-gray-500">Trim ID: {trim.id}</p>
              </div>
            </motion.div>
          ))}
        </motion.div>
      ) : (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <h3 className="text-xl font-semibold text-gray-700 mb-2">No trims found</h3>
          <p className="text-gray-500 mb-4">
            {searchQuery || selectedVariationId ? 'No trims match your search criteria.' : 'Start by adding a new trim.'}
          </p>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <Plus size={18} />
            <span>Add Trim</span>
          </button>
        </div>
      )}
      
      {/* Add Trim Modal */}
      <AddTrimModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        variations={variations}
        generations={generations}
        models={models}
        brands={brands}
        onSuccess={() => {
          handleRefresh();
          onTrimUpdated();
        }}
      />
      
      {/* Edit Trim Modal */}
      {selectedTrim && (
        <EditTrimModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          trim={selectedTrim}
          variations={variations}
          generations={generations}
          models={models}
          brands={brands}
          onSuccess={() => {
            handleRefresh();
            onTrimUpdated();
          }}
        />
      )}
      
      {/* Delete Confirmation Modal */}
      {selectedTrim && (
        <DeleteConfirmModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          itemId={selectedTrim.id}
          itemName={selectedTrim.trim}
          itemType="trim"
          tableName="variation_trim"
          idField="id"
          onSuccess={() => {
            handleRefresh();
            onTrimUpdated();
          }}
        />
      )}
    </div>
  );
};

export default TrimsTab;
