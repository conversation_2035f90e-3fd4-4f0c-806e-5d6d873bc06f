import { NestedSelect, NestedSelectItem } from '@/app/components/ui/inputs/NestedSelect';
import LoadingLabel from '@/app/components/ui/inputs/LoadingLabel';

interface CategorySelectionProps {
  categories: NestedSelectItem[];
  selectedCategory: string;
  handleCategoryChange: (value: string) => void;
  selectedCategoryName: string;
  isLoading: boolean;
}

const CategorySelection = ({
  categories,
  selectedCategory,
  handleCategoryChange,
  selectedCategoryName,
  isLoading
}: CategorySelectionProps) => {
  return (
    <div className="space-y-4">
      <label className="block text-sm font-medium mb-2">Category</label>
      <div className="relative">
        {isLoading ? (
          <div className="p-2 border rounded-md bg-gray-50">
            <LoadingLabel text="Loading categories" />
          </div>
        ) : (
          <NestedSelect
            items={categories}
            placeholder="Select a category"
            onValueChange={handleCategoryChange}
            value={selectedCategory}
            disabled={isLoading}
          />
        )}
      </div>
      {selectedCategoryName && !isLoading && (
        <p className="text-sm text-gray-600">
          Selected: {selectedCategoryName}
        </p>
      )}
    </div>
  );
};

export default CategorySelection;