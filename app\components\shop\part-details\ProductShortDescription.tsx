'use client';

import React from 'react';
import type { PartDetails } from '@/app/shop/product/[slug]/page';

interface ProductShortDescriptionProps {
  partDetails: PartDetails | null;
}

const ProductShortDescription: React.FC<ProductShortDescriptionProps> = ({ partDetails }) => {
  if (!partDetails || !partDetails.description) {
    return (
      <div className="border border-gray-200 rounded-md p-4 mb-4 bg-gray-50">
        <p className="text-gray-500 text-center italic">Product description here...</p>
      </div>
    );
  }

  return (
    <div className="border border-gray-200 rounded-md p-4 mb-4 bg-gray-50">
      <p className="text-gray-700 text-sm">
        {partDetails.description.length > 200
          ? `${partDetails.description.substring(0, 200)}...`
          : partDetails.description}
      </p>
    </div>
  );
};

export default ProductShortDescription;
