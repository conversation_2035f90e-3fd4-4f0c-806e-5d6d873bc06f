'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { createClient } from '@/app/libs/supabase/client';
import { Search, Filter, Edit, Trash2, Eye, ChevronLeft, ChevronRight, Plus, FileText } from 'lucide-react';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import toast from 'react-hot-toast';
import EditSaleModal from './modals/EditSaleModal';
import ViewSaleModal from './modals/ViewSaleModal';
import AddSaleModal from './modals/AddSaleModal';
import SetupSalesModule from './SetupSalesModule';
import { generatePDFReceipt } from '../utils/pdfGenerator';

interface SalesListProps {
  refreshTrigger: number;
  onEdit: () => void;
  onDelete: () => void;
}

const SalesList: React.FC<SalesListProps> = ({ refreshTrigger, onEdit, onDelete }) => {
  const [sales, setSales] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    saleType: '',
    dateFrom: '',
    dateTo: '',
  });
  const [viewSale, setViewSale] = useState<{ isOpen: boolean; saleId: string | null }>({
    isOpen: false,
    saleId: null,
  });
  const [editSale, setEditSale] = useState<{ isOpen: boolean; saleId: string | null }>({
    isOpen: false,
    saleId: null,
  });
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isSalesModuleSetup, setIsSalesModuleSetup] = useState(true);

  const itemsPerPage = 10;

  useEffect(() => {
    fetchSales();
  }, [refreshTrigger, currentPage, filters]);

  const fetchSales = async () => {
    setIsLoading(true);
    try {
      const supabase = createClient();

      // Check if we can access the sales table
      try {
        // Simply try to count the records - if the table doesn't exist, this will fail
        const { count, error: countError } = await supabase
          .from('sales')
          .select('*', { count: 'exact', head: true });

        if (countError) {
          console.error('Error accessing sales table:', JSON.stringify(countError, null, 2));

          // Check if the error is because the table doesn't exist
          if (countError.code === '42P01') { // PostgreSQL code for undefined_table
            setIsSalesModuleSetup(false);
            throw new Error('Sales table does not exist in the database. The sales module may not be fully set up.');
          } else {
            throw new Error(`Could not access sales data: ${countError.message}`);
          }
        }

        console.log('Sales table exists with', count, 'records');
      } catch (tableError) {
        console.error('Error accessing sales table:', JSON.stringify(tableError, null, 2));

        if (tableError instanceof Error) {
          throw tableError; // Re-throw the error with the message we already set
        } else {
          throw new Error('Could not access sales data. The sales module may not be fully set up.');
        }
      }

      console.log('Fetching sales data...');

      // Start building the query
      let query = supabase
        .from('sales')
        .select(`
          *,
          clients(id, name, phone_number, client_type),
          sale_items(
            id,
            part_id,
            quantity,
            price_at_sale,
            discount_amount,
            discount_reason,
            parts:part_id(id, title)
          )
        `, { count: 'exact' });

      // Apply filters
      if (filters.saleType) {
        query = query.eq('sale_type', filters.saleType);
      }

      if (filters.dateFrom) {
        query = query.gte('sale_timestamp', new Date(filters.dateFrom).toISOString());
      }

      if (filters.dateTo) {
        // Add one day to include the end date fully
        const endDate = new Date(filters.dateTo);
        endDate.setDate(endDate.getDate() + 1);
        query = query.lt('sale_timestamp', endDate.toISOString());
      }

      // Apply search if provided
      if (searchTerm) {
        query = query.or(`one_off_client_name.ilike.%${searchTerm}%,one_off_client_phone.ilike.%${searchTerm}%,clients.name.ilike.%${searchTerm}%,clients.phone_number.ilike.%${searchTerm}%`);
      }

      // Apply pagination
      const from = (currentPage - 1) * itemsPerPage;
      const to = from + itemsPerPage - 1;

      // Execute the query
      const { data, error, count } = await query
        .order('sale_timestamp', { ascending: false })
        .range(from, to);

      if (error) {
        console.error('Supabase query error:', JSON.stringify(error, null, 2));
        throw new Error(`Failed to fetch sales data: ${error.message || 'Unknown error'}`);
      }

      console.log('Sales data fetched successfully:', {
        count: count,
        dataLength: data?.length || 0
      });

      setSales(data || []);
      if (count !== null) {
        setTotalPages(Math.ceil(count / itemsPerPage));
      }
    } catch (error) {
      console.error('Error fetching sales:', JSON.stringify(error, null, 2));

      // Show a more specific error message
      if (error instanceof Error) {
        toast.error(`Failed to load sales data: ${error.message}`);
      } else {
        toast.error('Failed to load sales data');
      }

      // Set empty sales data to prevent UI issues
      setSales([]);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1); // Reset to first page on new search
    fetchSales();
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  const handleDeleteSale = async (saleId: string) => {
    if (!confirm('Are you sure you want to delete this sale? This action cannot be undone.')) {
      return;
    }

    try {
      const supabase = createClient();

      // Delete the sale (cascade will handle related records)
      const { error } = await supabase
        .from('sales')
        .delete()
        .eq('id', saleId);

      if (error) {
        console.error('Error deleting sale:', JSON.stringify(error, null, 2));
        throw new Error(`Failed to delete sale: ${error.message || 'Unknown error'}`);
      }

      toast.success('Sale deleted successfully');
      onDelete(); // Trigger refresh
    } catch (error) {
      console.error('Error deleting sale:', JSON.stringify(error, null, 2));

      if (error instanceof Error) {
        toast.error(`Failed to delete sale: ${error.message}`);
      } else {
        toast.error('Failed to delete sale');
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getClientName = (sale: any) => {
    if (sale.client_id && sale.clients) {
      return sale.clients.name;
    }
    return sale.one_off_client_name || 'One-off Customer';
  };

  const getSaleTypeLabel = (saleType: string) => {
    return saleType === 'cash' ? 'Cash' : 'Credit';
  };

  const getPaymentMethodLabel = (sale: any) => {
    if (sale.sale_type === 'credit') {
      return 'Credit';
    }
    return sale.cash_payment_method === 'cash' ? 'Cash' : 'MPESA';
  };

  const handleGeneratePDF = async (saleId: string) => {
    try {
      // Fetch the complete sale data for PDF generation
      const supabase = createClient();

      // First, try with the created_by relationship
      let { data: saleData, error } = await supabase
        .from('sales')
        .select(`
          *,
          clients:client_id(id, name, phone_number),
          profiles:created_by(id, full_name),
          sale_items(
            id,
            quantity,
            price_at_sale,
            discount_amount,
            discount_reason,
            parts:part_id(id, title)
          ),
          mpesa_payments(mpesa_confirmation_message)
        `)
        .eq('id', saleId)
        .single();

      // If the created_by relationship doesn't exist, try without it
      if (error && error.message.includes('Could not find a relationship between \'sales\' and \'created_by\'')) {
        console.log('created_by column not found, fetching without staff information...');
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('sales')
          .select(`
            *,
            clients:client_id(id, name, phone_number),
            sale_items(
              id,
              quantity,
              price_at_sale,
              discount_amount,
              discount_reason,
              parts:part_id(id, title)
            ),
            mpesa_payments(mpesa_confirmation_message)
          `)
          .eq('id', saleId)
          .single();

        if (fallbackError) {
          throw new Error(`Failed to fetch sale data: ${fallbackError.message}`);
        }

        saleData = fallbackData;
        toast.success('PDF generated successfully! Note: Run the database migration to include staff details in future receipts.');
      } else if (error) {
        throw new Error(`Failed to fetch sale data: ${error.message}`);
      }

      if (!saleData) {
        throw new Error('Sale not found');
      }

      await generatePDFReceipt(saleData);
      toast.success('PDF receipt generated successfully');
    } catch (error) {
      console.error('Error generating PDF:', error);
      if (error instanceof Error) {
        toast.error(`Failed to generate PDF: ${error.message}`);
      } else {
        toast.error('Failed to generate PDF receipt');
      }
    }
  };

  // If the sales module is not set up, show the setup component
  if (!isSalesModuleSetup) {
    return <SetupSalesModule onSetupComplete={() => {
      setIsSalesModuleSetup(true);
      fetchSales();
    }} />;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex flex-col md:flex-row justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4 md:mb-0">Recent Sales</h2>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row w-full md:w-auto gap-3">
          <form onSubmit={handleSearch} className="flex w-full md:w-auto">
            <div className="relative flex-grow">
              <input
                type="text"
                placeholder="Search by client name or phone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              />
              <button
                type="submit"
                className="absolute right-0 top-0 h-full px-3 bg-gray-100 border-l border-gray-300 rounded-r-md text-gray-600 hover:bg-gray-200"
              >
                <Search className="w-4 h-4" />
              </button>
            </div>
          </form>

          <div className="flex space-x-2">
            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </button>

            <button
              onClick={() => setIsAddModalOpen(true)}
              className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 flex items-center"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Sale
            </button>
          </div>
        </div>
      </div>

      {/* Filter Panel */}
      {isFilterOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="bg-gray-50 p-4 rounded-md mb-6"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Sale Type</label>
              <select
                name="saleType"
                value={filters.saleType}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              >
                <option value="">All Types</option>
                <option value="cash">Cash</option>
                <option value="credit">Credit</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">From Date</label>
              <input
                type="date"
                name="dateFrom"
                value={filters.dateFrom}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">To Date</label>
              <input
                type="date"
                name="dateTo"
                value={filters.dateTo}
                onChange={handleFilterChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
              />
            </div>
          </div>
          <div className="flex justify-end mt-4">
            <button
              onClick={() => {
                setFilters({ saleType: '', dateFrom: '', dateTo: '' });
                setCurrentPage(1);
              }}
              className="px-4 py-2 text-gray-700 mr-2"
            >
              Reset
            </button>
            <button
              onClick={() => {
                setCurrentPage(1);
                fetchSales();
              }}
              className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
            >
              Apply Filters
            </button>
          </div>
        </motion.div>
      )}

      {/* Sales Table */}
      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <LoadingSpinner size={24} />
        </div>
      ) : sales.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 mb-4">No sales found matching your criteria.</p>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="inline-flex items-center px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
          >
            <Plus className="w-4 h-4 mr-2" /> Add Your First Sale
          </button>
        </div>
      ) : (
        <>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Client
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Payment
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Items
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Discount
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sales.map((sale) => (
                  <tr key={sale.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(sale.sale_timestamp)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {getClientName(sale)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {getSaleTypeLabel(sale.sale_type)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {getPaymentMethodLabel(sale)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {sale.sale_items?.length || 0}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      Kshs {parseFloat(sale.total_amount).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {parseFloat(sale.total_discount) > 0 ?
                        `Kshs ${parseFloat(sale.total_discount).toLocaleString()}` :
                        '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleGeneratePDF(sale.id)}
                        className="text-orange-600 hover:text-orange-900 mr-3"
                        title="Generate PDF Receipt"
                      >
                        <FileText className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => setViewSale({ isOpen: true, saleId: sale.id })}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                        title="View Sale"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => setEditSale({ isOpen: true, saleId: sale.id })}
                        className="text-teal-600 hover:text-teal-900 mr-3"
                        title="Edit Sale"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteSale(sale.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete Sale"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-between items-center mt-6">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeft className="w-5 h-5" />
              </button>
              <span className="text-sm text-gray-700">
                Page {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
          )}
        </>
      )}

      {/* View Sale Modal */}
      {viewSale.saleId && (
        <ViewSaleModal
          isOpen={viewSale.isOpen}
          onClose={() => setViewSale({ isOpen: false, saleId: null })}
          saleId={viewSale.saleId}
        />
      )}

      {/* Edit Sale Modal */}
      {editSale.saleId && (
        <EditSaleModal
          isOpen={editSale.isOpen}
          onClose={() => setEditSale({ isOpen: false, saleId: null })}
          saleId={editSale.saleId}
          onSaleUpdated={onEdit}
        />
      )}

      {/* Add Sale Modal */}
      <AddSaleModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSaleAdded={onEdit}
      />
    </div>
  );
};

export default SalesList;
