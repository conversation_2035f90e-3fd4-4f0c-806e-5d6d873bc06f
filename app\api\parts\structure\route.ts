import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Get a sample part to examine its structure
    const { data: samplePart, error: sampleError } = await supabase
      .from('parts')
      .select('*')
      .limit(1)
      .single();
    
    if (sampleError) {
      console.error('Error getting sample part:', sampleError);
      return NextResponse.json({
        success: false,
        error: sampleError.message
      }, { status: 500 });
    }
    
    // Get part_images related to this part
    const { data: partImages, error: imagesError } = await supabase
      .from('part_images')
      .select('*')
      .eq('part_id', samplePart.id)
      .limit(5);
    
    // Check if we can get part_compatibility_groups
    const { data: compatibilityGroups, error: compatibilityError } = await supabase
      .from('part_compatibility_groups')
      .select('*')
      .limit(1);
    
    return NextResponse.json({
      success: true,
      partStructure: {
        samplePart,
        partImages,
        compatibilityGroups
      },
      tableColumns: {
        parts: Object.keys(samplePart || {}),
        partImages: partImages && partImages.length > 0 ? Object.keys(partImages[0]) : [],
        compatibilityGroups: compatibilityGroups && compatibilityGroups.length > 0 ? Object.keys(compatibilityGroups[0]) : []
      }
    });
  } catch (error) {
    console.error('Unexpected error in structure API:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
