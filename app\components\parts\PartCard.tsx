'use client';

import { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { createClient } from '@/app/libs/supabase/client';
import { MoreVertical, Trash2, X, Edit3, ImageIcon, Tag, Package, CheckCircle, Banknote } from 'lucide-react';
import DirectImage from '@/app/components/ui/DirectImage';
import ManageImagesModal from './ManageImagesModal';
import PartDetailsModal from './PartDetailsModal';
import UpdateDetailsModal from './UpdateDetailsModal/index';
import { useForm } from 'react-hook-form';
import NumberInput from '@/app/components/ui/inputs/NumberInput';
import { getAdjustedPrice } from '@/app/utils/priceAdjustment';

export interface PartCondition {
  id: string;
  condition: string;
  stock: number;
}

export interface Part {
  id: string;
  title: string;
  partNumber: string;
  actualPartNumber?: string;
  stock: number;
  price?: number;
  discountedPrice?: number;
  conditions?: PartCondition[];
  thumbnailUrl: string;
  imageUrl: string;
  images?: Array<{ image_url: string; is_main_image?: boolean }>;
  userId?: string;
}

interface PartCardProps {
  part: Part;
  onDelete?: (partId: string) => void;
}

export const PartCard: React.FC<PartCardProps> = ({ part: initialPart, onDelete }) => {
  // Use state to store the part data so we can update it
  const [part, setPart] = useState<Part>(initialPart);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isManageImagesModalOpen, setIsManageImagesModalOpen] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showStockModal, setShowStockModal] = useState(false);
  const [isUpdatingStock, setIsUpdatingStock] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [modalImageIndex, setModalImageIndex] = useState(0);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showUpdateDetailsModal, setShowUpdateDetailsModal] = useState(false);
  const [partPrice, setPartPrice] = useState<number | null>(null);
  const [isLoadingPrice, setIsLoadingPrice] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const supabase = createClient();

  // Prepare array of images from part data
  const partImages = useMemo(() => {
    if (part.images && part.images.length > 0) {
      return part.images;
    } else if (part.imageUrl) {
      // If no images array but there's an imageUrl, create a single-item array
      return [{ image_url: part.imageUrl, is_main_image: true }];
    }
    return [];
  }, [part.images, part.imageUrl]);

  const hasMultipleImages = partImages.length > 1;

  // Touch handling for swipe gestures
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = useCallback(() => {
    if (!hasMultipleImages) return;

    const swipeThreshold = 50;
    if (touchStart - touchEnd > swipeThreshold) {
      // Swipe left - go to next image
      setCurrentImageIndex((prev) => (prev + 1) % partImages.length);
    } else if (touchEnd - touchStart > swipeThreshold) {
      // Swipe right - go to previous image
      setCurrentImageIndex((prev) => (prev - 1 + partImages.length) % partImages.length);
    }
  }, [hasMultipleImages, touchStart, touchEnd, partImages.length]);

  // Touch handling for modal
  const [modalTouchStart, setModalTouchStart] = useState(0);
  const [modalTouchEnd, setModalTouchEnd] = useState(0);

  const handleModalTouchStart = (e: React.TouchEvent) => {
    setModalTouchStart(e.targetTouches[0].clientX);
  };

  const handleModalTouchMove = (e: React.TouchEvent) => {
    setModalTouchEnd(e.targetTouches[0].clientX);
  };

  const handleModalTouchEnd = useCallback(() => {
    if (!hasMultipleImages) return;

    const swipeThreshold = 50;
    if (modalTouchStart - modalTouchEnd > swipeThreshold) {
      // Swipe left - go to next image
      setModalImageIndex((prev) => (prev + 1) % partImages.length);
    } else if (modalTouchEnd - modalTouchStart > swipeThreshold) {
      // Swipe right - go to previous image
      setModalImageIndex((prev) => (prev - 1 + partImages.length) % partImages.length);
    }
  }, [hasMultipleImages, modalTouchStart, modalTouchEnd, partImages.length]);

  const stockForm = useForm<Record<string, number>>({
    defaultValues: {
      // Initialize with current stock values for each condition
      ...part.conditions?.reduce((acc, condition) => ({
        ...acc,
        [condition.id]: condition.stock
      }), {} as Record<string, number>)
    }
  });

  const updateStock = async (data: Record<string, number>) => {
    setIsUpdatingStock(true);
    try {
      const response = await fetch('/api/parts/updateStock', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          partId: part.id,
          conditionStocks: Object.entries(data).map(([conditionId, stock]) => ({
            conditionId,
            stock
          }))
        }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error('Update stock error:', responseData);
        throw new Error(responseData.error || 'Failed to update stock');
      }

      // Refresh just this part card
      refreshPartData();

      setShowStockModal(false);
    } catch (error: any) {
      console.error('Error updating stock:', error);
      alert(`Failed to update stock: ${error.message || 'Please try again.'}`);
    } finally {
      setIsUpdatingStock(false);
    }
  };

  // Function to create a new condition record if none exists
  const createCondition = async (condition: string) => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('parts_condition')
        .insert({
          part_id: part.id,
          condition: condition,
          stock: 0
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating condition:', error);
      throw error;
    }
  };

  const getImageUrl = (path: string) => {
    if (path.startsWith('http')) {
      return path;
    }

    if (part.userId && path.includes(part.userId)) {
      return supabase.storage.from('car-part-images').getPublicUrl(path).data.publicUrl;
    }

    const storagePath = part.userId
      ? `${part.userId}/${path.split('/').pop()}`
      : path;

    return supabase.storage.from('car-part-images').getPublicUrl(storagePath).data.publicUrl;
  };

  // Function to navigate to the next image
  const nextImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentImageIndex((prev) => (prev + 1) % partImages.length);
  };

  // Function to navigate to the previous image
  const prevImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentImageIndex((prev) => (prev - 1 + partImages.length) % partImages.length);
  };

  // Functions for modal image navigation
  const nextModalImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setModalImageIndex((prev) => (prev + 1) % partImages.length);
  };

  const prevModalImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setModalImageIndex((prev) => (prev - 1 + partImages.length) % partImages.length);
  };

  // Function to open the image modal directly (not used currently)
  // const openImageModal = (e: React.MouseEvent) => {
  //   e.stopPropagation();
  //   setModalImageIndex(currentImageIndex);
  //   setIsModalOpen(true);
  // };

  const openDetailsModal = () => {
    setShowDetailsModal(true);
  };

  // Handle swipe gestures for image carousel
  const handleDragEnd = useCallback((e: any, info: any) => {
    if (!hasMultipleImages) return;

    if (info.offset.x > 50) {
      prevImage(e);
    } else if (info.offset.x < -50) {
      nextImage(e);
    }
  }, [hasMultipleImages, partImages.length]);

  const handleModalDragEnd = useCallback((e: any, info: any) => {
    if (!hasMultipleImages) return;

    if (info.offset.x > 50) {
      prevModalImage(e);
    } else if (info.offset.x < -50) {
      nextModalImage(e);
    }
  }, [hasMultipleImages, partImages.length]);

  // Function to close the image modal
  const closeModal = () => setIsModalOpen(false);

  const toggleMenu = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowMenu(!showMenu);
  };

  const handleClickOutside = (e: MouseEvent) => {
    if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
      setShowMenu(false);
    }
  };

  // Add event listener when menu is shown
  useEffect(() => {
    if (showMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showMenu]);

  // Function to refresh a single part
  const refreshPartData = useCallback(async () => {
    if (!part.id) return;

    setIsRefreshing(true);
    try {
      // Fetch updated part data
      const { data: updatedPart, error: partError } = await supabase
        .from('parts')
        .select(`
          *,
          part_compatibility_groups:partnumber_group(id, part_number)
        `)
        .eq('id', part.id)
        .single();

      if (partError) throw partError;

      // Fetch part images
      const { data: images, error: imagesError } = await supabase
        .from('part_images')
        .select('*')
        .eq('part_id', part.id);

      if (imagesError) throw imagesError;

      // Fetch part conditions
      const { data: conditions, error: conditionsError } = await supabase
        .from('parts_condition')
        .select('*')
        .eq('part_id', part.id);

      if (conditionsError) throw conditionsError;

      // Calculate total stock
      const totalStock = conditions?.reduce((sum, condition) => sum + (condition.stock || 0), 0) || 0;

      // Prepare the updated part object
      const processedPart: Part = {
        id: updatedPart.id,
        title: updatedPart.title || 'Unnamed Part',
        partNumber: updatedPart.partnumber_group?.toString() || 'N/A',
        actualPartNumber: updatedPart.part_compatibility_groups?.part_number || 'N/A',
        stock: totalStock,
        thumbnailUrl: images?.[0]?.image_url || '/placeholder.jpg',
        imageUrl: images?.[0]?.image_url || '/placeholder.jpg',
        images: images || [],
        conditions: conditions || [],
        userId: updatedPart.user_id
      };

      // Update the part state
      setPart(processedPart);

      // Reset image error if we have new images
      if (images && images.length > 0) {
        setImageError(false);
        setCurrentImageIndex(0);
      }

      // Fetch price for the updated part
      fetchPartPrice(processedPart);

    } catch (err) {
      console.error('Error refreshing part data:', err);
    } finally {
      setIsRefreshing(false);
    }
  }, [part.id, supabase]);

  // Fetch part price
  const fetchPartPrice = useCallback(async (targetPart = part) => {
    if (!targetPart.id || !targetPart.conditions || targetPart.conditions.length === 0) return;

    setIsLoadingPrice(true);
    try {
      const conditionIds = targetPart.conditions.map(c => c.id);
      const { data: prices, error: pricesError } = await supabase
        .from('part_price')
        .select('*')
        .in('condition_id', conditionIds);

      if (pricesError) throw pricesError;

      if (prices && prices.length > 0) {
        // Use the main price (not the discounted price)
        const firstPrice = prices[0];
        const price = firstPrice.price || 0;
        setPartPrice(price);

        // Update the part object with the price
        setPart(prev => ({
          ...prev,
          price: price
        }));
      }
    } catch (priceErr) {
      console.error('Error fetching part prices:', priceErr);
    } finally {
      setIsLoadingPrice(false);
    }
  }, [supabase]);

  // Fetch part price on initial load
  useEffect(() => {
    fetchPartPrice();
  }, [fetchPartPrice]);

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowMenu(false);
    setShowDeleteConfirm(true);
  };

  // Function to handle stock modal (not used anymore)
  // const handleUpdateStockClick = (e: React.MouseEvent) => {
  //   e.stopPropagation();
  //   setShowMenu(false);
  //   setShowStockModal(true);
  // };

  const cancelDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDeleteConfirm(false);
  };

  const confirmDelete = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsDeleting(true);

    try {
      const response = await fetch('/api/parts/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          partId: part.id,
          imagePath: part.imageUrl,
          userId: part.userId
        }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error('Delete part error:', responseData);
        throw new Error(responseData.error || 'Failed to delete part');
      }

      // Check if the image was deleted successfully
      if (responseData.details && !responseData.details.imageDeleteSuccess) {
        console.warn('Part was deleted but image deletion failed:', responseData.details.imageDeleteError);
        // Continue with part deletion even if image deletion failed
      }

      // Call the onDelete callback if provided
      if (onDelete) {
        onDelete(part.id);
      }

      setShowDeleteConfirm(false);
    } catch (error: any) {
      console.error('Error deleting part:', error);
      alert(`Failed to delete part: ${error.message || 'Please try again.'}`);
    } finally {
      setIsDeleting(false);
    }
  };

  const thumbnailPublicUrl = partImages[currentImageIndex]
    ? getImageUrl(partImages[currentImageIndex].image_url)
    : getImageUrl(part.thumbnailUrl);

  const imagePublicUrl = partImages[modalImageIndex]
    ? getImageUrl(partImages[modalImageIndex].image_url)
    : getImageUrl(part.imageUrl);

  return (
    <>
      <motion.div
        className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 flex flex-col h-full relative"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        whileHover={{ scale: 1.02 }}
      >
        {/* Menu Button */}
        <div
          className="absolute top-2 right-2 z-10"
          onClick={(e) => e.stopPropagation()}
        >
          <button
            className="p-1 rounded-full bg-white bg-opacity-80 hover:bg-opacity-100 shadow-sm"
            onClick={toggleMenu}
          >
            <MoreVertical size={18} className="text-gray-700" />
          </button>

          {/* Dropdown Menu */}
          {showMenu && (
            <div
              ref={menuRef}
              className="absolute right-0 mt-1 w-36 bg-white rounded-md shadow-lg z-20 py-1"
            >
              <button
                className="w-full text-left px-4 py-2 text-sm text-blue-600 hover:bg-blue-50 flex items-center"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowMenu(false);
                  setShowUpdateDetailsModal(true);
                }}
              >
                <Edit3 size={16} className="mr-2" />
                Update Details
              </button>
              <button
                className="w-full text-left px-4 py-2 text-sm text-blue-600 hover:bg-blue-50 flex items-center"
                onClick={() => {
                  setShowMenu(false);
                  setIsManageImagesModalOpen(true);
                }}
              >
                <ImageIcon size={16} className="mr-2" />
                Manage Images
              </button>
              <button
                className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center"
                onClick={handleDeleteClick}
              >
                <Trash2 size={16} className="mr-2" />
                Delete Part
              </button>
            </div>
          )}
        </div>

        <div
          className="relative h-48 cursor-pointer"
          onClick={openDetailsModal}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {imageError ? (
            <div className="w-full h-full flex items-center justify-center bg-gray-100">
              <span className="text-gray-400">Image not available</span>
            </div>
          ) : (
            <motion.div
              className="w-full h-full relative"
              drag={hasMultipleImages ? "x" : false}
              dragConstraints={{ left: 0, right: 0 }}
              onDragEnd={handleDragEnd}
            >
              <DirectImage
                src={thumbnailPublicUrl}
                alt={part.title}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                onError={() => setImageError(true)}
              />

              {/* Image navigation buttons for multiple images */}
              {hasMultipleImages && (
                <>
                  <button
                    className="absolute left-1 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-70 rounded-full p-1 shadow-sm z-10 hover:bg-opacity-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      prevImage(e);
                    }}
                    aria-label="Previous image"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                  <button
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-70 rounded-full p-1 shadow-sm z-10 hover:bg-opacity-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      nextImage(e);
                    }}
                    aria-label="Next image"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </>
              )}

              {/* Image indicators */}
              {hasMultipleImages && (
                <div className="absolute bottom-1 left-0 right-0 flex justify-center space-x-1">
                  {partImages.map((_, index) => (
                    <span
                      key={index}
                      className={`h-1.5 rounded-full ${index === currentImageIndex ? 'bg-white w-3' : 'bg-white bg-opacity-50 w-1.5'}`}
                    />
                  ))}
                </div>
              )}
            </motion.div>
          )}
        </div>
        <div className="p-4 flex-grow flex flex-col" onClick={openDetailsModal}>
          <h3 className="text-sm font-semibold mb-2 line-clamp-2 text-gray-900" style={{ fontSize: '12px' }}>{part.title}</h3>

          {/* Part number */}
          <div className="mb-2">
            <p className="text-gray-600 text-xs">
              Part #: {part.actualPartNumber || part.partNumber}
            </p>
          </div>

          {/* Icons row for condition, stock, and price */}
          <div className="mt-auto border-t pt-2">
            <div className="flex items-center justify-between text-xs text-gray-600">
              {/* Condition */}
              <div className="flex items-center gap-1">
                <CheckCircle size={16} className="text-blue-500" />
                <span>{part.conditions && part.conditions.length > 0 ? part.conditions[0]?.condition || 'Unknown' : 'Unknown'}</span>
              </div>

              {/* Stock */}
              <div className="flex items-center gap-1">
                <Package size={16} className={part.stock > 0 ? "text-green-500" : "text-red-500"} />
                <span>{part.stock > 0 ? part.stock : 'Out'}</span>
              </div>

              {/* Price */}
              <div className="flex items-center gap-1">
                <Banknote size={16} className="text-orange-500" />
                <span>{isLoadingPrice ? 'Loading...' : (part.price ? `Kshs. ${getAdjustedPrice(part.price).toLocaleString()}` : 'Contact')}</span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={cancelDelete}
        >
          <div
            className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
            onClick={(e) => e.stopPropagation()}
          >
            <h3 className="text-xl font-semibold mb-4">Confirm Deletion</h3>
            <p className="mb-6 text-gray-700">
              Are you sure you want to delete <span className="font-semibold">{part.title}</span>? This action is irreversible and will permanently remove this part and all associated data.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                className="px-4 py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50"
                onClick={cancelDelete}
                disabled={isDeleting}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 rounded-md bg-red-600 text-white hover:bg-red-700 flex items-center"
                onClick={confirmDelete}
                disabled={isDeleting}
              >
                {isDeleting ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Update Stock Modal */}
      {showStockModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={(e) => {
            e.stopPropagation();
            setShowStockModal(false);
          }}
        >
          <div
            className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold">Update Stock</h3>
              <button
                className="p-1 rounded-full hover:bg-gray-100"
                onClick={() => setShowStockModal(false)}
              >
                <X size={18} />
              </button>
            </div>

            <p className="mb-4 text-gray-700">
              Update stock quantities for <span className="font-semibold">{part.title}</span>
            </p>

            <form onSubmit={stockForm.handleSubmit(updateStock)} className="space-y-4">
              {part.conditions && part.conditions.length > 0 ? (
                part.conditions.map(condition => (
                  <div key={condition.id} className="mb-4">
                    <NumberInput
                      name={String(condition.id)}
                      control={stockForm.control}
                      label={`${condition.condition} Condition Stock`}
                      className="w-full"
                    />
                  </div>
                ))
              ) : (
                <div>
                  <p className="text-gray-500 mb-4">No conditions available for this part. Please select a condition to add:</p>
                  <div className="space-y-4">
                    <div className="mb-4">
                      <button
                        type="button"
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 w-full mb-2"
                        onClick={async () => {
                          try {
                            setIsUpdatingStock(true);
                            const newCondition = await createCondition('New');
                            // Update the form with the new condition
                            stockForm.setValue(String(newCondition.id), 0);
                            // Update the part object to include the new condition
                            part.conditions = [...(part.conditions || []), newCondition];
                            setIsUpdatingStock(false);
                          } catch (error) {
                            console.error('Error creating New condition:', error);
                            alert('Failed to create New condition. Please try again.');
                            setIsUpdatingStock(false);
                          }
                        }}
                        disabled={isUpdatingStock}
                      >
                        Add New Condition
                      </button>
                      <button
                        type="button"
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 w-full"
                        onClick={async () => {
                          try {
                            setIsUpdatingStock(true);
                            const newCondition = await createCondition('Used');
                            // Update the form with the new condition
                            stockForm.setValue(String(newCondition.id), 0);
                            // Update the part object to include the new condition
                            part.conditions = [...(part.conditions || []), newCondition];
                            setIsUpdatingStock(false);
                          } catch (error) {
                            console.error('Error creating Used condition:', error);
                            alert('Failed to create Used condition. Please try again.');
                            setIsUpdatingStock(false);
                          }
                        }}
                        disabled={isUpdatingStock}
                      >
                        Add Used Condition
                      </button>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  className="px-4 py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50"
                  onClick={() => setShowStockModal(false)}
                  disabled={isUpdatingStock}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700"
                  disabled={isUpdatingStock}
                >
                  {isUpdatingStock ? 'Updating...' : 'Update Stock'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Manage Images Modal */}
      <ManageImagesModal
        isOpen={isManageImagesModalOpen}
        onClose={() => setIsManageImagesModalOpen(false)}
        partId={part.id}
        partImages={part.images || []}
        onImagesUpdated={() => {
          // Refresh just this part card
          refreshPartData();
        }}
      />

      {/* Part Details Modal */}
      <PartDetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        partId={part.id.toString()}
      />

      {/* Update Details Modal */}
      <UpdateDetailsModal
        isOpen={showUpdateDetailsModal}
        onClose={() => setShowUpdateDetailsModal(false)}
        partId={part.id.toString()}
        partTitle={part.title}
        partNumber={part.actualPartNumber || part.partNumber}
        conditions={part.conditions || []}
        onDetailsUpdated={() => {
          // Refresh just this part card
          refreshPartData();
        }}
      />

      {/* Modal for full image view */}
      {isModalOpen && partImages.length > 0 && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
          onClick={closeModal}
          onTouchStart={handleModalTouchStart}
          onTouchMove={handleModalTouchMove}
          onTouchEnd={handleModalTouchEnd}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="relative max-w-4xl max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              className="absolute top-4 right-4 bg-white rounded-full p-2 shadow-md z-10"
              onClick={closeModal}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            <div className="relative w-full h-[80vh]">
              {imageError ? (
                <div className="w-full h-full flex items-center justify-center bg-gray-100">
                  <span className="text-gray-400 text-xl">Full image not available</span>
                </div>
              ) : (
                <motion.div
                  className="w-full h-full relative"
                  drag={hasMultipleImages ? "x" : false}
                  dragConstraints={{ left: 0, right: 0 }}
                  onDragEnd={handleModalDragEnd}
                >
                  <DirectImage
                    src={imagePublicUrl}
                    alt={part.title}
                    fill
                    className="object-contain"
                    sizes="90vw"
                    onError={() => setImageError(true)}
                  />

                  {/* Image navigation buttons for multiple images in modal */}
                  {hasMultipleImages && (
                    <>
                      <button
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-70 rounded-full p-2 shadow-sm z-10 hover:bg-opacity-100"
                        onClick={prevModalImage}
                        aria-label="Previous image"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                        </svg>
                      </button>
                      <button
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-70 rounded-full p-2 shadow-sm z-10 hover:bg-opacity-100"
                        onClick={nextModalImage}
                        aria-label="Next image"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </button>
                    </>
                  )}

                  {/* Image indicators in modal */}
                  {hasMultipleImages && (
                    <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
                      {partImages.map((_, index) => (
                        <span
                          key={index}
                          className={`h-2 rounded-full ${index === modalImageIndex ? 'bg-white w-4' : 'bg-white bg-opacity-50 w-2'}`}
                        />
                      ))}
                    </div>
                  )}
                </motion.div>
              )}
            </div>
            <div className="bg-white p-4">
              <h3 className="text-xl font-semibold mb-2">{part.title}</h3>

              {/* Part number */}
              <p className="text-gray-600 mb-3">
                Part #: {part.actualPartNumber || part.partNumber}
              </p>

              {/* Icons row for condition, stock, and price */}
              <div className="border-t border-gray-200 pt-3">
                <div className="flex items-center justify-between text-gray-600">
                  {/* Condition */}
                  <div className="flex items-center gap-2">
                    <CheckCircle size={18} className="text-blue-500" />
                    <span>{part.conditions && part.conditions.length > 0 ? part.conditions[0]?.condition || 'Unknown' : 'Unknown'}</span>
                  </div>

                  {/* Stock */}
                  <div className="flex items-center gap-2">
                    <Package size={18} className={part.stock > 0 ? "text-green-500" : "text-red-500"} />
                    <span>{part.stock > 0 ? part.stock : 'Out of Stock'}</span>
                  </div>

                  {/* Price */}
                  <div className="flex items-center gap-2">
                    <Banknote size={18} className="text-orange-500" />
                    <span>{isLoadingPrice ? 'Loading...' : (part.price ? `Kshs. ${getAdjustedPrice(part.price).toLocaleString()}` : 'Contact us')}</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </>
  );
};

export default PartCard;
