export const dynamic = 'force-dynamic';

import { Suspense } from 'react';
import Spinner from '@/app/components/ui/Spinner';
import DiagnosticContent from './components/DiagnosticContent';

export default function DiagnosticPage() {
    return (
        <Suspense fallback={<div className="flex min-h-[300px] items-center justify-center">
            <Spinner size="lg" />
        </div>}>
            <DiagnosticContent />
        </Suspense>
    );
}
