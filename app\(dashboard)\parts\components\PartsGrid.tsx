'use client';

import React, { useState } from 'react';
import { Heart } from 'lucide-react';
import Image from 'next/image';

interface Part {
  id: string;
  name: string;
  price: number;
  discountPrice?: number;
  imageUrl: string;
  rating: number;
}

interface PartsGridProps {
  parts: Part[];
}

const PartsGrid: React.FC<PartsGridProps> = ({ parts }) => {
  const [favorites, setFavorites] = useState<Record<string, boolean>>({});

  const toggleFavorite = (id: string) => {
    setFavorites(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  return (
    <div className="grid grid-cols-2 gap-4">
      {parts.map((part) => (
        <div key={part.id} className="bg-gray-100 rounded-lg overflow-hidden">
          <div className="relative h-40 bg-white">
            <div className="absolute top-2 right-2 z-10">
              <button 
                onClick={() => toggleFavorite(part.id)}
                className="p-1.5 rounded-full"
              >
                <Heart 
                  size={20} 
                  className={favorites[part.id] ? "fill-red-500 text-red-500" : "text-gray-400"} 
                />
              </button>
            </div>
            <Image
              src={part.imageUrl}
              alt={part.name}
              fill
              style={{ objectFit: "contain" }}
              className="p-2"
            />
          </div>
          <div className="p-3">
            <h3 className="text-sm font-medium line-clamp-1">{part.name}</h3>
            <div className="flex items-center mt-1">
              <span className="text-sm font-semibold text-gray-900">KES {part.discountPrice || part.price}</span>
              {part.discountPrice && (
                <span className="ml-2 text-xs line-through text-gray-500">KES {part.price}</span>
              )}
            </div>
            <div className="flex items-center justify-between mt-2">
              <div className="flex items-center">
                <div className="flex items-center">
                  <span className="text-yellow-500">★</span>
                  <span className="text-xs ml-1">{part.rating.toFixed(1)}</span>
                </div>
              </div>
              <button className="bg-cyan-500 rounded-full p-1.5">
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  width="16" 
                  height="16" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="white" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                >
                  <path d="M6 13h12" />
                  <path d="M12 6v12" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default PartsGrid; 