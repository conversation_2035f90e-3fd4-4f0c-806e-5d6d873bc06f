// app/types/profile.ts

import { Database } from './authTypes';

// Correct UserProfile type reflecting the database structure
export type UserProfile = Database['public']['Tables']['profiles']['Row'];

export interface MissingProfileDetails {
  phone: boolean;
  full_name: boolean;
}

export interface UpdateProfileDetailsFormProps {
  onUpdate: (data: { phone: string | undefined; full_name: string | undefined }) => void;
  missingDetails: MissingProfileDetails;
}

export interface ProfileUpdateModalProps {
  isOpen: boolean;
  onClose: () => void;
  missingDetails: MissingProfileDetails;
  onProfileUpdate: (updatedProfile: Partial<UserProfile>) => void;
  children: React.ReactNode;
}