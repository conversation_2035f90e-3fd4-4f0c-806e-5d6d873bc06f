import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const { email, roleId, clientId } = await request.json();

    // --- Input Validation ---
    if (!email || typeof email !== 'string' || !email.includes('@')) {
      return NextResponse.json({ error: 'A valid email address is required' }, { status: 400 });
    }

    if (!roleId || typeof roleId !== 'string') {
      return NextResponse.json({ error: 'Role ID is required' }, { status: 400 });
    }

    if (!clientId || typeof clientId !== 'string') {
      return NextResponse.json({ error: 'Client ID is required' }, { status: 400 });
    }

    // --- Create Supabase Admin Client ---
    const supabase = createClient();

    // --- Verify Client Exists ---
    const { data: client, error: clientError } = await supabase
      .from('clients')
      .select('*')
      .eq('id', clientId)
      .single();

    if (clientError) {
      console.error('Error fetching client:', clientError);
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }

    // --- Update Client Email if Different ---
    if (client.email !== email) {
      const { error: updateError } = await supabase
        .from('clients')
        .update({ email })
        .eq('id', clientId);

      if (updateError) {
        console.error('Error updating client email:', updateError);
        return NextResponse.json({ error: 'Failed to update client email' }, { status: 500 });
      }
    }

    // --- Determine Base URL for Redirect ---
    const origin = request.headers.get('origin');
    const baseUrl = origin || process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    console.log(`Determined base URL for redirect: ${baseUrl} (Origin: ${origin || 'Not Provided'}, Env Var: ${process.env.NEXT_PUBLIC_APP_URL || 'Not Set'})`);

    // Construct the full redirect URL pointing to your registration/setup page
    const redirectUrl = `${baseUrl}/register?invitation=true&clientId=${clientId}`;
    console.log(`Generating Supabase invite link redirecting to: ${redirectUrl}`);

    // --- Send Supabase Invitation ---
    const { data, error: inviteError } = await supabase.auth.admin.inviteUserByEmail(email, {
      redirectTo: redirectUrl,
      data: {
        invited_by: 'admin',
        invited_at: new Date().toISOString(),
        is_invitation: true,
        registration_completed: false,
        role_id: roleId,
        client_id: clientId
      }
    });

    if (inviteError) {
      console.error('Supabase invitation error:', inviteError);
      let errorMessage = inviteError.message;
      if (inviteError.message.includes('User already exists')) {
        errorMessage = 'This email address is already registered or has a pending invitation.';
      } else if (inviteError.status === 429) {
        errorMessage = 'Too many requests. Please wait a moment and try again.';
      }
      const status = inviteError.status && inviteError.status >= 400 && inviteError.status < 500 ? inviteError.status : 400;
      return NextResponse.json({ error: errorMessage }, { status });
    }

    // --- Assign Role ---
    if (data?.user?.id) {
      const userId = data.user.id;

      console.log(`Invitation sent successfully for ${email}. User ID: ${userId}. Attempting to assign role ${roleId}.`);

      const { error: roleAssignmentError } = await supabase
        .from('user_roles')
        .insert({
          user_id: userId,
          role_id: roleId,
          assigned_at: new Date().toISOString()
        });

      if (roleAssignmentError) {
        console.warn(`Warning: Invitation sent for ${email} (User ID: ${userId}), but failed to assign role ${roleId} immediately. Error: ${roleAssignmentError.message}`);
      } else {
        console.log(`Successfully assigned role ${roleId} to invited user ${userId} (${email}) in user_roles table.`);
      }

      // --- Link Client to User Profile ---
      const { error: profileLinkError } = await supabase
        .from('client_profiles')
        .insert({
          client_id: clientId,
          profile_id: userId,
          linked_at: new Date().toISOString()
        });

      if (profileLinkError) {
        console.warn(`Warning: Invitation sent for ${email} (User ID: ${userId}), but failed to link client profile. Error: ${profileLinkError.message}`);
      } else {
        console.log(`Successfully linked client ${clientId} to user profile ${userId}.`);
      }
    } else {
      console.warn(`Warning: Invitation sent for ${email}, but user data (including ID) was not returned in the success response. Cannot assign role automatically.`);
    }

    // --- Return Success Response ---
    return NextResponse.json({
      success: true,
      message: 'Invitation sent successfully.',
      invitedEmail: email
    });

  } catch (error) {
    console.error('Error in client invitation process:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
