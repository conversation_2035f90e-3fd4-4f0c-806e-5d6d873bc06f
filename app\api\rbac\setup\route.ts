import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@/app/libs/supabase/server';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ 
      request, 
      response: NextResponse.next() 
    });
    
    // Get the current user (more secure than getSession)
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError) {
      return NextResponse.json({ error: 'Authentication error', details: authError.message }, { status: 401 });
    }

    if (!user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const userId = user.id;
    
    // Get or create Super Admin role
    let superAdminRoleId: string | undefined;
    
    // Check if Super Admin role exists
    const { data: existingRole, error: roleError } = await supabase
      .from('roles')
      .select('id')
      .eq('name', 'Super Admin')
      .single();
    
    if (roleError && roleError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
      return NextResponse.json({ error: 'Error checking for Super Admin role', details: roleError.message }, { status: 500 });
    }
    
    if (existingRole) {
      superAdminRoleId = existingRole.id;
    } else {
      // Create Super Admin role
      const { data: newRole, error: createRoleError } = await supabase
        .from('roles')
        .insert([{ name: 'Super Admin', description: 'Has full access to all system features' }])
        .select()
        .single();
      
      if (createRoleError) {
        return NextResponse.json({ error: 'Error creating Super Admin role', details: createRoleError.message }, { status: 500 });
      }
      
      superAdminRoleId = newRole.id;
      
      // Create basic permissions if they don't exist
      const basicPermissions = [
        { name: 'admin:access_panel', description: 'Access the admin panel', category: 'Administration' },
        { name: 'admin:manage_roles', description: 'Create, update, and delete roles', category: 'Administration' },
        { name: 'admin:manage_permissions', description: 'Manage system permissions', category: 'Administration' },
        { name: 'admin:assign_roles', description: 'Assign roles to users', category: 'Administration' },
        { name: 'admin:assign_permissions', description: 'Assign permissions to roles', category: 'Administration' },
        { name: 'admin:override_permissions', description: 'Create user-specific permission overrides', category: 'Administration' },
        { name: 'admin:view_audit_log', description: 'View the system audit log', category: 'Administration' },
      ];
      
      const { data: permissions, error: permissionsError } = await supabase
        .from('permissions')
        .upsert(basicPermissions, { onConflict: 'name' })
        .select();
      
      if (permissionsError) {
        return NextResponse.json({ error: 'Error creating basic permissions', details: permissionsError.message }, { status: 500 });
      }
      
      // Assign all permissions to Super Admin role
      const rolePermissions = permissions.map(permission => ({
        role_id: superAdminRoleId,
        permission_id: permission.id
      }));
      
      const { error: assignPermissionsError } = await supabase
        .from('role_permissions')
        .upsert(rolePermissions, { onConflict: 'role_id,permission_id' });
      
      if (assignPermissionsError) {
        return NextResponse.json({ error: 'Error assigning permissions to Super Admin role', details: assignPermissionsError.message }, { status: 500 });
      }
    }
    
    // Check if user already has Super Admin role
    const { data: existingUserRole, error: userRoleError } = await supabase
      .from('user_roles')
      .select()
      .eq('user_id', userId)
      .eq('role_id', superAdminRoleId)
      .maybeSingle();
    
    if (userRoleError) {
      return NextResponse.json({ error: 'Error checking user role', details: userRoleError.message }, { status: 500 });
    }
    
    if (existingUserRole) {
      return NextResponse.json({ 
        success: true, 
        message: 'User already has Super Admin role',
        userId,
        roleId: superAdminRoleId
      });
    }
    
    // Assign Super Admin role to user
    const { error: assignRoleError } = await supabase
      .from('user_roles')
      .insert([{ user_id: userId, role_id: superAdminRoleId }]);
    
    if (assignRoleError) {
      return NextResponse.json({ error: 'Error assigning Super Admin role to user', details: assignRoleError.message }, { status: 500 });
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Super Admin role assigned successfully',
      userId,
      roleId: superAdminRoleId
    });
    
  } catch (error: any) {
    console.error('Error in RBAC setup:', error);
    return NextResponse.json({ error: 'Internal server error', details: error.message }, { status: 500 });
  }
}
