// @/app/libs/supabase/admin.ts
import { createClient } from '@supabase/supabase-js'
import { Database } from '@/app/types/database'

// Ensure your Service Role Key is stored securely in environment variables
// IMPORTANT: Never expose this key publicly! Only use it server-side.
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceRoleKey) {
  throw new Error('Supabase URL or Service Role Key is missing from environment variables.');
}

// Create a singleton instance of the admin client
// Use createClient from the standard 'supabase-js' package for service role
export const supabaseAdmin = createClient<Database>(
  supabaseUrl,
  supabaseServiceRoleKey,
  {
    auth: {
      // Service role clients typically bypass RLS and don't persist sessions
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

// Function to get the admin client
export const createAdminClient = () => {
  return createClient<Database>(
    supabaseUrl!,
    supabaseServiceRoleKey!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
};