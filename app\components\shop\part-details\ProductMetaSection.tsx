'use client';

import React from 'react';
import type { PartDetails, CompatibleCar } from '@/app/shop/product/[slug]/page';
import { Tag } from 'lucide-react';

interface ProductMetaSectionProps {
  partDetails: PartDetails | null;
}

const ProductMetaSection: React.FC<ProductMetaSectionProps> = ({ partDetails }) => {
  if (!partDetails) {
    return null;
  }

  console.log('ProductMetaSection - compatibleCars:', partDetails.compatibleCars);

  return (
    <div className="flex flex-col gap-6">
      {/* Seller Info Section */}


      {/* Compatible Vehicles Section */}
      <div className="mt-2">
        <h3 className="text-xl font-semibold mb-4">Compatible Vehicles</h3>
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
          {partDetails.compatibleCars && partDetails.compatibleCars.length > 0 ? (
            <ul className="divide-y divide-gray-200">
              {partDetails.compatibleCars.map((car: CompatibleCar) => (
                <li key={car.id} className="py-3 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-500 mr-3">
                    <path d="M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.5 2.8C1.4 11.3 1 12.1 1 13v3c0 .6.4 1 1 1h2"></path>
                    <circle cx="7" cy="17" r="2"></circle>
                    <circle cx="17" cy="17" r="2"></circle>
                  </svg>
                  <div className="flex flex-col">
                    <span className="text-gray-700 font-medium">
                      {car.brand} {car.model} ({car.year})
                    </span>
                    {car.trim && (
                      <span className="text-gray-500 text-sm">
                        Trim: {car.trim}
                      </span>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <div className="py-3 text-center text-gray-500">
              No compatible vehicles information available for this part.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductMetaSection;
