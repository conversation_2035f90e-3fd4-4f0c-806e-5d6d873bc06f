'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import DirectImage from '@/app/components/ui/DirectImage';

interface ProductImageGalleryProps {
  images: string[];
  productTitle: string;
}

const ProductImageGallery: React.FC<ProductImageGalleryProps> = ({ images, productTitle }) => {
  const [selectedImage, setSelectedImage] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  return (
    <div className="w-full md:w-1/2 lg:w-3/5">
      <div className="relative aspect-square w-full overflow-hidden rounded-lg bg-gray-100">
        <AnimatePresence mode="wait">
          <motion.div
            key={selectedImage}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="relative h-full w-full"
          >
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                <div className="h-8 w-8 animate-spin rounded-full border-4 border-teal-600 border-t-transparent"></div>
              </div>
            )}
            <DirectImage
              src={images[selectedImage]}
              alt={`${productTitle} - Image ${selectedImage + 1}`}
              fill
              sizes="(max-width: 768px) 100vw, 50vw"
              className="object-cover"
              priority={selectedImage === 0}
              onLoad={handleImageLoad}
              quality={85}
            />
          </motion.div>
        </AnimatePresence>
      </div>

      {images.length > 1 && (
        <div className="mt-4 grid grid-cols-4 gap-2">
          {images.map((image, index) => (
            <button
              key={index}
              onClick={() => {
                setIsLoading(true);
                setSelectedImage(index);
              }}
              className={`relative aspect-square overflow-hidden rounded-lg ${
                selectedImage === index
                  ? 'ring-2 ring-teal-600'
                  : 'ring-1 ring-gray-200 hover:ring-teal-400'
              }`}
            >
              <DirectImage
                src={image}
                alt={`${productTitle} - Thumbnail ${index + 1}`}
                fill
                sizes="(max-width: 768px) 25vw, 12.5vw"
                className="object-cover"
                loading="lazy"
                quality={60}
              />
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProductImageGallery;
