'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSearchParams } from 'next/navigation';
import CustomTabs from './components/ui/CustomTabs';
import BrandsTab from './components/BrandsTab';
import ModelsTab from './components/ModelsTab';
import GenerationsTab from './components/GenerationsTab';
import VariationsTab from './components/VariationsTab';
import TrimsTab from './components/TrimsTab';
import DashboardHeader from './components/DashboardHeader';

export default function CarsDashboard() {
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabParam || 'brands');

  // Update active tab when URL parameter changes
  useEffect(() => {
    if (tabParam && ['brands', 'models', 'generations', 'variations', 'trims'].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Handle tab change and update URL
  const handleTabChange = (tabId: string | number) => {
    setActiveTab(tabId as string);

    // Update URL without full page reload
    const url = new URL(window.location.href);
    url.searchParams.set('tab', tabId as string);
    window.history.pushState({}, '', url);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader title="Cars Dashboard" />

      <div className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-lg shadow-md p-6 mb-8"
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Manage Vehicle Information</h2>
          <p className="text-gray-600 mb-4">
            Use this dashboard to manage car brands, models, generations, variations, and trims.
            Select a tab below to get started.
          </p>

          <CustomTabs
            tabs={[
              { id: 'brands', label: 'Brands' },
              { id: 'models', label: 'Models' },
              { id: 'generations', label: 'Generations' },
              { id: 'variations', label: 'Variations' },
              { id: 'trims', label: 'Trims' }
            ]}
            activeTabId={activeTab}
            onTabChange={handleTabChange}
            className="w-full"
          />

          <div className="mt-6">
            {activeTab === 'brands' && <BrandsTab onBrandUpdated={() => {}} />}
            {activeTab === 'models' && <ModelsTab onModelUpdated={() => {}} />}
            {activeTab === 'generations' && <GenerationsTab onGenerationUpdated={() => {}} />}
            {activeTab === 'variations' && <VariationsTab onVariationUpdated={() => {}} />}
            {activeTab === 'trims' && <TrimsTab onTrimUpdated={() => {}} />}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
