.menu-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  .menu-container.horizontal {
    display: flex;
    gap: 1.5rem;
  }
  
  .menu-container.vertical {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .menu-item {
    position: relative;
  }
  
  .menu-item.horizontal:hover .submenu {
    display: block;
  }
  
  .menu-link, .menu-button {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: #333;
    background: none;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
    border-radius: 6px;
  }
  
  .menu-link:hover, .menu-button:hover {
    background-color: #f5f5f5;
  }
  
  .menu-icon {
    flex-shrink: 0;
    color: #666;
  }
  
  .menu-title {
    white-space: nowrap;
  }
  
  .menu-arrow {
    margin-left: auto;
  }
  
  .submenu {
    min-width: 200px;
    background: white;
  }
  
  .submenu.hidden {
    display: none;
  }
  
  .menu-container.horizontal .submenu {
    position: absolute;
    top: 100%;
    left: 0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    padding: 0.5rem 0;
    z-index: 50;
  }
  
  .menu-container.vertical .submenu {
    margin-left: 1.5rem;
    border-left: 2px solid #eee;
    padding-left: 0.5rem;
  }