// components/ui/inputs/Input.tsx
"use client";

import React, { FocusEvent } from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  errorMessage?: string;
  helperText?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ label, errorMessage, helperText, id, onFocus, onBlur, ...props }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false);
    const hasError = !!errorMessage;
    const resolvedLabel = label || props.placeholder;
    const describedBy = hasError ? `${id}-error` : helperText ? `${id}-helper` : undefined;
    // Track if the input has a value
    const [hasInput, setHasInput] = React.useState(!!props.value || !!props.defaultValue);

    // Create a ref to access the input element directly
    const inputRef = React.useRef<HTMLInputElement | null>(null);

    // Combine refs (the forwarded ref and our local ref)
    const setRefs = React.useCallback(
      (element: HTMLInputElement | null) => {
        // Update our local ref
        inputRef.current = element;

        // Update the forwarded ref
        if (typeof ref === 'function') {
          ref(element);
        } else if (ref) {
          ref.current = element;
        }

        // Check if the input has a value immediately after mounting
        if (element && element.value) {
          setHasInput(true);
        }
      },
      [ref]
    );

    // Check for input value on mount and when props change
    React.useEffect(() => {
      const checkValue = () => {
        // Check props.value first
        if (props.value !== undefined && props.value !== '') {
          setHasInput(true);
          return;
        }

        // If no props.value, check the actual input element value
        if (inputRef.current && inputRef.current.value) {
          setHasInput(true);
          return;
        }

        // Otherwise, no value
        setHasInput(false);
      };

      checkValue();

      // Add a small delay to check again after the DOM has updated
      const timeoutId = setTimeout(checkValue, 100);
      return () => clearTimeout(timeoutId);
    }, [props.value, props.defaultValue]);

    // Handle input changes to detect when user types or clears the input
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setHasInput(!!e.target.value);
      props.onChange?.(e);
    };

    const handleFocus = (event: FocusEvent<HTMLInputElement>) => {
      setIsFocused(true);
      onFocus?.(event);
    };

    const handleBlur = (event: FocusEvent<HTMLInputElement>) => {
      setIsFocused(false);
      onBlur?.(event);
    };

    return (
      <div className="w-full">
        <div className="relative">
          {resolvedLabel && (
            <label
              htmlFor={id}
              className={`absolute transition-all duration-300 ease-in-out ${
                hasInput || isFocused
                  ? 'text-xs -top-0 left-4 px-1 pt-1 z-10' // Removed bg-white and dark:bg-gray-800
                  : 'text-base top-[50%] -translate-y-[50%] left-4'
              } ${
                hasError
                  ? 'text-red-700 dark:text-red-500'
                  : hasInput && !isFocused
                  ? 'text-gray-700 dark:text-gray-300 font-medium' // Make label more visible when input has value but not focused
                  : 'text-gray-500 dark:text-gray-400'
              } pointer-events-none`}
            >
              {resolvedLabel}
            </label>
          )}
          <input
            {...props}
            ref={setRefs}
            id={id}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleChange}
            // Remove placeholder when input has value to prevent overlap
            placeholder={hasInput ? '' : props.placeholder}
            aria-describedby={describedBy}
            className={`
              w-full h-12 px-4 text-gray-900 border rounded-lg
              focus:outline-none focus:ring-0
              transition-colors duration-200
              ${
                hasInput || isFocused
                  ? 'pt-6 pb-2'
                  : 'py-3'
              }
              ${
                hasError
                  ? 'border-red-500 focus:border-red-500 dark:bg-red-100 dark:border-red-400'
                  : isFocused
                  ? 'border-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:border-blue-500'
                  : 'border-gray-300 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400'
              }
              ${props.disabled ? 'bg-gray-100 dark:bg-gray-600' : ''}
              dark:text-white
            `}
          />
        </div>
        {/* Error and helper text remain same */}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;