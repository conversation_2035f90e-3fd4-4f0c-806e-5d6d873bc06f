// components/Upload.tsx
import React, { useRef, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/app/utils/cn';
import Icon from '../Icon';

interface UploadProps {
    onFileAdded?: (file: File) => void;
    onFilesAdded?: (files: File[]) => void; // New prop for batch processing
    onFileRemoved?: (file: File) => void;
    maxFiles?: number;
    acceptedFileTypes?: string[]; // e.g., ['image/*', 'application/pdf']
    capture?: 'user' | 'environment' | boolean;
    showPreview?: boolean;
}

const Upload: React.FC<UploadProps> = ({
    onFileAdded,
    onFilesAdded,
    onFileRemoved,
    maxFiles = 5, // Default maxFiles
    acceptedFileTypes = ['image/*', 'application/pdf', '.docx', '.doc', '.txt'], // Default accepted file types
    capture, // ADDED PROP to avoid unused warning, even if not used in logic
    showPreview // ADDED PROP to avoid unused warning, even if not used in logic
}) => {
    const [files, setFiles] = useState<File[]>([]);
    const [isDraggingOver, setIsDraggingOver] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [isFocused, setIsFocused] = useState(false);

    const handleFiles = useCallback((newFiles: File[]) => {
        let validFiles = newFiles.filter(file => {
            if (acceptedFileTypes && acceptedFileTypes.length > 0) {
                return acceptedFileTypes.some(acceptedType => {
                    if (acceptedType.includes('*')) { // Handle MIME type patterns like 'image/*'
                        const [baseType] = acceptedType.split('/');
                        return file.type.startsWith(baseType + '/');
                    } else if (acceptedType.startsWith('.')) { // Handle file extensions like '.pdf'
                        return file.name.toLowerCase().endsWith(acceptedType.toLowerCase());
                    }
                    return file.type === acceptedType; // Exact MIME type match
                });
            }
            return true; // If no accepted types specified, accept all
        });

        if (maxFiles && files.length + validFiles.length > maxFiles) {
            validFiles = validFiles.slice(0, maxFiles - files.length); // Limit files to maxFiles
            alert(`Maximum number of files exceeded. Only ${maxFiles} files are allowed.`);
        }

        setFiles((prevFiles) => {
            const combinedFiles = [...prevFiles, ...validFiles];
            validFiles.forEach(file => onFileAdded?.(file)); // Call onFileAdded for each valid file
            onFilesAdded?.(validFiles); // Call onFilesAdded for batch processing
            return combinedFiles;
        });

    }, [acceptedFileTypes, maxFiles, files, onFileAdded, onFilesAdded, onFileRemoved, setFiles]);

    const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        setIsDraggingOver(false);
        setIsFocused(false);

        const droppedFiles = Array.from(event.dataTransfer.files);
        handleFiles(droppedFiles);
    }, [handleFiles]); // Now handleFiles is declared before use

    const handleFileInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files) {
            const selectedFiles = Array.from(event.target.files);
            handleFiles(selectedFiles);
            // Reset file input to allow selecting the same file again
            if (fileInputRef.current) {
                fileInputRef.current.value = '';
            }
        }
    }, [handleFiles]); // Now handleFiles is declared before use

    const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault(); // Important to allow drop
    }, []);

    const handleDragEnter = useCallback((event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        setIsDraggingOver(true);
        setIsFocused(true); // Consider drag enter as focus
    }, []);

    const handleDragLeave = useCallback((event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        setIsDraggingOver(false);
        setIsFocused(false);
    }, []);

    const handleRemoveFile = useCallback((fileToRemove: File) => {
        setFiles(currentFiles => {
            const updatedFiles = currentFiles.filter(file => file !== fileToRemove);
            onFileRemoved?.(fileToRemove); // Call onFileRemoved when file is removed
            return updatedFiles;
        });
    }, [onFileRemoved]);

    const containerVariants = {
        initial: { opacity: 0, scale: 0.95 },
        animate: { opacity: 1, scale: 1, transition: { duration: 0.3, ease: "easeOut" } },
        exit: { opacity: 0, scale: 0.95, transition: { duration: 0.2, ease: "easeIn" } },
    };

    const focusRingVariants = {
        initial: { scaleX: 0 },
        animate: { scaleX: 1, transition: { duration: 0.4, ease: "easeOut" } },
        exit: { scaleX: 0, transition: { duration: 0.3, ease: "easeIn" } },
    };

    return (
        <motion.div
            className="w-full"
            variants={containerVariants}
            initial="initial"
            animate="animate"
            exit="exit"
        >
            <motion.div
                className="absolute inset-0 top-auto h-1 bg-primary-500 origin-top transform"
                variants={focusRingVariants}
                animate={isFocused ? "animate" : "initial"}
                exit="exit"
            />
            <div
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()} // Trigger file input on div click
                className={cn(
                    "relative rounded-md border-2 border-dashed transition-all duration-200 cursor-pointer",
                    isDraggingOver ? "border-primary-500 bg-gray-50 dark:bg-gray-800 dark:border-primary-500" : "border-gray-300 bg-white dark:bg-gray-700 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500",
                    isFocused ? "ring-2 ring-primary-500 ring-offset-1" : "", // Example focus ring using ring utilities if needed
                )}
            >
                <input
                    type="file"
                    multiple
                    accept={acceptedFileTypes.join(',')} // Set accepted file types for file input
                    className="hidden"
                    ref={fileInputRef}
                    onChange={handleFileInputChange}
                />
                <div className="px-4 py-6 flex flex-col items-center justify-center min-h-[150px]">
                    {files.length === 0 && (
                        <motion.p
                            className="text-gray-500 dark:text-gray-400 text-center"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1, transition: { delay: 0.3 } }}
                        >
                            <span className="font-semibold">Drag and drop</span> files here or <span className="font-semibold">click</span> to upload
                        </motion.p>
                    )}
                    {files.length > 0 && (
                        <motion.ul
                            className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mt-4"
                            layout
                        >
                            <AnimatePresence>
                                {files.map((file, index) => (
                                    <motion.li
                                        key={file.name + '-' + index}
                                        layout
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, y: 20 }}
                                        transition={{ duration: 0.2 }}
                                        className="relative rounded-md shadow-md overflow-hidden bg-white dark:bg-gray-800"
                                    >
                                        {file.type.startsWith('image/') ? (
                                            <img
                                                src={URL.createObjectURL(file)}
                                                alt={file.name}
                                                className="block w-full h-auto object-cover rounded-t-md max-h-[120px]"
                                            />
                                        ) : (
                                            <div className="flex items-center justify-center p-3 rounded-t-md bg-gray-100 dark:bg-gray-900">
                                                <Icon name="paperclip" size={20} color="gray-500" library="lucide" />
                                            </div>
                                        )}
                                        <div className="p-2">
                                            <p className="text-sm font-medium text-gray-900 dark:text-white truncate" title={file.name}>{file.name}</p>
                                            <p className="text-xs text-gray-500 dark:text-gray-300">{file.size < 1024 ? file.size + ' bytes' : (file.size / 1024).toFixed(1) + ' KB'}</p>
                                        </div>
                                        <button
                                            onClick={() => handleRemoveFile(file)}
                                            className="absolute top-1 right-1 bg-gray-200 dark:bg-gray-700 rounded-full p-1 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                                            aria-label={`Remove ${file.name}`}
                                        >
                                            <Icon name="x" size={16} color="gray-700" library="lucide" />
                                        </button>
                                    </motion.li>
                                ))}
                            </AnimatePresence>
                        </motion.ul>
                    )}
                </div>
            </div>
        </motion.div>
    );
};

export default Upload;