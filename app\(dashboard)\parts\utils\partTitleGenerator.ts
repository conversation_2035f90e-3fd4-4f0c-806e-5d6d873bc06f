// utils/partTitleGenerator.ts
import {
    fetchBrandName,
    fetchModelName,
    fetchGenerationDetails,
    fetchVariationName,
    fetchTrimName,
    fetchAttributeOption
  } from '@/app/libs/data';
  import type { PartFormValues } from '../types';
  
  export const generatePartTitle = async (
    data: PartFormValues,
    getValues: () => PartFormValues,
    requirePartNumber: boolean,
    flatCategories: any[]
  ): Promise<string> => {
    const titleParts: string[] = [];
    
    if (!requirePartNumber) {
      if (data.brandId) titleParts.push(await fetchBrandName(Number(data.brandId)));
      if (data.modelId) titleParts.push(await fetchModelName(Number(data.modelId)));
      
      if (data.generationId) {
        const generation = await fetchGenerationDetails(Number(data.generationId));
        if (generation) {
          titleParts.push(generation.name);
          if (generation.start_production_year) {
            const years = generation.end_production_year 
              ? `${generation.start_production_year}-${generation.end_production_year}`
              : `${generation.start_production_year}`;
            titleParts.push(years);
          }
        }
      }
      
      if (data.variationId) titleParts.push(await fetchVariationName(Number(data.variationId)));
      if (data.trimId) titleParts.push(await fetchTrimName(Number(data.trimId)));
    }
  
    if (data.condition) titleParts.push(data.condition);
    
    const categoryData = flatCategories.find(
      cat => cat.id.toString() === data.categoryId
    );
    if (categoryData?.label) titleParts.push(categoryData.label);
  
    const attributeValues = await Promise.all(
      (data.attributes || []).map(async (attr: { id?: number; value?: number | string }) => {
        if (attr.id !== undefined && attr.value !== undefined) {
          const option: { option_value?: string } | null = await fetchAttributeOption(Number(attr.id), Number(attr.value));
          return option?.option_value || attr.value;
        }
        return attr.value;
      })
    );
  
    titleParts.push(...attributeValues.filter(Boolean).map(String));
    return titleParts.join(' ');
  };