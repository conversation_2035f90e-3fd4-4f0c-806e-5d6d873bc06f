// menuData.tsx
import { createClient } from "@/app/libs/supabase/client";
import { Database } from "@/app/types/database";

export interface MenuItem {
  label: string;
  href: string;
  icon?: string;
  library?: string;
  children?: MenuItem[];
}

type CategoryRow = Database['public']['Tables']['car_part_categories']['Row'];

export async function getMenuItems(): Promise<MenuItem[]> {
  const supabase = createClient();
  
  // Fetch all categories from Supabase
  const { data, error } = await supabase
    .from('car_part_categories')
    .select('*');

  if (error) {
    console.error('Error fetching menu items:', error);
    return [];
  }

  // Build hierarchical menu structure
  return buildMenuTree(data || []);
}

function buildMenuTree(categories: CategoryRow[]): MenuItem[] {
  const map = new Map<number, MenuItem>();
  const roots: MenuItem[] = [];

  // First pass: create all map entries
  categories.forEach(category => {
    map.set(category.id, {
      label: category.label,
      href: category.href,
      icon: category.icon || undefined,
      library: category.library || undefined,
      children: []
    });
  });

  // Second pass: assign children to parents
  categories.forEach(category => {
    if (category.parent_category_id !== null) {
      const parent = map.get(category.parent_category_id);
      parent?.children?.push(map.get(category.id)!);
    } else {
      roots.push(map.get(category.id)!);
    }
  });

  return roots;
}