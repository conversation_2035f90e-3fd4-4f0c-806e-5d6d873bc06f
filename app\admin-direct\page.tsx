'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/app/hooks/useAuth';

export default function AdminDirectAccess() {
  const [isLoading, setIsLoading] = useState(true);
  const [userId, setUserId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (user) {
      setUserId(user.id);
      setIsLoading(false);
    } else {
      setIsLoading(false);
      setError('Not authenticated. Please log in to access this page.');
    }
  }, [user]);

  const adminPages = [
    { name: 'Roles', path: '/admin/roles' },
    { name: 'Permissions', path: '/admin/permissions' },
    { name: 'Users', path: '/admin/users' },
    { name: 'Role Permissions', path: '/admin/role-permissions' },
    { name: '<PERSON>t Log', path: '/admin/audit-log' },
  ];

  const handleAssignSuperAdmin = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/rbac/setup');
      const data = await response.json();
      
      if (data.success) {
        setError(null);
        alert(`Success: ${data.message}`);
      } else {
        setError(`Failed to assign Super Admin role: ${data.message || 'Unknown error'}`);
      }
    } catch (err) {
      console.error('Error assigning Super Admin role:', err);
      setError('Failed to assign Super Admin role. Check console for details.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearLocalStorage = () => {
    localStorage.clear();
    sessionStorage.clear();
    alert('Local storage and session storage cleared. Refreshing page...');
    window.location.reload();
  };

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-12 w-12 animate-spin rounded-full border-b-4 border-t-4 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="mb-6 text-3xl font-bold">Admin Direct Access</h1>
      
      {error && (
        <div className="mb-6 rounded-md bg-red-50 p-4 text-red-800">
          <p>{error}</p>
        </div>
      )}
      
      <div className="mb-6">
        <h2 className="mb-2 text-xl font-semibold">User Information</h2>
        <div className="rounded-md bg-gray-100 p-4">
          {userId ? (
            <p>
              <span className="font-medium">User ID:</span> {userId}
            </p>
          ) : (
            <p className="text-red-600">No user information available</p>
          )}
        </div>
      </div>
      
      <div className="mb-6">
        <h2 className="mb-2 text-xl font-semibold">Actions</h2>
        <div className="flex flex-wrap gap-4">
          <button
            onClick={handleAssignSuperAdmin}
            className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
            disabled={isLoading}
          >
            {isLoading ? 'Processing...' : 'Assign Super Admin Role'}
          </button>
          
          <button
            onClick={handleClearLocalStorage}
            className="rounded-md bg-yellow-600 px-4 py-2 text-white hover:bg-yellow-700"
          >
            Clear Browser Storage
          </button>
          
          <Link
            href="/rbac-debug"
            className="rounded-md bg-green-600 px-4 py-2 text-white hover:bg-green-700"
          >
            View RBAC Debug Info
          </Link>
        </div>
      </div>
      
      <div className="mb-6">
        <h2 className="mb-2 text-xl font-semibold">Direct Access Links</h2>
        <p className="mb-4 text-sm text-gray-600">
          These links bypass the normal permission checks. Use them only for troubleshooting.
        </p>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {adminPages.map((page) => (
            <a
              key={page.path}
              href={page.path}
              className="block rounded-md border border-gray-300 bg-white p-4 text-center hover:bg-gray-50"
            >
              {page.name}
            </a>
          ))}
        </div>
      </div>
      
      <div className="mt-8 rounded-md bg-yellow-50 p-4 text-yellow-800">
        <h3 className="mb-2 font-semibold">Troubleshooting Instructions</h3>
        <ol className="list-inside list-decimal space-y-2">
          <li>Click "Assign Super Admin Role" to ensure your user has the Super Admin role</li>
          <li>Click "Clear Browser Storage" to clear any cached permissions or authentication data</li>
          <li>Use the "View RBAC Debug Info" to check if permissions are being correctly assigned</li>
          <li>If you still can't access the admin panel normally, use the direct access links above</li>
          <li>After accessing a page directly, try navigating to other admin pages to see if the session is now working correctly</li>
        </ol>
      </div>
    </div>
  );
}
