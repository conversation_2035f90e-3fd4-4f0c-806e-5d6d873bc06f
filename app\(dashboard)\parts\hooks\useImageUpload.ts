// app/(dashboard)/parts/add/hooks/useImageUpload.ts
'use client';

import { useState, useCallback } from 'react';
import { Area } from 'react-easy-crop';
import { Crop } from 'react-image-crop';

const useImageUpload = () => {
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);
  const [croppedImageUrl, setCroppedImageUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileAdded = useCallback((file: File) => {
    setCroppedImageUrl(null);
    setCroppedAreaPixels(null);
    setError(null);

    const reader = new FileReader();
    reader.onload = () => {
      setImageSrc(reader.result as string);
    };
    reader.readAsDataURL(file);
  }, []);

  const onImageLoaded = useCallback((image: HTMLImageElement) => {
    console.log('Image loaded', image.naturalWidth, image.naturalHeight);
  }, []);

  const onChange = useCallback((newCrop: Crop) => {}, []);

  const onCropComplete = useCallback((cropArea: Crop) => {
    const pixelCrop = {
      x: cropArea.x,
      y: cropArea.y,
      width: cropArea.width,
      height: cropArea.height
    };
    setCroppedAreaPixels(pixelCrop as Area);
    generateCroppedImage(imageSrc, pixelCrop as Area)
      .then(url => {
        setCroppedImageUrl(url as string);
      })
      .catch(err => {
        console.error('Error generating cropped image:', err);
        setError('Failed to generate cropped image');
      });
  }, [imageSrc]);

  const generateCroppedImage = async (imageSrc: string | null, cropArea: Area): Promise<string | null> => {
    if (!imageSrc) return null;

    const image = new Image();
    image.src = imageSrc;

    return new Promise((resolve, reject) => {
      image.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('Could not get canvas context'));
          return;
        }
        canvas.width = cropArea.width;
        canvas.height = cropArea.height;
        ctx.drawImage(
          image,
          cropArea.x,
          cropArea.y,
          cropArea.width,
          cropArea.height,
          0,
          0,
          cropArea.width,
          cropArea.height
        );
        const dataUrl = canvas.toDataURL('image/jpeg');
        resolve(dataUrl);
      };
      image.onerror = () => {
        reject(new Error('Failed to load image'));
      };
    });
  };

  const clearImage = () => {
    setImageSrc(null);
    setCroppedImageUrl(null);
    setCroppedAreaPixels(null);
    setError(null);
  };

  return {
    imageSrc,
    croppedImageUrl,
    error,
    handleFileAdded,
    onImageLoaded,
    onChange,
    onCropComplete,
    clearImage
  };
};

export default useImageUpload;