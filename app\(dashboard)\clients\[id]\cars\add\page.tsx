import React from 'react';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import ClientCarForm from '../../../components/ClientCarForm';

export default function AddClientCarPage({ params }: { params: { id: string } }) {
  const clientId = params.id;
  
  return (
    <div className="p-4 md:p-6 lg:p-8 bg-gray-100 min-h-screen">
      {/* Back button */}
      <Link href={`/clients/${clientId}`} className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-6">
        <ArrowLeft className="w-4 h-4 mr-2" /> Back to Client
      </Link>

      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h1 className="text-2xl font-semibold text-gray-900 mb-1">Add Client Car</h1>
          <p className="text-gray-600">Add a car for this client</p>
        </div>

        <ClientCarForm clientId={clientId} />
      </div>
    </div>
  );
}
