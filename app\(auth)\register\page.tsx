// app/(auth)/register/page.tsx
'use client';

import { useEffect, useState, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import AdminRegistrationForm from './components/adminRegistrationForm'
import InvitationRegistrationForm from './components/InvitationRegistrationForm'

function RegisterContent() {
  const [isInvitation, setIsInvitation] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Check if this is an invitation registration
    const checkIfInvitation = () => {
      // Check URL parameters first
      const token = searchParams?.get('token');
      const type = searchParams?.get('type');

      if (token && type === 'invite') {
        setIsInvitation(true);
        return;
      }

      // Then check hash parameters (Supabase sometimes uses hash)
      const hash = window.location.hash;
      if (hash) {
        const hashParams = new URLSearchParams(hash.substring(1));
        const hashType = hashParams.get('type');
        const accessToken = hashParams.get('access_token');

        if ((hashType === 'invite' || hashType === 'recovery') && accessToken) {
          setIsInvitation(true);
          return;
        }
      }

      // Finally, check if there's a special flag in the URL
      if (searchParams?.get('invitation') === 'true') {
        setIsInvitation(true);
      }
    };

    checkIfInvitation();

    // Add event listener for hash changes (in case Supabase redirects with hash)
    const handleHashChange = () => {
      checkIfInvitation();
    };

    window.addEventListener('hashchange', handleHashChange);
    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, [searchParams]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      {isInvitation ? <InvitationRegistrationForm /> : <AdminRegistrationForm />}
    </div>
  )
}

export default function RegisterPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading...</div>}>
      <RegisterContent />
    </Suspense>
  )
}