'use client';

import { toast } from 'react-hot-toast';

interface SyncTask {
  id: string;
  type: 'cart' | 'favorites' | 'orders';
  data: any;
  timestamp: number;
}

class BackgroundSyncManager {
  private static instance: BackgroundSyncManager;
  private syncTasks: Map<string, SyncTask> = new Map();
  private isRegistered = false;

  private constructor() {
    this.init();
  }

  static getInstance(): BackgroundSyncManager {
    if (!BackgroundSyncManager.instance) {
      BackgroundSyncManager.instance = new BackgroundSyncManager();
    }
    return BackgroundSyncManager.instance;
  }

  private async init() {
    if ('serviceWorker' in navigator && 'PeriodicSyncManager' in window) {
      try {
        const registration = await navigator.serviceWorker.ready;
        await (registration as any).periodicSync.register('sync-parts', {
          minInterval: 24 * 60 * 60 * 1000, // 24 hours
        });
        this.isRegistered = true;
        console.log('Periodic sync registered');
      } catch (error) {
        console.error('Periodic sync registration failed:', error);
      }
    }
  }

  async addSyncTask(task: SyncTask): Promise<void> {
    if (!this.isRegistered) {
      console.warn('Background sync not available');
      return;
    }

    try {
      const registration = await navigator.serviceWorker.ready;
      this.syncTasks.set(task.id, task);
      
      // Store task in IndexedDB
      await this.storeTask(task);
      
      // Register sync
      await (registration as any).sync.register(`sync-${task.type}-${task.id}`);
      
      toast.success('Changes will be synced when online');
    } catch (error) {
      console.error('Failed to register sync:', error);
      toast.error('Failed to sync changes');
    }
  }

  private async storeTask(task: SyncTask): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('AutoFlowSync', 1);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains('syncTasks')) {
          db.createObjectStore('syncTasks', { keyPath: 'id' });
        }
      };

      request.onsuccess = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        const transaction = db.transaction(['syncTasks'], 'readwrite');
        const store = transaction.objectStore('syncTasks');
        store.put(task);
        transaction.oncomplete = () => resolve();
        transaction.onerror = () => reject(transaction.error);
      };
    });
  }

  async getPendingTasks(): Promise<SyncTask[]> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('AutoFlowSync', 1);

      request.onerror = () => reject(request.error);

      request.onsuccess = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        const transaction = db.transaction(['syncTasks'], 'readonly');
        const store = transaction.objectStore('syncTasks');
        const tasks: SyncTask[] = [];

        store.openCursor().onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result;
          if (cursor) {
            tasks.push(cursor.value);
            cursor.continue();
          } else {
            resolve(tasks);
          }
        };
      };
    });
  }

  async removeTask(taskId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('AutoFlowSync', 1);

      request.onerror = () => reject(request.error);

      request.onsuccess = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        const transaction = db.transaction(['syncTasks'], 'readwrite');
        const store = transaction.objectStore('syncTasks');
        store.delete(taskId);
        transaction.oncomplete = () => resolve();
        transaction.onerror = () => reject(transaction.error);
      };
    });
  }
}

export const backgroundSync = BackgroundSyncManager.getInstance(); 