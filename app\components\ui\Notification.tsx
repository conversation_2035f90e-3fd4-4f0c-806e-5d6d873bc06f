import React, { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle, Info, X } from 'lucide-react';

interface NotificationProps {
  header: string;
  body: string;
  type: 'hint' | 'info' | 'error' | 'success';
  duration?: number;
  position?: 'top-right' | 'top-left' | 'top-middle' | 'bottom-right' | 'bottom-left' | 'bottom-middle';
  onClose?: () => void;
}

const Notification: React.FC<NotificationProps> = ({
  header,
  body,
  type,
  duration = 5000,
  position = 'top-middle',
  onClose,
}) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      onClose?.(); // Call the onClose handler when the notification auto-closes
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  const handleClose = () => {
    setIsVisible(false);
    onClose?.(); // Call the onClose handler when the user manually closes
  };

  const getIcon = () => {
    switch (type) {
      case 'hint':
        return <Info className="h-6 w-6 text-white" />;
      case 'info':
        return <Info className="h-6 w-6 text-white" />;
      case 'error':
        return <AlertCircle className="h-6 w-6 text-white" />;
      case 'success':
        return <CheckCircle className="h-6 w-6 text-white" />;
      default:
        return null;
    }
  };

  const getPositionClass = () => {
    switch (position) {
      case 'top-right':
        return 'top-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      case 'top-middle':
        return 'top-4 left-1/2 -translate-x-1/2';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-middle':
        return 'bottom-4 left-1/2 -translate-x-1/2';
      default:
        return 'top-4 left-1/2 -translate-x-1/2';
    }
  };

  return (
    <div
      className={`fixed z-50 transition-opacity duration-300 ease-in-out transform notification-element ${
        isVisible ? 'opacity-100' : 'opacity-0 pointer-events-none'
      } ${getPositionClass()} sm:w-[calc(max(50%,_384px))] w-full px-4`}
    >
      <div
        className={`flex mx-auto overflow-hidden border rounded-lg shadow-md ${
          type === 'hint'
            ? 'border-gray-300 bg-gray-100'
            : type === 'info'
            ? 'border-blue-500 bg-blue-100'
            : type === 'error'
            ? 'border-red-500 bg-red-100'
            : 'border-green-500 bg-green-100'
        }`}
      >
        <div
          className={`flex items-center justify-center w-12 ${
            type === 'hint'
              ? 'bg-gray-300'
              : type === 'info'
              ? 'bg-blue-500'
              : type === 'error'
              ? 'bg-red-500'
              : 'bg-green-500'
          }`}
        >
          {getIcon()}
        </div>
        <div className="relative px-4 py-3 w-full">
          {/* Close button positioned absolutely */}
          <button
            onClick={handleClose}
            className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
          >
            <X className="h-4 w-4" />
          </button>

          <div className='flex flex-col text-center w-full'>
            <div className="font-bold">{header}</div>
            <p className="text-sm text-gray-700">{body}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Notification;