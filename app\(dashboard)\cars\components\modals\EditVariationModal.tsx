'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { createClient } from '@/app/libs/supabase/client';
import { Variation, VariationFormData, Generation, Model, Brand } from '../../types';

interface EditVariationModalProps {
  isOpen: boolean;
  onClose: () => void;
  variation: Variation;
  generations: Generation[];
  models: Model[];
  brands: Brand[];
  onSuccess: () => void;
}

const EditVariationModal: React.FC<EditVariationModalProps> = ({
  isOpen,
  onClose,
  variation,
  generations,
  models,
  brands,
  onSuccess
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedBrandId, setSelectedBrandId] = useState<number | ''>('');
  const [selectedModelId, setSelectedModelId] = useState<number | ''>('');
  const [filteredModels, setFilteredModels] = useState<Model[]>([]);
  const [filteredGenerations, setFilteredGenerations] = useState<Generation[]>([]);
  
  const { register, handleSubmit, reset, watch, setValue, formState: { errors } } = useForm<VariationFormData>({
    defaultValues: {
      generation_id: variation.generation_id,
      variation: variation.variation
    }
  });
  
  const supabase = createClient();

  // Set initial brand and model IDs based on the generation
  useEffect(() => {
    if (isOpen && variation.generation_id) {
      const generation = generations.find(g => g.id === variation.generation_id);
      if (generation) {
        setSelectedModelId(generation.model_id);
        
        const model = models.find(m => m.id === generation.model_id);
        if (model) {
          setSelectedBrandId(model.brand_id);
        }
      }
    }
  }, [isOpen, variation, generations, models]);

  // Reset form when variation changes
  useEffect(() => {
    if (isOpen) {
      reset({
        generation_id: variation.generation_id,
        variation: variation.variation
      });
    }
  }, [variation, isOpen, reset]);

  // Filter models based on selected brand
  useEffect(() => {
    if (selectedBrandId) {
      const filtered = models.filter(model => model.brand_id === selectedBrandId);
      setFilteredModels(filtered);
      
      // Reset model selection if the current model doesn't belong to the selected brand
      const modelId = selectedModelId;
      if (modelId && !filtered.some(model => model.id === modelId)) {
        setSelectedModelId('');
        setValue('generation_id', undefined);
      }
    } else {
      setFilteredModels(models);
    }
  }, [selectedBrandId, models, selectedModelId, setValue]);

  // Filter generations based on selected model
  useEffect(() => {
    if (selectedModelId) {
      const filtered = generations.filter(generation => generation.model_id === selectedModelId);
      setFilteredGenerations(filtered);
      
      // Reset generation selection if the current generation doesn't belong to the selected model
      const generationId = watch('generation_id');
      if (generationId && !filtered.some(generation => generation.id === generationId)) {
        setValue('generation_id', undefined);
      }
    } else {
      setFilteredGenerations(generations);
    }
  }, [selectedModelId, generations, watch, setValue]);

  const onSubmit = async (data: VariationFormData) => {
    setIsSubmitting(true);
    setError(null);
    
    try {
      // Update the variation
      const { error: updateError } = await supabase
        .from('car_variation')
        .update({
          generation_id: data.generation_id,
          variation: data.variation
        })
        .eq('id', variation.id);
        
      if (updateError) throw updateError;
      
      onSuccess();
      onClose();
    } catch (err: any) {
      console.error('Error updating variation:', err);
      setError(err.message || 'Failed to update variation. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.2 }}
          className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex justify-between items-center p-6 border-b border-gray-200">
            <h3 className="text-xl font-semibold text-gray-800">Edit Variation</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              disabled={isSubmitting}
            >
              <X size={24} />
            </button>
          </div>
          
          <form onSubmit={handleSubmit(onSubmit)} className="p-6">
            <div className="mb-6">
              <label htmlFor="brand_id" className="block text-sm font-medium text-gray-700 mb-2">
                Brand
              </label>
              <select
                id="brand_id"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                value={selectedBrandId}
                onChange={(e) => {
                  setSelectedBrandId(e.target.value ? Number(e.target.value) : '');
                  setSelectedModelId('');
                }}
              >
                <option value="">Select a brand</option>
                {brands.map((brand) => (
                  <option key={brand.brand_id} value={brand.brand_id}>
                    {brand.brand_name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="mb-6">
              <label htmlFor="model_id" className="block text-sm font-medium text-gray-700 mb-2">
                Model
              </label>
              <select
                id="model_id"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                value={selectedModelId}
                onChange={(e) => setSelectedModelId(e.target.value ? Number(e.target.value) : '')}
                disabled={!selectedBrandId}
              >
                <option value="">Select a model</option>
                {filteredModels.map((model) => (
                  <option key={model.id} value={model.id}>
                    {model.model_name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="mb-6">
              <label htmlFor="generation_id" className="block text-sm font-medium text-gray-700 mb-2">
                Generation
              </label>
              <select
                id="generation_id"
                className={`w-full px-4 py-2 border ${errors.generation_id ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500`}
                {...register('generation_id', { 
                  required: 'Generation is required',
                  valueAsNumber: true
                })}
                disabled={!selectedModelId}
              >
                <option value="">Select a generation</option>
                {filteredGenerations.map((generation) => (
                  <option key={generation.id} value={generation.id}>
                    {generation.name} ({generation.start_production_year} - {generation.end_production_year || 'Present'})
                  </option>
                ))}
              </select>
              {errors.generation_id && (
                <p className="mt-1 text-sm text-red-600">{errors.generation_id.message}</p>
              )}
            </div>
            
            <div className="mb-6">
              <label htmlFor="variation" className="block text-sm font-medium text-gray-700 mb-2">
                Variation Name
              </label>
              <input
                id="variation"
                type="text"
                className={`w-full px-4 py-2 border ${errors.variation ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500`}
                placeholder="Enter variation name (e.g., Sedan, Hatchback, SUV)"
                {...register('variation', { required: 'Variation name is required' })}
              />
              {errors.variation && (
                <p className="mt-1 text-sm text-red-600">{errors.variation.message}</p>
              )}
            </div>
            
            {error && (
              <div className="bg-red-50 text-red-600 p-4 rounded-md mb-6">
                {error}
              </div>
            )}
            
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 transition-colors"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default EditVariationModal;
