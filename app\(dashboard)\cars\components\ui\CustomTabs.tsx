'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/app/utils/cn';
import { Tab } from '@/app/components/ui/Tabs';

interface CustomTabsProps {
  tabs: Tab[];
  activeTabId: string | number;
  onTabChange: (tabId: string | number) => void;
  className?: string;
}

const CustomTabs: React.FC<CustomTabsProps> = ({
  tabs,
  activeTabId,
  onTabChange,
  className,
}) => {
  return (
    <div className={cn('w-full', className)}>
      <div className="grid grid-cols-5 gap-2 mb-8 bg-gray-100 p-1 rounded-lg">
        {tabs.map((tab) => (
          <motion.button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={cn(
              'py-2 px-4 text-sm font-medium rounded-md transition-all duration-200 text-center',
              activeTabId === tab.id
                ? 'bg-teal-600 text-white shadow-md'
                : 'text-gray-600 hover:bg-gray-200'
            )}
            whileHover={{ y: -2 }}
            whileTap={{ y: 0 }}
            aria-current={activeTabId === tab.id ? 'page' : undefined}
          >
            {tab.label}
          </motion.button>
        ))}
      </div>
    </div>
  );
};

export default CustomTabs;
