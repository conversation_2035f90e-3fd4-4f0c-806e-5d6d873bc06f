'use client';

import React, { useState, FormEvent, KeyboardEvent } from 'react';
import { Search, SlidersHorizontal, X } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface SearchBarProps {
  onSearch?: (value: string) => void;
  onOpenSettings?: () => void;
  placeholder?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({
  onSearch,
  onOpenSettings,
  placeholder = 'Search parts...'
}) => {
  const [searchValue, setSearchValue] = useState('');
  const router = useRouter();

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (searchValue.trim()) {
      if (onSearch) {
        onSearch(searchValue);
      } else {
        // Default behavior: navigate to parts page with search query
        router.push(`/parts?query=${encodeURIComponent(searchValue)}&page=1`);
      }
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSubmit(e as unknown as FormEvent);
    }
  };

  const clearSearch = () => {
    setSearchValue('');
  };

  return (
    <form onSubmit={handleSubmit} className="relative w-full">
      <div className="relative flex items-center">
        <div className="absolute left-3 text-gray-400">
          <Search size={20} />
        </div>
        <input
          type="text"
          className="w-full h-12 pl-10 pr-24 rounded-lg border border-gray-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
          placeholder={placeholder}
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          onKeyDown={handleKeyDown}
        />
        {searchValue && (
          <button
            type="button"
            onClick={clearSearch}
            className="absolute right-16 p-1 rounded-full hover:bg-gray-100 text-gray-400 hover:text-gray-600"
          >
            <X size={20} />
          </button>
        )}
        <button
          type="submit"
          className="absolute right-10 p-1 rounded-full hover:bg-gray-100 text-gray-400 hover:text-gray-600"
        >
          <Search size={20} />
        </button>
        <button
          type="button"
          onClick={onOpenSettings}
          className="absolute right-3 p-1 rounded-full hover:bg-gray-100 text-gray-400 hover:text-gray-600"
        >
          <SlidersHorizontal size={20} />
        </button>
      </div>
    </form>
  );
};

export default SearchBar;