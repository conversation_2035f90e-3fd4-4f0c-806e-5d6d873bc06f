-- Create client_activities table
CREATE TABLE IF NOT EXISTS public.client_activities (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id uuid NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,
    activity_type TEXT NOT NULL, -- e.g., 'login', 'purchase', 'profile_update', 'car_added'
    description TEXT NOT NULL,
    metadata JSONB, -- Optional additional data related to the activity
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_client_activities_client_id ON public.client_activities(client_id);
CREATE INDEX IF NOT EXISTS idx_client_activities_activity_type ON public.client_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_client_activities_created_at ON public.client_activities(created_at);

-- Add trigger for updated_at
DROP TRIGGER IF EXISTS set_timestamp_client_activities ON public.client_activities;
CREATE TRIGGER set_timestamp_client_activities
BEFORE UPDATE ON public.client_activities
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Add comments
COMMENT ON TABLE public.client_activities IS 'Stores activities and interactions for clients';
COMMENT ON COLUMN public.client_activities.client_id IS 'Reference to the client';
COMMENT ON COLUMN public.client_activities.activity_type IS 'Type of activity (login, purchase, etc.)';
COMMENT ON COLUMN public.client_activities.description IS 'Human-readable description of the activity';
COMMENT ON COLUMN public.client_activities.metadata IS 'Additional JSON data related to the activity';
