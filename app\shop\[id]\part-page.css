/* Part page specific styles */
.part-image-gallery img {
  transition: transform 0.3s ease;
}

.part-image-gallery img:hover {
  transform: scale(1.05);
}

.part-attributes {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

@media (max-width: 640px) {
  .part-attributes {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card hover effects */
.part-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.part-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
