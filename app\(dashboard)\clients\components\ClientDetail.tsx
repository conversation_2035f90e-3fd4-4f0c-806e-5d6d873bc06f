'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/app/libs/supabase/client';
import { motion } from 'framer-motion';
import Link from 'next/link';
import {
  User,
  Wrench,
  Store,
  Briefcase,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Edit,
  Trash2,
  UserPlus,
  Car,
  Plus,
  Check,
  X,
  AlertTriangle,
  Clock,
  History,
  Star
} from 'lucide-react';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import toast from 'react-hot-toast';
import ClientActivityFeed from './ClientActivityFeed';

// Types
interface Client {
  id: string;
  name: string;
  category_id: string;
  category_name?: string;
  client_type: 'credit' | 'cash';
  phone_number: string;
  email: string | null;
  can_receive_credit: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface ClientCategory {
  id: string;
  name: string;
  description: string | null;
}

interface CategoryField {
  id: string;
  category_id: string;
  field_name: string;
  field_label: string;
  field_type: string;
  is_required: boolean;
  description: string | null;
}

interface ClientData {
  id: string;
  client_id: string;
  field_id: string;
  value: string;
  field_name?: string;
  field_label?: string;
}

interface ClientCar {
  id: string;
  client_id: string;
  variation_trim_id: number;
  engine_id: number | null;
  registration_number: string | null;
  vin_number: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
  brand_name?: string;
  model_name?: string;
  generation_name?: string;
  variation_name?: string;
  trim_name?: string;
  engine_capacity?: string;
  engine_code?: string;
  fuel_type?: string;
}

// Delete Confirmation Modal
interface DeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  clientName: string;
}

const DeleteModal: React.FC<DeleteModalProps> = ({ isOpen, onClose, onConfirm, clientName }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Delete Client</h3>
        <p className="text-gray-600 mb-4">
          Are you sure you want to delete <span className="font-medium">{clientName}</span>? This action cannot be undone.
        </p>

        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Delete
          </button>
        </div>
      </motion.div>
    </div>
  );
};

// Invite Modal
interface InviteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (roleId: string) => void;
  clientName: string;
  clientEmail: string | null;
}

const InviteModal: React.FC<InviteModalProps> = ({ isOpen, onClose, onConfirm, clientName, clientEmail }) => {
  const [selectedRole, setSelectedRole] = useState('');
  const [roles, setRoles] = useState<{id: string, name: string}[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchRoles = async () => {
      if (!isOpen) return;

      setIsLoading(true);
      try {
        const supabase = createClient();
        const { data, error } = await supabase
          .from('roles')
          .select('id, name')
          .order('name');

        if (error) throw error;
        setRoles(data || []);

        // Set default role if available
        if (data && data.length > 0) {
          const clientRole = data.find(role => role.name === 'Client');
          setSelectedRole(clientRole?.id || data[0].id);
        }
      } catch (err) {
        console.error('Error fetching roles:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRoles();
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Invite Client to System</h3>

        {!clientEmail ? (
          <div className="mb-4">
            <div className="flex items-center text-amber-600 bg-amber-50 p-3 rounded-md">
              <AlertTriangle className="w-5 h-5 mr-2" />
              <p>This client doesn't have an email address. Please add an email before sending an invitation.</p>
            </div>
          </div>
        ) : (
          <>
            <p className="text-gray-600 mb-4">
              Send an invitation to <span className="font-medium">{clientName}</span> to create an account and access the system.
            </p>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Assign Role</label>
              {isLoading ? (
                <div className="flex justify-center py-2">
                  <LoadingSpinner size="sm" />
                </div>
              ) : (
                <select
                  value={selectedRole}
                  onChange={(e) => setSelectedRole(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                >
                  {roles.map((role) => (
                    <option key={role.id} value={role.id}>
                      {role.name}
                    </option>
                  ))}
                </select>
              )}
            </div>
          </>
        )}

        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          {clientEmail && (
            <button
              onClick={() => onConfirm(selectedRole)}
              className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
              disabled={isLoading || !selectedRole}
            >
              Send Invitation
            </button>
          )}
        </div>
      </motion.div>
    </div>
  );
};

// Delete Car Modal
interface DeleteCarModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  carDetails: string;
}

const DeleteCarModal: React.FC<DeleteCarModalProps> = ({ isOpen, onClose, onConfirm, carDetails }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Delete Car</h3>
        <p className="text-gray-600 mb-4">
          Are you sure you want to delete <span className="font-medium">{carDetails}</span>? This action cannot be undone.
        </p>

        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            Delete
          </button>
        </div>
      </motion.div>
    </div>
  );
};

// Main Client Detail Component
interface ClientDetailProps {
  clientId: string;
}

const ClientDetail: React.FC<ClientDetailProps> = ({ clientId }) => {
  const router = useRouter();
  const [client, setClient] = useState<Client | null>(null);
  const [category, setCategory] = useState<ClientCategory | null>(null);
  const [customFields, setCustomFields] = useState<ClientData[]>([]);
  const [cars, setCars] = useState<ClientCar[]>([]);
  const [contactPersons, setContactPersons] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Modal states
  const [deleteModal, setDeleteModal] = useState({ isOpen: false });
  const [inviteModal, setInviteModal] = useState({ isOpen: false });
  const [deleteCarModal, setDeleteCarModal] = useState({ isOpen: false, carId: '', carDetails: '' });

  // Fetch client data
  useEffect(() => {
    const fetchClientData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const supabase = createClient();

        // Fetch client with category
        const { data: clientData, error: clientError } = await supabase
          .from('clients')
          .select(`
            *,
            client_categories(*)
          `)
          .eq('id', clientId)
          .single();

        if (clientError) throw new Error(clientError.message);

        // Set client and category data
        setClient({
          ...clientData,
          category_name: clientData.client_categories?.name
        });
        setCategory(clientData.client_categories);

        // Fetch custom fields data
        const { data: fieldsData, error: fieldsError } = await supabase
          .from('client_data')
          .select(`
            *,
            category_fields(field_name, field_label)
          `)
          .eq('client_id', clientId);

        if (fieldsError) throw new Error(fieldsError.message);

        // Process custom fields data
        const processedFields = fieldsData.map(field => ({
          ...field,
          field_name: field.category_fields?.field_name,
          field_label: field.category_fields?.field_label
        }));

        setCustomFields(processedFields);

        // If client is an individual, fetch their cars
        if (clientData.client_categories?.name === 'Individual') {
          const { data: carsData, error: carsError } = await supabase
            .from('client_cars')
            .select(`
              *,
              variation_trim:variation_trim_id(
                trim,
                car_variation:variation_id(
                  variation,
                  car_generation:generation_id(
                    name,
                    car_models:model_id(
                      model_name,
                      car_brands:brand_id(
                        brand_name
                      )
                    )
                  )
                )
              ),
              engines:engine_id(
                capacity,
                engine_code,
                fuel_type
              )
            `)
            .eq('client_id', clientId);

          if (carsError) throw new Error(carsError.message);

          // Process cars data
          const processedCars = carsData.map(car => ({
            ...car,
            brand_name: car.variation_trim?.car_variation?.car_generation?.car_models?.car_brands?.brand_name,
            model_name: car.variation_trim?.car_variation?.car_generation?.car_models?.model_name,
            generation_name: car.variation_trim?.car_variation?.car_generation?.name,
            variation_name: car.variation_trim?.car_variation?.variation,
            trim_name: car.variation_trim?.trim,
            engine_capacity: car.engines?.capacity,
            engine_code: car.engines?.engine_code,
            fuel_type: car.engines?.fuel_type
          }));

          setCars(processedCars);
        }

        // Fetch contact persons for garage/shop/broker clients
        if (['Garage', 'Shop', 'Broker'].includes(clientData.client_categories?.name)) {
          const { data: contactsData, error: contactsError } = await supabase
            .from('client_contacts')
            .select('*')
            .eq('client_id', clientId)
            .order('is_primary', { ascending: false });

          if (contactsError) {
            console.warn('Error fetching contact persons:', contactsError);
          } else {
            setContactPersons(contactsData || []);
          }
        }
      } catch (err) {
        console.error('Error fetching client data:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchClientData();
  }, [clientId]);

  // Handle delete client
  const handleDeleteClient = async () => {
    try {
      const supabase = createClient();
      const { error } = await supabase
        .from('clients')
        .delete()
        .eq('id', clientId);

      if (error) throw error;

      // Redirect to clients list
      router.push('/clients');
    } catch (err) {
      console.error('Error deleting client:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  };

  // Handle invite client
  const handleInviteClient = async (roleId: string) => {
    if (!client || !client.email) return;

    try {
      // Show loading toast
      const loadingToast = toast.loading('Sending invitation...');

      // Call the client invitation API
      const response = await fetch('/api/clients/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: client.email,
          roleId: roleId,
          clientId: client.id
        }),
      });

      const data = await response.json();

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send invitation');
      }

      // Close the modal
      setInviteModal({ isOpen: false });

      // Show success message
      toast.success(`Invitation sent to ${client.email}`);
    } catch (err) {
      console.error('Error sending invitation:', err);
      toast.error(err instanceof Error ? err.message : 'Failed to send invitation');
    }
  };

  // Handle delete car
  const handleDeleteCar = async () => {
    if (!deleteCarModal.carId) return;

    try {
      const supabase = createClient();
      const { error } = await supabase
        .from('client_cars')
        .delete()
        .eq('id', deleteCarModal.carId);

      if (error) throw error;

      // Update cars list
      setCars(cars.filter(car => car.id !== deleteCarModal.carId));

      // Close modal
      setDeleteCarModal({ isOpen: false, carId: '', carDetails: '' });
    } catch (err) {
      console.error('Error deleting car:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  };

  // Get category icon
  const getCategoryIcon = () => {
    if (!client?.category_name) return <User className="w-6 h-6" />;

    switch(client.category_name) {
      case 'Individual':
        return <User className="w-6 h-6" />;
      case 'Garage':
        return <Wrench className="w-6 h-6" />;
      case 'Shop':
        return <Store className="w-6 h-6" />;
      case 'Broker':
        return <Briefcase className="w-6 h-6" />;
      default:
        return <User className="w-6 h-6" />;
    }
  };

  // Format car details
  const formatCarDetails = (car: ClientCar) => {
    const parts = [];

    if (car.brand_name) parts.push(car.brand_name);
    if (car.model_name) parts.push(car.model_name);
    if (car.generation_name) parts.push(car.generation_name);
    if (car.variation_name) parts.push(car.variation_name);
    if (car.trim_name) parts.push(car.trim_name);

    return parts.join(' ');
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size={24} />
      </div>
    );
  }

  // Render error state
  if (error || !client) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <p>{error || 'Client not found'}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Client Header */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between">
          <div className="flex items-center mb-4 md:mb-0">
            <div className="w-16 h-16 rounded-lg bg-gray-200 mr-4 flex items-center justify-center text-gray-600 text-xl font-bold">
              {getCategoryIcon()}
            </div>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">{client.name}</h1>
              <div className="flex items-center mt-1">
                <div className="text-gray-600 mr-3 flex items-center">
                  {getCategoryIcon()}
                  <span className="ml-1">{client.category_name}</span>
                </div>
                <span className={`px-3 py-1 text-sm rounded-full ${
                  client.is_active
                    ? 'bg-green-100 text-green-800 border border-green-200'
                    : 'bg-gray-100 text-gray-800 border border-gray-200'
                }`}>
                  {client.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
          </div>
          <div className="flex space-x-2">
            <Link
              href={`/clients/edit/${client.id}`}
              className="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 flex items-center"
            >
              <Edit className="w-4 h-4 mr-2" /> Edit
            </Link>
            <button
              onClick={() => setInviteModal({ isOpen: true })}
              className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 flex items-center"
            >
              <UserPlus className="w-4 h-4 mr-2" /> Invite
            </button>
          </div>
        </div>
      </div>

      {/* Client Details Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Contact Information */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Contact Information</h2>
          <div className="space-y-4">
            {client.email && (
              <div className="flex items-start">
                <Mail className="w-5 h-5 text-gray-500 mr-3 mt-0.5" />
                <div>
                  <p className="text-sm text-gray-500">Email</p>
                  <a href={`mailto:${client.email}`} className="text-teal-600 hover:underline">
                    {client.email}
                  </a>
                </div>
              </div>
            )}

            <div className="flex items-start">
              <Phone className="w-5 h-5 text-gray-500 mr-3 mt-0.5" />
              <div>
                <p className="text-sm text-gray-500">Phone</p>
                <a href={`tel:${client.phone_number}`} className="text-gray-900">
                  {client.phone_number}
                </a>
              </div>
            </div>

            <div className="flex items-start">
              <Calendar className="w-5 h-5 text-gray-500 mr-3 mt-0.5" />
              <div>
                <p className="text-sm text-gray-500">Client Since</p>
                <p className="text-gray-900">
                  {new Date(client.created_at).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Client Type Information */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Client Type</h2>
          <div className="space-y-4">
            <div className="flex items-center">
              <div className={`px-3 py-1 rounded-full ${
                client.client_type === 'credit'
                  ? 'bg-blue-100 text-blue-800 border border-blue-200'
                  : 'bg-orange-100 text-orange-800 border border-orange-200'
              }`}>
                {client.client_type === 'credit' ? 'Credit Client' : 'Cash Client'}
              </div>
            </div>

            {client.client_type === 'credit' && (
              <div className="flex items-center">
                <div className={`flex items-center ${
                  client.can_receive_credit ? 'text-green-600' : 'text-red-600'
                }`}>
                  {client.can_receive_credit ? (
                    <>
                      <Check className="w-5 h-5 mr-2" />
                      <span>Can receive credit</span>
                    </>
                  ) : (
                    <>
                      <X className="w-5 h-5 mr-2" />
                      <span>Cannot receive credit</span>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Custom Fields */}
        {customFields.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">
              {client.category_name} Details
            </h2>
            <div className="space-y-4">
              {customFields.map((field) => {
                // Get primary contact person for Contact Person Name field
                const primaryContact = contactPersons.find(contact => contact.is_primary);
                const isContactPersonField = field.field_label?.toLowerCase().includes('contact person') ||
                                           field.field_name?.toLowerCase().includes('contact_person');

                // Use primary contact name if this is a contact person field and we have a primary contact
                const displayValue = isContactPersonField && primaryContact
                  ? primaryContact.name
                  : (field.field_name === 'boolean'
                      ? (field.value === 'true' ? 'Yes' : 'No')
                      : field.value);

                return (
                  <div key={field.id} className="flex items-start">
                    <div>
                      <p className="text-sm text-gray-500">{field.field_label}</p>
                      <p className="text-gray-900">
                        {displayValue}
                        {isContactPersonField && primaryContact && (
                          <span className="ml-2 text-xs text-teal-600 bg-teal-50 px-2 py-1 rounded-full">
                            Auto-filled from primary contact
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Contact Persons */}
        {contactPersons.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">Contact Persons</h2>
            <div className="space-y-3">
              {contactPersons.map((contact, index) => (
                <div key={contact.id || index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <User className="w-5 h-5 text-gray-400 mr-3" />
                    <div>
                      <div className="flex items-center">
                        <p className="font-medium text-gray-900">{contact.name}</p>
                        {contact.is_primary && (
                          <span className="ml-2 flex items-center text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                            <Star className="w-3 h-3 mr-1" />
                            Primary
                          </span>
                        )}
                      </div>
                      <div className="flex items-center space-x-4 mt-1">
                        <div className="flex items-center text-sm text-gray-600">
                          <Phone className="w-4 h-4 mr-1" />
                          {contact.phone_number}
                        </div>
                        {contact.email && (
                          <div className="flex items-center text-sm text-gray-600">
                            <Mail className="w-4 h-4 mr-1" />
                            {contact.email}
                          </div>
                        )}
                        {contact.position && (
                          <div className="flex items-center text-sm text-gray-600">
                            <Briefcase className="w-4 h-4 mr-1" />
                            {contact.position}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <a
                      href={`tel:${contact.phone_number}`}
                      className="p-2 text-blue-600 hover:bg-blue-50 rounded-full"
                      title="Call"
                    >
                      <Phone className="w-4 h-4" />
                    </a>
                    {contact.email && (
                      <a
                        href={`mailto:${contact.email}`}
                        className="p-2 text-gray-600 hover:bg-gray-50 rounded-full"
                        title="Email"
                      >
                        <Mail className="w-4 h-4" />
                      </a>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Activity Feed */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold text-gray-800">Recent Activity</h2>
          <Link
            href={`/clients/${client.id}/activity`}
            className="text-teal-600 hover:text-teal-800 text-sm font-medium flex items-center"
          >
            <History className="w-4 h-4 mr-1" /> View All
          </Link>
        </div>

        <ClientActivityFeed clientId={client.id} limit={5} showPagination={false} />
      </div>

      {/* Cars Section (for Individual clients) */}
      {client.category_name === 'Individual' && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-gray-800">Client Cars</h2>
            <Link
              href={`/clients/${client.id}/cars/add`}
              className="px-3 py-1 bg-teal-600 text-white rounded-md hover:bg-teal-700 flex items-center text-sm"
            >
              <Plus className="w-4 h-4 mr-1" /> Add Car
            </Link>
          </div>

          {cars.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {cars.map((car) => (
                <div key={car.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center">
                      <Car className="w-5 h-5 text-gray-500 mr-2" />
                      <h3 className="font-medium text-gray-900">
                        {formatCarDetails(car)}
                      </h3>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Link
                        href={`/clients/${client.id}/cars/edit/${car.id}`}
                        className="p-1 text-blue-500 hover:bg-blue-50 rounded"
                      >
                        <Edit className="w-4 h-4" />
                      </Link>
                      <button
                        onClick={() => setDeleteCarModal({
                          isOpen: true,
                          carId: car.id,
                          carDetails: formatCarDetails(car)
                        })}
                        className="p-1 text-red-500 hover:bg-red-50 rounded"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  {car.registration_number && (
                    <div className="text-sm text-gray-600 mb-1">
                      <span className="font-medium">Reg:</span> {car.registration_number}
                    </div>
                  )}

                  {car.engine_code && (
                    <div className="text-sm text-gray-600 mb-1">
                      <span className="font-medium">Engine:</span> {car.engine_code}
                      {car.engine_capacity && ` (${car.engine_capacity})`}
                      {car.fuel_type && ` ${car.fuel_type}`}
                    </div>
                  )}

                  {car.notes && (
                    <div className="text-sm text-gray-600 mt-2 border-t pt-2">
                      {car.notes}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Car className="w-12 h-12 mx-auto mb-3 text-gray-400" />
              <p>No cars added yet</p>
              <Link
                href={`/clients/${client.id}/cars/add`}
                className="mt-2 inline-block text-teal-600 hover:underline"
              >
                Add a car
              </Link>
            </div>
          )}
        </div>
      )}

      {/* Danger Zone */}
      <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-red-500">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Danger Zone</h2>
        <p className="text-gray-600 mb-4">
          Permanently delete this client and all associated data. This action cannot be undone.
        </p>
        <button
          onClick={() => setDeleteModal({ isOpen: true })}
          className="px-4 py-2 bg-white border border-red-500 rounded-md text-red-600 hover:bg-red-50 flex items-center"
        >
          <Trash2 className="w-4 h-4 mr-2" /> Delete Client
        </button>
      </div>

      {/* Modals */}
      <DeleteModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false })}
        onConfirm={handleDeleteClient}
        clientName={client.name}
      />

      <InviteModal
        isOpen={inviteModal.isOpen}
        onClose={() => setInviteModal({ isOpen: false })}
        onConfirm={handleInviteClient}
        clientName={client.name}
        clientEmail={client.email}
      />

      <DeleteCarModal
        isOpen={deleteCarModal.isOpen}
        onClose={() => setDeleteCarModal({ isOpen: false, carId: '', carDetails: '' })}
        onConfirm={handleDeleteCar}
        carDetails={deleteCarModal.carDetails}
      />
    </div>
  );
};

export default ClientDetail;
