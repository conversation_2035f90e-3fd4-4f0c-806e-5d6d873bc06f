import React from 'react';
import { View, StyleSheet } from 'react-native';
import { ActivityIndicator, Text } from 'react-native-paper';
import { theme } from '@/theme';
import { CONFIG } from '@/constants/config';

interface LoadingScreenProps {
  message?: string;
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({ 
  message = 'Loading...' 
}) => {
  return (
    <View style={styles.container}>
      <ActivityIndicator 
        size="large" 
        color={theme.colors.primary}
        style={styles.spinner}
      />
      <Text variant="bodyLarge" style={styles.message}>
        {message}
      </Text>
      <Text variant="bodySmall" style={styles.appName}>
        {CONFIG.APP_NAME}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    padding: 20,
  },
  spinner: {
    marginBottom: 20,
  },
  message: {
    textAlign: 'center',
    color: theme.colors.onBackground,
    marginBottom: 10,
  },
  appName: {
    textAlign: 'center',
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
});
