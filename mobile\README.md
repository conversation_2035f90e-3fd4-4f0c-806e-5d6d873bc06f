# AutoFlow Mobile App

React Native mobile application for the AutoFlow auto parts platform.

## Features

- **Authentication**: Email/password login with OTP verification
- **Parts Catalog**: Browse and search auto parts
- **Role-based Access**: Different features for employees and super admins
- **M-PESA Integration**: Mobile payment support
- **Offline Support**: Cached data and offline functionality
- **Real-time Updates**: Live data synchronization

## Tech Stack

- **React Native 0.73.2**
- **TypeScript**
- **React Navigation 6**
- **React Native Paper** (Material Design)
- **Zustand** (State Management)
- **React Query** (Data Fetching)
- **Supabase** (Backend & Auth)
- **React Native Vector Icons**

## Prerequisites

- Node.js 16+
- React Native CLI
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

## Installation

1. **Install dependencies:**
   ```bash
   cd mobile
   npm install
   ```

2. **Install iOS dependencies (iOS only):**
   ```bash
   cd ios && pod install && cd ..
   ```

3. **Configure environment variables:**
   - Copy `.env.example` to `.env`
   - Update the values with your backend configuration

## Running the App

### Development

```bash
# Start Metro bundler
npm start

# Run on Android
npm run android

# Run on iOS
npm run ios
```

### Production Build

```bash
# Android
npm run build:android

# iOS
npm run build:ios
```

## Project Structure

```
src/
├── components/          # Reusable UI components
├── constants/          # App constants and configuration
├── navigation/         # Navigation setup
├── screens/           # Screen components
│   ├── auth/         # Authentication screens
│   ├── main/         # Main app screens
│   └── shop/         # Shop-related screens
├── services/          # API services
├── store/            # State management (Zustand)
├── theme/            # Theme configuration
├── types/            # TypeScript type definitions
└── utils/            # Utility functions
```

## API Integration

The app connects to your existing Next.js backend:

- **Base URL**: Configured in `.env` file
- **Authentication**: Supabase Auth with JWT tokens
- **Endpoints**: RESTful API endpoints for parts, users, sales, etc.

## Key Features

### Authentication Flow
1. Email/password validation
2. OTP verification via email
3. JWT token storage
4. Role-based access control

### Parts Management
- Browse parts catalog
- Search and filter functionality
- Category-based navigation
- Part details with images
- Car compatibility filtering

### M-PESA Integration
- Mobile payment initiation
- Payment status tracking
- Transaction history

### Offline Support
- Cached parts data
- Offline browsing
- Background sync

## Configuration

### Environment Variables

```env
# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# API Configuration
API_BASE_URL=your_production_api_url
API_BASE_URL_DEV=your_development_api_url

# M-PESA Configuration
MPESA_CONSUMER_KEY=your_mpesa_consumer_key
MPESA_CONSUMER_SECRET=your_mpesa_consumer_secret
MPESA_SHORT_CODE=your_mpesa_short_code
MPESA_ENVIRONMENT=sandbox_or_production
```

### Android Configuration

1. **Update `android/app/build.gradle`:**
   - Set your app's package name
   - Configure signing for release builds

2. **Update `android/app/src/main/AndroidManifest.xml`:**
   - Set app permissions
   - Configure deep linking if needed

### iOS Configuration

1. **Update `ios/AutoflowMobile/Info.plist`:**
   - Set app display name
   - Configure URL schemes if needed

2. **Configure signing in Xcode:**
   - Set your development team
   - Configure provisioning profiles

## Development Guidelines

### Code Style
- Use TypeScript for type safety
- Follow React Native best practices
- Use functional components with hooks
- Implement proper error handling

### State Management
- Use Zustand for global state
- Persist important data (auth, favorites)
- Keep state minimal and normalized

### API Calls
- Use React Query for data fetching
- Implement proper loading states
- Handle network errors gracefully
- Cache responses appropriately

## Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage
```

## Deployment

### Android
1. Generate signed APK or AAB
2. Upload to Google Play Console
3. Configure app signing and metadata

### iOS
1. Archive the app in Xcode
2. Upload to App Store Connect
3. Configure app metadata and screenshots

## Troubleshooting

### Common Issues

1. **Metro bundler issues:**
   ```bash
   npx react-native start --reset-cache
   ```

2. **Android build issues:**
   ```bash
   cd android && ./gradlew clean && cd ..
   ```

3. **iOS build issues:**
   ```bash
   cd ios && pod install && cd ..
   ```

### Debug Mode

Enable debug mode in development:
- Shake device or press Cmd+D (iOS) / Cmd+M (Android)
- Enable "Debug JS Remotely" for debugging

## Contributing

1. Follow the existing code structure
2. Add proper TypeScript types
3. Test on both platforms
4. Update documentation as needed

## Support

For issues and questions:
- Check the troubleshooting section
- Review React Native documentation
- Contact the development team
