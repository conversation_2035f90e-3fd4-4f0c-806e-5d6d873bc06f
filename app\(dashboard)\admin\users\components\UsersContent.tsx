'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { usePermissions } from '@/app/hooks/usePermissions';
import { getUsers, getUserRoles, getRoles, assignRoleToUser, removeRoleFromUser } from '@/app/libs/rbac/api';
import Spinner from '@/app/components/ui/Spinner';

interface User {
  id: string;
  full_name: string | null;
  username: string | null;
  phone: string | null;
}

interface Role {
  id: string;
  name: string;
  description: string | null;
}

const UsersContent = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userRoles, setUserRoles] = useState<Role[]>([]);
  const [isLoadingRoles, setIsLoadingRoles] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { hasPermission, isLoading: isPermissionLoading, error: permissionError } = usePermissions('admin:assign_roles');
  const router = useRouter();

  // Effect 1: Check permission and redirect if necessary
  useEffect(() => {
    // Wait for permission check to complete
    if (!isPermissionLoading) {
      console.log(`UsersPage: Permission check complete. hasPermission('admin:assign_roles'): ${hasPermission}`);
      if (!hasPermission) {
        console.log('UsersPage: Redirecting to /admin due to missing permission.');
        // Optionally set an error message before redirecting
        setError('You do not have permission to manage user roles.'); 
        // Add a small delay before redirecting to allow error message to potentially render
        setTimeout(() => router.push('/admin'), 50);
      }
    }
  }, [hasPermission, isPermissionLoading, router]);

  // Effect 2: Fetch data only if permission is granted and check is complete
  useEffect(() => {
    // Only fetch if permission is granted and the check isn't loading
    if (hasPermission && !isPermissionLoading) {
      console.log('UsersPage: Permission granted. Fetching users and roles...');
      fetchUsers();
      fetchRoles();
    } else if (!isPermissionLoading && !hasPermission) {
      // If permission is denied, ensure loading is set to false so the component doesn't hang
      setIsLoading(false);
    }
  }, [hasPermission, isPermissionLoading]); // Depends only on permission state

  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await getUsers();
      if (error) throw error;
      setUsers(data || []);
    } catch (err) {
      setError('Failed to fetch users. Please try again.');
      console.error('Error fetching users:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRoles = async () => {
    try {
      const { data, error } = await getRoles();
      if (error) throw error;
      setRoles(data || []);
    } catch (err) {
      setError('Failed to fetch roles. Please try again.');
      console.error('Error fetching roles:', err);
    }
  };

  const fetchUserRoles = async (userId: string) => {
    setIsLoadingRoles(true);
    try {
      const { data, error } = await getUserRoles(userId);
      if (error) throw error;
      setUserRoles(data || []);
    } catch (err) {
      setError('Failed to fetch user roles. Please try again.');
      console.error('Error fetching user roles:', err);
    } finally {
      setIsLoadingRoles(false);
    }
  };

  const handleSelectUser = async (user: User) => {
    setSelectedUser(user);
    await fetchUserRoles(user.id);
  };

  const handleToggleRole = async (roleId: string, isAssigned: boolean) => {
    if (!selectedUser) return;
    
    setIsSubmitting(true);
    try {
      if (isAssigned) {
        // Remove role
        const { error } = await removeRoleFromUser(selectedUser.id, roleId);
        if (error) throw error;
      } else {
        // Assign role
        const { error } = await assignRoleToUser(selectedUser.id, roleId);
        if (error) throw error;
      }
      
      // Refresh user roles
      await fetchUserRoles(selectedUser.id);
    } catch (err) {
      setError(`Failed to ${isAssigned ? 'remove' : 'assign'} role. Please try again.`);
      console.error(`Error ${isAssigned ? 'removing' : 'assigning'} role:`, err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const filteredUsers = users.filter(user => {
    const fullName = user.full_name?.toLowerCase() || '';
    const username = user.username?.toLowerCase() || '';
    const phone = user.phone?.toLowerCase() || '';
    const query = searchQuery.toLowerCase();
    
    return fullName.includes(query) || username.includes(query) || phone.includes(query);
  });

  // Render loading spinner while permission check OR initial data load is happening
  if (isPermissionLoading || isLoading) {
    return (
      <div className="flex justify-center py-10">
        <Spinner size="lg" />
      </div>
    );
  }

  // If permission check failed or explicit error is set, show error
  // (Handles the case where redirect might be slightly delayed or fails)
  if (!hasPermission || error) {
     return (
      <div className="rounded-md border border-red-200 bg-red-50 p-6 text-center shadow-sm">
        <h2 className="mb-2 text-xl font-semibold text-red-700">Access Denied</h2>
        <p className="mb-4 text-red-600">
          {error || 'You do not have the required permissions (admin:assign_roles) to view this page.'}
        </p>
        {permissionError && (
           <p className="mb-4 text-sm text-red-500">Details: {permissionError.message}</p>
        )}
        <button
          onClick={() => router.push('/admin')}
          className="rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700"
        >
          Return to Admin Home
        </button>
      </div>
    );
  }

  // If permission is granted and loading is done, render the page content
  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <h2 className="text-xl font-semibold">User Management</h2>
      </div>

      {error && (
        <div className="mb-4 rounded-md bg-red-50 p-4 text-red-800">
          <p>{error}</p>
          <button
            onClick={() => setError(null)}
            className="ml-2 text-sm font-medium text-red-600 hover:text-red-800"
          >
            Dismiss
          </button>
        </div>
      )}

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* User List */}
        <div className="col-span-1 rounded-lg border border-gray-200 bg-white shadow-sm">
          <div className="p-4">
            <h3 className="mb-4 text-lg font-medium">Users</h3>
            <div className="mb-4">
              <input
                type="text"
                placeholder="Search users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-blue-500"
              />
            </div>
            <div className="max-h-96 overflow-y-auto">
              {filteredUsers.length === 0 ? (
                <p className="py-4 text-center text-sm text-gray-500">
                  {searchQuery ? 'No users match your search.' : 'No users found.'}
                </p>
              ) : (
                <ul className="divide-y divide-gray-200">
                  {filteredUsers.map((user) => (
                    <li
                      key={user.id}
                      className={`cursor-pointer py-3 hover:bg-gray-50 ${
                        selectedUser?.id === user.id ? 'bg-blue-50' : ''
                      }`}
                      onClick={() => handleSelectUser(user)}
                    >
                      <div className="px-2">
                        <p className="font-medium text-gray-900">
                          {user.full_name || 'Unnamed User'}
                        </p>
                        {user.username && (
                          <p className="text-sm text-gray-500">@{user.username}</p>
                        )}
                        {user.phone && (
                          <p className="text-sm text-gray-500">{user.phone}</p>
                        )}
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        </div>

        {/* Role Assignment */}
        <div className="col-span-1 lg:col-span-2">
          {selectedUser ? (
            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
              <h3 className="mb-4 text-lg font-medium">
                Roles for {selectedUser.full_name || 'Unnamed User'}
              </h3>
              
              {isLoadingRoles ? (
                <div className="flex justify-center py-10">
                  <Spinner size="md" />
                </div>
              ) : (
                <>
                  <p className="mb-4 text-sm text-gray-600">
                    Select the roles you want to assign to this user. Changes are saved automatically.
                  </p>
                  
                  <div className="space-y-3">
                    {roles.map((role) => {
                      const isAssigned = userRoles.some((r) => r.id === role.id);
                      
                      return (
                        <div key={role.id} className="flex items-center justify-between rounded-md border border-gray-200 p-3">
                          <div>
                            <p className="font-medium text-gray-900">{role.name}</p>
                            {role.description && (
                              <p className="text-sm text-gray-500">{role.description}</p>
                            )}
                          </div>
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              id={`role-${role.id}`}
                              checked={isAssigned}
                              onChange={() => handleToggleRole(role.id, isAssigned)}
                              disabled={isSubmitting}
                              className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <label
                              htmlFor={`role-${role.id}`}
                              className="ml-2 text-sm font-medium text-gray-700"
                            >
                              {isAssigned ? 'Assigned' : 'Not Assigned'}
                            </label>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </>
              )}
            </div>
          ) : (
            <div className="flex h-full items-center justify-center rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
              <p className="text-center text-gray-500">
                Select a user to manage their roles.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UsersContent;
