// /components/modals/UpdateDetailsModal/UpdateDetailsModal.tsx
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/app/libs/supabase/client'; // Adjust path as needed
import { X } from 'lucide-react';
import { useForm, SubmitHandler } from 'react-hook-form';
import Tabs, { Tab } from '@/app/components/ui/tabs/Tabs'; // Adjust path
import PartInfoTab from './PartInfoTab';
import AttributesTab from './AttributesTab';
import LocationFormTab from './LocationFormTab';
import CarsTab from './CarsTab';
import CategoriesTab from './CategoriesTab';
import {
  PartDetailsFormValues,
  CompatibleVehicle,
  StorageArea,
  StorageUnit,
  PartLocation,
  CategoryAttribute,
  AttributeOption,
  AttributeValue,
  LocationSubType,
  CarData
} from './types';

interface UpdateDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  partId: string; // Keep as string if used as such, otherwise number
  partTitle: string; // Initial title
  partNumber: string; // Initial part number (may differ from compatibility group)
  conditions: any[]; // Initial conditions structure
  onDetailsUpdated: () => void; // Callback on success
}

const UpdateDetailsModal: React.FC<UpdateDetailsModalProps> = ({
  isOpen,
  onClose,
  partId,
  partTitle,
  partNumber, // This is the initial display number, might be from group
  conditions,
  onDetailsUpdated
}) => {
  const [activeTab, setActiveTab] = useState<string | number>('partInfo');
  const [isSubmitting, setIsSubmitting] = useState(false); // Changed name for clarity
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false); // General loading state
  const [originalValues, setOriginalValues] = useState<PartDetailsFormValues | null>(null); // Store original form values
  const [modifiedTabs, setModifiedTabs] = useState<Set<string | number>>(new Set()); // Track which tabs have been modified

  // Data states
  const [actualPartNumber, setActualPartNumber] = useState<string>(partNumber); // Fetched part number
  const [compatibilityGroupId, setCompatibilityGroupId] = useState<number | null>(null);
  const [categoryAttributes, setCategoryAttributes] = useState<CategoryAttribute[]>([]);
  const [attributeOptions, setAttributeOptions] = useState<{ [key: string]: AttributeOption[] }>({});
  const [initialAttributeValues, setInitialAttributeValues] = useState<AttributeValue[]>([]);
  const [storageAreas, setStorageAreas] = useState<StorageArea[]>([]);
  const [allStorageUnits, setAllStorageUnits] = useState<StorageUnit[]>([]);
  const [initialLocation, setInitialLocation] = useState<PartLocation | null>(null);
  const [initialCarData, setInitialCarData] = useState<CarData | null>(null);
  const [initialCategoryId, setInitialCategoryId] = useState<number | string | null>(null);
  const [compatibleVehicles, setCompatibleVehicles] = useState<CompatibleVehicle[]>([]);
  const [aiGeneratedAlternatives, setAiGeneratedAlternatives] = useState<string[]>([]);

  // Debug compatible vehicles state changes
  useEffect(() => {
    console.log('=== MODAL COMPATIBLE VEHICLES STATE CHANGED ===');
    console.log('Compatible vehicles count:', compatibleVehicles.length);
    console.log('Compatible vehicles:', compatibleVehicles);
  }, [compatibleVehicles]);
  const [isLoadingAttributes, setIsLoadingAttributes] = useState(false);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [isLoadingCars, setIsLoadingCars] = useState(false);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);

  // Tabs configuration
  const modalTabs: Tab[] = [
    { id: 'partInfo', label: 'Part Info & Price' },
    { id: 'categories', label: 'Categories' },
    { id: 'attributes', label: 'Attributes' },
    { id: 'location', label: 'Location' },
    { id: 'cars', label: 'Cars' },
  ];

  // Function to handle tab changes and track modified tabs
  const handleTabChange = (tabId: string | number) => {
    setActiveTab(tabId);

    // Add the tab to the set of modified tabs when the user switches to it
    // This assumes that if a user visits a tab, they intend to modify it
    const newModifiedTabs = new Set(modifiedTabs);
    newModifiedTabs.add(tabId);
    setModifiedTabs(newModifiedTabs);
  };

  // Function to mark a tab as modified (called by child components)
  const handleTabModified = (tabId: string | number) => {
    const newModifiedTabs = new Set(modifiedTabs);
    newModifiedTabs.add(tabId);
    setModifiedTabs(newModifiedTabs);
    console.log(`Tab ${tabId} marked as modified`);
  };

  // React Hook Form setup
  const { register, control, handleSubmit, setValue, watch, getValues, reset, formState: { errors, dirtyFields } } = useForm<PartDetailsFormValues>({
    // Default values will be populated by useEffect hooks
    defaultValues: {
      title: partTitle, // Initial display title
      partNumber: partNumber, // Initial display part number
      conditions: conditions.map(c => ({
        id: c.id,
        condition: c.condition,
        stock: c.stock || 0,
        price: 0, // Will be fetched
        discountedPrice: null, // Will be fetched
        newCondition: c.condition, // Initialize newCondition with current
      })),
      attributes: [], // Will be populated
      location: { // Default empty location structure
        location_id: undefined,
        part_id: parseInt(partId, 10), // Ensure partId is number if needed
        area_id: null,
        unit_id: null,
        quantity: 1,
        location_subtype: '',
        details: null,
        notes: '',
      },
      categoryData: { // Default empty category data
        categoryId: '',
      },
    }
  });

  // --- Data Fetching ---
  const fetchPartData = useCallback(async () => {
    if (!isOpen || !partId) return;
    setIsLoading(true);
    setError(null);
    console.log(`Fetching data for partId: ${partId}`);

    try {
      const supabase = createClient();

      // 1. Fetch Core Part Details (Title, Compatibility Group Part Number)
      console.log("Fetching core part details...");
      const { data: partData, error: partError } = await supabase
        .from('parts')
        .select(`
          id,
          title,
          partnumber_group,
          category_id,
          part_compatibility_groups:partnumber_group (id, part_number)
        `)
        .eq('id', partId)
        .single();

      if (partError) throw new Error(`Part fetch error: ${partError.message}`);
      if (!partData) throw new Error("Part not found.");
      console.log("Core part details fetched:", partData);

      // Set Title, Actual Part Number, Compatibility Group ID
      setValue('title', partData.title || partTitle); // Use fetched title or initial prop
      const fetchedPartNumber = partData.part_compatibility_groups?.[0]?.part_number ?? partNumber;
      setActualPartNumber(fetchedPartNumber);
      setValue('partNumber', fetchedPartNumber); // Update form with actual part number
      setCompatibilityGroupId(partData.partnumber_group);

      // 2. Fetch Prices for Conditions
      if (conditions && conditions.length > 0) {
          console.log("Fetching prices for conditions...");
          const conditionIds = conditions.map(c => parseInt(c.id, 10)).filter(id => !isNaN(id));
          if (conditionIds.length > 0) {
              const { data: prices, error: pricesError } = await supabase
                  .from('part_price')
                  .select('*')
                  .in('condition_id', conditionIds);

              if (pricesError) console.error("Price fetch error:", pricesError.message); // Log error but continue
              else {
                  console.log("Prices fetched:", prices);
                  // Update form values with fetched prices
                  const updatedConditions = conditions.map((condition, index) => {
                      const priceData = prices?.find(p => p.condition_id === parseInt(condition.id, 10));
                      // Use setValue to update form state for prices
                      setValue(`conditions.${index}.price`, priceData?.price ?? 0);
                      setValue(`conditions.${index}.discountedPrice`, priceData?.discounted_price ?? null);
                      // Return the structure needed for the PartInfoTab prop (or adjust if needed)
                      return {
                          ...condition, // Keep original condition data
                          price: priceData?.price ?? 0,
                          discountedPrice: priceData?.discounted_price ?? null,
                      };
                  });
                  // Optionally update the conditions state if PartInfoTab relies on it directly,
                  // but it's better if it reads from form state via watch or control.
              }
          } else {
              console.log("No valid condition IDs to fetch prices for.");
          }
      }


      // 3. Fetch Category Attributes and Options (Run concurrently with location)
      const fetchAttributes = async () => {
        if (!partData.category_id) {
            console.log("Part has no category_id, skipping attributes.");
            setCategoryAttributes([]);
            setInitialAttributeValues([]);
            setAttributeOptions({});
            return;
        }
        setIsLoadingAttributes(true);
        console.log(`Fetching attributes for category_id: ${partData.category_id}...`);
        try {
          const { data: attributes, error: attributesError } = await supabase
            .from('parts_category_attributes')
            .select('*')
            .eq('category_id', partData.category_id);

          if (attributesError) throw new Error(`Attributes fetch error: ${attributesError.message}`);
          setCategoryAttributes(attributes || []);
          console.log("Attributes fetched:", attributes);

          if (attributes && attributes.length > 0) {
            // Fetch existing values for this part
            const { data: values, error: valuesError } = await supabase
              .from('parts_category_attribute_values')
              .select('*')
              .eq('part_id', partId);
            if (valuesError) throw new Error(`Attribute values fetch error: ${valuesError.message}`);
            setInitialAttributeValues(values || []);
            console.log("Initial attribute values fetched:", values);

            // Fetch options for radio/checkbox/select
            const attributeIdsWithOptions = attributes
              .filter(attr => ['radio', 'checkbox', 'select'].includes(attr.input_type))
              .map(attr => attr.id);

            if (attributeIdsWithOptions.length > 0) {
              const { data: options, error: optionsError } = await supabase
                .from('parts_category_attribute_input_option')
                .select('*')
                .in('attribute_id', attributeIdsWithOptions);
              if (optionsError) throw new Error(`Attribute options fetch error: ${optionsError.message}`);

              const optionsMap: { [key: string]: AttributeOption[] } = {};
              options?.forEach(option => {
                if (!optionsMap[option.attribute_id]) {
                  optionsMap[option.attribute_id] = [];
                }
                optionsMap[option.attribute_id].push(option);
              });
              setAttributeOptions(optionsMap);
              console.log("Attribute options fetched:", optionsMap);
            }
          } else {
              setInitialAttributeValues([]);
              setAttributeOptions({});
          }
        } catch (err: any) {
          console.error("Error fetching attributes:", err);
          setError(prev => prev ? `${prev}\nFailed to load attributes.` : 'Failed to load attributes.');
          setCategoryAttributes([]);
          setInitialAttributeValues([]);
          setAttributeOptions({});
        } finally {
          setIsLoadingAttributes(false);
        }
      };

      // 4. Fetch Location Data (Areas, Units, Part's Location) (Run concurrently with attributes)
       const fetchLocation = async () => {
           setIsLoadingLocation(true);
           console.log("Fetching location data...");
           try {
               // Fetch all storage areas
               const { data: areasData, error: areasError } = await supabase
                   .from('storage_areas')
                   .select('*');
               if (areasError) throw new Error(`Storage areas fetch error: ${areasError.message}`);
               setStorageAreas(areasData || []);
               console.log("Storage areas fetched:", areasData);

               // Fetch all storage units
               const { data: unitsData, error: unitsError } = await supabase
                   .from('storage_units')
                   .select('*');
               if (unitsError) throw new Error(`Storage units fetch error: ${unitsError.message}`);
               setAllStorageUnits(unitsData || []);
               console.log("All storage units fetched:", unitsData);

               // Fetch location data in separate steps to avoid join issues
               console.log(`Fetching location data for part ID: ${partId}`);

               try {
                   // Step 1: Get the basic part location data
                   const { data: locationData, error: locationError } = await supabase
                       .from('part_locations')
                       .select('*')
                       .eq('part_id', partId)
                       .limit(1)
                       .maybeSingle();

                   if (locationError) throw new Error(`Part location fetch error: ${locationError.message}`);

                   if (locationData) {
                       console.log("Part location data found:", JSON.stringify(locationData, null, 2));

                       // Step 2: Get the storage unit data to get the area_id
                       const { data: unitData, error: unitError } = await supabase
                           .from('storage_units')
                           .select('*')
                           .eq('unit_id', locationData.unit_id)
                           .single();

                       if (unitError) throw new Error(`Storage unit fetch error: ${unitError.message}`);

                       console.log("Storage unit data found:", JSON.stringify(unitData, null, 2));

                       // Step 3: Get the storage area data
                       const { data: areaData, error: areaError } = await supabase
                           .from('storage_areas')
                           .select('*')
                           .eq('area_id', unitData.area_id)
                           .single();

                       if (areaError) throw new Error(`Storage area fetch error: ${areaError.message}`);

                       console.log("Storage area data found:", JSON.stringify(areaData, null, 2));

                       // Convert part_id to number
                       const partIdInt = parseInt(partId, 10);

                       // Create the location object with all the data
                       const locationWithArea = {
                           location_id: locationData.location_id,
                           part_id: partIdInt,
                           unit_id: locationData.unit_id,
                           area_id: unitData.area_id,
                           quantity: locationData.quantity || 1,
                           location_subtype: locationData.location_subtype || '',
                           details: typeof locationData.details === 'object' ? locationData.details : null,
                           notes: locationData.notes || ''
                       };

                       console.log("Complete location object created:", JSON.stringify(locationWithArea, null, 2));

                       // Set the form values directly
                       setValue('location.location_id', locationWithArea.location_id);
                       setValue('location.part_id', locationWithArea.part_id);
                       setValue('location.area_id', locationWithArea.area_id);
                       setValue('location.unit_id', locationWithArea.unit_id);
                       setValue('location.quantity', locationWithArea.quantity);
                       setValue('location.location_subtype', locationWithArea.location_subtype);
                       setValue('location.details', locationWithArea.details);
                       setValue('location.notes', locationWithArea.notes);

                       // Set the initialLocation state last
                       setInitialLocation(locationWithArea as PartLocation);

                       console.log("Location data set in form and state");
                   } else {
                       console.log("No location found for this part");
                       // Set initialLocation to null but make sure the form is ready for a new location
                       setInitialLocation(null);

                       // Reset location form values to defaults
                       setValue('location.location_id', undefined);
                       setValue('location.part_id', parseInt(partId, 10));
                       setValue('location.area_id', null);
                       setValue('location.unit_id', null);
                       setValue('location.quantity', 1);
                       setValue('location.location_subtype', '');
                       setValue('location.details', {});
                       setValue('location.notes', '');

                       console.log("Location form reset to defaults for new location");
                   }
               } catch (err: any) {
                   console.error("Error fetching location data:", err);
                   setInitialLocation(null);
               }

           } catch (err: any) {
               console.error("Error fetching location data:", err);
               setError(prev => prev ? `${prev}\nFailed to load location data.` : 'Failed to load location data.');
               setStorageAreas([]);
               setAllStorageUnits([]);
               setInitialLocation(null);
           } finally {
               setIsLoadingLocation(false);
           }
       };


      // 5. Fetch Car Data (Run concurrently with other fetches)
      const fetchCarData = async () => {
        setIsLoadingCars(true);
        console.log("Fetching car data...");
        try {
          // Fetch ALL car data from parts_car table - can have multiple compatible vehicles
          const { data: carData, error: carError } = await supabase
            .from('parts_car')
            .select('id, variation_trim_id')
            .eq('part_id', partId);

          if (carError) throw new Error(`Car data fetch error: ${carError.message}`);

          if (carData && carData.length > 0) {
            // For now, use the first compatible car for the form (we'll show all in the cars tab)
            const firstCarData = carData[0];

            try {
              // Get the variation_trim data
              const { data: variationTrim, error: trimError } = await supabase
                .from('variation_trim')
                .select('*')
                .eq('id', firstCarData.variation_trim_id)
                .single();

              if (trimError) {
                console.error('Trim fetch error:', trimError);
                throw new Error(`Trim fetch error: ${trimError.message}`);
              }

              console.log('Found variation_trim:', variationTrim);

              // Get the variation data
              const { data: variationData, error: variationError } = await supabase
                .from('car_variation')
                .select('*')
                .eq('id', variationTrim.variation_id)
                .single();

              if (variationError) {
                console.error('Variation fetch error:', variationError);
                throw new Error(`Variation fetch error: ${variationError.message}`);
              }

              console.log('Found variation:', variationData);

              // Get the generation data
              const { data: generationData, error: generationError } = await supabase
                .from('car_generation')
                .select('*')
                .eq('id', variationData.generation_id)
                .single();

              if (generationError) {
                console.error('Generation fetch error:', generationError);
                throw new Error(`Generation fetch error: ${generationError.message}`);
              }

              console.log('Found generation:', generationData);

              // Get the model data
              const { data: modelData, error: modelError } = await supabase
                .from('car_models')
                .select('*')
                .eq('id', generationData.model_id)
                .single();

              if (modelError) {
                console.error('Model fetch error:', modelError);
                throw new Error(`Model fetch error: ${modelError.message}`);
              }

              console.log('Found model:', modelData);

              // Get the brand data
              const { data: brandData, error: brandError } = await supabase
                .from('car_brands')
                .select('*')
                .eq('brand_id', modelData.brand_id)
                .single();

              if (brandError) {
                console.error('Brand fetch error:', brandError);
                throw new Error(`Brand fetch error: ${brandError.message}`);
              }

              console.log('Found brand:', brandData);

              // Set the car data values
              const carDataValues = {
                brandId: brandData.brand_id,
                modelId: modelData.id,
                generationId: variationData.generation_id,
                variationId: variationData.id,
                trimId: variationTrim.id
              };

              setInitialCarData(carDataValues);

              // Set form values for car data
              setValue('carData.brandId', String(carDataValues.brandId));
              setValue('carData.modelId', String(carDataValues.modelId));
              setValue('carData.generationId', String(carDataValues.generationId));
              setValue('carData.variationId', String(carDataValues.variationId));
              setValue('carData.trimId', String(carDataValues.trimId));
            } catch (err) {
              console.error('Error processing car data:', err);
              setError(prev => prev ? `${prev}\nFailed to process car data.` : 'Failed to process car data.');
              setInitialCarData(null);

              // Reset car form values to defaults
              setValue('carData.brandId', '');
              setValue('carData.modelId', '');
              setValue('carData.generationId', '');
              setValue('carData.variationId', '');
              setValue('carData.trimId', '');
            }
          } else {
            setInitialCarData(null);

            // Reset car form values to defaults
            setValue('carData.brandId', '');
            setValue('carData.modelId', '');
            setValue('carData.generationId', '');
            setValue('carData.variationId', '');
            setValue('carData.trimId', '');
          }
        } catch (err: any) {
          console.error("Error fetching car data:", err);
          setError(prev => prev ? `${prev}\nFailed to load car data.` : 'Failed to load car data.');
          setInitialCarData(null);
        } finally {
          setIsLoadingCars(false);
        }
      };

      // 6. Fetch Category Data (Run concurrently with other fetches)
      const fetchCategoryData = async () => {
        setIsLoadingCategories(true);
        console.log("Fetching category data...");
        try {
          // Set the initial category ID from the part data
          if (partData.category_id) {
            setInitialCategoryId(partData.category_id);
            setValue('categoryData.categoryId', String(partData.category_id));
            console.log(`Set initial category ID: ${partData.category_id}`);
          } else {
            setInitialCategoryId(null);
            setValue('categoryData.categoryId', '');
            console.log('No category ID found for this part');
          }
        } catch (err: any) {
          console.error("Error fetching category data:", err);
          setError(prev => prev ? `${prev}\nFailed to load category data.` : 'Failed to load category data.');
          setInitialCategoryId(null);
        } finally {
          setIsLoadingCategories(false);
        }
      };

      // Run fetches concurrently
      await Promise.all([fetchAttributes(), fetchLocation(), fetchCarData(), fetchCategoryData()]);

    } catch (err: any) {
      console.error('Error fetching initial part data:', err);
      setError(`Failed to load part details: ${err.message}`);
      // Reset form state in case of partial load?
      // reset(); // Consider if reset is appropriate here
    } finally {
      setIsLoading(false);
    }
  }, [isOpen, partId, conditions, setValue, partTitle, partNumber, reset]); // Add reset to dependencies

  // Initial data fetch and reset on open/partId change
  useEffect(() => {
    // Reset form state when modal opens or partId changes
    reset({ // Pass default values structure matching the form
       title: partTitle,
       partNumber: partNumber,
       conditions: conditions.map(c => ({
         id: c.id,
         condition: c.condition,
         stock: c.stock || 0,
         price: 0,
         discountedPrice: null,
         reorderLevel: c.reorder_level || 0,
         newCondition: c.condition,
       })),
       attributes: [],
       location: {
         location_id: undefined,
         part_id: parseInt(partId, 10),
         area_id: null,
         unit_id: null,
         quantity: 1,
         location_subtype: '',
         details: {},
         notes: '',
       },
       carData: {
         brandId: '',
         modelId: '',
         generationId: '',
         variationId: '',
         trimId: '',
       },
       categoryData: {
         categoryId: '',
       },
    }, { keepDefaultValues: true });
    // Reset local component state
    setCategoryAttributes([]);
    setAttributeOptions({});
    setInitialAttributeValues([]);
    setStorageAreas([]);
    setAllStorageUnits([]);
    setInitialLocation(null);
    setInitialCarData(null);
    setCompatibilityGroupId(null);
    setActualPartNumber(partNumber);
    setIsLoadingAttributes(false);
    setIsLoadingLocation(false);
    setIsLoadingCars(false);
    setIsLoadingCategories(false);
    setAiGeneratedAlternatives([]); // Clear AI alternatives when modal closes

    // Fetch data
    fetchPartData();
  }, [isOpen, partId, fetchPartData, reset, partTitle, partNumber, conditions]); // Add reset dependencies


  // --- Form Submission ---
  const onSubmit: SubmitHandler<PartDetailsFormValues> = async (data) => {
    setIsSubmitting(true);
    setError(null);

    // First, check if car data has been modified but the title hasn't been updated yet
    // This typically happens when submitting directly from the car tab
    if (dirtyFields.carData && data.carData && initialCarData && !dirtyFields.title) {
      try {
        console.log("Car data changed, updating title before submission...");
        const { updateTitleWithCarDetails } = await import('./utils/titleUpdater');

        // Create car data objects for the update
        const oldCarData = {
          brandId: initialCarData.brandId,
          modelId: initialCarData.modelId,
          generationId: initialCarData.generationId,
          variationId: initialCarData.variationId,
          trimId: initialCarData.trimId
        };

        const newCarData = {
          brandId: data.carData.brandId,
          modelId: data.carData.modelId,
          generationId: data.carData.generationId,
          variationId: data.carData.variationId,
          trimId: data.carData.trimId
        };

        // Update the title based on car changes
        const updatedTitle = await updateTitleWithCarDetails(data.title, oldCarData, newCarData);
        console.log("Title updated before submission:", updatedTitle);

        // Update the title in the form data
        data.title = updatedTitle;

        // Also add partInfo to the tabs to update
        (dirtyFields as any).title = true;
      } catch (err) {
        console.error("Error updating title based on car changes:", err);
        // Continue with submission despite title update error
      }
    }

    // Automatically determine which tabs to update based on dirty fields
    const tabsToUpdate = new Set<string | number>();

    // Check if part info fields are dirty
    if (dirtyFields.title || dirtyFields.partNumber ||
        (dirtyFields.conditions && Array.isArray(dirtyFields.conditions) && dirtyFields.conditions.length > 0)) {
      tabsToUpdate.add('partInfo');
    }

    // Check if attributes are dirty
    if (dirtyFields.attributes && Array.isArray(dirtyFields.attributes) && dirtyFields.attributes.length > 0) {
      tabsToUpdate.add('attributes');
    }

    // Check if location is dirty
    if (dirtyFields.location) {
      tabsToUpdate.add('location');
    }

    // Check if car data is dirty
    if (dirtyFields.carData) {
      tabsToUpdate.add('cars');
    }

    // If nothing is dirty, just update the active tab
    if (tabsToUpdate.size === 0) {
      tabsToUpdate.add(activeTab);
    }

    console.log("Dirty fields:", JSON.stringify(dirtyFields, null, 2));

    // Convert Set to Array for iteration
    const tabsToUpdateArray = Array.from(tabsToUpdate);
    console.log("Form Data Submitted for tabs:", tabsToUpdateArray);

    const supabase = createClient();
    const partIdInt = parseInt(partId, 10);

    try {
      // Process each tab that needs to be updated
      for (const tabId of tabsToUpdateArray) {
        console.log(`Processing tab: ${tabId}`);

        // Update part info tab
        if (tabId === 'partInfo') {
          // 1. Update Part Title
          console.log("Updating part title...");
          const { error: titleError } = await supabase
            .from('parts')
            .update({ title: data.title })
            .eq('id', partIdInt);
          if (titleError) throw new Error(`Title update failed: ${titleError.message}`);
          console.log("Part title updated.");

          // 2. Update Part Number in Compatibility Group (if changed and group exists)
          if (data.partNumber !== actualPartNumber && compatibilityGroupId !== null) {
            console.log("Updating part number in group...");
            const { error: partNumberError } = await supabase
              .from('part_compatibility_groups')
              .update({ part_number: data.partNumber })
              .eq('id', compatibilityGroupId);
            if (partNumberError) throw new Error(`Part number update failed: ${partNumberError.message}`);
            console.log("Part number updated.");
          } else {
            console.log("Part number unchanged or no compatibility group, skipping update.");
          }

          // 3. Update Conditions (Stock and potentially Condition Name) & Prices
          console.log("Updating conditions and prices...");
          for (const condition of data.conditions) {
            const conditionIdInt = parseInt(condition.id, 10);
            if (isNaN(conditionIdInt)) {
              console.warn(`Skipping invalid condition ID: ${condition.id}`);
              continue;
            }

            // Update stock, reorder level, and condition name in parts_condition
            const conditionUpdateData: { stock: number; reorder_level: number; condition?: string } = {
              stock: condition.stock,
              reorder_level: condition.reorderLevel
            };
            const hasConditionChanged = condition.newCondition && condition.newCondition !== condition.condition;
            if (hasConditionChanged) {
              conditionUpdateData.condition = condition.newCondition;
              console.log(`Condition name change detected for ID ${conditionIdInt}: ${condition.condition} -> ${condition.newCondition}`);
            }
            console.log(`Updating stock to ${condition.stock} and reorder level to ${condition.reorderLevel} for condition ID ${conditionIdInt}`);

            const { error: stockError } = await supabase
              .from('parts_condition')
              .update(conditionUpdateData)
              .eq('id', conditionIdInt);
            if (stockError) throw new Error(`Stock/Condition update failed for condition ID ${conditionIdInt}: ${stockError.message}`);

            // Check if price record exists for this condition
            const { data: existingPrice, error: priceCheckError } = await supabase
              .from('part_price')
              .select('id')
              .eq('condition_id', conditionIdInt)
              .maybeSingle();

            if (priceCheckError) throw new Error(`Price check failed for condition ID ${conditionIdInt}: ${priceCheckError.message}`);

            const priceData = {
              condition_id: conditionIdInt,
              price: condition.price,
              discounted_price: condition.discountedPrice,
            };
            console.log(`Processing price for condition ID ${conditionIdInt}:`, priceData);

            let priceError;

            if (existingPrice) {
              // Update existing price record
              console.log(`Updating existing price record ID ${existingPrice.id} for condition ID ${conditionIdInt}`);
              const { error } = await supabase
                .from('part_price')
                .update(priceData)
                .eq('id', existingPrice.id);
              priceError = error;
            } else {
              // Insert new price record
              console.log(`Inserting new price record for condition ID ${conditionIdInt}`);
              const { error } = await supabase
                .from('part_price')
                .insert(priceData);
              priceError = error;
            }

            if (priceError) throw new Error(`Price update failed for condition ID ${conditionIdInt}: ${priceError.message}`);
          }
          console.log("Conditions and prices updated.");

          // 4. Save AI-generated alternative part numbers to part_to_group table
          if (aiGeneratedAlternatives.length > 0) {
            console.log("Saving AI-generated alternative part numbers...");
            console.log("AI alternatives to save:", aiGeneratedAlternatives);

            // First, get the part's compatibility group
            const { data: partData, error: partDataError } = await supabase
              .from('parts')
              .select('partnumber_group')
              .eq('id', partIdInt)
              .single();

            if (partDataError || !partData?.partnumber_group) {
              console.warn("No compatibility group found for part, skipping alternative part numbers save");
            } else {
              const groupId = partData.partnumber_group;
              console.log("Part compatibility group ID:", groupId);

              // Check which alternatives already exist GLOBALLY (not just in this group)
              // Since partnumber is a PRIMARY KEY, each part number can only exist once
              const { data: existingAlternatives, error: existingError } = await supabase
                .from('part_to_group')
                .select('partnumber, group_id')
                .in('partnumber', aiGeneratedAlternatives);

              if (existingError) {
                console.warn("Error checking existing alternatives:", existingError);
              } else {
                const existingPartNumbers = existingAlternatives?.map(alt => alt.partnumber) || [];
                console.log("Existing alternatives in database (any group):", existingPartNumbers);

                // Filter out alternatives that already exist anywhere in the database
                const newAlternatives = aiGeneratedAlternatives.filter(alt =>
                  !existingPartNumbers.includes(alt)
                );

                console.log("New alternatives to insert:", newAlternatives);

                // Also log which alternatives already exist and in which groups
                if (existingAlternatives && existingAlternatives.length > 0) {
                  console.log("Skipping alternatives that already exist:");
                  existingAlternatives.forEach(existing => {
                    console.log(`  - ${existing.partnumber} (already in group ${existing.group_id})`);
                  });
                }

                if (newAlternatives.length > 0) {
                  // Insert new alternative part numbers
                  const alternativesToInsert = newAlternatives.map(partNumber => ({
                    partnumber: partNumber,
                    group_id: groupId
                  }));

                  const { error: insertError } = await supabase
                    .from('part_to_group')
                    .insert(alternativesToInsert);

                  if (insertError) {
                    console.error("Error saving alternative part numbers:");
                    console.error("Error details:", insertError);
                    console.error("Error message:", insertError.message);
                    console.error("Error code:", insertError.code);
                    console.error("Attempted to insert:", alternativesToInsert);
                    // Don't throw error - this is not critical for the main update
                  } else {
                    console.log(`✅ Successfully saved ${newAlternatives.length} alternative part numbers`);
                  }
                } else {
                  console.log("All AI-generated alternatives already exist in database");
                  if (existingAlternatives && existingAlternatives.length > 0) {
                    console.log("Summary: All alternatives were already in the database:");
                    existingAlternatives.forEach(existing => {
                      console.log(`  ✓ ${existing.partnumber} (group ${existing.group_id})`);
                    });
                  }
                }
              }
            }
          }
        }

        // Update attributes tab
        if (tabId === 'attributes') {
          // 4. Update/Insert Attributes
          console.log("Updating attributes...");
          if (data.attributes && data.attributes.length > 0) {
            for (const attribute of data.attributes) {
              const attributeIdInt = parseInt(attribute.id, 10);
              if (isNaN(attributeIdInt)) {
                console.warn(`Skipping invalid attribute ID: ${attribute.id}`);
                continue;
              }

              let valueToSave: string | null = null;
              let selectionValueToSave: string | null = null;

              // Handle different input types for saving
              if (['select', 'radio'].includes(attribute.inputType)) {
                selectionValueToSave = attribute.value as string ?? null;
              } else if (attribute.inputType === 'checkbox') {
                // Handle checkbox values
                if (Array.isArray(attribute.value)) {
                  selectionValueToSave = attribute.value.join(',');
                } else if (typeof attribute.value === 'string') {
                  selectionValueToSave = attribute.value;
                } else if (typeof attribute.value === 'object' && attribute.value !== null) {
                  selectionValueToSave = Object.entries(attribute.value)
                    .filter(([, checked]) => checked)
                    .map(([key]) => key)
                    .join(',');
                }
              } else { // text, number
                valueToSave = attribute.value as string ?? null;
              }

              // Skip upsert if both values are null/empty
              if (valueToSave === null && selectionValueToSave === null) {
                continue;
              }

              const attributeValueData = {
                part_id: partIdInt,
                attribute_id: attributeIdInt,
                value: valueToSave,
                selection_value: selectionValueToSave,
              };
              console.log(`Upserting attribute value for attribute ID ${attributeIdInt}:`, attributeValueData);

              // Check if attribute value exists
              const { data: existingValue, error: valueCheckError } = await supabase
                .from('parts_category_attribute_values')
                .select('*')
                .eq('part_id', partIdInt)
                .eq('attribute_id', attributeIdInt)
                .maybeSingle();

              if (valueCheckError) throw new Error(`Attribute value check failed for attribute ID ${attributeIdInt}: ${valueCheckError.message}`);

              let attributeError;

              if (existingValue) {
                // Update existing attribute value
                console.log(`Updating existing attribute value for part ID ${partIdInt}, attribute ID ${attributeIdInt}`);
                const { error } = await supabase
                  .from('parts_category_attribute_values')
                  .update(attributeValueData)
                  .eq('part_id', partIdInt)
                  .eq('attribute_id', attributeIdInt);
                attributeError = error;
              } else {
                // Insert new attribute value
                console.log(`Inserting new attribute value for part ID ${partIdInt}, attribute ID ${attributeIdInt}`);
                const { error } = await supabase
                  .from('parts_category_attribute_values')
                  .insert(attributeValueData);
                attributeError = error;
              }

              if (attributeError) throw new Error(`Attribute value update failed for attribute ID ${attributeIdInt}: ${attributeError.message}`);
            }
          }
          console.log("Attributes updated.");
        }

        // Update location tab
        if (tabId === 'location') {
          // 5. Update/Insert Location
          console.log("Updating location...");
          if (data.location && data.location.unit_id) { // Ensure unit_id is selected
            const locationDetails = data.location.details ? { ...data.location.details } : {};

            // Clean details based on subtype before saving
            Object.keys(locationDetails).forEach(key => {
              // Remove empty string values, keep potentially valid '0' or false
              if (locationDetails[key as keyof typeof locationDetails] === '') {
                delete locationDetails[key as keyof typeof locationDetails];
              }
            });

            const locationData = {
              location_id: data.location.location_id || undefined, // Use existing ID if available
              part_id: partIdInt,
              unit_id: data.location.unit_id,
              quantity: data.location.quantity,
              location_subtype: data.location.location_subtype as LocationSubType, // Ensure it's the enum type
              details: locationDetails,
              notes: data.location.notes || null,
            };

            console.log("Location data to upsert:", locationData);

            // Upsert location data
            let locationError;

            if (data.location.location_id) {
              // Update existing location
              console.log(`Updating existing location ID ${data.location.location_id}`);
              const { error } = await supabase
                .from('part_locations')
                .update(locationData)
                .eq('location_id', data.location.location_id);
              locationError = error;
            } else {
              // Insert new location
              console.log(`Inserting new location for part ID ${partIdInt}`);
              const { error } = await supabase
                .from('part_locations')
                .insert(locationData);
              locationError = error;
            }

            if (locationError) throw new Error(`Location update failed: ${locationError.message}`);
            console.log("Location updated.");
          } else if (initialLocation?.location_id) {
            // If unit_id was cleared but there was an initial location, delete the old location record.
            console.log(`Location cleared, deleting existing location ID: ${initialLocation.location_id}`);
            const { error: deleteError } = await supabase
              .from('part_locations')
              .delete()
              .eq('location_id', initialLocation.location_id);
            if (deleteError) console.error(`Failed to delete previous location: ${deleteError.message}`);
            else console.log("Previous location deleted.");
          } else {
            console.log("No location selected or no previous location to delete, skipping location update.");
          }
        }

        // Update categories tab
        if (tabId === 'categories') {
          // 6. Update Category Data
          console.log("Updating category data for part ID:", partIdInt);

          // Check if category ID is provided
          if (data.categoryData && data.categoryData.categoryId) {
            console.log("Category ID provided:", data.categoryData.categoryId);

            // Ensure the category ID is converted to a number
            const categoryId = parseInt(data.categoryData.categoryId as string, 10);

            if (isNaN(categoryId)) {
              throw new Error(`Invalid category ID: ${data.categoryData.categoryId}`);
            }

            // Update the part's category_id
            const { error: categoryError } = await supabase
              .from('parts')
              .update({ category_id: categoryId })
              .eq('id', partIdInt);

            if (categoryError) throw new Error(`Category update failed: ${categoryError.message}`);

            console.log("Category updated successfully to ID:", categoryId);

            // Update the initialCategoryId state to reflect the changes
            setInitialCategoryId(categoryId);
          } else if (initialCategoryId) {
            // If category was cleared but there was an initial category, set category_id to null
            console.log("Category cleared, setting category_id to null");

            const { error: clearError } = await supabase
              .from('parts')
              .update({ category_id: null })
              .eq('id', partIdInt);

            if (clearError) throw new Error(`Failed to clear category: ${clearError.message}`);

            console.log("Category cleared successfully");
            setInitialCategoryId(null);
          } else {
            console.log("No category selected or no previous category to clear, skipping category update.");
          }
        }

        // Update cars tab
        if (tabId === 'cars') {
          // 7. Update/Insert Car Data
          console.log("Updating car data for part ID:", partIdInt);

          // Check if all required car data fields are provided
          const hasAllCarData = data.carData &&
                               data.carData.brandId &&
                               data.carData.modelId &&
                               data.carData.generationId &&
                               data.carData.variationId &&
                               data.carData.trimId;



          if (hasAllCarData) {
            console.log("All car data fields provided:", data.carData);

            // Ensure the trim ID is converted to a number
            const trimId = parseInt(data.carData.trimId as string, 10);

            if (isNaN(trimId)) {
              throw new Error(`Invalid trim ID: ${data.carData.trimId}`);
            }

            // Check if this specific vehicle combination already exists
            const { data: existingCarData, error: carCheckError } = await supabase
              .from('parts_car')
              .select('id')
              .eq('part_id', partIdInt)
              .eq('variation_trim_id', trimId)
              .maybeSingle();

            if (carCheckError) throw new Error(`Car data check failed: ${carCheckError.message}`);

            const carData = {
              part_id: partIdInt,
              variation_trim_id: trimId,
            };

            console.log("Car data to upsert:", carData);

            let carError;

            if (existingCarData) {
              // This specific vehicle combination already exists, no need to update
              console.log(`Vehicle combination already exists for part ${partIdInt} and trim ${trimId}`);
              carError = null; // No error, just skip
            } else {
              // Insert new car data
              console.log(`Inserting new car data for part ID ${partIdInt}`);

              // First, get the next available ID for parts_car table
              const { data: maxIdData, error: maxIdError } = await supabase
                .from('parts_car')
                .select('id')
                .order('id', { ascending: false })
                .limit(1)
                .single();

              if (maxIdError && maxIdError.code !== 'PGRST116') {
                // PGRST116 is "Results contain 0 rows" which is fine for an empty table
                throw new Error(`Failed to get max ID: ${maxIdError.message}`);
              }

              const nextId = maxIdData ? maxIdData.id + 1 : 1;
              console.log(`Next available ID for parts_car: ${nextId}`);

              // Insert with the next available ID
              const { error } = await supabase
                .from('parts_car')
                .insert({
                  ...carData,
                  id: nextId
                });
              carError = error;
            }

            if (carError) throw new Error(`Car data update failed: ${carError.message}`);
            console.log("Car data updated successfully.");

            // Update the initialCarData state to reflect the changes
            setInitialCarData({
              brandId: data.carData.brandId,
              modelId: data.carData.modelId,
              generationId: data.carData.generationId,
              variationId: data.carData.variationId,
              trimId: data.carData.trimId
            });
          } else if (initialCarData) {
            // If car data was cleared but there was initial car data, delete the old car data record
            console.log("Car data cleared, deleting existing car data");
            const { data: existingCarData, error: carCheckError } = await supabase
              .from('parts_car')
              .select('id')
              .eq('part_id', partIdInt)
              .maybeSingle();

            if (carCheckError) throw new Error(`Car data check failed: ${carCheckError.message}`);

            if (existingCarData) {
              const { error: deleteError } = await supabase
                .from('parts_car')
                .delete()
                .eq('id', existingCarData.id);
              if (deleteError) console.error(`Failed to delete previous car data: ${deleteError.message}`);
              else {
                console.log("Previous car data deleted.");
                setInitialCarData(null);
              }
            }
          }
        }
      }

      // Success - call callback and close modal
      console.log(`Updates successful for tabs: ${tabsToUpdateArray.join(', ')}`);
      onDetailsUpdated();
      onClose();

    } catch (err: any) {
      console.error('Error submitting updates:', err);
      setError(`Update failed: ${err.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // --- Render ---

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4"
      onClick={() => !isSubmitting && onClose()}
    >
      <div
        className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h3 className="text-xl font-semibold text-gray-800">Update Part Details</h3>
          <button
            className="p-1 rounded-full text-gray-500 hover:bg-gray-200 hover:text-gray-700 disabled:opacity-50"
            onClick={() => !isSubmitting && onClose()}
            disabled={isSubmitting}
            aria-label="Close modal"
          >
            <X size={20} />
          </button>
        </div>

        {/* Loading Indicator */}
        {isLoading && (
          <div className="p-6 text-center text-gray-600">Loading details...</div>
        )}

        {/* Error Message */}
        {error && !isLoading && (
          <div className="m-4 p-3 bg-red-50 text-red-700 rounded-md border border-red-200">
            <p className="font-medium">Error</p>
            <p className="text-sm whitespace-pre-wrap">{error}</p>
          </div>
        )}

        {/* Form and Tabs (only render if not loading initial data) */}
        {!isLoading && (
          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col overflow-hidden flex-1">
            {/* Tabs Navigation */}
            <div className="px-4 pt-2 border-b border-gray-200">
              <Tabs
                tabs={modalTabs}
                activeTabId={activeTab}
                onTabChange={handleTabChange}
              />
            </div>

            {/* Tab Content */}
            <div className="p-4 overflow-y-auto flex-1">
              {activeTab === 'partInfo' && (
                <PartInfoTab
                  register={register}
                  errors={errors}
                  control={control}
                  setValue={setValue}
                  watch={watch}
                  getValues={getValues}
                  conditions={conditions} // Pass original conditions structure
                  onTabModified={handleTabModified}
                  partId={partId}
                  compatibleVehicles={compatibleVehicles}
                  onAiAlternativesGenerated={setAiGeneratedAlternatives}
                />
              )}
              {activeTab === 'categories' && (
                <CategoriesTab
                  register={register}
                  errors={errors}
                  control={control}
                  setValue={setValue}
                  watch={watch}
                  getValues={getValues}
                  initialCategoryId={initialCategoryId}
                  isLoadingCategories={isLoadingCategories}
                />
              )}
              {activeTab === 'attributes' && (
                <AttributesTab
                  register={register}
                  errors={errors}
                  control={control}
                  setValue={setValue}
                  watch={watch}
                  getValues={getValues}
                  categoryAttributes={categoryAttributes}
                  attributeOptions={attributeOptions}
                  isLoadingAttributes={isLoadingAttributes}
                  initialAttributeValues={initialAttributeValues}
                />
              )}
              {activeTab === 'location' && (
                <LocationFormTab
                  register={register}
                  errors={errors}
                  control={control}
                  setValue={setValue}
                  watch={watch}
                  getValues={getValues}
                  storageAreas={storageAreas}
                  allStorageUnits={allStorageUnits}
                  initialLocation={initialLocation}
                  partId={partId}
                  isLoadingLocation={isLoadingLocation}
                />
              )}
              {activeTab === 'cars' && (
                <CarsTab
                  register={register}
                  control={control}
                  errors={errors}
                  setValue={setValue}
                  watch={watch}
                  getValues={getValues}
                  initialCarData={initialCarData}
                  isLoadingCars={isLoadingCars}
                  partId={partId}
                  compatibleVehicles={compatibleVehicles}
                  setCompatibleVehicles={setCompatibleVehicles}
                />
              )}
            </div>

            {/* Submit Button */}
            <div className="p-4 border-t border-gray-200 flex justify-end space-x-3">
              <button
                type="button"
                className="px-4 py-2 rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50"
                onClick={() => !isSubmitting && onClose()}
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700 disabled:bg-blue-400"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default UpdateDetailsModal;
