'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, AlertTriangle, Trash2 } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';

interface DeleteConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  categoryId: number;
  categoryName: string;
  hasChildren: boolean;
  onSuccess: () => void;
}

const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({ 
  isOpen, 
  onClose, 
  categoryId,
  categoryName,
  hasChildren,
  onSuccess 
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const supabase = createClient();
  
  const handleDelete = async () => {
    setIsDeleting(true);
    setError(null);
    
    try {
      // Check if category has children
      if (hasChildren) {
        throw new Error('Cannot delete a category with subcategories. Please delete or reassign all subcategories first.');
      }
      
      // Check if category is used by any parts
      const { data: parts, error: partsError } = await supabase
        .from('parts')
        .select('id')
        .eq('category_id', categoryId)
        .limit(1);
      
      if (partsError) throw partsError;
      
      if (parts && parts.length > 0) {
        throw new Error('Cannot delete a category that is used by parts. Please reassign all parts to another category first.');
      }
      
      // Delete the category
      const { error: deleteError } = await supabase
        .from('car_part_categories')
        .delete()
        .eq('id', categoryId);
      
      if (deleteError) throw deleteError;
      
      // Success
      onSuccess();
      onClose();
    } catch (err: any) {
      console.error('Error deleting category:', err);
      setError(err.message || 'Failed to delete category');
    } finally {
      setIsDeleting(false);
    }
  };
  
  // Animation variants
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3 } }
  };
  
  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { 
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    exit: { 
      opacity: 0, 
      y: 50, 
      scale: 0.95,
      transition: { duration: 0.2 }
    }
  };
  
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          onClick={onClose}
        >
          <motion.div
            className="bg-white rounded-lg shadow-xl w-full max-w-md overflow-hidden"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <div className="flex items-center">
                <AlertTriangle className="w-6 h-6 text-red-600 mr-3" />
                <h2 className="text-xl font-semibold text-gray-800">Delete Category</h2>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="p-6">
              {error ? (
                <div className="mb-4 p-4 bg-red-50 text-red-700 rounded-md">
                  <p className="font-medium mb-1">Error</p>
                  <p className="text-sm">{error}</p>
                </div>
              ) : (
                <div className="mb-4">
                  <p className="text-gray-700 mb-4">
                    Are you sure you want to delete the category <span className="font-semibold">{categoryName}</span>?
                  </p>
                  
                  {hasChildren && (
                    <div className="p-4 bg-yellow-50 text-yellow-700 rounded-md text-sm mb-4">
                      <p className="font-medium mb-1">Warning</p>
                      <p>This category has subcategories. You must delete or reassign all subcategories before deleting this category.</p>
                    </div>
                  )}
                  
                  <p className="text-sm text-gray-500">
                    This action cannot be undone. This will permanently delete the category and remove it from our servers.
                  </p>
                </div>
              )}
              
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 mr-2"
                  disabled={isDeleting}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleDelete}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center"
                  disabled={isDeleting || hasChildren}
                >
                  {isDeleting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete Category
                    </>
                  )}
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default DeleteConfirmModal;
