'use client';

import { useEffect, useState } from 'react';
import { useApp } from '../providers/AppProviders';
import { api } from '@/app/utils/api';

interface Part {
  id: string;
  name: string;
  description: string;
  price: number;
  stock: number;
}

export default function PartsList() {
  const { setIsLoading, setError } = useApp();
  const [parts, setParts] = useState<Part[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchParts();
  }, []);

  const fetchParts = async () => {
    setIsLoading(true);
    try {
      // Using the enhanced API utility with caching
      const data = await api.get<Part[]>('/parts', {
        cache: 'force-cache', // Use cache for this request
        retry: 3, // Retry up to 3 times
        retryDelay: 1000, // Start with 1 second delay
      });
      setParts(data);
    } catch (error) {
      setError(error instanceof Error ? error : new Error('Failed to fetch parts'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddToCart = async (partId: string) => {
    try {
      await api.post('/cart/add', { partId, quantity: 1 }, {
        showToast: true, // Show success/error toast
        retry: 2, // Retry up to 2 times for cart operations
      });
    } catch (error) {
      // Error is already handled by the API utility
      console.error('Failed to add to cart:', error);
    }
  };

  const filteredParts = parts.filter(part =>
    part.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    part.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Available Parts</h2>
        <input
          type="text"
          placeholder="Search parts..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredParts.map((part) => (
          <div
            key={part.id}
            className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow"
          >
            <h3 className="text-lg font-semibold">{part.name}</h3>
            <p className="text-gray-600 mt-2">{part.description}</p>
            <div className="mt-4 flex items-center justify-between">
              <div>
                <p className="text-lg font-bold">${part.price.toFixed(2)}</p>
                <p className="text-sm text-gray-500">
                  {part.stock > 0 ? `${part.stock} in stock` : 'Out of stock'}
                </p>
              </div>
              <button
                onClick={() => handleAddToCart(part.id)}
                disabled={part.stock === 0}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Add to Cart
              </button>
            </div>
          </div>
        ))}
      </div>

      {filteredParts.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No parts found matching your search.</p>
        </div>
      )}
    </div>
  );
} 