'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Search, RefreshCw, Edit, Trash2, Car } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { Brand, BrandFormData } from '../types';
import AddBrandModal from './modals/AddBrandModal';
import EditBrandModal from './modals/EditBrandModal';
import DeleteConfirmModal from './modals/DeleteConfirmModal';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

interface BrandsTabProps {
  onBrandUpdated: () => void;
}

const BrandsTab: React.FC<BrandsTabProps> = ({ onBrandUpdated }) => {
  const [brands, setBrands] = useState<Brand[]>([]);
  const [filteredBrands, setFilteredBrands] = useState<Brand[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const supabase = createClient();

  // Fetch brands from Supabase
  useEffect(() => {
    const fetchBrands = async () => {
      setIsLoading(true);
      try {
        const { data, error } = await supabase
          .from('car_brands')
          .select('*')
          .order('brand_name');

        if (error) throw error;

        setBrands(data || []);
        setFilteredBrands(data || []);
      } catch (error) {
        console.error('Error fetching brands:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBrands();
  }, [refreshTrigger, supabase]);

  // Filter brands based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredBrands(brands);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = brands.filter(brand => 
      brand.brand_name.toLowerCase().includes(query)
    );
    setFilteredBrands(filtered);
  }, [searchQuery, brands]);

  // Refresh brands
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Open edit modal
  const handleEdit = (brand: Brand) => {
    setSelectedBrand(brand);
    setIsEditModalOpen(true);
  };

  // Open delete modal
  const handleDelete = (brand: Brand) => {
    setSelectedBrand(brand);
    setIsDeleteModalOpen(true);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.3,
        ease: "easeOut"
      }
    },
    hover: {
      y: -5,
      boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
      transition: { 
        duration: 0.2
      }
    }
  };

  return (
    <div>
      {/* Search and Actions */}
      <div className="flex flex-col md:flex-row justify-between items-center mb-6 gap-4">
        <div className="relative w-full md:w-96">
          <input
            type="text"
            placeholder="Search brands..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
        </div>
        
        <div className="flex gap-2 w-full md:w-auto">
          <button
            onClick={handleRefresh}
            className="flex items-center gap-2 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
          >
            <RefreshCw size={18} />
            <span>Refresh</span>
          </button>
          
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <Plus size={18} />
            <span>Add Brand</span>
          </button>
        </div>
      </div>
      
      {/* Brands Grid */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size={40} />
        </div>
      ) : filteredBrands.length > 0 ? (
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {filteredBrands.map((brand) => (
            <motion.div
              key={brand.brand_id}
              className="bg-white rounded-lg shadow-md p-6"
              variants={itemVariants}
              whileHover="hover"
            >
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center">
                  <div className="p-3 bg-teal-100 text-teal-600 rounded-full mr-4">
                    <Car size={24} />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800">{brand.brand_name}</h3>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleEdit(brand)}
                    className="p-2 rounded-md hover:bg-gray-100 text-gray-500 hover:text-teal-600 transition-colors"
                    aria-label="Edit brand"
                  >
                    <Edit size={18} />
                  </button>
                  <button
                    onClick={() => handleDelete(brand)}
                    className="p-2 rounded-md hover:bg-gray-100 text-gray-500 hover:text-red-600 transition-colors"
                    aria-label="Delete brand"
                  >
                    <Trash2 size={18} />
                  </button>
                </div>
              </div>
              
              <div className="mt-4 pt-4 border-t border-gray-100">
                <p className="text-sm text-gray-500">Brand ID: {brand.brand_id}</p>
              </div>
            </motion.div>
          ))}
        </motion.div>
      ) : (
        <div className="bg-white rounded-lg shadow p-8 text-center">
          <h3 className="text-xl font-semibold text-gray-700 mb-2">No brands found</h3>
          <p className="text-gray-500 mb-4">
            {searchQuery ? 'No brands match your search criteria.' : 'Start by adding a new brand.'}
          </p>
          <button
            onClick={() => setIsAddModalOpen(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <Plus size={18} />
            <span>Add Brand</span>
          </button>
        </div>
      )}
      
      {/* Add Brand Modal */}
      <AddBrandModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onSuccess={() => {
          handleRefresh();
          onBrandUpdated();
        }}
      />
      
      {/* Edit Brand Modal */}
      {selectedBrand && (
        <EditBrandModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          brand={selectedBrand}
          onSuccess={() => {
            handleRefresh();
            onBrandUpdated();
          }}
        />
      )}
      
      {/* Delete Confirmation Modal */}
      {selectedBrand && (
        <DeleteConfirmModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          itemId={selectedBrand.brand_id}
          itemName={selectedBrand.brand_name}
          itemType="brand"
          tableName="car_brands"
          idField="brand_id"
          onSuccess={() => {
            handleRefresh();
            onBrandUpdated();
          }}
        />
      )}
    </div>
  );
};

export default BrandsTab;
