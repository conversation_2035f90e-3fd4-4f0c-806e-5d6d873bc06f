import { apiClient } from './api';
import { CONFIG } from '@/constants/config';
import { 
  CarBrand, 
  CarModel, 
  CarGeneration, 
  CarVariation, 
  CarTrim 
} from '@/types';

export interface CarData {
  brands: <PERSON>Brand[];
  models: CarModel[];
  generations: CarGeneration[];
  variations: CarVariation[];
  trims: CarTrim[];
}

export class CarsService {
  // Get all car data
  async getAllCarData(): Promise<CarData> {
    try {
      const response = await apiClient.get<CarData>(
        CONFIG.ENDPOINTS.CARS.ALL
      );

      return response;
    } catch (error) {
      console.error('Get all car data error:', error);
      throw error;
    }
  }

  // Get car brands
  async getBrands(): Promise<CarBrand[]> {
    try {
      const response = await apiClient.get<CarBrand[]>(
        CONFIG.ENDPOINTS.CARS.BRANDS
      );

      return response;
    } catch (error) {
      console.error('Get brands error:', error);
      throw error;
    }
  }

  // Get models by brand
  async getModelsByBrand(brandId: number): Promise<CarModel[]> {
    try {
      const response = await apiClient.get<CarModel[]>(
        CONFIG.ENDPOINTS.CARS.MODELS,
        { brand_id: brandId }
      );

      return response;
    } catch (error) {
      console.error('Get models by brand error:', error);
      throw error;
    }
  }

  // Get all models
  async getAllModels(): Promise<CarModel[]> {
    try {
      const response = await apiClient.get<CarModel[]>(
        CONFIG.ENDPOINTS.CARS.MODELS
      );

      return response;
    } catch (error) {
      console.error('Get all models error:', error);
      throw error;
    }
  }

  // Get generations by model
  async getGenerationsByModel(modelId: number): Promise<CarGeneration[]> {
    try {
      const response = await apiClient.get<CarGeneration[]>(
        CONFIG.ENDPOINTS.CARS.GENERATIONS,
        { model_id: modelId }
      );

      return response;
    } catch (error) {
      console.error('Get generations by model error:', error);
      throw error;
    }
  }

  // Get all generations
  async getAllGenerations(): Promise<CarGeneration[]> {
    try {
      const response = await apiClient.get<CarGeneration[]>(
        CONFIG.ENDPOINTS.CARS.GENERATIONS
      );

      return response;
    } catch (error) {
      console.error('Get all generations error:', error);
      throw error;
    }
  }

  // Get variations by generation
  async getVariationsByGeneration(generationId: number): Promise<CarVariation[]> {
    try {
      const response = await apiClient.get<CarVariation[]>(
        CONFIG.ENDPOINTS.CARS.VARIATIONS,
        { generation_id: generationId }
      );

      return response;
    } catch (error) {
      console.error('Get variations by generation error:', error);
      throw error;
    }
  }

  // Get all variations
  async getAllVariations(): Promise<CarVariation[]> {
    try {
      const response = await apiClient.get<CarVariation[]>(
        CONFIG.ENDPOINTS.CARS.VARIATIONS
      );

      return response;
    } catch (error) {
      console.error('Get all variations error:', error);
      throw error;
    }
  }

  // Get trims by variation
  async getTrimsByVariation(variationId: number): Promise<CarTrim[]> {
    try {
      const response = await apiClient.get<CarTrim[]>(
        CONFIG.ENDPOINTS.CARS.TRIMS,
        { variation_id: variationId }
      );

      return response;
    } catch (error) {
      console.error('Get trims by variation error:', error);
      throw error;
    }
  }

  // Get all trims
  async getAllTrims(): Promise<CarTrim[]> {
    try {
      const response = await apiClient.get<CarTrim[]>(
        CONFIG.ENDPOINTS.CARS.TRIMS
      );

      return response;
    } catch (error) {
      console.error('Get all trims error:', error);
      throw error;
    }
  }

  // Helper method to get car hierarchy
  async getCarHierarchy(
    brandId?: number,
    modelId?: number,
    generationId?: number,
    variationId?: number
  ): Promise<{
    brands: CarBrand[];
    models: CarModel[];
    generations: CarGeneration[];
    variations: CarVariation[];
    trims: CarTrim[];
  }> {
    try {
      const promises: Promise<any>[] = [this.getBrands()];

      if (brandId) {
        promises.push(this.getModelsByBrand(brandId));
      } else {
        promises.push(Promise.resolve([]));
      }

      if (modelId) {
        promises.push(this.getGenerationsByModel(modelId));
      } else {
        promises.push(Promise.resolve([]));
      }

      if (generationId) {
        promises.push(this.getVariationsByGeneration(generationId));
      } else {
        promises.push(Promise.resolve([]));
      }

      if (variationId) {
        promises.push(this.getTrimsByVariation(variationId));
      } else {
        promises.push(Promise.resolve([]));
      }

      const [brands, models, generations, variations, trims] = await Promise.all(promises);

      return {
        brands,
        models,
        generations,
        variations,
        trims,
      };
    } catch (error) {
      console.error('Get car hierarchy error:', error);
      throw error;
    }
  }
}

export const carsService = new CarsService();
export default carsService;
