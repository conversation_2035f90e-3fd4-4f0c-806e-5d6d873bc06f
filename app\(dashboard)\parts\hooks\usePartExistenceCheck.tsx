// This is a replacement for the usePartExistenceCheck hook
// that avoids checking the part_compatibility_groups table to prevent 406 errors

import { useState } from 'react';
import { createClient } from '@/app/libs/supabase/client';

export const usePartExistenceCheck = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [exists, setExists] = useState(false);
  const [groupId, setGroupId] = useState<number | null>(null);
  const [table, setTable] = useState<'part_to_group' | null>(null);
  const [error, setError] = useState<string | null>(null);

  const checkPartExistence = async (partNumber: string) => {
    if (!partNumber) {
      setError('Part number is required');
      return;
    }

    setIsLoading(true);
    setError(null);
    setExists(false);
    setGroupId(null);
    setTable(null);

    try {
      console.log("Checking part existence for:", partNumber);
      const supabase = createClient();

      // ONLY check part_to_group table to avoid 406 errors
      console.log("Checking part_to_group table for partnumber:", partNumber);
      const { data: partToGroupData, error: partToGroupError } = await supabase
        .from('part_to_group')
        .select('group_id')
        .eq('partnumber', partNumber)
        .maybeSingle(); // Use maybeSingle instead of single to avoid errors

      if (partToGroupError) {
        console.error("Error checking part in part_to_group:", partToGroupError);
        setError(partToGroupError.message);
        return;
      }

      if (partToGroupData) {
        console.log("Part found in part_to_group:", partToGroupData);
        setExists(true);
        setGroupId(partToGroupData.group_id);
        setTable('part_to_group');
        return;
      }

      console.log("Part not found in database");
      setExists(false);
    } catch (error) {
      console.error("Error in checkPartExistence:", error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    exists,
    groupId,
    table,
    error,
    checkPartExistence
  };
}; 