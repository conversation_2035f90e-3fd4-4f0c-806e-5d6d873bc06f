import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';
import { generateProductSlug } from '@/app/utils/slugify';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const partId = params.id;

  try {
    // Get the part title to generate the slug
    const supabase = createClient();
    const { data: part, error } = await supabase
      .from('parts')
      .select('title')
      .eq('id', partId)
      .single();

    if (error || !part) {
      // If the part doesn't exist, return a 404
      return NextResponse.json(
        { error: 'Part not found' },
        { status: 404 }
      );
    }

    // Generate the slug from the part title and ID
    const slug = generateProductSlug(part.title, partId);

    // Get the base URL from the request
    const baseUrl = request.nextUrl.origin;

    // Redirect to the new URL format
    return NextResponse.redirect(`${baseUrl}/shop/product/${slug}`);
  } catch (error) {
    console.error('Error redirecting to slug URL:', error);
    return NextResponse.json(
      { error: 'Failed to redirect' },
      { status: 500 }
    );
  }
}
