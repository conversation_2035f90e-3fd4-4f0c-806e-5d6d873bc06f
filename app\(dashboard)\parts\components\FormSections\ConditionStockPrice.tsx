import React, { useState, useEffect } from 'react';
import { Control, FieldErrors, useWatch, Controller } from 'react-hook-form';
import NumberInput from '@/app/components/ui/inputs/Number';
import CurrencyInput from '@/app/components/ui/inputs/Currency';
import { PartFormValues } from '../../types';
import { X } from 'lucide-react';
import Button from '@/app/components/ui/inputs/Button';
import { motion, AnimatePresence } from 'framer-motion';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/inputs/Select';
import toast from 'react-hot-toast';

interface ConditionStockPriceProps {
  control: Control<PartFormValues>;
  errors: FieldErrors<PartFormValues>;
  condition: string | undefined;
}

const getErrorMessage = (error: any): string | undefined => {
  return error?.message?.toString();
};

const validateDiscountPrice = (price: number, discountPrice: string): boolean => {
  const discount = parseFloat(discountPrice.replace(/[^0-9.]/g, ''));
  if (isNaN(discount)) return false;
  
  if (discount >= price) {
    toast.error('Discounted price must be less than the actual price');
    return false;
  }
  
  const discountPercentage = ((price - discount) / price) * 100;
  if (discountPercentage > 20) {
    toast('Discount is more than 20% of the original price', {
      icon: '⚠️',
      style: { background: '#FFF7ED', color: '#9A3412' }
    });
  }
  
  return true;
};

export default function ConditionStockPrice({
  control,
  errors,
  condition,
}: ConditionStockPriceProps) {
  const [showNewDiscount, setShowNewDiscount] = useState(false);
  const [showUsedDiscount, setShowUsedDiscount] = useState(false);
  const [newDiscountPrice, setNewDiscountPrice] = useState('');
  const [usedDiscountPrice, setUsedDiscountPrice] = useState('');
  
  // Watch the condition field directly from the form control
  const watchedCondition = useWatch({
    control,
    name: 'condition',
  });

  // Watch price fields
  const newPrice = useWatch({
    control,
    name: 'newPrice',
  });
  const usedPrice = useWatch({
    control,
    name: 'usedPrice',
  });
  const price = useWatch({
    control,
    name: 'price',
  });
  
  // Force re-render when condition changes
  const [forceRender, setForceRender] = useState(0);
  
  useEffect(() => {
    // Log when condition changes
    console.log('Condition changed to:', watchedCondition);
    // Force re-render
    setForceRender(prev => prev + 1);
  }, [watchedCondition]);

  const handleCloseNewDiscount = () => {
    setShowNewDiscount(false);
    setNewDiscountPrice('');
  };

  const handleCloseUsedDiscount = () => {
    setShowUsedDiscount(false);
    setUsedDiscountPrice('');
  };

  const handleNewDiscountChange = (value: string) => {
    if (watchedCondition === 'Both' && newPrice) {
      if (validateDiscountPrice(newPrice, value)) {
        setNewDiscountPrice(value);
      }
    } else if (price) {
      if (validateDiscountPrice(price, value)) {
        setNewDiscountPrice(value);
      }
    }
  };

  const handleUsedDiscountChange = (value: string) => {
    if (usedPrice && validateDiscountPrice(usedPrice, value)) {
      setUsedDiscountPrice(value);
    }
  };

  console.log('Render count:', forceRender);
  console.log('Condition prop value:', condition);
  console.log('Watched condition value:', watchedCondition);
  
  // Determine if we should show stock and price inputs
  const shouldShowInputs = 
    watchedCondition === 'New' || 
    watchedCondition === 'Used' || 
    watchedCondition === 'Both';
    
  console.log('Should show stock and price?', shouldShowInputs);

  return (
    <div className="space-y-6">
      <div className="w-full">
        <Controller
          name="condition"
          control={control}
          render={({ field }) => (
            <div className="space-y-2">
              <Select
                label="Condition"
                value={field.value || ''}
                onValueChange={(value) => {
                  console.log('Condition selected:', value);
                  field.onChange(value);
                }}
              >
                <SelectTrigger placeholder="Select condition">
                  <SelectValue>{field.value || 'Select condition'}</SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="New">New</SelectItem>
                  <SelectItem value="Used">Used</SelectItem>
                  <SelectItem value="Both">Both</SelectItem>
                </SelectContent>
              </Select>
              {errors.condition && (
                <p className="text-sm text-red-500">{getErrorMessage(errors.condition)}</p>
              )}
            </div>
          )}
        />
      </div>

      {shouldShowInputs && (
        <div className="grid gap-4 md:grid-cols-2">
          {watchedCondition === 'Both' ? (
            <>
              {/* New Condition Section */}
              <div className="space-y-4 p-4 border rounded-lg">
                <h3 className="font-medium">New Condition</h3>
                <NumberInput
                  name="newStock"
                  control={control}
                  label="Stock Quantity"
                  errorMessage={getErrorMessage(errors.newStock)}
                />
                <div className="space-y-2">
                  <CurrencyInput
                    name="newPrice"
                    control={control}
                    label="Price"
                    currencySymbol="KES"
                    errorMessage={getErrorMessage(errors.newPrice)}
                  />
                  {!showNewDiscount && (
                    <button
                      type="button"
                      onClick={() => setShowNewDiscount(true)}
                      className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      Add discounted price
                    </button>
                  )}
                  {showNewDiscount && (
                    <AnimatePresence>
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="relative"
                      >
                        <div className="flex items-center gap-2">
                          <CurrencyInput
                            name="newDiscountPrice"
                            control={control}
                            label="Discounted Price"
                            currencySymbol="KES"
                            value={newDiscountPrice}
                            onChange={handleNewDiscountChange}
                            errorMessage={getErrorMessage(errors.newDiscountPrice)}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-10 w-10 shrink-0"
                            onClick={handleCloseNewDiscount}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </motion.div>
                    </AnimatePresence>
                  )}
                </div>
              </div>

              {/* Used Condition Section */}
              <div className="space-y-4 p-4 border rounded-lg">
                <h3 className="font-medium">Used Condition</h3>
                <NumberInput
                  name="usedStock"
                  control={control}
                  label="Stock Quantity"
                  errorMessage={getErrorMessage(errors.usedStock)}
                />
                <div className="space-y-2">
                  <CurrencyInput
                    name="usedPrice"
                    control={control}
                    label="Price"
                    currencySymbol="KES"
                    errorMessage={getErrorMessage(errors.usedPrice)}
                  />
                  {!showUsedDiscount && (
                    <button
                      type="button"
                      onClick={() => setShowUsedDiscount(true)}
                      className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      Add discounted price
                    </button>
                  )}
                  {showUsedDiscount && (
                    <AnimatePresence>
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="relative"
                      >
                        <div className="flex items-center gap-2">
                          <CurrencyInput
                            name="usedDiscountPrice"
                            control={control}
                            label="Discounted Price"
                            currencySymbol="KES"
                            value={usedDiscountPrice}
                            onChange={handleUsedDiscountChange}
                            errorMessage={getErrorMessage(errors.usedDiscountPrice)}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-10 w-10 shrink-0"
                            onClick={handleCloseUsedDiscount}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </motion.div>
                    </AnimatePresence>
                  )}
                </div>
              </div>
            </>
          ) : (
            <>
              <NumberInput
                name="stock"
                control={control}
                label="Stock Quantity"
                errorMessage={getErrorMessage(errors.stock)}
              />

              <div className="space-y-2">
                <CurrencyInput
                  name="price"
                  control={control}
                  label="Price"
                  currencySymbol="KES"
                  errorMessage={getErrorMessage(errors.price)}
                />
                
                {!showNewDiscount && (
                  <button
                    type="button"
                    onClick={() => setShowNewDiscount(true)}
                    className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    Add discounted price
                  </button>
                )}
                {showNewDiscount && (
                  <AnimatePresence>
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="relative"
                    >
                      <div className="flex items-center gap-2">
                        <CurrencyInput
                          name="discountPrice"
                          control={control}
                          label="Discounted Price"
                          currencySymbol="KES"
                          value={newDiscountPrice}
                          onChange={handleNewDiscountChange}
                          errorMessage={getErrorMessage(errors.discountPrice)}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="h-10 w-10 shrink-0"
                          onClick={handleCloseNewDiscount}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </motion.div>
                  </AnimatePresence>
                )}
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
}