import React, { useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

import { useAuth, useUser } from '@/store/auth';
import { useParts } from '@/store/parts';
import { theme } from '@/theme';
import { CONFIG } from '@/constants/config';

export const HomeScreen: React.FC = () => {
  const { user } = useAuth();
  const { isEmployee, isSuperAdmin } = useUser();
  const { loadCategories, categories } = useParts();

  useEffect(() => {
    loadCategories();
  }, []);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const quickActions = [
    {
      title: 'Browse Parts',
      icon: 'car-wrench',
      description: 'Find auto parts for your vehicle',
      action: () => {}, // Navigate to shop
    },
    {
      title: 'Search',
      icon: 'magnify',
      description: 'Search for specific parts',
      action: () => {}, // Navigate to search
    },
    ...(isEmployee || isSuperAdmin ? [
      {
        title: 'Dashboard',
        icon: 'view-dashboard',
        description: 'Access your dashboard',
        action: () => {}, // Navigate to dashboard
      },
    ] : []),
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text variant="headlineSmall" style={styles.greeting}>
            {getGreeting()}!
          </Text>
          <Text variant="bodyLarge" style={styles.userName}>
            {user?.full_name || user?.email || 'Welcome'}
          </Text>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text variant="titleLarge" style={styles.sectionTitle}>
            Quick Actions
          </Text>
          <View style={styles.quickActionsGrid}>
            {quickActions.map((action, index) => (
              <Card key={index} style={styles.quickActionCard} onPress={action.action}>
                <Card.Content style={styles.quickActionContent}>
                  <Icon 
                    name={action.icon} 
                    size={32} 
                    color={theme.colors.primary} 
                    style={styles.quickActionIcon}
                  />
                  <Text variant="titleMedium" style={styles.quickActionTitle}>
                    {action.title}
                  </Text>
                  <Text variant="bodySmall" style={styles.quickActionDescription}>
                    {action.description}
                  </Text>
                </Card.Content>
              </Card>
            ))}
          </View>
        </View>

        {/* Categories Preview */}
        {categories.length > 0 && (
          <View style={styles.section}>
            <Text variant="titleLarge" style={styles.sectionTitle}>
              Popular Categories
            </Text>
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              style={styles.categoriesScroll}
            >
              {categories.slice(0, 5).map((category) => (
                <Card key={category.id} style={styles.categoryCard}>
                  <Card.Content style={styles.categoryContent}>
                    <Text variant="titleSmall" style={styles.categoryName}>
                      {category.name || category.label}
                    </Text>
                  </Card.Content>
                </Card>
              ))}
            </ScrollView>
          </View>
        )}

        {/* App Info */}
        <View style={styles.section}>
          <Card style={styles.infoCard}>
            <Card.Content>
              <Text variant="titleMedium" style={styles.infoTitle}>
                Welcome to {CONFIG.APP_NAME}
              </Text>
              <Text variant="bodyMedium" style={styles.infoDescription}>
                Your one-stop shop for auto parts. Browse our extensive catalog, 
                search for specific parts, and manage your orders all in one place.
              </Text>
              <Button 
                mode="outlined" 
                style={styles.infoButton}
                onPress={() => {}} // Navigate to shop
              >
                Start Shopping
              </Button>
            </Card.Content>
          </Card>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  greeting: {
    color: theme.colors.onBackground,
    fontWeight: 'bold',
  },
  userName: {
    color: theme.colors.primary,
    marginTop: 4,
  },
  section: {
    padding: 20,
    paddingTop: 10,
  },
  sectionTitle: {
    color: theme.colors.onBackground,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionCard: {
    width: '48%',
    marginBottom: 12,
    elevation: 2,
  },
  quickActionContent: {
    alignItems: 'center',
    padding: 16,
  },
  quickActionIcon: {
    marginBottom: 8,
  },
  quickActionTitle: {
    textAlign: 'center',
    marginBottom: 4,
    color: theme.colors.onSurface,
  },
  quickActionDescription: {
    textAlign: 'center',
    color: theme.colors.onSurface,
    opacity: 0.7,
  },
  categoriesScroll: {
    marginHorizontal: -20,
    paddingHorizontal: 20,
  },
  categoryCard: {
    width: 120,
    marginRight: 12,
    elevation: 2,
  },
  categoryContent: {
    padding: 12,
    alignItems: 'center',
  },
  categoryName: {
    textAlign: 'center',
    color: theme.colors.onSurface,
  },
  infoCard: {
    elevation: 2,
  },
  infoTitle: {
    color: theme.colors.onSurface,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  infoDescription: {
    color: theme.colors.onSurface,
    marginBottom: 16,
    lineHeight: 20,
  },
  infoButton: {
    alignSelf: 'flex-start',
  },
});
