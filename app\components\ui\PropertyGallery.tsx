'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface PropertyGalleryProps {
  images: string[];
  alt: string;
}

const PropertyGallery: React.FC<PropertyGalleryProps> = ({ images, alt }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isZoomed, setIsZoomed] = useState(false);

  const nextImage = () => {
    setActiveIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  };

  const prevImage = () => {
    setActiveIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  };

  const toggleZoom = () => {
    setIsZoomed(!isZoomed);
  };

  const handleThumbnailClick = (index: number) => {
    setActiveIndex(index);
  };

  // If no images are provided, show a placeholder
  if (!images || images.length === 0) {
    return (
      <div className="relative w-full h-[400px] bg-gray-200 rounded-lg flex items-center justify-center">
        <span className="text-gray-500">No image available</span>
      </div>
    );
  }

  return (
    <div className="property-gallery">
      {/* Main large image */}
      <div className="main-image-container relative rounded-lg overflow-hidden mb-2">
        <div 
          className="relative w-full h-[400px] cursor-pointer"
          onClick={toggleZoom}
        >
          <AnimatePresence mode="wait">
            <motion.div
              key={activeIndex}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="relative w-full h-full"
            >
              <Image
                src={images[activeIndex]}
                alt={`${alt} - Image ${activeIndex + 1}`}
                fill
                className={`object-cover transition-transform duration-300 ${isZoomed ? 'scale-110' : 'scale-100'}`}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                priority
              />
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Navigation Arrows */}
        {images.length > 1 && (
          <>
            <button
              onClick={prevImage}
              className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md z-10 transition-all"
              aria-label="Previous image"
            >
              <ChevronLeft size={20} />
            </button>
            <button
              onClick={nextImage}
              className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-md z-10 transition-all"
              aria-label="Next image"
            >
              <ChevronRight size={20} />
            </button>
          </>
        )}
      </div>

      {/* Thumbnails row */}
      {images.length > 1 && (
        <div className="thumbnails-container grid grid-cols-5 gap-2">
          {images.map((image, index) => (
            <div 
              key={index}
              className={`relative h-20 cursor-pointer rounded-md overflow-hidden border-2 transition-all ${
                activeIndex === index ? 'border-blue-500 opacity-100' : 'border-transparent opacity-70 hover:opacity-100'
              }`}
              onClick={() => handleThumbnailClick(index)}
            >
              <Image
                src={image}
                alt={`Thumbnail ${index + 1}`}
                fill
                className="object-cover"
                sizes="100px"
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default PropertyGallery;
