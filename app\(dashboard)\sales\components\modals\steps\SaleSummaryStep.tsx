'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { Check, AlertTriangle, RefreshCw, Smartphone, FileText } from 'lucide-react';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import { SelectedProduct, SaleFormData } from '../AddSaleModal';
import { formatPhoneNumber, isValidKenyanPhoneNumber } from '@/app/libs/mpesa/utils';
import { generatePDFReceipt } from '../../../utils/pdfGenerator';
import { toast } from 'react-hot-toast';
import { getCurrentUserId } from '../../../utils/userUtils';

interface SaleSummaryStepProps {
  products: SelectedProduct[];
  formData: SaleFormData;
  onBack: () => void;
  onComplete: () => void;
}

const SaleSummaryStep: React.FC<SaleSummaryStepProps> = ({
  products,
  formData,
  onBack,
  onComplete
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [completedSaleId, setCompletedSaleId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [clientDetails, setClientDetails] = useState<any>(null);

  // M-PESA payment states
  const [mpesaPaymentInitiated, setMpesaPaymentInitiated] = useState(false);
  const [mpesaPaymentStatus, setMpesaPaymentStatus] = useState<'pending' | 'completed' | 'failed' | null>(null);
  const [mpesaCheckoutRequestId, setMpesaCheckoutRequestId] = useState<string | null>(null);
  const [mpesaReceiptNumber, setMpesaReceiptNumber] = useState<string | null>(null);
  const [isCheckingPayment, setIsCheckingPayment] = useState(false);

  // VAT state
  const [vatRate, setVatRate] = useState<number>(16); // Default 16% VAT for Kenya
  const [includeVat, setIncludeVat] = useState<boolean>(true);

  // Calculate totals
  const subtotal = products.reduce((sum, product) => sum + (product.price * product.quantity), 0);
  const totalDiscount = products.reduce((sum, product) => sum + (product.discount || 0), 0);
  const totalBeforeVat = subtotal - totalDiscount;
  const vatAmount = includeVat ? (totalBeforeVat * vatRate / 100) : 0;
  const total = totalBeforeVat + vatAmount;

  // Fetch client details if a client ID is provided
  useEffect(() => {
    const fetchClientDetails = async () => {
      if (!formData.clientId) return;

      try {
        const supabase = createClient();

        // Use a Promise with a timeout to handle network issues
        const fetchWithTimeout = async (timeoutMs = 5000) => {
          let timeoutId;

          const timeoutPromise = new Promise((_, reject) => {
            timeoutId = setTimeout(() => {
              reject(new Error('Connection timed out. Please check your internet connection.'));
            }, timeoutMs);
          });

          try {
            const result = await Promise.race([
              supabase
                .from('clients')
                .select('id, name, phone_number, email, client_type')
                .eq('id', formData.clientId)
                .single(),
              timeoutPromise
            ]);

            clearTimeout(timeoutId);
            return result;
          } catch (error) {
            clearTimeout(timeoutId);
            throw error;
          }
        };

        // Fetch with timeout
        const { data, error } = await fetchWithTimeout();

        if (error) {
          console.error('Supabase error fetching client details:', error.message, error.code, error.details);
          throw new Error(`Database error: ${error.message}`);
        }

        setClientDetails(data);

        // If we couldn't get client details, create a mock client for testing
        if (!data) {
          console.log('No client details found, using fallback data');
          // This is just for development/testing purposes
          setClientDetails({
            id: formData.clientId,
            name: 'Client Name (Fallback)',
            phone_number: '07XXXXXXXX',
            email: '<EMAIL>',
            client_type: formData.saleType === 'credit' ? 'credit' : 'cash'
          });
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error('Error fetching client details:', errorMessage);

        // Check if it's a network error
        if (errorMessage.includes('Failed to fetch') || errorMessage.includes('timed out')) {
          setError('Network error: Unable to connect to the database. Please check your internet connection.');

          // Create mock client for testing
          setClientDetails({
            id: formData.clientId,
            name: 'Client Name (Offline)',
            phone_number: '07XXXXXXXX',
            email: '<EMAIL>',
            client_type: formData.saleType === 'credit' ? 'credit' : 'cash'
          });
        } else {
          setError(`Failed to load client details: ${errorMessage}`);
        }
      }
    };

    fetchClientDetails();
  }, [formData.clientId, formData.saleType]);

  // Fetch VAT rate from database
  useEffect(() => {
    const fetchVatRate = async () => {
      try {
        const supabase = createClient();
        const { data, error } = await supabase.rpc('get_vat_rate');

        if (error) {
          console.error('Error fetching VAT rate:', error);
        } else if (data !== null) {
          setVatRate(data);
        }
      } catch (error) {
        console.error('Error fetching VAT rate:', error);
      }
    };

    fetchVatRate();
  }, []);

  // Function to initiate M-PESA payment
  const initiateMpesaPayment = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Get the phone number to use for payment
      let phoneNumber = '';

      if (clientDetails && clientDetails.phone_number) {
        // Use the selected client's phone number
        phoneNumber = clientDetails.phone_number;
      } else if (formData.oneOffClientPhone) {
        // Use the one-off client's phone number
        phoneNumber = formData.oneOffClientPhone;
      } else if (formData.clientId) {
        // We have a client ID but no client details yet
        // This can happen if the client details haven't been fetched yet
        throw new Error('Client details not loaded yet. Please try again in a moment.');
      } else {
        // No phone number available
        throw new Error('No phone number available for M-PESA payment. Please select a client or enter a phone number.');
      }

      // Validate the phone number
      if (!isValidKenyanPhoneNumber(phoneNumber)) {
        throw new Error('Invalid phone number format. Please use a valid Kenyan phone number.');
      }

      // Format the phone number for M-PESA
      const formattedPhone = formatPhoneNumber(phoneNumber);

      // Generate a unique reference for this sale
      const reference = `SALE-${Date.now()}`;

      // Calculate the total amount (including VAT)
      const amount = total;

      // Make the API call to initiate the STK push
      console.log('Initiating M-PESA payment with phone number:', formattedPhone);

      const payload = {
        phoneNumber: formattedPhone,
        amount,
        reference,
        description: `Payment for ${products.length} items`,
      };

      console.log('M-PESA payment payload:', payload);

      const response = await fetch('/api/mpesa/initiate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
        credentials: 'include', // Include cookies for authentication
      });

      console.log('M-PESA initiate response status:', response.status);

      // Check for authentication errors
      if (response.status === 401) {
        throw new Error('Authentication failed. Please refresh the page and try again.');
      }

      const result = await response.json();

      console.log('M-PESA initiate response data:', result);

      if (!result.success) {
        // Check if this is a development mode mock response
        if (result.message && result.message.includes('MOCK')) {
          console.log('Using mock M-PESA response in development mode');
          // Continue with the mock data
        } else {
          console.error('M-PESA API error:', result);

          // Show a more user-friendly error message
          const errorMessage = result.message || 'Failed to initiate M-PESA payment';

          // If it's an invalid token error, provide more specific guidance
          if (errorMessage.includes('Invalid Access Token')) {
            throw new Error('M-PESA payment system is currently unavailable. Please try again later or use a different payment method.');
          } else {
            throw new Error(errorMessage);
          }
        }
      } else {
        console.log('M-PESA payment initiated successfully:', result.data);
      }

      // Store the checkout request ID for later use
      setMpesaCheckoutRequestId(result.data.CheckoutRequestID);
      setMpesaPaymentInitiated(true);
      setMpesaPaymentStatus('pending');

      // Start polling for payment status
      setTimeout(() => {
        checkMpesaPaymentStatus(result.data.CheckoutRequestID);
      }, 5000);
    } catch (error: any) {
      console.error('Error initiating M-PESA payment:', error);

      // Show a user-friendly error message
      if (error.message && error.message.includes('M-PESA payment system is currently unavailable')) {
        setError(error.message);

        // Suggest alternative payment methods
        setError(
          'M-PESA payment system is currently unavailable. Please try again later or use a different payment method. ' +
          'You can also manually enter the M-PESA confirmation code if you have already made the payment.'
        );
      } else if (error.message && error.message.includes('Failed to fetch')) {
        setError('Network error: Unable to connect to the payment system. Please check your internet connection and try again.');
      } else {
        setError(error.message || 'Failed to initiate M-PESA payment');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to check M-PESA payment status
  const checkMpesaPaymentStatus = async (checkoutRequestId: string) => {
    if (!checkoutRequestId) return;

    setIsCheckingPayment(true);

    try {
      const response = await fetch('/api/mpesa/query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          checkoutRequestId,
        }),
        credentials: 'include', // Include cookies for authentication
      });

      // Check for authentication errors
      if (response.status === 401) {
        throw new Error('Authentication failed. Please refresh the page and try again.');
      }

      const result = await response.json();

      if (result.success && result.data.status === 'completed') {
        // Payment was successful
        setMpesaPaymentStatus('completed');
        setMpesaReceiptNumber(result.data.mpesaReceiptNumber || 'N/A');

        // Proceed with recording the sale
        await recordSale(checkoutRequestId);
      } else if (!result.success && result.data.status === 'failed') {
        // Payment failed
        setMpesaPaymentStatus('failed');
        setError(`M-PESA payment failed: ${result.data.resultDescription || 'Unknown error'}`);
      } else {
        // Payment is still pending, continue polling
        setMpesaPaymentStatus('pending');
        setTimeout(() => {
          checkMpesaPaymentStatus(checkoutRequestId);
        }, 5000);
      }
    } catch (error: any) {
      console.error('Error checking M-PESA payment status:', error);

      // Don't show errors for authentication issues, just log them
      if (error.message && error.message.includes('Authentication failed')) {
        console.log('Authentication error when checking payment status, will retry');
      } else if (error.message && error.message.includes('Failed to fetch')) {
        // For network errors, just log and don't show to user since we'll retry
        console.log('Network error when checking payment status, will retry');
      } else {
        // For other errors, show to the user
        setError(error.message || 'Failed to check M-PESA payment status');
      }

      // Continue polling even if there was an error
      if (mpesaPaymentStatus === 'pending') {
        setTimeout(() => {
          checkMpesaPaymentStatus(checkoutRequestId);
        }, 5000);
      }
    } finally {
      setIsCheckingPayment(false);
    }
  };

  // Function to record the sale in the database
  const recordSale = async (checkoutRequestId?: string) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const supabase = createClient();

      // Get the current user ID from the watu cookie
      const currentUserId = await getCurrentUserId();
      if (!currentUserId) {
        throw new Error('You must be logged in to record a sale');
      }

      // Validate user ID format
      if (!currentUserId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        console.error('Invalid user ID format:', currentUserId);
        throw new Error('Invalid user ID format. Please log out and log in again.');
      }

      // Prepare M-PESA confirmation message early for validation
      let mpesaConfirmation = formData.mpesaConfirmation;
      if (mpesaPaymentStatus === 'completed' && mpesaReceiptNumber) {
        mpesaConfirmation = `Payment completed via M-PESA. Receipt Number: ${mpesaReceiptNumber}`;
      }

      // Validate required fields based on sale type
      if (formData.saleType === 'cash') {
        if (!formData.cashPaymentMethod) {
          throw new Error('Cash payment method is required for cash sales');
        }

        if (formData.cashPaymentMethod === 'cash' && !formData.deliveryOption) {
          throw new Error('Delivery option is required for cash payment method');
        }

        if (formData.cashPaymentMethod === 'mpesa') {
          if (!mpesaConfirmation && mpesaPaymentStatus !== 'completed') {
            throw new Error('M-PESA payment must be completed or a confirmation message must be provided');
          }
        }
      }

      // Validate client information
      if (!formData.clientId && formData.saleType !== 'cash') {
        throw new Error('Client ID is required for credit sales');
      }

      // For cash sales, validate client information based on payment method
      if (formData.saleType === 'cash') {
        if (formData.cashPaymentMethod === 'cash') {
          // For cash payments, we need either a regular client or one-off client details
          if (!formData.clientId && !formData.oneOffClientPhone) {
            throw new Error('Either a client must be selected or one-off client phone must be provided for cash payments');
          }
        } else if (formData.cashPaymentMethod === 'mpesa') {
          // For M-PESA payments, we need either:
          // 1. A client (regular or one-off) for automated payment, OR
          // 2. A manual M-PESA confirmation message
          const hasClientForAutomatedPayment = formData.clientId || formData.oneOffClientPhone;
          const hasManualConfirmation = !!mpesaConfirmation;

          if (!hasClientForAutomatedPayment && !hasManualConfirmation) {
            throw new Error('For M-PESA payments, either select a client for automated payment or provide a manual M-PESA confirmation message');
          }
        }
      }

      // Validate products
      if (!products || products.length === 0) {
        throw new Error('At least one product is required for a sale');
      }

      // Check stock availability for all products before proceeding
      for (const product of products) {
        try {
          // Get the current stock for this part
          const { data: stockData, error: stockError } = await supabase
            .from('parts_condition')
            .select('condition, stock')
            .eq('part_id', product.id);

          if (stockError) {
            throw new Error(`Error checking stock for part ${product.title}: ${stockError.message}`);
          }

          // Calculate total available stock across all conditions
          const totalStock = stockData?.reduce((sum, item) => sum + (item.stock || 0), 0) || 0;

          if (totalStock < product.quantity) {
            throw new Error(`Insufficient stock for ${product.title}. Available: ${totalStock}, Requested: ${product.quantity}`);
          }

          console.log(`Stock check passed for ${product.title}: Available: ${totalStock}, Requested: ${product.quantity}`);
        } catch (stockError: any) {
          console.error('Stock check error:', stockError);
          throw stockError;
        }
      }

      // Prepare items for the record_sale function
      const items = products.map(product => ({
        part_id: product.id,
        quantity: product.quantity,
        price_at_sale: product.price,
        discount_amount: product.discount || 0,
        discount_reason: product.discountReason || null
      }));

      // Validate and prepare the parameters for the record_sale function
      console.log('Preparing RPC parameters...');
      console.log('formData.saleType:', formData.saleType, typeof formData.saleType);
      console.log('currentUserId:', currentUserId, typeof currentUserId);
      console.log('items:', items);
      console.log('vatRate:', vatRate, typeof vatRate);
      console.log('includeVat:', includeVat);

      const rpcParams = {
        p_client_id: formData.clientId || null,
        p_sale_type: formData.saleType,
        p_items: items,
        p_staff_user_id: currentUserId,
        p_cash_payment_method: formData.cashPaymentMethod || null,
        p_delivery_option: formData.deliveryOption || null,
        p_mpesa_confirmation: mpesaConfirmation || null,
        p_one_off_client_name: formData.oneOffClientName || null,
        p_one_off_client_phone: formData.oneOffClientPhone || null,
        p_vat_rate: includeVat ? Number(vatRate) : 0
      };

      // Validate critical parameters
      if (!rpcParams.p_sale_type || (rpcParams.p_sale_type !== 'cash' && rpcParams.p_sale_type !== 'credit')) {
        throw new Error(`Invalid sale type: ${rpcParams.p_sale_type}`);
      }

      if (!rpcParams.p_staff_user_id) {
        throw new Error('Staff user ID is required');
      }

      if (!rpcParams.p_items || !Array.isArray(rpcParams.p_items) || rpcParams.p_items.length === 0) {
        throw new Error('Items array is required and must not be empty');
      }

      // Log the parameters for debugging (without showing sensitive data)
      console.log('Record sale parameters:', {
        ...rpcParams,
        p_mpesa_confirmation: mpesaConfirmation ? 'Present (not shown for privacy)' : null,
      });

      // Test Supabase connection first
      try {
        console.log('Testing Supabase connection...');
        const testResponse = await supabase.rpc('get_vat_rate');
        console.log('VAT rate test response:', testResponse);

        if (testResponse.error) {
          throw new Error(`Supabase connection test failed: ${testResponse.error.message}`);
        }

        // Test parameter passing
        console.log('Testing parameter passing...');
        const paramTestResponse = await supabase.rpc('test_record_sale_params', rpcParams);
        console.log('Parameter test response:', paramTestResponse);

        if (paramTestResponse.error) {
          throw new Error(`Parameter test failed: ${paramTestResponse.error.message}`);
        }
      } catch (testError) {
        console.error('Supabase connection test failed:', testError);
        throw new Error('Unable to connect to database. Please check your connection and try again.');
      }

      // Call the record_sale function
      try {
        console.log('Calling record_sale RPC function...');
        console.log('RPC Parameters:', JSON.stringify(rpcParams, null, 2));

        const response = await supabase.rpc('record_sale', rpcParams);
        console.log('Raw RPC response:', response);

        const { data, error } = response;

        if (error) {
          console.error('RPC error details:', {
            message: error.message,
            details: error.details,
            hint: error.hint,
            code: error.code,
            fullError: error
          });
          throw new Error(error.message || error.details || error.hint || 'Database function error');
        }

        if (data === null || data === undefined) {
          throw new Error('Database function returned null/undefined result');
        }

        console.log('Sale recorded successfully with ID:', data);
        setCompletedSaleId(data); // Store the sale ID for PDF generation
      } catch (rpcError: any) {
        console.error('Error in RPC call:', rpcError);
        console.error('RPC Error type:', typeof rpcError);
        console.error('RPC Error constructor:', rpcError?.constructor?.name);
        console.error('RPC Error keys:', Object.keys(rpcError || {}));
        console.error('RPC Error JSON:', JSON.stringify(rpcError, null, 2));

        // Check for specific error messages from the database function
        if (rpcError?.message && rpcError.message.includes('Insufficient stock')) {
          throw new Error(`Stock error: ${rpcError.message}`);
        }

        // If it's a Supabase error, extract the message
        if (rpcError?.message) {
          throw new Error(rpcError.message);
        }

        // If it's an empty object or no useful error info, provide a helpful message
        if (!rpcError || Object.keys(rpcError).length === 0) {
          throw new Error('Unknown error occurred while recording sale. Please check your network connection and try again.');
        }

        // Fallback error message
        throw new Error('Failed to record sale. Please check the console for details.');
      }

      setSuccess(true);
      setTimeout(() => {
        onComplete();
      }, 1500);
    } catch (error: any) {
      console.error('Error recording sale:', error);
      console.error('Error type:', typeof error);
      console.error('Error constructor:', error.constructor.name);
      console.error('Error keys:', Object.keys(error));

      // Log more detailed information about the error
      if (error.code) {
        console.error('Error code:', error.code);
      }
      if (error.details) {
        console.error('Error details:', error.details);
      }
      if (error.hint) {
        console.error('Error hint:', error.hint);
      }
      if (error.message) {
        console.error('Error message:', error.message);
      }

      // Try to extract meaningful error message
      let errorMessage = 'Failed to record sale. Please try again.';

      if (error.message) {
        errorMessage = error.message;
      } else if (error.details) {
        errorMessage = error.details;
      } else if (error.hint) {
        errorMessage = error.hint;
      }

      // Check if it's a Supabase error
      if (error.code && error.code.startsWith('PGRST')) {
        setError(`Database error: ${errorMessage}`);
      } else if (error.code && error.code.startsWith('22')) {
        setError(`Data format error: ${errorMessage}`);
      } else {
        setError(errorMessage);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Main submit handler
  const handleSubmit = async () => {
    // If this is an M-PESA payment and we haven't initiated it yet
    if (
      formData.cashPaymentMethod === 'mpesa' &&
      !formData.mpesaConfirmation &&
      !mpesaPaymentInitiated
    ) {
      await initiateMpesaPayment();
    }
    // If this is an M-PESA payment that has been completed
    else if (
      formData.cashPaymentMethod === 'mpesa' &&
      mpesaPaymentStatus === 'completed'
    ) {
      await recordSale(mpesaCheckoutRequestId || undefined);
    }
    // If this is an M-PESA payment with manual confirmation
    else if (
      formData.cashPaymentMethod === 'mpesa' &&
      formData.mpesaConfirmation
    ) {
      await recordSale();
    }
    // If this is an M-PESA payment that is still pending
    else if (
      formData.cashPaymentMethod === 'mpesa' &&
      mpesaPaymentInitiated &&
      mpesaPaymentStatus === 'pending'
    ) {
      // Just check the payment status again
      if (mpesaCheckoutRequestId) {
        checkMpesaPaymentStatus(mpesaCheckoutRequestId);
      }
    }
    // For all other payment methods
    else {
      await recordSale();
    }
  };

  const getClientName = () => {
    if (clientDetails) {
      return clientDetails.name;
    }

    if (formData.clientType === 'one_off') {
      return formData.oneOffClientName || 'One-off Client';
    }

    return 'Unknown Client';
  };

  const getClientPhone = () => {
    if (clientDetails) {
      return clientDetails.phone_number;
    }

    if (formData.clientType === 'one_off') {
      return formData.oneOffClientPhone || 'N/A';
    }

    return 'N/A';
  };

  const getSaleTypeLabel = () => {
    return formData.saleType === 'cash' ? 'Cash Sale' : 'Credit Sale';
  };

  const getPaymentMethodLabel = () => {
    if (formData.saleType === 'credit') {
      return 'Credit';
    }
    return formData.cashPaymentMethod === 'cash' ? 'Cash' : 'MPESA';
  };

  const getDeliveryOptionLabel = () => {
    if (formData.deliveryOption === 'at_shop') {
      return 'At Shop';
    }
    if (formData.deliveryOption === 'delivered') {
      return 'Delivered';
    }
    return 'N/A';
  };

  const handleGeneratePDF = async () => {
    if (!completedSaleId) return;

    try {
      // Fetch the complete sale data for PDF generation
      const supabase = createClient();

      // First, try with the created_by relationship
      let { data: saleData, error } = await supabase
        .from('sales')
        .select(`
          *,
          clients:client_id(id, name, phone_number),
          profiles:created_by(id, full_name),
          sale_items(
            id,
            quantity,
            price_at_sale,
            discount_amount,
            discount_reason,
            parts:part_id(id, title)
          ),
          mpesa_payments(mpesa_confirmation_message)
        `)
        .eq('id', completedSaleId)
        .single();

      // If the created_by relationship doesn't exist, try without it
      if (error && error.message.includes('Could not find a relationship between \'sales\' and \'created_by\'')) {
        console.log('created_by column not found, fetching without staff information...');
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('sales')
          .select(`
            *,
            clients:client_id(id, name, phone_number),
            sale_items(
              id,
              quantity,
              price_at_sale,
              discount_amount,
              discount_reason,
              parts:part_id(id, title)
            ),
            mpesa_payments(mpesa_confirmation_message)
          `)
          .eq('id', completedSaleId)
          .single();

        if (fallbackError) {
          throw new Error(`Failed to fetch sale data: ${fallbackError.message}`);
        }

        saleData = fallbackData;
        toast.success('PDF generated successfully! Note: Run the database migration to include staff details in future receipts.');
      } else if (error) {
        throw new Error(`Failed to fetch sale data: ${error.message}`);
      }

      if (!saleData) {
        throw new Error('Sale not found');
      }

      await generatePDFReceipt(saleData);
      toast.success('PDF receipt generated successfully');
    } catch (error) {
      console.error('Error generating PDF:', error);
      if (error instanceof Error) {
        toast.error(`Failed to generate PDF: ${error.message}`);
      } else {
        toast.error('Failed to generate PDF receipt');
      }
    }
  };

  if (success) {
    return (
      <div className="text-center py-8">
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
          <Check className="h-6 w-6 text-green-600" />
        </div>
        <h3 className="mt-2 text-lg font-medium text-gray-900">Sale Recorded Successfully</h3>
        <p className="mt-2 text-sm text-gray-500">
          The sale has been recorded and inventory has been updated.
        </p>
        {completedSaleId && (
          <div className="mt-4">
            <button
              onClick={handleGeneratePDF}
              className="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
            >
              <FileText className="h-4 w-4 mr-2" />
              Generate PDF Receipt
            </button>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded flex items-start">
          <AlertTriangle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
          <div>{error}</div>
        </div>
      )}

      <div className="bg-gray-50 p-4 rounded-md">
        <h3 className="font-medium text-gray-900 mb-2">Sale Summary</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <p className="text-sm text-gray-500">Sale Type</p>
            <p className="font-medium">{getSaleTypeLabel()}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Payment Method</p>
            <p className="font-medium">{getPaymentMethodLabel()}</p>
          </div>
          {formData.deliveryOption && (
            <div>
              <p className="text-sm text-gray-500">Delivery Option</p>
              <p className="font-medium">{getDeliveryOptionLabel()}</p>
            </div>
          )}
          <div>
            <p className="text-sm text-gray-500">Client</p>
            <p className="font-medium">{getClientName()}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Phone</p>
            <p className="font-medium">{getClientPhone()}</p>
          </div>
        </div>

        {/* M-PESA Payment Status */}
        {formData.cashPaymentMethod === 'mpesa' && (
          <div className="mb-4">
            {formData.mpesaConfirmation ? (
              <>
                <p className="text-sm text-gray-500">M-PESA Confirmation</p>
                <p className="text-sm bg-gray-100 p-2 rounded mt-1">{formData.mpesaConfirmation}</p>
              </>
            ) : mpesaPaymentInitiated ? (
              <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
                <div className="flex items-center mb-2">
                  {mpesaPaymentStatus === 'pending' && (
                    <>
                      <div className="animate-spin mr-2">
                        <RefreshCw className="h-5 w-5 text-teal-600" />
                      </div>
                      <p className="font-medium text-gray-900">Payment in progress...</p>
                    </>
                  )}
                  {mpesaPaymentStatus === 'completed' && (
                    <>
                      <div className="mr-2">
                        <Check className="h-5 w-5 text-green-600" />
                      </div>
                      <p className="font-medium text-gray-900">Payment completed!</p>
                    </>
                  )}
                  {mpesaPaymentStatus === 'failed' && (
                    <>
                      <div className="mr-2">
                        <AlertTriangle className="h-5 w-5 text-red-600" />
                      </div>
                      <p className="font-medium text-gray-900">Payment failed</p>
                    </>
                  )}
                </div>

                {mpesaPaymentStatus === 'pending' && (
                  <p className="text-sm text-gray-600">
                    An M-PESA payment request has been sent to {getClientPhone()}.
                    Please ask the client to check their phone and enter their M-PESA PIN to complete the payment.
                  </p>
                )}

                {mpesaPaymentStatus === 'completed' && mpesaReceiptNumber && (
                  <p className="text-sm text-gray-600">
                    M-PESA Receipt Number: <span className="font-medium">{mpesaReceiptNumber}</span>
                  </p>
                )}

                {mpesaPaymentStatus === 'failed' && (
                  <div className="mt-2">
                    <button
                      type="button"
                      onClick={() => initiateMpesaPayment()}
                      disabled={isSubmitting}
                      className="px-3 py-1 bg-teal-600 text-white text-sm rounded-md hover:bg-teal-700 disabled:opacity-50 flex items-center"
                    >
                      <Smartphone className="h-4 w-4 mr-1" />
                      Try Again
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
                <p className="text-sm text-gray-600">
                  When you click "Complete Sale", the client will receive an M-PESA payment prompt on their phone.
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* VAT Settings */}
      <div className="bg-gray-50 p-4 rounded-md border border-gray-200 mb-6">
        <h3 className="font-medium text-gray-900 mb-3">VAT Settings</h3>
        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={includeVat}
              onChange={(e) => setIncludeVat(e.target.checked)}
              className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
            />
            <span className="ml-2 text-sm text-gray-700">Include VAT</span>
          </label>
          {includeVat && (
            <div className="flex items-center space-x-2">
              <label className="text-sm text-gray-700">VAT Rate:</label>
              <input
                type="number"
                value={vatRate}
                onChange={(e) => setVatRate(parseFloat(e.target.value) || 0)}
                min="0"
                max="100"
                step="0.01"
                className="w-20 px-2 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal-500"
              />
              <span className="text-sm text-gray-700">%</span>
            </div>
          )}
        </div>
      </div>

      {/* Products Table */}
      <div>
        <h3 className="font-medium text-gray-900 mb-2">Products</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Quantity
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Discount
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {products.map((product) => (
                <tr key={product.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {product.title}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    Kshs {product.price.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {product.quantity}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {product.discount ? `Kshs ${product.discount.toLocaleString()}` : '-'}
                    {product.discountReason && (
                      <p className="text-xs text-gray-500 mt-1">{product.discountReason}</p>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Kshs {((product.price * product.quantity) - (product.discount || 0)).toLocaleString()}
                  </td>
                </tr>
              ))}
            </tbody>
            <tfoot className="bg-gray-50">
              <tr>
                <td colSpan={4} className="px-6 py-3 text-right text-sm font-medium text-gray-900">
                  Subtotal:
                </td>
                <td className="px-6 py-3 text-sm font-medium text-gray-900">
                  Kshs {subtotal.toLocaleString()}
                </td>
              </tr>
              <tr>
                <td colSpan={4} className="px-6 py-3 text-right text-sm font-medium text-gray-900">
                  Total Discount:
                </td>
                <td className="px-6 py-3 text-sm font-medium text-gray-900">
                  Kshs {totalDiscount.toLocaleString()}
                </td>
              </tr>
              <tr>
                <td colSpan={4} className="px-6 py-3 text-right text-sm font-medium text-gray-900">
                  Total Before VAT:
                </td>
                <td className="px-6 py-3 text-sm font-medium text-gray-900">
                  Kshs {totalBeforeVat.toLocaleString()}
                </td>
              </tr>
              {includeVat && (
                <tr>
                  <td colSpan={4} className="px-6 py-3 text-right text-sm font-medium text-gray-900">
                    VAT ({vatRate}%):
                  </td>
                  <td className="px-6 py-3 text-sm font-medium text-gray-900">
                    Kshs {vatAmount.toLocaleString()}
                  </td>
                </tr>
              )}
              <tr className="border-t-2 border-gray-300">
                <td colSpan={4} className="px-6 py-3 text-right text-sm font-bold text-gray-900">
                  Total {includeVat ? '(Inc. VAT)' : ''}:
                </td>
                <td className="px-6 py-3 text-sm font-bold text-gray-900">
                  Kshs {total.toLocaleString()}
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between pt-4">
        <button
          type="button"
          onClick={onBack}
          disabled={isSubmitting}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50"
        >
          Back
        </button>
        <button
          type="button"
          onClick={handleSubmit}
          disabled={isSubmitting || isCheckingPayment}
          className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 disabled:opacity-50 flex items-center"
        >
          {isSubmitting || isCheckingPayment ? (
            <>
              <LoadingSpinner size={16} />
              <span className="ml-2">
                {isCheckingPayment ? 'Checking payment...' : 'Processing...'}
              </span>
            </>
          ) : mpesaPaymentInitiated && mpesaPaymentStatus === 'pending' ? (
            'Waiting for payment...'
          ) : mpesaPaymentInitiated && mpesaPaymentStatus === 'completed' ? (
            'Complete Sale'
          ) : formData.cashPaymentMethod === 'mpesa' && !formData.mpesaConfirmation ? (
            <>
              <Smartphone className="h-4 w-4 mr-2" />
              <span>Send Payment Request</span>
            </>
          ) : (
            'Complete Sale'
          )}
        </button>
      </div>
    </div>
  );
};

export default SaleSummaryStep;
