import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Generate smart mock alternatives based on VW/Audi part number patterns
function generateMockAlternatives(partNumber: string): string[] {
  const alternatives: string[] = [];

  // VW/Audi revision letters (excluding I and O)
  const revisionLetters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T'];

  // Check if part number ends with a letter (revision)
  const lastChar = partNumber.slice(-1);
  const isRevisionLetter = /[A-Z]/.test(lastChar);

  if (isRevisionLetter) {
    // Generate superseded and newer revisions
    const currentIndex = revisionLetters.indexOf(lastChar);
    const baseNumber = partNumber.slice(0, -1);

    // Add earlier revisions (superseded)
    for (let i = Math.max(0, currentIndex - 3); i < currentIndex; i++) {
      alternatives.push(baseNumber + revisionLetters[i]);
    }

    // Add later revisions (newer versions)
    for (let i = currentIndex + 1; i < Math.min(revisionLetters.length, currentIndex + 4); i++) {
      alternatives.push(baseNumber + revisionLetters[i]);
    }

    // Add version without revision letter
    if (baseNumber.length >= 8) {
      alternatives.push(baseNumber);
    }
  } else {
    // If no revision letter, add some with letters
    alternatives.push(partNumber + 'A');
    alternatives.push(partNumber + 'B');
    alternatives.push(partNumber + 'C');
  }

  // Generate cross-platform variants (change some middle digits)
  if (partNumber.length >= 8) {
    // Change first digit for platform variants
    const firstDigitVariant = (parseInt(partNumber[0]) % 9 + 1).toString() + partNumber.slice(1);
    if (firstDigitVariant !== partNumber) {
      alternatives.push(firstDigitVariant);
    }

    // Change middle section for application variants
    const middleVariant = partNumber.slice(0, 3) + '0' + partNumber.slice(4);
    if (middleVariant !== partNumber && middleVariant.length === partNumber.length) {
      alternatives.push(middleVariant);
    }
  }

  // Remove duplicates and original part number
  return [...new Set(alternatives)].filter(alt => alt !== partNumber).slice(0, 6);
}

export async function POST(request: NextRequest) {
  try {
    console.log('🤖 Alternative part numbers API called');

    const { partNumber, partId } = await request.json();
    console.log('📝 Request data:', { partNumber, partId });

    if (!partNumber) {
      console.error('❌ Missing required part number');
      return NextResponse.json(
        { error: 'Part number is required' },
        { status: 400 }
      );
    }

    // Initialize Gemini AI
    const apiKey = process.env.GEMINI_API_KEY;
    console.log('🔑 API key available:', !!apiKey);
    console.log('🔑 Environment variables available:', Object.keys(process.env).filter(key => key.includes('GEMINI')));

    if (!apiKey) {
      console.error('❌ Gemini API key not configured');
      console.error('❌ Available env vars:', Object.keys(process.env).slice(0, 10));

      // Generate smart mock data based on part number patterns
      const mockAlternatives = generateMockAlternatives(partNumber);

      return NextResponse.json({
        success: true,
        alternatives: mockAlternatives,
        originalPartNumber: partNumber,
        count: mockAlternatives.length,
        source: 'mock_data'
      });
    }

    console.log('🤖 Initializing Gemini AI...');

    let genAI, model;
    try {
      genAI = new GoogleGenerativeAI(apiKey);
      model = genAI.getGenerativeModel({ model: 'gemini-2.0-flash' });
      console.log('✅ Gemini AI initialized successfully');
    } catch (initError: any) {
      console.error('❌ Failed to initialize Gemini AI:', initError);
      throw new Error(`Gemini AI initialization failed: ${initError.message}`);
    }

    console.log('🤖 Generating alternative part numbers for:', { partNumber });

    // Create a comprehensive prompt focused on part number analysis
    const prompt = `
You are an automotive parts expert specializing in Volkswagen (VW) and Audi parts numbering systems.

Analyze this VW/Audi part number: ${partNumber}

Generate alternative and superseded part numbers based on:

1. **Part Number Pattern Analysis:**
   - VW/Audi part numbers often follow patterns like: 1K0199262M, 7L6199207B, 06J127025E
   - Last character often indicates revision (A, B, C, D, E, F, G, H, J, K, L, M, N, P, Q, R, S, T)
   - Sometimes numbers change in middle digits for different applications
   - Regional variations may exist

2. **Generate These Types:**
   - **Superseded versions**: Earlier revisions (if current ends in M, generate L, K, J, etc.)
   - **Newer revisions**: Later versions (if current ends in C, generate D, E, F, etc.)
   - **Cross-platform variants**: Same component for different models (slight number variations)
   - **Regional variants**: Different market versions of same part

3. **VW/Audi Numbering Logic:**
   - Analyze the pattern of the given part number
   - Generate realistic variations following VW/Audi conventions
   - Consider that parts often have 8-11 character alphanumeric codes
   - Revision letters typically: A, B, C, D, E, F, G, H, J, K, L, M, N, P, Q, R, S, T (no I, O)

4. **Guidelines:**
   - Generate 4-8 realistic alternative part numbers
   - Each must follow VW/Audi part numbering conventions
   - Exclude the original part number: ${partNumber}
   - Focus on likely superseded/alternative versions
   - Make variations that would realistically exist

Return ONLY a JSON array of part numbers, like this:
["7L6199207", "7L6199207C", "7L6199207D", "7L0199207B"]

No additional text or explanation.
`;

    console.log('🤖 Sending request to Gemini AI...');
    const result = await model.generateContent(prompt);
    console.log('🤖 Received response from Gemini AI');

    const response = await result.response;
    const text = response.text();

    console.log('🤖 Gemini AI response:', text);

    // Parse the JSON response
    let alternatives: string[] = [];
    try {
      // Clean the response text (remove any markdown formatting)
      const cleanText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      alternatives = JSON.parse(cleanText);
      
      // Validate that it's an array of strings
      if (!Array.isArray(alternatives)) {
        throw new Error('Response is not an array');
      }
      
      // Filter out invalid entries and the original part number
      alternatives = alternatives
        .filter(alt => typeof alt === 'string' && alt.trim().length > 0)
        .filter(alt => alt.trim() !== partNumber.trim())
        .map(alt => alt.trim())
        .slice(0, 8); // Limit to 8 alternatives max

    } catch (parseError) {
      console.error('🚨 Error parsing AI response:', parseError);
      console.log('🚨 Raw response:', text);
      
      // Fallback: try to extract part numbers from text using regex
      const partNumberPattern = /[A-Z0-9]{6,15}/g;
      const matches = text.match(partNumberPattern) || [];
      alternatives = matches
        .filter(match => match !== partNumber)
        .slice(0, 6);
      
      if (alternatives.length === 0) {
        return NextResponse.json(
          { error: 'Failed to parse AI response', rawResponse: text },
          { status: 500 }
        );
      }
    }

    console.log('✅ Generated alternative part numbers:', alternatives);

    return NextResponse.json({
      success: true,
      alternatives: alternatives,
      originalPartNumber: partNumber,
      count: alternatives.length
    });

  } catch (error: any) {
    console.error('🚨 Error in generate-alternative-part-numbers API:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to generate alternative part numbers',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
