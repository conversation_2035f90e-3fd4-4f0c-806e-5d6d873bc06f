'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Car } from 'lucide-react';

interface DashboardHeaderProps {
  title: string;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({ title }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white p-6 border-b border-gray-200 mb-6 shadow-sm"
    >
      <div className="container mx-auto flex items-center">
        <Car size={32} className="text-teal-600 mr-4" />
        <div>
          <h1 className="text-2xl font-bold text-gray-800">{title}</h1>
          <p className="text-gray-600">Manage your vehicle database</p>
        </div>
      </div>
    </motion.div>
  );
};

export default DashboardHeader;
