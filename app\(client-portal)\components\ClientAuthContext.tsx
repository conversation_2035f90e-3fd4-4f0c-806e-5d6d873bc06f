'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { User, Session } from '@supabase/supabase-js';
import { useRouter, usePathname } from 'next/navigation';

interface ClientAuthContextType {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isClient: boolean;
  clientData: any | null;
}

const ClientAuthContext = createContext<ClientAuthContextType>({
  user: null,
  session: null,
  isLoading: true,
  isClient: false,
  clientData: null
});

export const useClientAuth = () => useContext(ClientAuthContext);

interface ClientAuthProviderProps {
  children: ReactNode;
}

const ClientAuthProvider: React.FC<ClientAuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isClient, setIsClient] = useState(false);
  const [clientData, setClientData] = useState<any | null>(null);
  
  const router = useRouter();
  const pathname = usePathname();
  
  useEffect(() => {
    const supabase = createClient();
    
    // Check for existing user
    const checkSession = async () => {
      setIsLoading(true);

      try {
        // Use getUser() instead of getSession() for security
        const { data: { user }, error } = await supabase.auth.getUser();

        if (error) {
          console.error('Error getting user:', error);
          throw error;
        }

        if (user) {
          // Get session for additional data if needed
          const { data: { session } } = await supabase.auth.getSession();
          setSession(session);
          setUser(user);
          
          // Check if user is a client
          const { data: clientProfile, error: clientError } = await supabase
            .from('client_profiles')
            .select(`
              client_id,
              clients(*)
            `)
            .eq('profile_id', session.user.id)
            .single();
          
          if (clientError && clientError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
            console.error('Error fetching client profile:', clientError);
          }
          
          if (clientProfile) {
            setIsClient(true);
            setClientData(clientProfile.clients);
          } else {
            setIsClient(false);
            
            // If not a client and trying to access client portal, redirect to login
            if (pathname?.startsWith('/client') && pathname !== '/client/unauthorized') {
              router.push('/client/unauthorized');
            }
          }
        } else if (pathname?.startsWith('/client') && pathname !== '/login') {
          // No session and trying to access client portal, redirect to login
          router.push('/login?redirect=/client');
        }
      } catch (err) {
        console.error('Session check error:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    // Initial session check
    checkSession();
    
    // Subscribe to auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, newSession) => {
        if (event === 'SIGNED_IN' && newSession) {
          setSession(newSession);
          setUser(newSession.user);
          
          // Check if user is a client
          const { data: clientProfile, error: clientError } = await supabase
            .from('client_profiles')
            .select(`
              client_id,
              clients(*)
            `)
            .eq('profile_id', newSession.user.id)
            .single();
          
          if (clientError && clientError.code !== 'PGRST116') {
            console.error('Error fetching client profile:', clientError);
          }
          
          if (clientProfile) {
            setIsClient(true);
            setClientData(clientProfile.clients);
          } else {
            setIsClient(false);
            
            // If not a client and trying to access client portal, redirect to unauthorized
            if (pathname?.startsWith('/client') && pathname !== '/client/unauthorized') {
              router.push('/client/unauthorized');
            }
          }
        } else if (event === 'SIGNED_OUT') {
          setSession(null);
          setUser(null);
          setIsClient(false);
          setClientData(null);
          
          // If signed out and trying to access client portal, redirect to login
          if (pathname?.startsWith('/client') && pathname !== '/login') {
            router.push('/login?redirect=/client');
          }
        }
      }
    );
    
    // Cleanup subscription
    return () => {
      subscription.unsubscribe();
    };
  }, [router, pathname]);
  
  return (
    <ClientAuthContext.Provider value={{ user, session, isLoading, isClient, clientData }}>
      {children}
    </ClientAuthContext.Provider>
  );
};

export default ClientAuthProvider;
