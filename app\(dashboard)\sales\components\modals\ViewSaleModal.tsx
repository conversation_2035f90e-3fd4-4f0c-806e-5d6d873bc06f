'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { X, Printer, FileText } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import { generatePDFReceipt } from '../../utils/pdfGenerator';
import { toast } from 'react-hot-toast';

interface ViewSaleModalProps {
  isOpen: boolean;
  onClose: () => void;
  saleId: string;
}

const ViewSaleModal: React.FC<ViewSaleModalProps> = ({ isOpen, onClose, saleId }) => {
  const [sale, setSale] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSaleDetails = async () => {
      if (!saleId) return;

      setIsLoading(true);
      setError(null);

      try {
        const supabase = createClient();

        console.log('Fetching sale details for ID:', saleId);

        // Fetch sale with related data
        // First, try with the created_by relationship
        let { data, error } = await supabase
          .from('sales')
          .select(`
            *,
            clients(id, name, phone_number),
            sale_items(
              id,
              part_id,
              quantity,
              price_at_sale,
              discount_amount,
              discount_reason,
              parts(id, title)
            ),
            mpesa_payments(id, mpesa_confirmation_message),
            profiles:created_by(id, full_name)
          `)
          .eq('id', saleId)
          .single();

        // If the created_by relationship doesn't exist, try without it
        if (error && error.message.includes('Could not find a relationship between \'sales\' and \'created_by\'')) {
          console.log('created_by column not found, fetching without staff information...');
          const { data: fallbackData, error: fallbackError } = await supabase
            .from('sales')
            .select(`
              *,
              clients(id, name, phone_number),
              sale_items(
                id,
                part_id,
                quantity,
                price_at_sale,
                discount_amount,
                discount_reason,
                parts(id, title)
              ),
              mpesa_payments(id, mpesa_confirmation_message)
            `)
            .eq('id', saleId)
            .single();

          if (fallbackError) {
            console.error('Supabase error details:', {
              message: fallbackError.message,
              details: fallbackError.details,
              hint: fallbackError.hint,
              code: fallbackError.code
            });
            throw fallbackError;
          }

          data = fallbackData;
        } else if (error) {
          console.error('Supabase error details:', {
            message: error.message,
            details: error.details,
            hint: error.hint,
            code: error.code
          });
          throw error;
        }

        console.log('Sale query response:', { data, error });

        if (!data) {
          throw new Error('No sale found with the provided ID');
        }

        console.log('Sale data loaded successfully:', data);
        setSale(data);
      } catch (error: any) {
        console.error('Error fetching sale details:', error);
        console.error('Error type:', typeof error);
        console.error('Error keys:', Object.keys(error || {}));
        console.error('Error JSON:', JSON.stringify(error, null, 2));

        let errorMessage = 'Failed to load sale details';
        if (error?.message) {
          errorMessage = error.message;
        } else if (error?.details) {
          errorMessage = error.details;
        }

        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };
    
    if (isOpen) {
      fetchSaleDetails();
    }
  }, [saleId, isOpen]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getClientName = () => {
    if (sale?.client_id && sale?.clients) {
      return sale.clients.name;
    }
    return sale?.one_off_client_name || 'One-off Customer';
  };

  const getClientPhone = () => {
    if (sale?.client_id && sale?.clients) {
      return sale.clients.phone_number;
    }
    return sale?.one_off_client_phone || 'N/A';
  };

  const getSaleTypeLabel = () => {
    return sale?.sale_type === 'cash' ? 'Cash Sale' : 'Credit Sale';
  };

  const getPaymentMethodLabel = () => {
    if (sale?.sale_type === 'credit') {
      return 'Credit';
    }
    return sale?.cash_payment_method === 'cash' ? 'Cash' : 'MPESA';
  };

  const getDeliveryOptionLabel = () => {
    if (sale?.delivery_option === 'at_shop') {
      return 'At Shop';
    }
    if (sale?.delivery_option === 'delivered') {
      return 'Delivered';
    }
    return 'N/A';
  };

  // Calculate totals
  const subtotal = sale?.sale_items?.reduce(
    (sum: number, item: any) => sum + (item.price_at_sale * item.quantity),
    0
  ) || 0;

  const totalDiscount = sale?.total_discount || 0;
  const totalBeforeVat = sale?.total_before_vat || (subtotal - totalDiscount);
  const vatAmount = sale?.vat_amount || 0;
  const vatRate = sale?.vat_rate || 0;
  const total = sale?.total_amount || sale?.total_with_vat || totalBeforeVat;

  const handlePrint = () => {
    window.print();
  };

  const handleGeneratePDF = async () => {
    if (!sale) return;

    try {
      await generatePDFReceipt(sale);
      toast.success('PDF receipt generated successfully');
    } catch (error) {
      console.error('Error generating PDF:', error);
      if (error instanceof Error) {
        toast.error(`Failed to generate PDF: ${error.message}`);
      } else {
        toast.error('Failed to generate PDF receipt');
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] overflow-y-auto">
      {/* Overlay and Modal Content as siblings in the same stacking context */}
      <div className="fixed inset-0 z-[9999]">
        {/* Overlay */}
        <div
          className="absolute inset-0 bg-gray-500 opacity-75 z-[9998]"
          aria-hidden="true"
          onClick={onClose}
        ></div>

        {/* Modal Content */}
        <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0 relative z-[10000]">
          <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full z-[10000] max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
          {/* Modal Header */}
          <div className="bg-gray-50 px-6 py-4 flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">
              Sale Details
            </h3>
            <div className="flex items-center">
              <button
                onClick={handleGeneratePDF}
                className="mr-3 text-gray-600 hover:text-gray-900 focus:outline-none"
                title="Generate PDF Receipt"
              >
                <FileText className="h-5 w-5" />
              </button>
              <button
                onClick={handlePrint}
                className="mr-3 text-gray-600 hover:text-gray-900 focus:outline-none"
                title="Print Receipt"
              >
                <Printer className="h-5 w-5" />
              </button>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Modal Content */}
          <div className="bg-white px-6 py-4">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <LoadingSpinner size={24} />
              </div>
            ) : error ? (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                {error}
              </div>
            ) : (
              <div className="space-y-6">
                {/* Sale Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 bg-gray-50 p-4 rounded-md">
                  <div>
                    <p className="text-sm text-gray-500">Sale ID</p>
                    <p className="font-medium">{sale.id}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Date</p>
                    <p className="font-medium">{formatDate(sale.sale_timestamp)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Sale Type</p>
                    <p className="font-medium">{getSaleTypeLabel()}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Payment Method</p>
                    <p className="font-medium">{getPaymentMethodLabel()}</p>
                  </div>
                  {sale.delivery_option && (
                    <div>
                      <p className="text-sm text-gray-500">Delivery Option</p>
                      <p className="font-medium">{getDeliveryOptionLabel()}</p>
                    </div>
                  )}
                  <div>
                    <p className="text-sm text-gray-500">Staff</p>
                    <p className="font-medium">{sale.profiles?.full_name || 'Unknown'}</p>
                  </div>
                </div>

                {/* Client Info */}
                <div className="bg-gray-50 p-4 rounded-md">
                  <h4 className="font-medium text-gray-900 mb-2">Client Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Name</p>
                      <p className="font-medium">{getClientName()}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Phone</p>
                      <p className="font-medium">{getClientPhone()}</p>
                    </div>
                  </div>
                </div>

                {/* MPESA Confirmation */}
                {sale.mpesa_payments && sale.mpesa_payments.length > 0 && (
                  <div className="bg-gray-50 p-4 rounded-md">
                    <h4 className="font-medium text-gray-900 mb-2">MPESA Confirmation</h4>
                    <p className="text-sm bg-white p-2 rounded">
                      {sale.mpesa_payments[0].mpesa_confirmation_message}
                    </p>
                  </div>
                )}

                {/* Items Table */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Items</h4>

                  {/* Desktop Table */}
                  <div className="hidden md:block">
                    <table className="w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Product
                          </th>
                          <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Price
                          </th>
                          <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Qty
                          </th>
                          <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Discount
                          </th>
                          <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Total
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {sale.sale_items.map((item: any) => (
                          <tr key={item.id}>
                            <td className="px-3 py-2 text-sm text-gray-900">
                              <div className="font-medium truncate max-w-xs" title={item.parts?.title || `Part ID: ${item.part_id}`}>
                                {item.parts?.title || `Part ID: ${item.part_id}`}
                              </div>
                            </td>
                            <td className="px-2 py-2 text-sm text-gray-900">
                              {parseFloat(item.price_at_sale).toLocaleString()}
                            </td>
                            <td className="px-2 py-2 text-sm text-gray-900 text-center">
                              {item.quantity}
                            </td>
                            <td className="px-2 py-2 text-sm text-gray-900">
                              {parseFloat(item.discount_amount) > 0
                                ? parseFloat(item.discount_amount).toLocaleString()
                                : '-'}
                              {item.discount_reason && (
                                <div className="text-xs text-gray-500 mt-1 truncate max-w-20" title={item.discount_reason}>
                                  {item.discount_reason}
                                </div>
                              )}
                            </td>
                            <td className="px-3 py-2 text-sm font-medium text-gray-900">
                              {((parseFloat(item.price_at_sale) * item.quantity) - parseFloat(item.discount_amount)).toLocaleString()}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot className="bg-gray-50">
                        <tr>
                          <td colSpan={4} className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                            Subtotal:
                          </td>
                          <td className="px-3 py-2 text-sm font-medium text-gray-900">
                            {subtotal.toLocaleString()}
                          </td>
                        </tr>
                        <tr>
                          <td colSpan={4} className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                            Discount:
                          </td>
                          <td className="px-3 py-2 text-sm font-medium text-gray-900">
                            {totalDiscount.toLocaleString()}
                          </td>
                        </tr>
                        <tr>
                          <td colSpan={4} className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                            Before VAT:
                          </td>
                          <td className="px-3 py-2 text-sm font-medium text-gray-900">
                            {totalBeforeVat.toLocaleString()}
                          </td>
                        </tr>
                        {vatAmount > 0 && (
                          <tr>
                            <td colSpan={4} className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                              VAT ({vatRate}%):
                            </td>
                            <td className="px-3 py-2 text-sm font-medium text-gray-900">
                              {vatAmount.toLocaleString()}
                            </td>
                          </tr>
                        )}
                        <tr className="border-t-2 border-gray-300">
                          <td colSpan={4} className="px-3 py-2 text-right text-sm font-bold text-gray-900">
                            Total {vatAmount > 0 ? '(Inc. VAT)' : ''}:
                          </td>
                          <td className="px-3 py-2 text-sm font-bold text-gray-900">
                            {total.toLocaleString()}
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>

                  {/* Mobile Cards */}
                  <div className="md:hidden space-y-3">
                    {sale.sale_items.map((item: any) => (
                      <div key={item.id} className="bg-gray-50 rounded-lg p-3">
                        <div className="font-medium text-gray-900 mb-2">
                          {item.parts?.title || `Part ID: ${item.part_id}`}
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <span className="text-gray-500">Price:</span>
                            <span className="ml-1 font-medium">Kshs {parseFloat(item.price_at_sale).toLocaleString()}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Qty:</span>
                            <span className="ml-1 font-medium">{item.quantity}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Discount:</span>
                            <span className="ml-1 font-medium">
                              {parseFloat(item.discount_amount) > 0
                                ? `Kshs ${parseFloat(item.discount_amount).toLocaleString()}`
                                : '-'}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-500">Total:</span>
                            <span className="ml-1 font-medium">
                              Kshs {((parseFloat(item.price_at_sale) * item.quantity) - parseFloat(item.discount_amount)).toLocaleString()}
                            </span>
                          </div>
                        </div>
                        {item.discount_reason && (
                          <div className="mt-2 text-xs text-gray-500">
                            <span className="font-medium">Reason:</span> {item.discount_reason}
                          </div>
                        )}
                      </div>
                    ))}

                    {/* Mobile Totals Summary */}
                    <div className="bg-gray-100 rounded-lg p-3 mt-4">
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Subtotal:</span>
                          <span className="font-medium">Kshs {subtotal.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Discount:</span>
                          <span className="font-medium">Kshs {totalDiscount.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Before VAT:</span>
                          <span className="font-medium">Kshs {totalBeforeVat.toLocaleString()}</span>
                        </div>
                        {vatAmount > 0 && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">VAT ({vatRate}%):</span>
                            <span className="font-medium">Kshs {vatAmount.toLocaleString()}</span>
                          </div>
                        )}
                        <div className="flex justify-between border-t pt-2 border-gray-300">
                          <span className="font-bold text-gray-900">Total {vatAmount > 0 ? '(Inc. VAT)' : ''}:</span>
                          <span className="font-bold text-gray-900">Kshs {total.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default ViewSaleModal;
