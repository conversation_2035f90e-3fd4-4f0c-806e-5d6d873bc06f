import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
// import PropertyAttribute from './PropertyAttribute';
const PropertyAttribute = ({ label, value }: { label: string; value: any }) => (
  <div className="flex justify-between">
    <span className="font-medium">{label}:</span>
    <span>{value}</span>
  </div>
);

interface PropertyCardProps {
  id: string;
  title: string;
  price: number;
  location: string;
  imageUrl: string;
  beds: number;
  baths: number;
  sqFt: number;
  className?: string;
}

const PropertyCard: React.FC<PropertyCardProps> = ({
  id,
  title,
  price,
  location,
  imageUrl,
  beds,
  baths,
  sqFt,
  className = '',
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ y: -5 }}
      className={`bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow ${className}`}
    >
      <Link href={`/shop/${id}`}>
        <div className="relative h-48 w-full">
          <Image
            src={imageUrl}
            alt={title}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        </div>
      </Link>
      
      <div className="p-4">
        <div className="mb-2">
          <span className="text-xl font-bold text-gray-900">${price.toLocaleString()}</span>
        </div>
        
        <Link href={`/shop/${id}`}>
          <h3 className="text-lg font-medium text-gray-800 mb-1 hover:text-blue-600 transition-colors">
            {title}
          </h3>
        </Link>
        
        <p className="text-sm text-gray-500 mb-3">{location}</p>
        
        <div className="flex justify-between items-center">
          <PropertyAttribute icon="bed" value={beds} />
          <PropertyAttribute icon="bath" value={baths} />
          <PropertyAttribute icon="square" value={sqFt} label="sq. ft." />
        </div>
      </div>
    </motion.div>
  );
};

export default PropertyCard;
