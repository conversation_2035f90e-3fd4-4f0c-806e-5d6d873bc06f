// app/(dashboard)/profile/components/ProfileData.tsx
'use client';

import { useEffect, useState } from 'react';
import { User } from "@/app/types/authTypes";
import { UserProfile } from '@/app/types/profile';
import ProfileForm from './forms/profileForm';
import { fetchProfile } from '../actions';

interface ProfileDataProps {
  user: User;
  onProfileUpdate: (updatedProfile: Partial<UserProfile>) => void;
}

export default function ProfileData({ user, onProfileUpdate }: ProfileDataProps) {
  const [profile, setProfile] = useState<UserProfile | null>(null);

  useEffect(() => {
    const loadProfile = async () => {
      const fetchedProfile = await fetchProfile(user.id);
      setProfile(fetchedProfile);
    };

    loadProfile();
  }, [user.id]);

  return (
    <>
      {profile && (
        <ProfileForm
          user={user}
          initialProfile={profile}
          onProfileUpdate={onProfileUpdate}
        />
      )}
    </>
  );
}