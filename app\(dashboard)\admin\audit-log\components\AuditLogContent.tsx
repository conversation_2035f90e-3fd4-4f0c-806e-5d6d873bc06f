'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter } from 'next/navigation';
import { usePermissions } from '@/app/hooks/usePermissions';
import { getAuditLogs } from '@/app/libs/rbac/api';
import Spinner from '@/app/components/ui/Spinner';
import { Button } from '@/app/components/ui/Button';

interface AuditLog {
  id: string;
  timestamp: string;
  user_id: string | null;
  action_type: string;
  target_table: string | null;
  target_record_id: string | null;
  change_details: any | null;
  ip_address: string | null;
  profiles?: {
    full_name: string | null;
    username: string | null;
  } | null;
}

const AuditLogContent = () => {
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const pageSize = 20;

  const { hasPermission, isLoading: isPermissionLoading, error: permissionError } = usePermissions('admin:view_audit_log');
  const router = useRouter();

  // Effect 1: Check permission and redirect if necessary
  useEffect(() => {
    if (!isPermissionLoading) {
      console.log(`AuditLogPage: Permission check complete. hasPermission('admin:view_audit_log'): ${hasPermission}`);
      if (!hasPermission) {
        console.log('AuditLogPage: Redirecting to /admin due to missing permission.');
        setError('You do not have permission to view the audit log.');
        setTimeout(() => router.push('/admin'), 50);
      }
    }
  }, [hasPermission, isPermissionLoading, router]);

  // Effect 2: Fetch data only if permission is granted and check is complete
  useEffect(() => {
    if (hasPermission && !isPermissionLoading && page >= 0) {
      console.log(`AuditLogPage: Permission granted. Fetching audit logs for page ${page}...`);
      fetchLogs();
    } else if (!isPermissionLoading && !hasPermission) {
      setIsLoading(false); // Ensure loading stops if permission denied
    }
  }, [hasPermission, isPermissionLoading, page]);

  const fetchLogs = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const { data, error } = await getAuditLogs(pageSize, page * pageSize);
      if (error) throw error;

      setLogs(prev => page === 0 ? data || [] : [...prev, ...(data || [])]);
      setHasMore((data || []).length === pageSize);
    } catch (err: any) {
      setError(`Failed to fetch audit logs: ${err.message || 'Please try again.'}`);
      console.error('Error fetching audit logs:', err);
      if (page === 0) {
        setLogs([]); // Reset logs if initial page load fails
      }
    } finally {
      setIsLoading(false);
    }
  };

  const loadMore = () => {
    if (!isLoading && hasMore) {
      setPage(prev => prev + 1);
    }
  };

  const formatActionType = (actionType: string) => {
    return actionType
      .split('_')
      .map((word, index) => {
        if (index === 0) {
          return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
        }
        return word.toLowerCase();
      })
      .join(' ');
  };

  const getActionColor = (actionType: string) => {
    if (actionType.startsWith('CREATE')) {
      return 'bg-green-100 text-green-800';
    } else if (actionType.startsWith('UPDATE')) {
      return 'bg-blue-100 text-blue-800';
    } else if (actionType.startsWith('DELETE')) {
      return 'bg-red-100 text-red-800';
    }
    return 'bg-gray-100 text-gray-800';
  };

  // Render loading spinner while permission check OR initial data load is happening
  if ((isPermissionLoading || (isLoading && page === 0))) {
    return (
      <div className="flex min-h-[300px] items-center justify-center">
        <Spinner size="lg" />
      </div>
    );
  }

  // If permission check failed, show error message
  if (!isPermissionLoading && !hasPermission) {
    return (
      <div className="rounded-md border border-red-200 bg-red-50 p-6 text-center shadow-sm">
        <h2 className="mb-2 text-xl font-semibold text-red-700">Access Denied</h2>
        <p className="mb-4 text-red-600">
          {error || 'You do not have the required permission (admin:view_audit_log) to view the audit log.'}
        </p>
        {permissionError && (
          <p className="mb-4 text-sm text-red-500">Details: {permissionError.message}</p>
        )}
        <Button
          onClick={() => router.push('/admin')}
          variant="destructive"
          className="px-4 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        >
          Return to Admin Home
        </Button>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-xl font-semibold">Audit Log</h2>
        <p className="mt-2 text-gray-600">
          View a record of all changes made to roles, permissions, and assignments.
        </p>
      </div>

      {error && (
        <div className="mb-4 rounded-md bg-red-50 p-4 text-red-800">
          <p>{error}</p>
          <button
            onClick={() => setError(null)}
            className="ml-2 text-sm font-medium text-red-600 hover:text-red-800"
          >
            Dismiss
          </button>
        </div>
      )}

      <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Timestamp
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                User
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Action
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Target
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                Details
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 bg-white">
            {logs.length === 0 ? (
              <tr>
                <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                  No audit logs found.
                </td>
              </tr>
            ) : (
              logs.map((log) => (
                <tr key={log.id}>
                  <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                    {new Date(log.timestamp).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {log.profiles?.full_name || log.profiles?.username || 'System'}
                  </td>
                  <td className="px-6 py-4 text-sm">
                    <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getActionColor(log.action_type)}`}>
                      {formatActionType(log.action_type)}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {log.target_table && (
                      <div>
                        <div className="font-medium">{log.target_table}</div>
                        {log.target_record_id && (
                          <div className="text-xs">{log.target_record_id}</div>
                        )}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {log.change_details ? (
                      <details className="cursor-pointer">
                        <summary className="text-blue-600 hover:text-blue-800">View Details</summary>
                        <pre className="mt-2 max-h-40 overflow-auto rounded bg-gray-50 p-2 text-xs">
                          {JSON.stringify(log.change_details, null, 2)}
                        </pre>
                      </details>
                    ) : (
                      'No details available'
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {hasMore && (
        <div className="mt-4 flex justify-center">
          <Button
            onClick={loadMore}
            disabled={isLoading}
            variant="outline"
            className="inline-flex items-center"
          >
            {isLoading ? (
              <>
                <Spinner size="sm" /> <span className="ml-2">Loading...</span>
              </>
            ) : (
              'Load More'
            )}
          </Button>
        </div>
      )}
    </div>
  );
};

export default AuditLogContent;
