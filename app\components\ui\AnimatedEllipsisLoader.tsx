import React, { useState, useEffect } from 'react';

interface AnimatedEllipsisLoaderProps {
  text?: string; // Text to display before the ellipsis
  bgColor?: string; // Background color
  textColor?: string; // Text and dots color
  numDots?: number; // Number of dots in the ellipsis (default: 3)
  animationDuration?: number; // Duration of the animation in milliseconds (default: 1000)
}

const AnimatedEllipsisLoader: React.FC<AnimatedEllipsisLoaderProps> = ({
  text = '',
  bgColor = 'transparent',
  textColor = 'black',
  numDots = 3,
  animationDuration = 1000,
}) => {
  const [dots, setDots] = useState('');

  useEffect(() => {
    const intervalId = setInterval(() => {
      setDots((prevDots) => {
        if (prevDots.length === numDots) {
          return '';
        } else {
          return prevDots + '.';
        }
      });
    }, animationDuration / (numDots + 1));

    return () => clearInterval(intervalId);
  }, [numDots, animationDuration]);

  return (
    <div
      style={{
        backgroundColor: bgColor,
        color: textColor,
        display: 'inline-flex',
        alignItems: 'center',
        padding: '5px 10px',
        borderRadius: '5px'
      }}
    >
      {text}
      <span
        style={{
          whiteSpace: 'nowrap'
        }}
      >
        {dots}
        <span
            style={{
                visibility: 'hidden'
            }}
        >
            {'.'.repeat(numDots - dots.length)}
        </span>
      </span>
    </div>
  );
};

export default AnimatedEllipsisLoader;

/**
 * Examples of how to use the AnimatedEllipsisLoader component:
 *
 * 1. Inside a button:
 *
 * <button disabled>
 *   <AnimatedEllipsisLoader text="Loading" textColor="white" bgColor="#007bff" />
 * </button>
 *
 * 2. Inside an input field (as a placeholder or part of the value):
 *   // Note: Using it directly in an input's value or placeholder might not be the best UX,
 *   // as it's dynamic and can be confusing. Consider a separate element to display the loading state.
 *
 * <input type="text" placeholder="Enter text" disabled />
 * <AnimatedEllipsisLoader text="Searching" textColor="#6c757d" />
 *
 * 3. As a standalone loading indicator:
 *
 * <AnimatedEllipsisLoader text="Processing" numDots={4} animationDuration={1500} />
 *
 * 4. Customizing colors:
 *
 * <AnimatedEllipsisLoader text="Working" bgColor="lightgreen" textColor="darkgreen" />
 *
 * 5. More/Less dots
 * <AnimatedEllipsisLoader text="Waiting" numDots={5} textColor="red" />
 * <AnimatedEllipsisLoader text="Waiting" numDots={2} textColor="blue" animationDuration={500}/>
 */