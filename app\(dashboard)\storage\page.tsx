'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { createClient } from '@/app/libs/supabase/client';
import DashboardHeader from './components/DashboardHeader';
import StorageAreasView from './components/StorageAreasView';
import StorageStats from './components/StorageStats';
import { StorageStats as StorageStatsType } from './types';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

export default function StorageDashboard() {
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<StorageStatsType>({
    totalAreas: 0,
    totalUnits: 0,
    indoorAreas: 0,
    outdoorAreas: 0,
    shelfUnits: 0,
    cageUnits: 0,
    hangingLineUnits: 0,
    openSpaceUnits: 0,
    engineAreaUnits: 0,
    partsStored: 0
  });
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Handle refresh
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Fetch storage statistics
  useEffect(() => {
    const fetchStats = async () => {
      setIsLoading(true);
      try {
        const supabase = createClient();

        // Fetch storage areas
        const { data: areas, error: areasError } = await supabase
          .from('storage_areas')
          .select('*');

        if (areasError) throw areasError;

        // Fetch storage units
        const { data: units, error: unitsError } = await supabase
          .from('storage_units')
          .select('*');

        if (unitsError) throw unitsError;

        // Fetch part locations
        const { data: partLocations, error: locationsError } = await supabase
          .from('part_locations')
          .select('*');

        if (locationsError) throw locationsError;

        // Calculate statistics
        const indoorAreas = areas?.filter(area => area.location_type === 'indoor').length || 0;
        const outdoorAreas = areas?.filter(area => area.location_type === 'outdoor').length || 0;

        const shelfUnits = units?.filter(unit => unit.unit_type === 'shelf').length || 0;
        const cageUnits = units?.filter(unit => unit.unit_type === 'cage').length || 0;
        const hangingLineUnits = units?.filter(unit => unit.unit_type === 'hanging_line').length || 0;
        const openSpaceUnits = units?.filter(unit => unit.unit_type === 'open_space').length || 0;
        const engineAreaUnits = units?.filter(unit => unit.unit_type === 'engine_area').length || 0;

        setStats({
          totalAreas: areas?.length || 0,
          totalUnits: units?.length || 0,
          indoorAreas,
          outdoorAreas,
          shelfUnits,
          cageUnits,
          hangingLineUnits,
          openSpaceUnits,
          engineAreaUnits,
          partsStored: partLocations?.length || 0
        });
      } catch (error) {
        console.error('Error fetching storage statistics:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, [refreshTrigger]);

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader title="Storage Management" />

      <div className="container mx-auto px-4 py-8">
        {/* Stats Section */}
        <StorageStats stats={stats} isLoading={isLoading} />

        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-lg shadow-md p-6 mb-8"
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Manage Storage Locations</h2>
          <p className="text-gray-600 mb-4">
            Use this dashboard to manage storage areas and units for your inventory.
            Storage units are organized within their respective storage areas.
          </p>

          <div className="mt-6">
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <LoadingSpinner size={40} />
              </div>
            ) : (
              <StorageAreasView onRefresh={handleRefresh} />
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
