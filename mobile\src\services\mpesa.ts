import { apiClient } from './api';
import { CONFIG } from '@/constants/config';
import { MpesaPaymentRequest, MpesaPaymentResponse, ApiResponse } from '@/types';

export class MpesaService {
  // Initiate M-PESA payment
  async initiatePayment(
    amount: number,
    phoneNumber: string,
    accountReference: string = 'AUTOFLOW',
    transactionDescription: string = 'Payment for parts purchase'
  ): Promise<MpesaPaymentResponse> {
    try {
      // Format phone number (ensure it starts with 254)
      const formattedPhone = this.formatPhoneNumber(phoneNumber);

      const paymentRequest: MpesaPaymentRequest = {
        amount,
        phoneNumber: formattedPhone,
        accountReference,
        transactionDescription,
      };

      const response = await apiClient.post<MpesaPaymentResponse>(
        CONFIG.ENDPOINTS.MPESA.INITIATE,
        paymentRequest
      );

      return response;
    } catch (error) {
      console.error('Initiate M-PESA payment error:', error);
      throw error;
    }
  }

  // Query payment status
  async queryPaymentStatus(checkoutRequestId: string): Promise<ApiResponse<any>> {
    try {
      const response = await apiClient.post<ApiResponse<any>>(
        CONFIG.ENDPOINTS.MPESA.QUERY,
        { checkoutRequestId }
      );

      return response;
    } catch (error) {
      console.error('Query M-PESA payment status error:', error);
      throw error;
    }
  }

  // Format phone number for M-PESA (ensure it starts with 254)
  private formatPhoneNumber(phoneNumber: string): string {
    // Remove any non-digit characters
    let cleaned = phoneNumber.replace(/\D/g, '');

    // Handle different formats
    if (cleaned.startsWith('0')) {
      // Convert 0712345678 to 254712345678
      cleaned = '254' + cleaned.substring(1);
    } else if (cleaned.startsWith('7') || cleaned.startsWith('1')) {
      // Convert 712345678 to 254712345678
      cleaned = '254' + cleaned;
    } else if (!cleaned.startsWith('254')) {
      // If it doesn't start with 254, assume it's a local number
      cleaned = '254' + cleaned;
    }

    return cleaned;
  }

  // Validate phone number
  validatePhoneNumber(phoneNumber: string): boolean {
    const formatted = this.formatPhoneNumber(phoneNumber);
    
    // Kenyan mobile numbers should be 12 digits starting with 254
    // and the next digit should be 7 or 1
    const regex = /^254[71]\d{8}$/;
    return regex.test(formatted);
  }

  // Get payment methods available
  getPaymentMethods(): Array<{
    id: string;
    name: string;
    description: string;
    icon: string;
    enabled: boolean;
  }> {
    return [
      {
        id: 'mpesa',
        name: 'M-PESA',
        description: 'Pay with M-PESA mobile money',
        icon: 'cellphone',
        enabled: CONFIG.MPESA.CONSUMER_KEY ? true : false,
      },
      {
        id: 'cash',
        name: 'Cash',
        description: 'Pay with cash on delivery/pickup',
        icon: 'cash',
        enabled: true,
      },
      {
        id: 'bank_transfer',
        name: 'Bank Transfer',
        description: 'Pay via bank transfer',
        icon: 'bank',
        enabled: true,
      },
    ];
  }

  // Check if M-PESA is configured and available
  isMpesaAvailable(): boolean {
    return !!(
      CONFIG.MPESA.CONSUMER_KEY &&
      CONFIG.MPESA.CONSUMER_SECRET &&
      CONFIG.MPESA.SHORT_CODE
    );
  }

  // Get M-PESA configuration status
  getMpesaStatus(): {
    configured: boolean;
    environment: string;
    shortCode: string;
  } {
    return {
      configured: this.isMpesaAvailable(),
      environment: CONFIG.MPESA.ENVIRONMENT,
      shortCode: CONFIG.MPESA.SHORT_CODE,
    };
  }

  // Simulate payment for development/testing
  async simulatePayment(
    amount: number,
    phoneNumber: string,
    success: boolean = true
  ): Promise<MpesaPaymentResponse> {
    // Only allow simulation in development mode
    if (!__DEV__) {
      throw new Error('Payment simulation is only available in development mode');
    }

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    if (success) {
      return {
        success: true,
        checkoutRequestId: `sim_${Date.now()}`,
        responseCode: '0',
        responseDescription: 'Success. Request accepted for processing',
        customerMessage: 'Success. Request accepted for processing',
      };
    } else {
      return {
        success: false,
        responseCode: '1',
        responseDescription: 'Insufficient funds',
        customerMessage: 'The balance is insufficient for the transaction',
      };
    }
  }
}

export const mpesaService = new MpesaService();
export default mpesaService;
