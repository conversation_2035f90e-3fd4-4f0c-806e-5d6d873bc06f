import React from 'react';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import ClientCarForm from '../../../../components/ClientCarForm';

export default function EditClientCarPage({ params }: { params: { id: string, carId: string } }) {
  const clientId = params.id;
  const carId = params.carId;
  
  return (
    <div className="p-4 md:p-6 lg:p-8 bg-gray-100 min-h-screen">
      {/* Back button */}
      <Link href={`/clients/${clientId}`} className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-6">
        <ArrowLeft className="w-4 h-4 mr-2" /> Back to Client
      </Link>

      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h1 className="text-2xl font-semibold text-gray-900 mb-1">Edit Client Car</h1>
          <p className="text-gray-600">Update car information for this client</p>
        </div>

        <ClientCarForm clientId={clientId} carId={carId} />
      </div>
    </div>
  );
}
