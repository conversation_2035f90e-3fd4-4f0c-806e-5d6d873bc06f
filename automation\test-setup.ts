#!/usr/bin/env ts-node

import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables from parent directory
dotenv.config({ path: '../.env.local' });

async function testSetup(): Promise<void> {
  console.log('🧪 Testing Jiji Automation Setup');
  console.log('================================\n');

  let allTestsPassed = true;

  // Test 1: Environment Variables
  console.log('1️⃣  Testing Environment Variables...');
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ];

  const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingEnvVars.length > 0) {
    console.log('❌ Missing environment variables:');
    missingEnvVars.forEach(varName => console.log(`   - ${varName}`));
    allTestsPassed = false;
  } else {
    console.log('✅ All required environment variables are set');
  }

  // Test 2: Database Connection
  console.log('\n2️⃣  Testing Database Connection...');
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    const { data, error } = await supabase
      .from('parts')
      .select('id')
      .limit(1);

    if (error) {
      console.log('❌ Database connection failed:', error.message);
      allTestsPassed = false;
    } else {
      console.log('✅ Database connection successful');
    }
  } catch (error) {
    console.log('❌ Database connection error:', error);
    allTestsPassed = false;
  }

  // Test 3: Required Tables
  console.log('\n3️⃣  Testing Required Tables...');
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    const tables = ['parts', 'part_images', 'parts_condition', 'part_price', 'car_part_categories'];

    for (const table of tables) {
      try {
        const { error } = await supabase
          .from(table)
          .select('*')
          .limit(1);

        if (error) {
          console.log(`❌ Table '${table}' not accessible:`, error.message);
          allTestsPassed = false;
        } else {
          console.log(`✅ Table '${table}' is accessible`);
        }
      } catch (error) {
        console.log(`❌ Error accessing table '${table}':`, error);
        allTestsPassed = false;
      }
    }
  } catch (error) {
    console.log('❌ Error testing tables:', error);
    allTestsPassed = false;
  }

  // Test 4: Jiji Tables
  console.log('\n4️⃣  Testing Jiji Automation Tables...');
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    const jijiTables = ['jiji_listings'];

    for (const table of jijiTables) {
      try {
        const { error } = await supabase
          .from(table)
          .select('*')
          .limit(1);

        if (error) {
          console.log(`❌ Jiji table '${table}' not accessible:`, error.message);
          console.log('   Run: npx ts-node automation/setup-database.ts setup');
          allTestsPassed = false;
        } else {
          console.log(`✅ Jiji table '${table}' is accessible`);
        }
      } catch (error) {
        console.log(`❌ Error accessing Jiji table '${table}':`, error);
        allTestsPassed = false;
      }
    }
  } catch (error) {
    console.log('❌ Error testing Jiji tables:', error);
    allTestsPassed = false;
  }

  // Test 5: Database Functions
  console.log('\n5️⃣  Testing Database Functions...');
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    const { data, error } = await supabase.rpc('get_parts_ready_for_jiji_listing', { limit_count: 1 });

    if (error) {
      console.log('❌ Function get_parts_ready_for_jiji_listing failed:', error.message);
      console.log('   Run: npx ts-node automation/setup-database.ts setup');
      allTestsPassed = false;
    } else {
      console.log('✅ Function get_parts_ready_for_jiji_listing is working');
      console.log(`   Found ${data?.length || 0} parts ready for listing`);
    }
  } catch (error) {
    console.log('❌ Error testing functions:', error);
    allTestsPassed = false;
  }

  // Test 6: Parts Data Quality
  console.log('\n6️⃣  Testing Parts Data Quality...');
  let uniquePartsWithImages = 0;
  let uniquePartsWithPricing = 0;
  let partsWithTitles = 0;

  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    // Count total parts
    const { count: totalParts } = await supabase
      .from('parts')
      .select('*', { count: 'exact', head: true });

    console.log(`📦 Total parts in database: ${totalParts || 0}`);

    // Count parts with titles
    const { count: titlesCount } = await supabase
      .from('parts')
      .select('*', { count: 'exact', head: true })
      .not('title', 'is', null)
      .neq('title', '');

    partsWithTitles = titlesCount || 0;
    console.log(`📝 Parts with titles: ${partsWithTitles}`);

    // Count parts with images
    const { data: imageData } = await supabase
      .from('part_images')
      .select('part_id');

    uniquePartsWithImages = new Set(imageData?.map(img => img.part_id) || []).size;
    console.log(`🖼️  Parts with images: ${uniquePartsWithImages}`);

    // Count parts with pricing
    const { data: pricingData } = await supabase
      .from('part_price')
      .select('condition_id, parts_condition!inner(part_id)')
      .not('price', 'is', null);

    uniquePartsWithPricing = new Set(
      pricingData?.map((price: any) => price.parts_condition?.part_id).filter(Boolean) || []
    ).size;
    console.log(`💰 Parts with pricing: ${uniquePartsWithPricing}`);

    if (partsWithTitles === 0) {
      console.log('⚠️  No parts with titles found - listings may fail');
      allTestsPassed = false;
    }

    if (uniquePartsWithImages === 0) {
      console.log('⚠️  No parts with images found - consider adding images for better listings');
    }

    if (uniquePartsWithPricing === 0) {
      console.log('⚠️  No parts with pricing found - listings may fail');
      allTestsPassed = false;
    }

  } catch (error) {
    console.log('❌ Error testing parts data:', error);
    allTestsPassed = false;
  }

  // Test 7: Dependencies
  console.log('\n7️⃣  Testing Dependencies...');
  try {
    // Test Playwright
    try {
      require('playwright');
      console.log('✅ Playwright is installed');
    } catch (error) {
      console.log('❌ Playwright not found - run: npm install playwright');
      allTestsPassed = false;
    }

    // Test other dependencies
    const dependencies = ['@supabase/supabase-js', 'dotenv', 'commander'];
    for (const dep of dependencies) {
      try {
        require(dep);
        console.log(`✅ ${dep} is installed`);
      } catch (error) {
        console.log(`❌ ${dep} not found - run: npm install ${dep}`);
        allTestsPassed = false;
      }
    }
  } catch (error) {
    console.log('❌ Error testing dependencies:', error);
    allTestsPassed = false;
  }

  // Summary
  console.log('\n📋 Test Summary');
  console.log('===============');

  // Check if critical components are working
  const criticalTestsPassed = uniquePartsWithImages > 0 && uniquePartsWithPricing > 0 && partsWithTitles > 0;

  if (criticalTestsPassed) {
    console.log('🎉 Critical tests passed! Your setup is ready for Jiji automation.');
    console.log('\n📊 Data Summary:');
    console.log(`   📦 ${partsWithTitles} parts with titles`);
    console.log(`   🖼️  ${uniquePartsWithImages} parts with images`);
    console.log(`   💰 ${uniquePartsWithPricing} parts with pricing`);
    console.log('\n✅ The automation will:');
    console.log('   - Use frontend markup pricing algorithm');
    console.log('   - Upload images from your database');
    console.log('   - Create professional listings on Jiji.co.ke');
    console.log('\nNext steps:');
    console.log('1. Install Playwright browsers: npx playwright install');
    console.log('2. Set up Jiji credentials in .env or provide via CLI');
    console.log('3. Run test automation: npx ts-node run-jiji-automation.ts test');
    console.log('4. Run full automation: npx ts-node run-jiji-automation.ts run');

    if (!allTestsPassed) {
      console.log('\n⚠️  Minor issues detected (categories table missing) but automation will work fine.');
    }
  } else {
    console.log('❌ Critical tests failed. Please fix the issues above before running the automation.');
    console.log('\nCommon fixes:');
    console.log('- Set up environment variables in .env file');
    console.log('- Run database setup: npx ts-node setup-database.ts setup');
    console.log('- Install missing dependencies: npm install');
    console.log('- Check your Supabase connection and permissions');
  }
}

// Run the test
if (require.main === module) {
  testSetup().catch(console.error);
}
