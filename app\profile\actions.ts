'use server';

import { supabaseAdmin } from '@/app/libs/supabase/admin';
import { createClient } from '@/app/libs/supabase/server';

/**
 * Server action that updates a user's profile using admin privileges
 * This bypasses RLS policies to ensure profile data is properly saved
 */
export async function updateUserProfile(
  userId: string,
  profileData: {
    full_name: string;
    phone: string;
    email: string;
    created_at?: string;
    updated_at?: string;
  }
) {
  if (!userId) {
    throw new Error('User ID is required');
  }

  try {
    // Check if profile exists
    const { data: existingProfile, error: checkError } = await supabaseAdmin
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .maybeSingle();

    if (checkError) {
      console.error('Error checking profile existence:', checkError);
    }

    // Add timestamps
    const now = new Date().toISOString();
    const finalData = {
      ...profileData,
      id: userId,
      updated_at: now,
      created_at: existingProfile ? undefined : now, // Only add created_at for new profiles
    };

    // Use upsert with onConflict to handle both insert and update cases
    const { error } = await supabaseAdmin
      .from('profiles')
      .upsert(finalData, { onConflict: 'id' });

    if (error) {
      console.error('Profile update error from server action:', error);
      throw new Error(`Failed to update profile: ${error.message}`);
    }

    return { success: true };
  } catch (err: any) {
    console.error('Server action error:', err);
    return { 
      success: false, 
      error: err.message || 'An error occurred updating profile'
    };
  }
}

/**
 * Modified server action to update user data only in the profiles table
 * This avoids the problematic auth metadata update
 */
export async function completeUserRegistration(
  userId: string,
  userData: {
    full_name: string;
    phone: string;
    email: string;
    password?: string;
  }
) {
  if (!userId) {
    throw new Error('User ID is required');
  }

  try {
    // Only update the profile table with user information
    const now = new Date().toISOString();
    
    const profileData = {
      id: userId,
      full_name: userData.full_name,
      phone: userData.phone,
      email: userData.email,
      updated_at: now,
      created_at: now, // For new profiles
    };

    // Use upsert with onConflict to handle both insert and update cases
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .upsert(profileData, { onConflict: 'id' });

    if (profileError) {
      console.error('Profile creation error:', profileError);
      throw new Error(`Failed to create profile: ${profileError.message}`);
    }

    return { success: true };
  } catch (err: any) {
    console.error('Registration completion error:', err);
    return { 
      success: false, 
      error: err.message || 'An error occurred during registration'
    };
  }
}