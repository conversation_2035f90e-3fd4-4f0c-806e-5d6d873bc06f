// app/(auth)/forgot-password/page.tsx
'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/app/libs/supabase/client';
import Input from '@/app/components/ui/inputs/Input';
import Button from '@/app/components/ui/inputs/Button';
import Notification from '@/app/components/ui/Notification';
import AuthLayout from '@/app/layouts/AuthLayout';

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const router = useRouter();
  const supabase = createClient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrorMessage('');
    setSuccessMessage('');

    try {
      // Get the base URL for the application
      const baseUrl = window.location.origin;

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${baseUrl}/reset-password`,
      });

      if (error) throw error;
      setSuccessMessage('Password reset link sent to your email!');
    } catch (error: any) {
      setErrorMessage(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthLayout>
      <div className="w-full max-w-md space-y-6">
        <h2 className="text-2xl font-bold">Forgot Password</h2>
        {errorMessage && (
          <Notification type="error" header="Error" body={errorMessage} />
        )}
        {successMessage && (
          <Notification type="success" header="Success" body={successMessage} />
        )}
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            type="email"
            label="Email Address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            disabled={loading}
          />
          <Button
            type="submit"
            disabled={loading}
            className="w-full"
            variant="primary"
          >
            {loading ? 'Sending...' : 'Send Reset Link'}
          </Button>
        </form>
        <div className="text-center text-sm text-muted-foreground">
          <Button
            variant="link"
            className="text-primary underline"
            onClick={() => router.push('/login')}
          >
            Back to Login
          </Button>
        </div>
      </div>
    </AuthLayout>
  );
}