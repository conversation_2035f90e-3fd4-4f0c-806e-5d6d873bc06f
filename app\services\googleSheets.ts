import { google } from 'googleapis';
import { OAuth2Client, Credentials } from 'google-auth-library';
import { createClient } from '../../app/libs/supabase/server';
import { SupabaseClient } from '@supabase/supabase-js';
import { Product } from '../types/product';
import { SyncStatus } from '../types/sync';
import { generateProductSlug } from '@/app/utils/slugify';

// Define error types
interface GoogleApiError {
  code: number;
  message: string;
  status?: string;
  errors?: Array<{
    message: string;
    domain: string;
    reason: string;
  }>;
}

interface TokenData {
  access_token: string;
  refresh_token: string;
  expiry_date: number;
  spreadsheet_id: string;
}

export class GoogleSheetsService {
  private auth: OAuth2Client;
  private sheets: any;
  private spreadsheetId: string | null = null;
  private spreadsheetName: string = 'Google Merchant Center feed - Products source';
  private supabase: SupabaseClient;

  constructor() {
    // Initialize OAuth2 client
    const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
    const redirectUri = process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI;

    if (!clientId || !clientSecret || !redirectUri) {
      console.error('Missing Google OAuth credentials:', {
        hasClientId: !!clientId,
        hasClientSecret: !!clientSecret,
        hasRedirectUri: !!redirectUri
      });
      throw new Error('Missing required Google OAuth configuration');
    }

    this.auth = new OAuth2Client(clientId, clientSecret, redirectUri);

    // Initialize Google Sheets API
    this.sheets = google.sheets({ version: 'v4', auth: this.auth });

    // Initialize Supabase client
    this.supabase = createClient();

    // Add token refresh listener
    this.auth.on('tokens', (tokens: Credentials) => {
      console.log('New tokens received:', {
        hasAccessToken: !!tokens.access_token,
        hasRefreshToken: !!tokens.refresh_token,
        expiryDate: tokens.expiry_date
      });
    });

    // Check if we have stored tokens
    this.loadStoredCredentials(); // Attempt to load for current session user on init
  }

  private async loadStoredCredentials(userId?: string): Promise<void> {
    let currentUserId = userId;
    if (!currentUserId) {
      const { data: { session } } = await this.supabase.auth.getSession();
      if (!session?.user?.id) {
        console.log('loadStoredCredentials: No user session found, cannot load user-specific tokens.');
        return;
      }
      currentUserId = session.user.id;
    }
    try {
      // Create a new admin client to bypass RLS
      const adminClient = createClient();

      const { data: tokenData, error: tokenError } = await adminClient
        .from('google_tokens')
        .select('*')
        .eq('user_id', currentUserId)
        .single();

      if (tokenError) {
        console.error('Error loading stored credentials:', tokenError);
        return;
      }

      if (tokenData) {
        const credentials = {
          access_token: tokenData.access_token,
          refresh_token: tokenData.refresh_token,
          expiry_date: tokenData.expiry_date
        } as Credentials;

        this.auth.setCredentials(credentials);
        this.spreadsheetId = tokenData.spreadsheet_id;
      }
    } catch (error) {
      console.error('Error in loadStoredCredentials:', error);
    }
  }

  private async storeCredentials(userId: string, tokens: Credentials, spreadsheetId?: string): Promise<void> {
    try {
      if (!tokens.access_token || !tokens.refresh_token || !tokens.expiry_date) {
        throw new Error('Invalid token data');
      }

      const credentials = {
        user_id: userId,
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        expiry_date: tokens.expiry_date,
        spreadsheet_id: spreadsheetId || this.spreadsheetId
      };

      // Create a new admin client to bypass RLS
      const adminClient = createClient();

      // Upsert the credentials, creating a new record or updating if one exists for the user_id
      const { error: upsertError } = await adminClient
        .from('google_tokens')
        .upsert(credentials, { onConflict: 'user_id' });

      if (upsertError) {
        console.error('Error upserting credentials:', upsertError);
        throw upsertError;
      }

      // Update the current instance
      this.auth.setCredentials(tokens);
      if (spreadsheetId) {
        this.spreadsheetId = spreadsheetId;
      }
    } catch (error) {
      console.error('Error in storeCredentials:', error);
      throw error;
    }
  }

  public getSpreadsheetId(): string | null {
    return this.spreadsheetId;
  }

  public async fetchSpreadsheetId(): Promise<string | null> {
    try {
      const { data, error } = await this.supabase
        .from('google_spreadsheets')
        .select('spreadsheet_id')
        .eq('spreadsheet_name', this.spreadsheetName)
        .single();

      if (error) {
        console.error('Error getting spreadsheet ID:', error);
        return null;
      }

      return data?.spreadsheet_id || null;
    } catch (error) {
      console.error('Error in getSpreadsheetId:', error);
      return null;
    }
  }

  public async setSpreadsheetId(id: string): Promise<void> {
    try {
      this.spreadsheetId = id;

      // Create a new admin client to bypass RLS
      const adminClient = createClient();

      const { error } = await adminClient
        .from('google_spreadsheets')
        .upsert([
          {
            id,
            name: this.spreadsheetName
          }
        ]);

      if (error) {
        console.error('Error setting spreadsheet ID:', error);
      }
    } catch (error) {
      console.error('Error in setSpreadsheetId:', error);
      throw error;
    }
  }

  private async linkDefaultSpreadsheet(userId: string): Promise<void> {
    const defaultSpreadsheetId = process.env.GOOGLE_SPREADSHEET_ID;
    const defaultSpreadsheetName = this.spreadsheetName;

    if (!defaultSpreadsheetId) {
      console.warn('GOOGLE_SPREADSHEET_ID is not set in .env.local. Cannot link default spreadsheet.');
      return;
    }

    try {
      // Create a new admin client to bypass RLS
      const adminClient = createClient();

      // First try to update existing record
      const { error: updateError } = await adminClient
        .from('google_spreadsheets')
        .update({
          spreadsheet_id: defaultSpreadsheetId,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('spreadsheet_name', defaultSpreadsheetName);

      // If no record was updated, insert a new one
      if (updateError) {
        const { error: insertError } = await adminClient
          .from('google_spreadsheets')
          .insert([{
            user_id: userId,
            spreadsheet_name: defaultSpreadsheetName,
            spreadsheet_id: defaultSpreadsheetId
          }]);

        if (insertError) {
          console.error('Error inserting spreadsheet record:', insertError);
          return;
        }
      }

      // Update the current instance
      this.spreadsheetId = defaultSpreadsheetId;
      console.log(`Default spreadsheet ${defaultSpreadsheetId} successfully linked for user ${userId}`);
    } catch (error) {
      console.error('Error in linkDefaultSpreadsheet:', error);
    }
  }

  public async initializeApi(): Promise<boolean> {
    try {
      if (!this.auth.credentials?.access_token) {
        console.log('No credentials found, redirecting to auth flow');
        return false;
      }

      // Verify token validity
      const now = Math.floor(Date.now() / 1000);
      const token = this.auth.credentials;
      if (token.expiry_date && token.expiry_date < now) {
        console.log('Token expired, refreshing...');
        await this.auth.refreshAccessToken();
      }

      return true;
    } catch (error) {
      console.error('Error initializing API:', error);
      return false;
    }
  }

  public async authenticate(code: string, redirectUri: string, userId: string): Promise<Credentials> {
    try {
      const { tokens } = await this.auth.getToken(code);
      if (!tokens || !tokens.access_token) {
        throw new Error('No access token received from OAuth flow');
      }

      this.auth.setCredentials(tokens); // Set tokens on the auth client first

      // Attempt to link the default spreadsheet for the user.
      // This will set this.spreadsheetId if GOOGLE_SPREADSHEET_ID is available.
      await this.linkDefaultSpreadsheet(userId);

      // Now store credentials. It will use this.spreadsheetId if set by linkDefaultSpreadsheet,
      // or an existing one if loaded by constructor, or null if none are found.
      await this.storeCredentials(userId, tokens);

      // Reload stored credentials to ensure the instance reflects the DB state,
      // especially if storeCredentials inserted/updated the spreadsheet_id.
      await this.loadStoredCredentials(userId);

      return tokens;
    } catch (error) {
      console.error('Error in authenticate:', error);
      throw error;
    }
  }

  public async isAuthenticated(userId?: string): Promise<boolean> {
    console.log('Checking Google Sheets authentication status...');

    // If no userId is provided, use the current credentials
    if (!userId) {
      const hasToken = !!this.auth.credentials?.access_token;
      console.log('No userId provided, using current credentials:', { hasToken });
      return hasToken;
    }

    // If userId is provided, check if there are credentials for this user
    try {
      // Create a new admin client to bypass RLS
      const adminClient = createClient();

      const { data, error } = await adminClient
        .from('google_tokens')
        .select('access_token, refresh_token, expiry_date, spreadsheet_id')
        .eq('user_id', userId)
        .single();

      if (error || !data) {
        console.log(`No credentials found for user ${userId}`);
        return false;
      }

      // Update the current credentials with the ones from the database
      if (data.access_token) {
        this.auth.setCredentials({
          access_token: data.access_token,
          refresh_token: data.refresh_token,
          expiry_date: data.expiry_date
        });

        // Update spreadsheet ID
        if (data.spreadsheet_id) {
          this.spreadsheetId = data.spreadsheet_id;
        }

        console.log('Successfully loaded credentials for user:', userId);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error checking authentication status:', error);
      return false;
    }
  }

  private async getAvailableRow(): Promise<number> {
    try {
      // Get the last row with data
      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId: this.spreadsheetId!,
        range: 'Products!A:A'
      });

      const values = response.data.values || [];
      return values.length + 1; // Add 1 to get the next empty row
    } catch (error) {
      console.error('Error getting available row:', error);
      return 2; // Default to row 2 if error occurs
    }
  }

  public async getLatestSyncStatus(): Promise<SyncStatus | null> {
    try {
      // Create a new admin client to bypass RLS
      const adminClient = createClient();

      // Use maybeSingle instead of single to avoid errors when no rows exist
      const { data, error } = await adminClient
        .from('sync_status')
        .select('*')
        .order('last_sync_time', { ascending: false })
        .limit(1);

      if (error) {
        console.error('Error getting sync status:', error);
        return null;
      }

      // If no data or empty array, return null
      if (!data || data.length === 0) {
        console.log('No sync status records found');
        return null;
      }

      // Return the first (and only) item in the array
      return data[0];
    } catch (error) {
      console.error('Error in getLatestSyncStatus:', error);
      return null;
    }
  }

  public async loadProducts(): Promise<Product[]> {
    try {
      // Ensure the Products sheet exists
      await this.ensureProductsSheetExists();

      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId: this.spreadsheetId!,
        range: 'Products!A2:Z'
      });

      return response.data.values.map((row: string[]) => ({
        id: row[0],
        title: row[1],
        description: row[2],
        link: row[3],
        image_link: row[4],
        availability: row[5],
        price: row[6],
        brand: row[7],
        gtin: row[8],
        mpn: row[9],
        condition: row[10],
        custom_label_0: row[11],
        custom_label_1: row[12],
        custom_label_2: row[13],
        custom_label_3: row[14],
        custom_label_4: row[15]
      }));
    } catch (error) {
      console.error('Error in loadProducts:', error);
      throw error;
    }
  }

  // Helper method to ensure the Products sheet exists
  private async ensureProductsSheetExists(): Promise<void> {
    try {
      // Get the spreadsheet metadata
      const spreadsheet = await this.sheets.spreadsheets.get({
        spreadsheetId: this.spreadsheetId!
      });

      // Check if the Products sheet already exists
      const productsSheet = spreadsheet.data.sheets?.find(
        (sheet: any) => sheet.properties?.title === 'Products'
      );

      // If the Products sheet doesn't exist, create it
      if (!productsSheet) {
        console.log('Creating Products sheet...');
        await this.sheets.spreadsheets.batchUpdate({
          spreadsheetId: this.spreadsheetId!,
          requestBody: {
            requests: [
              {
                addSheet: {
                  properties: {
                    title: 'Products',
                    gridProperties: {
                      rowCount: 1000,
                      columnCount: 20
                    }
                  }
                }
              }
            ]
          }
        });
        console.log('Products sheet created successfully');
      }
    } catch (error) {
      console.error('Error ensuring Products sheet exists:', error);
      throw error;
    }
  }

  public async syncProductData(userId?: string): Promise<SyncStatus> {
    const syncStartTime = new Date();
    let syncDetails: any = {};
    let status: 'success' | 'failed' | 'in_progress' = 'in_progress';

    try {
      // Check if userId is provided
      if (!userId) {
        // Try to get from session as fallback
        try {
          const { data: sessionData, error: sessionError } = await this.supabase.auth.getSession();
          if (sessionError || !sessionData.session) {
            throw new Error('No active user session found. Please log in again.');
          }
          userId = sessionData.session.user.id;
        } catch (sessionError) {
          throw new Error('User ID is required for syncing products. Please provide a user ID or log in again.');
        }
      }

      // Load stored credentials for the current user
      await this.loadStoredCredentials(userId);

      // Check if we have valid credentials
      if (!this.auth.credentials?.access_token) {
        throw new Error('No valid Google credentials found. Please authenticate with Google first.');
      }

      // Verify token validity and refresh if needed
      const now = Math.floor(Date.now() / 1000);
      if (this.auth.credentials.expiry_date && this.auth.credentials.expiry_date < now) {
        console.log('Token expired, refreshing...');
        const { credentials } = await this.auth.refreshAccessToken();
        await this.storeCredentials(userId, credentials);
      }

      // Ensure we have a valid spreadsheet ID
      if (!this.spreadsheetId) {
        const storedSpreadsheetId = await this.getSpreadsheetId();
        if (!storedSpreadsheetId) {
          throw new Error('Spreadsheet ID not configured. Please ensure a spreadsheet is linked.');
        }
        this.spreadsheetId = storedSpreadsheetId;
      }

      // Ensure the Products sheet exists
      await this.ensureProductsSheetExists();

      // Create a new admin client to bypass RLS
      const adminClient = createClient();

      console.log('Fetching parts data from Supabase...');

      // Get parts data with part numbers
      const { data: parts, error: partsError } = await adminClient
        .from('parts')
        .select(`
          id,
          title,
          description,
          partnumber_group,
          category_id,
          part_compatibility_groups:partnumber_group(id, part_number)
        `)
        .limit(1000);

      if (partsError) {
        console.error('Error fetching parts from Supabase:', partsError);
        throw partsError;
      }

      console.log(`Fetched ${parts?.length || 0} parts from Supabase.`);

      // Get part images
      console.log('Fetching part images...');
      const { data: partImages, error: imagesError } = await adminClient
        .from('part_images')
        .select('part_id, image_url, is_main_image');

      if (imagesError) {
        console.error('Error fetching part images:', imagesError);
      }

      console.log(`Fetched ${partImages?.length || 0} part images.`);

      // Get part conditions and stock
      console.log('Fetching part conditions and stock...');
      const { data: partConditions, error: conditionsError } = await adminClient
        .from('parts_condition')
        .select('id, part_id, condition, stock');

      if (conditionsError) {
        console.error('Error fetching part conditions:', conditionsError);
      }

      console.log(`Fetched ${partConditions?.length || 0} part conditions.`);

      // Get part prices
      console.log('Fetching part prices...');
      const { data: partPrices, error: pricesError } = await adminClient
        .from('part_price')
        .select('condition_id, price, discounted_price');

      if (pricesError) {
        console.error('Error fetching part prices:', pricesError);
      }

      console.log(`Fetched ${partPrices?.length || 0} part prices.`);

      // Process the data to create a combined product list
      const products = parts.map(part => {
        // Find main image for this part
        const mainImage = partImages?.find(img => img.part_id === part.id && img.is_main_image) ||
                         partImages?.find(img => img.part_id === part.id);

        // Find condition with stock for this part
        const condition = partConditions?.find(cond => cond.part_id === part.id && cond.stock > 0) ||
                         partConditions?.find(cond => cond.part_id === part.id);

        // Find price for this condition
        const price = condition ?
                     partPrices?.find(price => price.condition_id === condition.id) : null;

        // Get part number from part_compatibility_groups
        const partNumber = (part.part_compatibility_groups as any)?.part_number || '';

        return {
          part_id: part.id,
          title: part.title,
          description: part.description,
          partnumber: partNumber,
          partnumber_group: part.partnumber_group,
          category_id: part.category_id,
          thumbnail_url: mainImage?.image_url || '',
          condition: condition?.condition || 'unknown',
          stock: condition?.stock || 0,
          price: price?.price || 0
        };
      });

      if (!products || products.length === 0) {
        console.log('No products found in Supabase to sync.');
        status = 'success';
        syncDetails = { message: 'No products to sync. The sheet will be cleared.' };
        await this.sheets.spreadsheets.values.clear({
          spreadsheetId: this.spreadsheetId!,
          range: 'Products!A1:Z',
        });
        await this.updateSyncStatus(userId, 'google_sheets_products', status, syncDetails, syncStartTime);
        return {
          service_name: 'google_sheets_products',
          status,
          last_sync_time: syncStartTime.toISOString(),
          details: JSON.stringify(syncDetails)
        };
      }

      console.log(`Processed ${products.length} products to sync.`);
      console.log('Sample product data:', JSON.stringify(products[0]));

      const headerRow = [
        'id', 'title', 'description', 'link', 'image_link', 'availability',
        'price', 'brand', 'gtin', 'mpn', 'condition',
        'custom_label_0', 'custom_label_1', 'custom_label_2',
        'custom_label_3', 'custom_label_4'
      ];

      const productRows = products.map((product: any) => {
        try {
          // Create a link to the part with SEO-friendly slug
          const partId = product.part_id;
          const title = product.title || '';

          // Import the slugify function
          const { generateProductSlug } = require('@/app/utils/slugify');

          // Generate SEO-friendly URL for the frontend
          const slug = generateProductSlug(title, partId);
          const link = `https://autoflow.parts/shop/product/${slug}`;

          // Get the image URL
          const imageUrl = product.thumbnail_url || '';

          // Determine availability based on stock
          const stock = parseInt(product.stock?.toString() || '0');
          const availability = stock > 0 ? 'in stock' : 'out of stock';

          // Format the price
          const price = parseFloat(product.price?.toString() || '0');
          const formattedPrice = price > 0 ? `${price} KES` : '0 KES';

          // Get the part number from part_compatibility_groups
          let mpn = product.partnumber || '';

          // If no part number is available, try to extract it from the title as a fallback
          if (!mpn) {
            const title = product.title || '';
            const titleParts = title.split(' ');
            const possiblePartNumber = titleParts[titleParts.length - 1];
            mpn = possiblePartNumber && /^[A-Z0-9-]+$/.test(possiblePartNumber) ? possiblePartNumber : partId.toString();
          }

          // Use description if available, otherwise use title
          const description = product.description || product.title || '';

          console.log(`Processing product ${partId}: ${product.title}, price: ${price}, image: ${imageUrl ? 'Yes' : 'No'}`);

          return [
            partId.toString(),
            product.title || '',
            description,
            link,
            imageUrl,
            availability,
            formattedPrice,
            'OEM', // Brand
            partId.toString(), // GTIN
            mpn, // MPN
            product.condition || 'used', // Condition
            '', // Custom Label 0
            '', // Custom Label 1
            '', // Custom Label 2
            '', // Custom Label 3
            '', // Custom Label 4
          ];
        } catch (error) {
          console.error('Error processing product:', product, error);
          // Return a placeholder row to avoid breaking the sync
          return [
            product.part_id?.toString() || 'unknown',
            product.title || 'Unknown Product',
            product.description || product.title || 'Unknown Product',
            `https://autoflow.parts/shop/product/${generateProductSlug(product.title || 'Unknown Product', product.part_id || 'unknown')}`,
            '',
            'out of stock',
            '0 KES',
            'OEM',
            product.part_id?.toString() || 'unknown',
            product.partnumber || 'unknown',
            product.condition || 'used',
            '',
            '',
            '',
            '',
            '',
          ];
        }
      });

      const values = [headerRow, ...productRows];
      const range = 'Products!A1';

      let updateResult: any;

      try {
        console.log('Clearing existing data in Products sheet...');
        await this.sheets.spreadsheets.values.clear({
          spreadsheetId: this.spreadsheetId!,
          range: 'Products!A1:Z',
        });
        console.log('Successfully cleared Products sheet.');

        console.log(`Updating Products sheet with ${values.length} rows of data...`);
        updateResult = await this.sheets.spreadsheets.values.update({
          spreadsheetId: this.spreadsheetId!,
          range,
          valueInputOption: 'USER_ENTERED',
          requestBody: {
            values,
          },
        });
        console.log('Successfully updated Products sheet.');

        console.log('Products synced to Google Sheets:', updateResult.data);
        status = 'success';
        syncDetails = {
          message: 'Products synced successfully.',
          updatedCells: updateResult.data.updatedCells,
          updatedRows: updateResult.data.updatedRows,
          updatedColumns: updateResult.data.updatedColumns,
          productsSynced: products.length
        };
      } catch (error) {
        console.error('Error updating Google Sheets:', error);
        status = 'failed';
        syncDetails = {
          message: 'Failed to sync products with Google Sheets.',
          error: error instanceof Error ? error.message : JSON.stringify(error)
        };
      }

    } catch (error: any) {
      console.error('Error syncing products with Google Sheets:', error);
      status = 'failed';
      syncDetails = {
        message: 'Failed to sync products with Google Sheets.',
        error: error.message || JSON.stringify(error.errors || error)
      };
    }

    await this.updateSyncStatus(userId, 'google_sheets_products', status, syncDetails, syncStartTime);

    return {
      service_name: 'google_sheets_products',
      status,
      last_sync_time: syncStartTime.toISOString(),
      details: JSON.stringify(syncDetails)
    };
  }

  private async updateSyncStatus(
    userId: string | undefined,
    serviceName: string,
    status: 'success' | 'failed' | 'in_progress',
    details: any,
    syncTime: Date
  ): Promise<void> {
    try {
      if (!userId) {
        console.warn('Cannot update sync_status without user_id. Sync event not logged for a specific user.');
        return;
      }

      // Create a new admin client to bypass RLS
      const adminClient = createClient();

      const { error } = await adminClient
        .from('sync_status')
        .insert([{
          user_id: userId,
          service_name: serviceName,
          last_sync_time: syncTime.toISOString(),
          status,
          details: JSON.stringify(details)
        }]);
      if (error) {
        console.error('Error updating sync status in Supabase:', error);
      }
    } catch (e) {
      console.error('Exception in updateSyncStatus:', e);
    }
  }
}

// Export the service instance
export const googleSheetsService = new GoogleSheetsService();

export default googleSheetsService;
