// app/layout.tsx
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import AppProviders from './components/providers/AppProviders'
import ServiceWorkerProvider from './components/providers/ServiceWorkerProvider'
import ServiceWorkerErrorBoundary from './components/error/ServiceWorkerErrorBoundary'
import PWAProvider from './components/providers/PWAProvider'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'AutoFlow - Auto Parts Store',
  description: 'Your one-stop shop for auto parts and accessories',
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'AutoFlow',
  },
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  themeColor: '#14b8a6',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="theme-color" content="#14b8a6" />
        <link rel="manifest" href="/manifest.json" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <meta name="google-site-verification" content="V5UI1URgCvpXU1PBxWMsXra39MINjl-Gi7SCdsYZC3k" />
      </head>
      <body className={inter.className}>
        <ServiceWorkerErrorBoundary>
          <ServiceWorkerProvider>
            <PWAProvider>
              <AppProviders>
                {children}
                <div id="modal-root" style={{ position: 'relative', zIndex: 100 }}></div>
              </AppProviders>
            </PWAProvider>
          </ServiceWorkerProvider>
        </ServiceWorkerErrorBoundary>
      </body>
    </html>
  )
}