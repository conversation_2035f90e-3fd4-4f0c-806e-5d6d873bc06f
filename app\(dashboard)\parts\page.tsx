export const dynamic = 'force-dynamic';

import { Suspense } from 'react';
import Spinner from '@/app/components/ui/Spinner';
import PartsDashboard from './components/PartsDashboard';
import PartsContent from './components/PartsContent';

import ProtectedPartsPage from './components/ProtectedPartsPage';

export default function PartsPage({ searchParams }: { searchParams: { [key: string]: string | string[] | undefined } }) {
    // Check if we have any query parameters that would indicate we should show the parts list
    const hasQueryParams = Object.keys(searchParams).length > 0;
    const showPartsList = hasQueryParams && (
        searchParams.sort === 'newest' ||
        searchParams.filter === 'reorder' ||
        searchParams.query !== undefined ||
        searchParams.view === 'all'
    );

    return (
        <ProtectedPartsPage>
            <Suspense fallback={<div className="flex min-h-[300px] items-center justify-center">
                <Spinner size="lg" />
            </div>}>
                {showPartsList ? <PartsContent /> : <PartsDashboard />}
            </Suspense>
        </ProtectedPartsPage>
    );
}
