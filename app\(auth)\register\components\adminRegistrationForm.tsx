'use client'

import { useState } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import Input from '@/app/components/ui/inputs/Input';
import Button from '@/app/components/ui/inputs/Button';
import toast, { Toaster } from 'react-hot-toast';
import Password from '@/app/components/ui/inputs/Password';
import { useRouter } from 'next/navigation';

export default function AdminRegistrationForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const supabase = createClient();
  const router = useRouter();

  const handleRegister = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${location.origin}/api/auth/callback`,
          data: {
            phone: phoneNumber,
          },
        },
      });

      if (error) {
        if (error.code === 'auth/invalid-email') {
          toast.error('Please enter a valid email address.');
        } else if (error.code === 'auth/weak-password') {
          toast.error('Password should be at least 6 characters long.');
        } else if (error.code === 'auth/email-already-in-use') {
          toast.error('An account with this email already exists.');
        } else if (error.code === 'auth/network-request-failed') {
          toast.error('There was a network issue. Please check your connection.');
        } else {
          console.error('Supabase registration error:', error);
          toast.error(
            'An error occurred during registration. Please try again later.',
          );
        }
      } else {
        toast.success(
          'Admin registered successfully! Check your email to confirm the registration.',
          {
            duration: 10000, // 10 seconds
          }
        );
        setEmail('');
        setPassword('');
        setPhoneNumber('');

        setTimeout(() => {
          router.push('/login');
        }, 10000);
      }
    } catch (error) {
      console.error('Unexpected registration error:', error);
      toast.error('An unexpected error occurred. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-2">
      <main className="flex w-full flex-1 flex-col items-center justify-center px-20 text-center">
        <h1 className="text-4xl font-bold">Admin Registration</h1>

        <form onSubmit={handleRegister} className="mt-6 w-full max-w-xs">
          <Input
            type="email"
            placeholder="Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="mb-4"
          />
          <Password
            type="password"
            placeholder="Password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            className="mb-4"
            helperText="Password must be at least 6 characters long."
          />
          <Input
            type="tel"
            placeholder="Phone Number"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value)}
            required
            className="mb-4"
          />
          <Button type="submit" disabled={loading} className="w-full">
            {loading ? 'Registering...' : 'Register'}
          </Button>
        </form>
      </main>
      <Toaster />
    </div>
  );
}