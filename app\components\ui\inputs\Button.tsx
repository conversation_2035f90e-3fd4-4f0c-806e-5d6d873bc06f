"use client";

import React from 'react';
import AnimatedEllipsisLoader from '../AnimatedEllipsisLoader';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark' | 'ghost' | 'destructive' | 'link' | 'outline';
  size?: 'sm' | 'md' | 'lg' | 'icon';
  outline?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  loadingText?: string; // Text to display when loading with ellipsis
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  outline = false,
  loading = false,
  fullWidth = false,
  className = '',
  disabled,
  loadingText,
  ...rest
}) => {
  const baseClasses = 'font-medium rounded-lg focus:ring-4 focus:outline-none transition duration-300 ease-in-out inline-flex items-center justify-center mt-4';

  const variantClasses = {
    primary: outline
      ? 'border border-blue-500 text-blue-500 hover:text-white hover:bg-blue-600 focus:ring-blue-200'
      : 'bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-200',
    secondary: outline
      ? 'border border-gray-500 text-gray-500 hover:text-white hover:bg-gray-600 focus:ring-gray-200'
      : 'bg-gray-500 text-white hover:bg-gray-600 focus:ring-gray-200',
    success: outline
      ? 'border border-green-500 text-green-500 hover:text-white hover:bg-green-600 focus:ring-green-200'
      : 'bg-green-500 text-white hover:bg-green-600 focus:ring-green-200',
    danger: outline
      ? 'border border-red-500 text-red-500 hover:text-white hover:bg-red-600 focus:ring-red-200'
      : 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-200',
    warning: outline
      ? 'border border-yellow-400 text-yellow-400 hover:text-white hover:bg-yellow-500 focus:ring-yellow-200'
      : 'bg-yellow-400 text-white hover:bg-yellow-500 focus:ring-yellow-200',
    info: outline
      ? 'border border-cyan-400 text-cyan-400 hover:text-white hover:bg-cyan-500 focus:ring-cyan-200'
      : 'bg-cyan-400 text-white hover:bg-cyan-500 focus:ring-cyan-200',
    light: outline
      ? 'border border-gray-200 text-gray-500 hover:text-gray-900 hover:bg-gray-100 focus:ring-gray-100'
      : 'bg-gray-200 text-gray-900 hover:bg-gray-100 focus:ring-gray-100',
    dark: outline
      ? 'border border-gray-800 text-gray-200 hover:text-white hover:bg-gray-900 focus:ring-gray-700'
      : 'bg-gray-800 text-white hover:bg-gray-900 focus:ring-gray-700',
    ghost: 'hover:bg-gray-100 text-gray-900 focus:ring-gray-200',
    destructive: outline
      ? 'border border-red-600 text-red-600 hover:text-white hover:bg-red-700 focus:ring-red-300'
      : 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-300',
    link: 'text-blue-500 hover:underline focus:ring-blue-200',
    outline:
      "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
  };

  const sizeClasses = {
    sm: 'text-sm px-3 py-1.5',
    md: 'text-base px-5 py-2.5',
    lg: 'text-lg px-6 py-3',
    icon: "h-10 w-10",
  };

  const fullWidthClass = fullWidth ? 'w-full' : '';

  const loadingClasses = loading ? 'opacity-75 cursor-not-allowed' : '';

  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : '';

  // Determine the text and background color for the AnimatedEllipsisLoader based on button variant
  let loaderTextColor;
  let loaderBgColor;
  
  if (variant === 'ghost' || variant === 'link' || variant === 'outline') {
    loaderTextColor = 'gray-500';
    loaderBgColor = 'transparent';
  } else if (variant === 'destructive' && outline) {
    loaderTextColor = "red-600";
    loaderBgColor = 'transparent';
  } else {
    // Add defensive checks to prevent errors when splitting
    const variantClass = variantClasses[variant] || '';
    const variantParts = variantClass.split ? variantClass.split(' ') : [];
    
    loaderTextColor = outline ? (variantParts[1] || 'gray-500') : 'white';
    loaderBgColor = outline ? 'transparent' : (variantParts[0] || 'bg-gray-200');
  }

  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${fullWidthClass} ${loadingClasses} ${disabledClasses} ${className}`}
      disabled={disabled || loading}
      style={{ position: 'relative', zIndex: 'auto' }}
      {...rest}
    >
      {loading ? (
        <AnimatedEllipsisLoader
          text={loadingText}
          textColor={loaderTextColor}
          bgColor={loaderBgColor}
        />
      ) : (
        children
      )}
    </button>
  );
};

export default Button;

/**
 * Button Component
 *
 * A versatile button component for React applications with various styles, sizes, and states.
 *
 * @param {object} props - The properties passed to the Button component.
 * @param {ReactNode} props.children - The content of the button.
 * @param {'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'light' | 'dark' | 'ghost' | 'destructive' | 'link' | 'outline'} [props.variant='primary'] - The style variant of the button.
 * @param {'sm' | 'md' | 'lg' | 'icon'} [props.size='md'] - The size of the button.
 * @param {boolean} [props.outline=false] - Whether the button should have an outlined style.
 * @param {boolean} [props.loading=false] - Whether the button is in a loading state.
 * @param {boolean} [props.fullWidth=false] - Whether the button should take up the full width of its container.
 * @param {string} [props.className=''] - Additional CSS classes for the button.
 * @param {boolean} [props.disabled=false] - Whether the button is disabled.
 * @param {string} [props.loadingText] - Text to display with animated ellipsis when the button is in a loading state.
 * @param {...} rest - Any other props will be passed directly to the underlying button element.
 *
 * How to use the Button component:
 *
 * 1. Basic usage:
 *    <Button>Click me</Button>
 *
 * 2. With a specific variant:
 *    <Button variant="success">Success Button</Button>
 *
 * 3. With a specific size:
 *    <Button size="sm">Small Button</Button>
 *
 * 4. As an outlined button:
 *    <Button variant="danger" outline>Outlined Danger Button</Button>
 *
 * 5. In a loading state:
 *    <Button loading loadingText="Processing">Click me</Button>
 *
 * 6. Full-width button:
 *    <Button fullWidth>Full Width Button</Button>
 *
 * 7. Disabled button:
 *    <Button disabled>Disabled Button</Button>
 *
 * 8. With custom classes:
 *    <Button className="my-custom-class">Custom Button</Button>
 *
 * 9. Handling click events:
 *    <Button onClick={() => alert('Button clicked!')}>Click me</Button>
 *
 * 10. Icon Button
 *     <Button size="icon" aria-label="close">
          <X className="h-4 w-4" />
       </Button>
 */