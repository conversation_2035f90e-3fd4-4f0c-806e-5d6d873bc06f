'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { X, Save, AlertTriangle, Edit2, UserPlus, Search } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';
import toast from 'react-hot-toast';
import WalkInCustomerModal from './WalkInCustomerModal';
import { WalkInCustomer } from '../../utils/walkInCustomerUtils';

interface EditSaleModalProps {
  isOpen: boolean;
  onClose: () => void;
  saleId: string;
  onSaleUpdated: () => void;
}

interface SaleItem {
  id: string;
  part_id: number;
  quantity: number;
  price_at_sale: number;
  discount_amount: number;
  discount_reason: string | null;
  part_title?: string;
}

const EditSaleModal: React.FC<EditSaleModalProps> = ({ 
  isOpen, 
  onClose, 
  saleId,
  onSaleUpdated
}) => {
  const [sale, setSale] = useState<any>(null);
  const [saleItems, setSaleItems] = useState<SaleItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // VAT state
  const [vatRate, setVatRate] = useState<number>(16); // Default 16% VAT for Kenya
  const [includeVat, setIncludeVat] = useState<boolean>(false);

  // Client editing states
  const [isEditingClient, setIsEditingClient] = useState(false);
  const [isWalkInModalOpen, setIsWalkInModalOpen] = useState(false);
  const [clients, setClients] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredClients, setFilteredClients] = useState<any[]>([]);
  const [isClientDropdownOpen, setIsClientDropdownOpen] = useState(false);
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
  const [oneOffClientName, setOneOffClientName] = useState('');
  const [oneOffClientPhone, setOneOffClientPhone] = useState('');

  useEffect(() => {
    const fetchSaleDetails = async () => {
      if (!saleId) {
        setError('No sale ID provided');
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const supabase = createClient();

        console.log('Fetching sale details for ID:', saleId);
        
        // Fetch sale with related data
        const { data, error } = await supabase
          .from('sales')
          .select(`
            *,
            clients(id, name, phone_number, client_type),
            sale_items(
              id,
              part_id,
              quantity,
              price_at_sale,
              discount_amount,
              discount_reason,
              parts(id, title)
            ),
            mpesa_payments(id, mpesa_confirmation_message)
          `)
          .eq('id', saleId)
          .single();
        
        if (error) {
          console.error('Supabase query error:', error);
          throw error;
        }

        if (!data) {
          throw new Error('Sale not found');
        }

        console.log('Sale data loaded:', data);

        setSale(data);

        // Set client information
        setSelectedClientId(data.client_id);
        setOneOffClientName(data.one_off_client_name || '');
        setOneOffClientPhone(data.one_off_client_phone || '');

        // Set VAT information
        setVatRate(data.vat_rate || 16);
        setIncludeVat(data.vat_rate !== null && data.vat_rate > 0);

        // Format sale items with part titles
        if (data.sale_items && Array.isArray(data.sale_items)) {
          const formattedItems = data.sale_items.map((item: any) => ({
            id: item.id,
            part_id: item.part_id,
            quantity: item.quantity,
            price_at_sale: parseFloat(item.price_at_sale),
            discount_amount: parseFloat(item.discount_amount),
            discount_reason: item.discount_reason,
            part_title: item.parts?.title || `Part ID: ${item.part_id}`
          }));

          setSaleItems(formattedItems);
        } else {
          console.warn('No sale items found or invalid format');
          setSaleItems([]);
        }
      } catch (error: any) {
        console.error('Error fetching sale details:', error);
        console.error('Error details:', {
          message: error?.message,
          details: error?.details,
          hint: error?.hint,
          code: error?.code
        });

        let errorMessage = 'Failed to load sale details';
        if (error?.message) {
          errorMessage = `Failed to load sale details: ${error.message}`;
        } else if (error?.details) {
          errorMessage = `Failed to load sale details: ${error.details}`;
        }

        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };
    
    if (isOpen) {
      fetchSaleDetails();
      loadClients();
    }
  }, [saleId, isOpen]);

  // Load clients for selection
  const loadClients = async () => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('clients')
        .select('id, name, phone_number, client_type')
        .order('name');

      if (error) throw error;
      setClients(data || []);
    } catch (error) {
      console.error('Error loading clients:', error);
    }
  };

  // Filter clients based on search term
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredClients([]);
      setIsClientDropdownOpen(false);
    } else {
      const filtered = clients.filter(client =>
        client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.phone_number.includes(searchTerm)
      );
      setFilteredClients(filtered);
      setIsClientDropdownOpen(filtered.length > 0);
    }
  }, [searchTerm, clients]);

  const handleQuantityChange = (itemId: string, quantity: number) => {
    if (quantity < 1) return; // Prevent negative or zero quantities
    
    setSaleItems(prev => 
      prev.map(item => {
        if (item.id === itemId) {
          return { ...item, quantity };
        }
        return item;
      })
    );
  };

  const handleDiscountChange = (itemId: string, discount: number) => {
    if (discount < 0) return; // Prevent negative discounts
    
    const item = saleItems.find(item => item.id === itemId);
    if (!item) return;
    
    // Check if discount exceeds total price
    const totalPrice = item.price_at_sale * item.quantity;
    if (discount > totalPrice) {
      toast.error(`Discount cannot exceed total price (${totalPrice})`);
      return;
    }
    
    setSaleItems(prev => 
      prev.map(item => {
        if (item.id === itemId) {
          return { ...item, discount_amount: discount };
        }
        return item;
      })
    );
  };

  const handleDiscountReasonChange = (itemId: string, reason: string) => {
    setSaleItems(prev =>
      prev.map(item => {
        if (item.id === itemId) {
          return { ...item, discount_reason: reason };
        }
        return item;
      })
    );
  };

  // Client handling functions
  const handleClientSelect = (client: any) => {
    setSelectedClientId(client.id);
    setSearchTerm(client.name);
    setIsClientDropdownOpen(false);
    setOneOffClientName('');
    setOneOffClientPhone('');
  };

  const handleWalkInCustomerSelected = (customer: WalkInCustomer) => {
    setSelectedClientId(customer.id);
    setSearchTerm(customer.name);
    setIsWalkInModalOpen(false);
    setOneOffClientName('');
    setOneOffClientPhone('');

    // Add to clients list if not already there
    const existingClient = clients.find(c => c.id === customer.id);
    if (!existingClient) {
      const newClient = {
        id: customer.id,
        name: customer.name,
        phone_number: customer.phone_number,
        client_type: customer.client_type
      };
      setClients(prev => [...prev, newClient]);
    }
  };

  const handleOneOffCustomer = () => {
    setSelectedClientId(null);
    setSearchTerm('');
    setIsClientDropdownOpen(false);
  };

  const startEditingClient = () => {
    setIsEditingClient(true);
    if (sale?.client_id && sale?.clients) {
      setSearchTerm(sale.clients.name);
    } else {
      setSearchTerm('');
    }
  };

  const cancelEditingClient = () => {
    setIsEditingClient(false);
    setSearchTerm('');
    setIsClientDropdownOpen(false);
    // Reset to original values
    setSelectedClientId(sale?.client_id);
    setOneOffClientName(sale?.one_off_client_name || '');
    setOneOffClientPhone(sale?.one_off_client_phone || '');
  };

  const handleSave = async () => {
    setIsSaving(true);
    setError(null);
    
    try {
      const supabase = createClient();
      
      // Calculate new total amount and discount
      const subtotal = saleItems.reduce((sum, item) => sum + (item.price_at_sale * item.quantity), 0);
      const totalDiscount = saleItems.reduce((sum, item) => sum + item.discount_amount, 0);
      const totalBeforeVat = subtotal - totalDiscount;

      // Calculate VAT
      const vatAmount = includeVat ? (totalBeforeVat * vatRate) / 100 : 0;
      const totalWithVat = totalBeforeVat + vatAmount;
      
      // Update sale items
      for (const item of saleItems) {
        const { error } = await supabase
          .from('sale_items')
          .update({
            quantity: item.quantity,
            discount_amount: item.discount_amount,
            discount_reason: item.discount_reason
          })
          .eq('id', item.id);
        
        if (error) throw error;
      }
      
      // Prepare sale update data
      const saleUpdateData: any = {
        total_amount: includeVat ? totalWithVat : totalBeforeVat,
        total_discount: totalDiscount,
        vat_rate: includeVat ? vatRate : null,
        vat_amount: includeVat ? vatAmount : null,
        total_before_vat: includeVat ? totalBeforeVat : null,
        total_with_vat: includeVat ? totalWithVat : null,
        updated_at: new Date().toISOString()
      };

      // Check if client information has changed
      const originalClientId = sale?.client_id;
      const originalOneOffName = sale?.one_off_client_name;
      const originalOneOffPhone = sale?.one_off_client_phone;

      const clientChanged =
        selectedClientId !== originalClientId ||
        oneOffClientName !== (originalOneOffName || '') ||
        oneOffClientPhone !== (originalOneOffPhone || '');

      console.log('Client change check:', {
        originalClientId,
        selectedClientId,
        originalOneOffName,
        oneOffClientName,
        originalOneOffPhone,
        oneOffClientPhone,
        clientChanged
      });

      // Add client information if it has changed
      if (clientChanged) {
        if (selectedClientId) {
          saleUpdateData.client_id = selectedClientId;
          saleUpdateData.one_off_client_name = null;
          saleUpdateData.one_off_client_phone = null;
          console.log('Updating to registered client:', selectedClientId);
        } else {
          saleUpdateData.client_id = null;
          saleUpdateData.one_off_client_name = oneOffClientName.trim() || null;
          saleUpdateData.one_off_client_phone = oneOffClientPhone.trim() || null;
          console.log('Updating to one-off client:', { name: oneOffClientName, phone: oneOffClientPhone });
        }
      } else {
        console.log('No client changes detected');
      }

      // Update sale
      const { error } = await supabase
        .from('sales')
        .update(saleUpdateData)
        .eq('id', saleId);
      
      if (error) throw error;
      
      toast.success('Sale updated successfully');
      onSaleUpdated();
      onClose();
    } catch (error: any) {
      console.error('Error updating sale:', error);
      setError(error.message || 'Failed to update sale');
    } finally {
      setIsSaving(false);
    }
  };

  // Calculate totals
  const subtotal = saleItems.reduce((sum, item) => sum + (item.price_at_sale * item.quantity), 0);
  const totalDiscount = saleItems.reduce((sum, item) => sum + item.discount_amount, 0);
  const totalBeforeVat = subtotal - totalDiscount;
  const vatAmount = includeVat ? (totalBeforeVat * vatRate) / 100 : 0;
  const totalWithVat = totalBeforeVat + vatAmount;
  const finalTotal = includeVat ? totalWithVat : totalBeforeVat;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity z-[9998]" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full relative z-[10000]"
        >
          {/* Modal Header */}
          <div className="bg-gray-50 px-6 py-4 flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">
              Edit Sale
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Modal Content */}
          <div className="bg-white px-6 py-4">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <LoadingSpinner size={24} />
              </div>
            ) : error ? (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded flex items-start">
                <AlertTriangle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
                <div>{error}</div>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Sale Info (Read-only) */}
                <div className="bg-gray-50 p-4 rounded-md">
                  <h4 className="font-medium text-gray-900 mb-2">Sale Information (Read-only)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">Sale Type</p>
                      <p className="font-medium">{sale.sale_type === 'cash' ? 'Cash Sale' : 'Credit Sale'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Payment Method</p>
                      <p className="font-medium">
                        {sale.sale_type === 'credit' 
                          ? 'Credit' 
                          : sale.cash_payment_method === 'cash' ? 'Cash' : 'MPESA'}
                      </p>
                    </div>
                    <div>
                      <div className="flex items-center justify-between">
                        <p className="text-sm text-gray-500">Client</p>
                        {!isEditingClient && (
                          <button
                            type="button"
                            onClick={startEditingClient}
                            className="text-teal-600 hover:text-teal-800 text-xs flex items-center"
                          >
                            <Edit2 className="h-3 w-3 mr-1" />
                            Edit
                          </button>
                        )}
                      </div>

                      {isEditingClient ? (
                        <div className="mt-2 space-y-3">
                          {/* Client Search */}
                          <div className="relative">
                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <Search className="h-4 w-4 text-gray-400" />
                              </div>
                              <input
                                type="text"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                placeholder="Search for client..."
                                className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 text-sm"
                              />
                            </div>

                            {/* Client Dropdown */}
                            {isClientDropdownOpen && filteredClients.length > 0 && (
                              <div className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md max-h-40 overflow-auto border">
                                <ul className="py-1">
                                  {filteredClients.map((client) => (
                                    <li
                                      key={client.id}
                                      onClick={() => handleClientSelect(client)}
                                      className="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                                    >
                                      <div className="flex justify-between">
                                        <span className="font-medium">{client.name}</span>
                                        <span className="text-gray-500">{client.phone_number}</span>
                                      </div>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>

                          {/* Walk-in Customer Button */}
                          <button
                            type="button"
                            onClick={() => setIsWalkInModalOpen(true)}
                            className="w-full flex items-center justify-center px-3 py-2 border border-teal-300 text-teal-700 bg-teal-50 rounded-md hover:bg-teal-100 text-sm"
                          >
                            <UserPlus className="h-4 w-4 mr-2" />
                            Walk-in Customer
                          </button>

                          {/* One-off Customer Option */}
                          <div className="border-t pt-3">
                            <button
                              type="button"
                              onClick={handleOneOffCustomer}
                              className="text-sm text-gray-600 hover:text-gray-800"
                            >
                              Or use one-off customer details
                            </button>

                            {!selectedClientId && (
                              <div className="mt-2 space-y-2">
                                <input
                                  type="text"
                                  value={oneOffClientName}
                                  onChange={(e) => setOneOffClientName(e.target.value)}
                                  placeholder="Customer name"
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                                />
                                <input
                                  type="text"
                                  value={oneOffClientPhone}
                                  onChange={(e) => setOneOffClientPhone(e.target.value)}
                                  placeholder="Customer phone (optional)"
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                                />
                              </div>
                            )}
                          </div>

                          {/* Edit Actions */}
                          <div className="flex space-x-2 pt-2">
                            <button
                              type="button"
                              onClick={cancelEditingClient}
                              className="flex-1 px-3 py-1 border border-gray-300 text-gray-700 rounded text-xs hover:bg-gray-50"
                            >
                              Cancel
                            </button>
                            <button
                              type="button"
                              onClick={() => {
                                setIsEditingClient(false);
                                // Force a re-render to show updated client info
                                setSearchTerm('');
                                setIsClientDropdownOpen(false);
                              }}
                              className="flex-1 px-3 py-1 bg-teal-600 text-white rounded text-xs hover:bg-teal-700"
                            >
                              Done
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div>
                          <p className="font-medium">
                            {selectedClientId
                              ? (clients.find(c => c.id === selectedClientId)?.name || sale.clients?.name)
                              : (oneOffClientName || sale.one_off_client_name || 'One-off Customer')}
                          </p>
                          {/* Show if client has been changed */}
                          {(selectedClientId !== sale?.client_id ||
                            oneOffClientName !== (sale?.one_off_client_name || '') ||
                            oneOffClientPhone !== (sale?.one_off_client_phone || '')) && (
                            <p className="text-xs text-orange-600 mt-1">
                              Client information has been modified
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    Note: To change sale type or payment method, please delete this sale and create a new one.
                  </p>
                </div>

                {/* VAT Settings */}
                <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                  <h4 className="font-medium text-gray-900 mb-3">VAT Settings</h4>
                  <div className="flex items-center space-x-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={includeVat}
                        onChange={(e) => setIncludeVat(e.target.checked)}
                        className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">Include VAT</span>
                    </label>
                    {includeVat && (
                      <div className="flex items-center space-x-2">
                        <label className="text-sm text-gray-700">VAT Rate:</label>
                        <input
                          type="number"
                          value={vatRate}
                          onChange={(e) => setVatRate(parseFloat(e.target.value) || 0)}
                          min="0"
                          max="100"
                          step="0.01"
                          className="w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-teal-500"
                        />
                        <span className="text-sm text-gray-700">%</span>
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    VAT will be calculated on the total amount after discounts
                  </p>
                </div>

                {/* Editable Items */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Items</h4>
                  <div className="overflow-x-auto border border-gray-200 rounded-lg">
                    <table className="w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-2/5">
                            Product
                          </th>
                          <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                            Price
                          </th>
                          <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/12">
                            Qty
                          </th>
                          <th scope="col" className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4">
                            Discount
                          </th>
                          <th scope="col" className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                            Total
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {saleItems.map((item) => (
                          <tr key={item.id} className="hover:bg-gray-50">
                            <td className="px-3 py-3 text-sm text-gray-900">
                              <div className="max-w-xs truncate" title={item.part_title}>
                                {item.part_title}
                              </div>
                            </td>
                            <td className="px-2 py-3 text-sm text-gray-900">
                              <div className="text-xs">Kshs {item.price_at_sale.toLocaleString()}</div>
                            </td>
                            <td className="px-2 py-3">
                              <input
                                type="number"
                                min="1"
                                value={item.quantity}
                                onChange={(e) => handleQuantityChange(item.id, parseInt(e.target.value) || 1)}
                                className="w-12 px-1 py-1 border border-gray-300 rounded text-xs text-center"
                              />
                            </td>
                            <td className="px-2 py-3">
                              <div className="space-y-1">
                                <input
                                  type="number"
                                  min="0"
                                  value={item.discount_amount}
                                  onChange={(e) => handleDiscountChange(item.id, parseFloat(e.target.value) || 0)}
                                  className="w-20 px-1 py-1 border border-gray-300 rounded text-xs"
                                  placeholder="Amount"
                                />
                                <input
                                  type="text"
                                  value={item.discount_reason || ''}
                                  onChange={(e) => handleDiscountReasonChange(item.id, e.target.value)}
                                  className="w-full px-1 py-1 border border-gray-300 rounded text-xs"
                                  placeholder="Reason"
                                />
                              </div>
                            </td>
                            <td className="px-3 py-3 text-right text-sm font-medium text-gray-900">
                              <div className="text-xs">Kshs {((item.price_at_sale * item.quantity) - item.discount_amount).toLocaleString()}</div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot className="bg-gray-50">
                        <tr>
                          <td colSpan={4} className="px-3 py-2 text-right text-xs font-medium text-gray-700">
                            Subtotal:
                          </td>
                          <td className="px-3 py-2 text-right text-xs font-medium text-gray-900">
                            Kshs {subtotal.toLocaleString()}
                          </td>
                        </tr>
                        <tr>
                          <td colSpan={4} className="px-3 py-2 text-right text-xs font-medium text-gray-700">
                            Total Discount:
                          </td>
                          <td className="px-3 py-2 text-right text-xs font-medium text-gray-900">
                            Kshs {totalDiscount.toLocaleString()}
                          </td>
                        </tr>
                        <tr>
                          <td colSpan={4} className="px-3 py-2 text-right text-xs font-medium text-gray-700">
                            Total Before VAT:
                          </td>
                          <td className="px-3 py-2 text-right text-xs font-medium text-gray-900">
                            Kshs {totalBeforeVat.toLocaleString()}
                          </td>
                        </tr>
                        {includeVat && (
                          <tr>
                            <td colSpan={4} className="px-3 py-2 text-right text-xs font-medium text-gray-700">
                              VAT ({vatRate}%):
                            </td>
                            <td className="px-3 py-2 text-right text-xs font-medium text-gray-900">
                              Kshs {vatAmount.toLocaleString()}
                            </td>
                          </tr>
                        )}
                        <tr className="border-t-2 border-gray-300">
                          <td colSpan={4} className="px-3 py-2 text-right text-sm font-bold text-gray-900">
                            {includeVat ? 'Total (Inc. VAT):' : 'Total:'}
                          </td>
                          <td className="px-3 py-2 text-right text-sm font-bold text-gray-900">
                            Kshs {finalTotal.toLocaleString()}
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end pt-4">
                  <button
                    type="button"
                    onClick={onClose}
                    disabled={isSaving}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 mr-3 disabled:opacity-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleSave}
                    disabled={isSaving}
                    className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 disabled:opacity-50 flex items-center"
                  >
                    {isSaving ? (
                      <>
                        <LoadingSpinner size={16} /> 
                        <span className="ml-2">Saving...</span>
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Save Changes
                      </>
                    )}
                  </button>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </div>

      {/* Walk-in Customer Modal */}
      <WalkInCustomerModal
        isOpen={isWalkInModalOpen}
        onClose={() => setIsWalkInModalOpen(false)}
        onCustomerSelected={handleWalkInCustomerSelected}
      />
    </div>
  );
};

export default EditSaleModal;
