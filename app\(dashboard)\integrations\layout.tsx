import { ReactNode } from 'react';
import { Metadata } from 'next';
import DashboardLayout from '@/app/components/dashboard/DashboardLayout';

export const metadata: Metadata = {
  title: 'Integrations | Autoflow Dashboard',
  description: 'Manage external integrations for your Autoflow store',
};

export default function IntegrationsLayout({
  children,
}: {
  children: ReactNode;
}) {
  return <DashboardLayout>{children}</DashboardLayout>;
}
