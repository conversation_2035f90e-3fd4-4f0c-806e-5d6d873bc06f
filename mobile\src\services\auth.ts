import { supabase } from './supabase';
import { apiClient } from './api';
import { CONFIG } from '@/constants/config';
import { User, UserProfile, ApiResponse } from '@/types';

export class AuthService {
  // Sign in with email and password (Phase 1: Validate credentials)
  async signInWithPassword(email: string, password: string): Promise<boolean> {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      // Immediately sign out to prevent session creation (as per your flow)
      await supabase.auth.signOut();
      
      return true;
    } catch (error) {
      console.error('Password validation error:', error);
      throw error;
    }
  }

  // Send OTP (Phase 2: Initiate OTP flow)
  async sendOTP(email: string): Promise<void> {
    try {
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: false,
        },
      });

      if (error) throw error;
    } catch (error) {
      console.error('OTP send error:', error);
      throw error;
    }
  }

  // Verify OTP and complete sign in
  async verifyOTP(email: string, token: string): Promise<User> {
    try {
      const { data, error } = await supabase.auth.verifyOtp({
        email,
        token,
        type: 'email',
      });

      if (error) throw error;
      if (!data.user) throw new Error('No user data received');

      return data.user as User;
    } catch (error) {
      console.error('OTP verification error:', error);
      throw error;
    }
  }

  // Complete login flow (password validation + OTP)
  async login(email: string, password: string): Promise<{ requiresOTP: boolean }> {
    try {
      // Phase 1: Validate credentials
      await this.signInWithPassword(email, password);
      
      // Phase 2: Send OTP
      await this.sendOTP(email);
      
      return { requiresOTP: true };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  // Complete OTP verification
  async completeLogin(email: string, otp: string): Promise<User> {
    try {
      const user = await this.verifyOTP(email, otp);
      return user;
    } catch (error) {
      console.error('Complete login error:', error);
      throw error;
    }
  }

  // Register new user
  async register(
    email: string,
    password: string,
    fullName: string,
    phone: string
  ): Promise<User> {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            phone: phone,
          },
        },
      });

      if (error) throw error;
      if (!data.user) throw new Error('No user data received');

      return data.user as User;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  // Sign out
  async signOut(): Promise<void> {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  }

  // Get current user
  async getCurrentUser(): Promise<User | null> {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error) throw error;
      return user as User | null;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  // Get user profile with roles
  async getUserProfile(): Promise<UserProfile | null> {
    try {
      const user = await this.getCurrentUser();
      if (!user) return null;

      // Fetch user profile with roles from your API
      const profile = await apiClient.get<UserProfile>(`/api/users/${user.id}/profile`);
      return profile;
    } catch (error) {
      console.error('Get user profile error:', error);
      return null;
    }
  }

  // Reset password
  async resetPassword(email: string): Promise<void> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${CONFIG.API_BASE_URL}/reset-password`,
      });

      if (error) throw error;
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    }
  }

  // Update password
  async updatePassword(newPassword: string): Promise<void> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) throw error;
    } catch (error) {
      console.error('Update password error:', error);
      throw error;
    }
  }

  // Update profile
  async updateProfile(updates: Partial<User>): Promise<User> {
    try {
      const { data, error } = await supabase.auth.updateUser({
        data: updates,
      });

      if (error) throw error;
      if (!data.user) throw new Error('No user data received');

      return data.user as User;
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  }

  // Listen to auth state changes
  onAuthStateChange(callback: (user: User | null) => void) {
    return supabase.auth.onAuthStateChange((event, session) => {
      callback(session?.user as User | null);
    });
  }
}

export const authService = new AuthService();
export default authService;
