export const dynamic = 'force-dynamic';

import { Suspense } from 'react';
import Spinner from '@/app/components/ui/Spinner';

// Import the content component
import AuditLogContent from './components/AuditLogContent';

export default function AuditLogPage() {
  return (
    <Suspense fallback={<div className="flex min-h-[300px] items-center justify-center">
      <Spinner size="lg" />
    </div>}>
      <AuditLogContent />
    </Suspense>
  );
}
