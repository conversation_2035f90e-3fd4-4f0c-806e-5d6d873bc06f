'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Save, Edit } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';
import { StorageUnit, StorageArea, StorageUnitFormData } from '../../types';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

interface EditUnitModalProps {
  isOpen: boolean;
  onClose: () => void;
  unit: StorageUnit;
  areas: StorageArea[];
  onSuccess: (updatedItem?: any, itemType?: 'area' | 'unit', action?: 'add' | 'update' | 'delete') => void;
}

// Form validation schema
const unitSchema = z.object({
  area_id: z.number().min(1, 'Storage area is required'),
  unit_type: z.enum(['shelf', 'cage', 'hanging_line', 'open_space', 'engine_area']),
  identifier: z.string().min(1, 'Identifier is required'),
  description: z.string().optional(),
});

const EditUnitModal: React.FC<EditUnitModalProps> = ({
  isOpen,
  onClose,
  unit,
  areas,
  onSuccess
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    setValue,
    watch
  } = useForm<StorageUnitFormData>({
    resolver: zodResolver(unitSchema),
    defaultValues: {
      area_id: 0,
      unit_type: 'shelf',
      identifier: '',
      description: ''
    }
  });

  const selectedUnitType = watch('unit_type');

  // Initialize form with unit data
  useEffect(() => {
    if (unit) {
      setValue('area_id', unit.area_id);
      setValue('unit_type', unit.unit_type);
      setValue('identifier', unit.identifier);
      setValue('description', unit.description || '');
    }
  }, [unit, setValue]);

  const supabase = createClient();

  const onSubmit: SubmitHandler<StorageUnitFormData> = async (data) => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Validate identifier format for cage units
      if (data.unit_type === 'cage' && !data.identifier.startsWith('CG')) {
        data.identifier = `CG-${data.identifier}`;
      }

      const { data: updatedData, error: updateError } = await supabase
        .from('storage_units')
        .update({
          area_id: data.area_id,
          unit_type: data.unit_type,
          identifier: data.identifier,
          description: data.description || null,
          updated_at: new Date().toISOString()
        })
        .eq('unit_id', unit.unit_id)
        .select('*')
        .single();

      if (updateError) throw updateError;

      // Success
      onSuccess(updatedData, 'unit', 'update');
      onClose();
    } catch (err: any) {
      console.error('Error updating storage unit:', err);
      setError(err.message || 'Failed to update storage unit');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Animation variants
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3 } }
  };

  const modalVariants = {
    hidden: { opacity: 0, y: 50, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    },
    exit: {
      opacity: 0,
      y: 50,
      scale: 0.95,
      transition: { duration: 0.2 }
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
          onClick={onClose}
        >
          <motion.div
            className="bg-white rounded-lg shadow-xl w-full max-w-md overflow-hidden"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <div className="flex items-center">
                <Edit className="w-6 h-6 text-teal-600 mr-3" />
                <h2 className="text-xl font-semibold text-gray-800">Edit Storage Unit</h2>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {error && (
              <div className="px-6 pt-4 pb-0">
                <div className="p-3 bg-red-50 text-red-700 rounded-md text-sm">
                  {error}
                </div>
              </div>
            )}

            <div className="p-6">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div>
                  <label htmlFor="area_id" className="block text-sm font-medium text-gray-700 mb-1">
                    Storage Area *
                  </label>
                  <select
                    id="area_id"
                    {...register('area_id', { valueAsNumber: true })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    disabled={isSubmitting}
                  >
                    <option value={0}>Select a storage area</option>
                    {areas.map(area => (
                      <option key={area.area_id} value={area.area_id}>
                        {area.name} ({area.location_type}, {area.level})
                      </option>
                    ))}
                  </select>
                  {errors.area_id && (
                    <p className="mt-1 text-sm text-red-600">{errors.area_id.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="unit_type" className="block text-sm font-medium text-gray-700 mb-1">
                    Unit Type *
                  </label>
                  <select
                    id="unit_type"
                    {...register('unit_type')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    disabled={isSubmitting}
                  >
                    <option value="shelf">Shelf</option>
                    <option value="cage">Cage</option>
                    <option value="hanging_line">Hanging Line</option>
                    <option value="open_space">Open Space</option>
                    <option value="engine_area">Engine Area</option>
                  </select>
                  {errors.unit_type && (
                    <p className="mt-1 text-sm text-red-600">{errors.unit_type.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="identifier" className="block text-sm font-medium text-gray-700 mb-1">
                    Identifier *
                  </label>
                  <input
                    id="identifier"
                    type="text"
                    {...register('identifier')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    disabled={isSubmitting}
                  />
                  {selectedUnitType === 'cage' && (
                    <p className="mt-1 text-xs text-gray-500">
                      Cage identifiers should start with 'CG'. If not provided, it will be added automatically.
                    </p>
                  )}
                  {errors.identifier && (
                    <p className="mt-1 text-sm text-red-600">{errors.identifier.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description (optional)
                  </label>
                  <textarea
                    id="description"
                    {...register('description')}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    disabled={isSubmitting}
                  />
                </div>

                <div className="mt-6 flex justify-end">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 mr-2"
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 flex items-center"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-2" />
                        Save Changes
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default EditUnitModal;
