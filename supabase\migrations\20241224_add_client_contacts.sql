-- Create client_contacts table for multiple contact persons per client
CREATE TABLE IF NOT EXISTS public.client_contacts (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id uuid NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,
    name text NOT NULL,
    phone_number varchar(255) NOT NULL,
    email text,
    position text, -- e.g., 'Manager', 'Owner', 'Mechanic', 'Contact Person'
    is_primary boolean NOT NULL DEFAULT false,
    is_active boolean NOT NULL DEFAULT true,
    notes text,
    created_at timestamptz(6) NOT NULL DEFAULT now(),
    updated_at timestamptz(6) NOT NULL DEFAULT now()
);

-- Add comments
COMMENT ON TABLE public.client_contacts IS 'Stores multiple contact persons for each client, especially useful for garages and businesses';
COMMENT ON COLUMN public.client_contacts.client_id IS 'Links to the client this contact belongs to';
COMMENT ON COLUMN public.client_contacts.name IS 'Full name of the contact person';
COMMENT ON COLUMN public.client_contacts.phone_number IS 'Phone number of the contact person';
COMMENT ON COLUMN public.client_contacts.email IS 'Email address of the contact person (optional)';
COMMENT ON COLUMN public.client_contacts.position IS 'Role or position of the contact person in the organization';
COMMENT ON COLUMN public.client_contacts.is_primary IS 'Indicates if this is the primary contact person';
COMMENT ON COLUMN public.client_contacts.is_active IS 'Indicates if this contact is currently active';
COMMENT ON COLUMN public.client_contacts.notes IS 'Additional notes about the contact person';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_client_contacts_client_id ON public.client_contacts(client_id);
CREATE INDEX IF NOT EXISTS idx_client_contacts_primary ON public.client_contacts(client_id, is_primary) WHERE is_primary = true;
CREATE INDEX IF NOT EXISTS idx_client_contacts_active ON public.client_contacts(client_id, is_active) WHERE is_active = true;

-- Add constraint to ensure only one primary contact per client
CREATE UNIQUE INDEX IF NOT EXISTS idx_client_contacts_one_primary 
ON public.client_contacts(client_id) 
WHERE is_primary = true;

-- Enable RLS (Row Level Security)
ALTER TABLE public.client_contacts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (adjust based on your authentication setup)
CREATE POLICY "Enable read access for authenticated users" ON public.client_contacts
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Enable insert for authenticated users" ON public.client_contacts
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Enable update for authenticated users" ON public.client_contacts
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Enable delete for authenticated users" ON public.client_contacts
    FOR DELETE USING (auth.role() = 'authenticated');

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_client_contacts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_client_contacts_updated_at
    BEFORE UPDATE ON public.client_contacts
    FOR EACH ROW
    EXECUTE FUNCTION update_client_contacts_updated_at();

-- Function to ensure only one primary contact per client
CREATE OR REPLACE FUNCTION ensure_one_primary_contact()
RETURNS TRIGGER AS $$
BEGIN
    -- If setting a contact as primary, unset all other primary contacts for this client
    IF NEW.is_primary = true THEN
        UPDATE public.client_contacts 
        SET is_primary = false 
        WHERE client_id = NEW.client_id 
        AND id != NEW.id 
        AND is_primary = true;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_ensure_one_primary_contact
    BEFORE INSERT OR UPDATE ON public.client_contacts
    FOR EACH ROW
    EXECUTE FUNCTION ensure_one_primary_contact();
