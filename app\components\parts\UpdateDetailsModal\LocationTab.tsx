// /components/modals/UpdateDetailsModal/LocationTab.tsx
import React, { useState, useEffect } from 'react';
import { LocationTabProps, LocationSubType, StorageUnit } from './types';
import NumberInput from '@/app/components/ui/inputs/NumberInput'; // Assuming path
import Input from '@/app/components/ui/inputs/Input'; // Assuming path

const LocationTab: React.FC<LocationTabProps> = ({
  register,
  control,
  errors,
  setValue,
  watch,
  storageAreas,
  allStorageUnits,
  initialLocation,
  isLoadingLocation,
}) => {
  const [filteredUnits, setFilteredUnits] = useState<StorageUnit[]>([]);

  // Watch relevant form fields
  const selectedAreaId = watch('location.area_id');
  const selectedUnitId = watch('location.unit_id');
  const selectedSubtype = watch('location.location_subtype');

  // --- Effects ---

  // Effect 1: Filter units when selectedAreaId changes or allStorageUnits load
  useEffect(() => {
    console.log("LocationTab: Effect 1 - selectedAreaId changed:", selectedAreaId);
    console.log("LocationTab: Effect 1 - allStorageUnits length:", allStorageUnits.length);

    if (selectedAreaId && allStorageUnits.length > 0) {
      const units = allStorageUnits.filter(unit => unit.area_id === selectedAreaId);
      console.log("LocationTab: Filtered units for area", selectedAreaId, ":", units);
      setFilteredUnits(units);

      // If the previously selected unit is not in the new list, reset unit selection
      // But only if we're not in the initial loading phase (when initialLocation is being set)
      if (selectedUnitId) {
        const currentUnitStillValid = units.some(unit => unit.unit_id === selectedUnitId);
        console.log("LocationTab: Current unit still valid?", currentUnitStillValid, "selectedUnitId:", selectedUnitId);

        if (!currentUnitStillValid) {
          console.log("LocationTab: Resetting unit_id because it's not valid for the selected area");
          setValue('location.unit_id', null); // Reset unit if area changes and unit is invalid
          setValue('location.location_subtype', ''); // Reset subtype if unit changes
          setValue('location.details', null); // Reset details
        }
      }
    } else {
      console.log("LocationTab: Clearing filtered units");
      setFilteredUnits([]); // Clear units if no area is selected

      // Optionally reset unit_id if area is cleared
      // But only if we're not in the initial loading phase
      if (!selectedAreaId && !initialLocation) {
        console.log("LocationTab: Resetting unit_id because area_id is cleared");
        setValue('location.unit_id', null);
        setValue('location.location_subtype', '');
        setValue('location.details', null);
      }
    }
  }, [selectedAreaId, allStorageUnits, setValue, selectedUnitId, initialLocation]);


  // Effect 2: Populate form with initial location data when it loads
  useEffect(() => {
    console.log("LocationTab: initialLocation changed:", initialLocation);

    if (initialLocation) {
      console.log("LocationTab: Setting form values from initialLocation");

      // First, filter the units for the selected area
      if (initialLocation.area_id && allStorageUnits.length > 0) {
        const units = allStorageUnits.filter(unit => unit.area_id === initialLocation.area_id);
        console.log(`LocationTab: Filtered ${units.length} units for area_id ${initialLocation.area_id}`);
        setFilteredUnits(units);
      }

      // Set all form values in one go using React Hook Form's setValue
      setValue('location.location_id', initialLocation.location_id);

      // Convert area_id to number if it's a string
      const areaId = typeof initialLocation.area_id === 'string'
        ? parseInt(initialLocation.area_id, 10)
        : initialLocation.area_id;
      setValue('location.area_id', areaId);

      // Convert unit_id to number if it's a string
      const unitId = typeof initialLocation.unit_id === 'string'
        ? parseInt(initialLocation.unit_id, 10)
        : initialLocation.unit_id;
      setValue('location.unit_id', unitId);

      // Set the remaining values
      setValue('location.location_subtype', initialLocation.location_subtype || '');
      setValue('location.quantity', initialLocation.quantity ?? 1);
      setValue('location.details', initialLocation.details ?? null);
      setValue('location.notes', initialLocation.notes ?? '');

      console.log("LocationTab: Form values set from initialLocation");
    } else {
      console.log("LocationTab: No initialLocation, setting defaults");
      // Set defaults for a new location if needed
      setValue('location.quantity', 1);
      setValue('location.area_id', null);
      setValue('location.unit_id', null);
      setValue('location.location_subtype', '');
      setValue('location.details', null);
      setValue('location.notes', '');
    }
  }, [initialLocation, setValue, allStorageUnits]);

   // Effect 3: Reset details when subtype changes
   useEffect(() => {
       // Keep existing details if they are compatible, otherwise reset
       const currentDetails = watch('location.details');
       if (!currentDetails) return; // No details to reset

       let shouldReset = false;
       switch (selectedSubtype) {
           case LocationSubType.OpenShelf:
           case LocationSubType.ShelfSection:
               if (currentDetails.crate_code || currentDetails.container_code || currentDetails.row || currentDetails.col) shouldReset = true;
               break;
           case LocationSubType.Crate:
               if (currentDetails.container_code || currentDetails.row || currentDetails.col) shouldReset = true;
               break;
           case LocationSubType.Container:
               if (currentDetails.crate_code || currentDetails.row || currentDetails.col) shouldReset = true;
               break;
           case LocationSubType.CageSection:
               if (currentDetails.crate_code || currentDetails.container_code) shouldReset = true;
               break;
           case LocationSubType.HangingLine:
           case LocationSubType.EngineArea:
                // These might not have specific details in the JSON, reset if any exist
                if (Object.keys(currentDetails).length > 0) shouldReset = true;
               break;
           default:
               // If no subtype or unknown, reset if any details exist
               if (Object.keys(currentDetails).length > 0) shouldReset = true;
               break;
       }

       if (shouldReset) {
            setValue('location.details', null);
       }

   }, [selectedSubtype, setValue, watch]);


  // --- Render Logic ---

  if (isLoadingLocation) {
    return <div className="text-center py-6 text-gray-500">Loading location info...</div>;
  }

  // Determine which detail fields to show based on subtype
  // Convert selectedSubtype to LocationSubType enum value if it's a string
  const subtypeValue = selectedSubtype as LocationSubType;

  // Log the current subtype for debugging
  console.log("LocationTab: Current subtype:", subtypeValue);

  const showLevel = [
    LocationSubType.OpenShelf,
    LocationSubType.Crate,
    LocationSubType.Container,
    LocationSubType.ShelfSection
  ].includes(subtypeValue);

  const showCrateCode = subtypeValue === LocationSubType.Crate;
  const showContainerCode = subtypeValue === LocationSubType.Container;
  const showRowCol = subtypeValue === LocationSubType.CageSection;

  return (
    <div className="space-y-4 pt-4">
      <h4 className="font-medium mb-2 text-gray-700">Part Location</h4>

      {/* Show a message if no location exists yet */}
      {!initialLocation && (
        <div className="bg-blue-50 p-3 rounded-md mb-4 text-blue-700 text-sm">
          No location has been set for this part yet. Use the form below to assign a location.
        </div>
      )}

       {/* Hidden field for location_id (if updating existing) */}
       {initialLocation?.location_id && (
           <input type="hidden" {...register('location.location_id')} />
       )}

      {/* Storage Area Dropdown */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <label htmlFor="location.area_id" className="block text-sm font-medium mb-1 text-gray-600">Storage Area</label>
          <select
            id="location.area_id"
            {...register('location.area_id', {
                // valueAsNumber: true, // Cast value to number
                required: 'Storage Area is required'
            })}
            className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white disabled:bg-gray-100"
            aria-invalid={errors.location?.area_id ? "true" : "false"}
             onChange={(e) => {
                 const value = e.target.value ? parseInt(e.target.value, 10) : null;
                 setValue('location.area_id', value, { shouldValidate: true });
                 // Reset unit when area changes
                 setValue('location.unit_id', null);
                 setValue('location.location_subtype', '');
                 setValue('location.details', null);
             }}
             value={selectedAreaId ?? ''} // Convert null to empty string
          >
            <option value="">Select Area...</option>
            {storageAreas.map(area => (
              <option key={area.area_id} value={area.area_id}>
                {area.name} ({area.location_type} / {area.level})
              </option>
            ))}
          </select>
          {errors.location?.area_id && (
            <p className="text-red-500 text-sm mt-1" role="alert">{errors.location.area_id.message}</p>
          )}
        </div>

        {/* Storage Unit Dropdown */}
        <div>
          <label htmlFor="location.unit_id" className="block text-sm font-medium mb-1 text-gray-600">Storage Unit</label>
          <select
            id="location.unit_id"
            {...register('location.unit_id', {
                required: 'Storage Unit is required'
            })}
            disabled={!selectedAreaId}
            className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white disabled:bg-gray-100"
            aria-invalid={errors.location?.unit_id ? "true" : "false"}
            onChange={(e) => {
                const value = e.target.value ? parseInt(e.target.value, 10) : null;
                setValue('location.unit_id', value, { shouldValidate: true });
                // Reset subtype and details when unit changes
                setValue('location.location_subtype', '');
                setValue('location.details', null);
            }}
          >
            <option value="">Select Unit...</option>
            {filteredUnits.map(unit => (
              <option key={unit.unit_id} value={unit.unit_id}>
                {unit.identifier} ({unit.unit_type})
              </option>
            ))}
             {selectedAreaId && filteredUnits.length === 0 && (
                 <option value="" disabled>No units found for this area</option>
             )}
          </select>
          {errors.location?.unit_id && (
            <p className="text-red-500 text-sm mt-1" role="alert">{errors.location.unit_id.message}</p>
          )}
        </div>
      </div>


        {/* Location Subtype Dropdown */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
                <label htmlFor="location.location_subtype" className="block text-sm font-medium mb-1 text-gray-600">Location Type</label>
                <select
                    id="location.location_subtype"
                    {...register('location.location_subtype', { required: 'Location Type is required' })}
                    className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white disabled:bg-gray-100"
                    aria-invalid={errors.location?.location_subtype ? "true" : "false"}
                    onChange={(e) => {
                        const newSubtype = e.target.value as LocationSubType | '';
                        console.log("LocationTab: Changing subtype to:", newSubtype);
                        setValue('location.location_subtype', newSubtype, { shouldValidate: true });

                        // Always initialize details object when subtype changes
                        setValue('location.details', {});

                        // Set default values based on subtype
                        if (newSubtype === LocationSubType.Container) {
                            setValue('location.details.container_code', 'C-');
                        }
                    }}
                >
                    <option value="">Select Type...</option>
                    {/* Populate with LocationSubType enum values */}
                    {Object.values(LocationSubType).map(subtype => (
                        <option key={subtype} value={subtype}>{subtype.replace(/_/g, ' ')}</option>
                    ))}
                </select>
                {errors.location?.location_subtype && (
                    <p className="text-red-500 text-sm mt-1" role="alert">{errors.location.location_subtype.message}</p>
                )}
            </div>

            {/* Quantity Input */}
            <div>
                <label htmlFor="location.quantity" className="block text-sm font-medium mb-1 text-gray-600">Quantity in Location</label>
                <NumberInput
                    name="location.quantity"
                    control={control}
                    label="Quantity"
                    rules={{ required: 'Quantity is required', min: { value: 0, message: 'Quantity cannot be negative' } }}
                    className="w-full"
                />
                {errors.location?.quantity && (
                    <p className="text-red-500 text-sm mt-1" role="alert">{errors.location.quantity.message}</p>
                )}
            </div>
        </div>


      {/* Dynamic Detail Fields based on Subtype */}
      {selectedSubtype && (
        <div className="bg-gray-100 p-3 rounded-md border border-gray-200 mt-3">
          <h5 className="text-sm font-medium mb-2 text-gray-700">Location Details ({selectedSubtype.replace(/_/g, ' ')})</h5>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Shelf Level */}
            {showLevel && (
              <div>
                <label htmlFor="location.details.level" className="block text-xs font-medium mb-1 text-gray-600">Level / Shelf #</label>
                <Input
                  id="location.details.level"
                  {...register('location.details.level', { required: showLevel ? 'Level is required' : false })}
                  className="w-full text-sm"
                  placeholder="e.g., 3 or Top"
                  aria-invalid={errors.location?.details?.level ? "true" : "false"}
                />
                 {errors.location?.details?.level && (
                    <p className="text-red-500 text-xs mt-1" role="alert">{errors.location.details.level.message}</p>
                 )}
              </div>
            )}

            {/* Crate Code */}
            {showCrateCode && (
              <div>
                <label htmlFor="location.details.crate_code" className="block text-xs font-medium mb-1 text-gray-600">Crate Code</label>
                <Input
                  id="location.details.crate_code"
                  {...register('location.details.crate_code', { required: showCrateCode ? 'Crate Code is required' : false })}
                  className="w-full text-sm"
                  placeholder="e.g., A15"
                   aria-invalid={errors.location?.details?.crate_code ? "true" : "false"}
               />
                {errors.location?.details?.crate_code && (
                   <p className="text-red-500 text-xs mt-1" role="alert">{errors.location.details.crate_code.message}</p>
                )}
              </div>
            )}

             {/* Container Code */}
             {showContainerCode && (
               <div>
                 <label htmlFor="location.details.container_code" className="block text-xs font-medium mb-1 text-gray-600">Container Code</label>
                 <Input
                   id="location.details.container_code"
                   {...register('location.details.container_code', {
                       required: showContainerCode ? 'Container Code is required' : false,
                       pattern: { value: /^C-.+/, message: 'Container code must start with C-' } // Example validation
                   })}
                   className="w-full text-sm"
                   placeholder="e.g., C-101"
                   aria-invalid={errors.location?.details?.container_code ? "true" : "false"}
                 />
                 {errors.location?.details?.container_code && (
                    <p className="text-red-500 text-xs mt-1" role="alert">{errors.location.details.container_code.message}</p>
                 )}
               </div>
             )}

             {/* Cage Row/Col */}
             {showRowCol && (
                 <>
                     <div>
                         <label htmlFor="location.details.row" className="block text-xs font-medium mb-1 text-gray-600">Row</label>
                         <Input
                             id="location.details.row"
                             {...register('location.details.row', { required: showRowCol ? 'Row is required' : false })}
                             className="w-full text-sm"
                             placeholder="e.g., A"
                             aria-invalid={errors.location?.details?.row ? "true" : "false"}
                         />
                         {errors.location?.details?.row && (
                            <p className="text-red-500 text-xs mt-1" role="alert">{errors.location.details.row.message}</p>
                         )}
                     </div>
                     <div>
                         <label htmlFor="location.details.col" className="block text-xs font-medium mb-1 text-gray-600">Column</label>
                         <Input
                             id="location.details.col"
                             {...register('location.details.col', { required: showRowCol ? 'Column is required' : false })}
                             className="w-full text-sm"
                             placeholder="e.g., 5"
                             aria-invalid={errors.location?.details?.col ? "true" : "false"}
                         />
                          {errors.location?.details?.col && (
                             <p className="text-red-500 text-xs mt-1" role="alert">{errors.location.details.col.message}</p>
                          )}
                     </div>
                 </>
             )}
          </div>
        </div>
      )}


      {/* Notes */}
      <div>
        <label htmlFor="location.notes" className="block text-sm font-medium mb-1 text-gray-600">Location Notes (Optional)</label>
        <textarea
          id="location.notes"
          {...register('location.notes')}
          rows={2}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
          placeholder="Any additional notes about this location..."
        />
      </div>

    </div>
  );
};

export default LocationTab;
