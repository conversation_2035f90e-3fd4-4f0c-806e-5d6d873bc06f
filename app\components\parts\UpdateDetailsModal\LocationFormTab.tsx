'use client';

import React, { useState, useEffect } from 'react';
import { Controller } from 'react-hook-form';
import { createClient } from '@/app/libs/supabase/client';
import { LocationTabProps, LocationSubType, StorageUnit } from './types';
import Input from '@/app/components/ui/inputs/Input';
import NumberInput from '@/app/components/ui/inputs/NumberInput';

const LocationFormTab: React.FC<LocationTabProps> = ({
  register,
  control,
  errors,
  setValue,
  watch,
  storageAreas,
  allStorageUnits,
  initialLocation,
  partId,
  isLoadingLocation,
}) => {
  const [filteredUnits, setFilteredUnits] = useState<StorageUnit[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Watch relevant form fields
  const selectedAreaId = watch('location.area_id');
  const selectedUnitId = watch('location.unit_id');
  const selectedSubtype = watch('location.location_subtype');

  // --- Effects ---

  // Effect 1: Filter units when selectedAreaId changes or allStorageUnits load
  useEffect(() => {
    console.log("LocationFormTab: selectedAreaId changed:", selectedAreaId);

    if (selectedAreaId && allStorageUnits.length > 0) {
      const units = allStorageUnits.filter(unit => unit.area_id === selectedAreaId);
      console.log(`LocationFormTab: Filtered ${units.length} units for area_id ${selectedAreaId}`);
      setFilteredUnits(units);

      // If the previously selected unit is not in the new list, reset unit selection
      if (selectedUnitId) {
        const currentUnitStillValid = units.some(unit => unit.unit_id === selectedUnitId);
        if (!currentUnitStillValid) {
          console.log("LocationFormTab: Resetting unit_id because it's not valid for the selected area");
          setValue('location.unit_id', null);
          setValue('location.location_subtype', '');
          setValue('location.details', {});
        }
      }
    } else {
      console.log("LocationFormTab: Clearing filtered units");
      setFilteredUnits([]);

      // Reset unit_id if area is cleared
      if (!selectedAreaId && !initialLocation) {
        setValue('location.unit_id', null);
        setValue('location.location_subtype', '');
        setValue('location.details', {});
      }
    }
  }, [selectedAreaId, allStorageUnits, setValue, selectedUnitId, initialLocation]);

  // Effect 2: Populate form with initial location data when it loads
  useEffect(() => {
    console.log("LocationFormTab: initialLocation changed:", initialLocation);

    if (initialLocation) {
      console.log("LocationFormTab: Setting form values from initialLocation");

      // First, filter the units for the selected area
      if (initialLocation.area_id && allStorageUnits.length > 0) {
        const units = allStorageUnits.filter(unit => unit.area_id === initialLocation.area_id);
        console.log(`LocationFormTab: Filtered ${units.length} units for area_id ${initialLocation.area_id}`);
        setFilteredUnits(units);
      }

      // Set all form values
      setValue('location.location_id', initialLocation.location_id);

      // Convert area_id to number if it's a string, or empty string if null
      const areaId = initialLocation.area_id
        ? (typeof initialLocation.area_id === 'string'
            ? parseInt(initialLocation.area_id, 10)
            : initialLocation.area_id)
        : '';
      setValue('location.area_id', areaId === '' ? null : areaId);

      // Convert unit_id to number if it's a string, or empty string if null
      const unitId = initialLocation.unit_id
        ? (typeof initialLocation.unit_id === 'string'
            ? parseInt(initialLocation.unit_id, 10)
            : initialLocation.unit_id)
        : '';
      setValue('location.unit_id', unitId === '' ? null : unitId);

      // Set the remaining values
      setValue('location.location_subtype', initialLocation.location_subtype || '');
      setValue('location.quantity', initialLocation.quantity ?? 1);
      setValue('location.details', initialLocation.details ?? {});
      setValue('location.notes', initialLocation.notes ?? '');

      console.log("LocationFormTab: Form values set from initialLocation");
    } else {
      console.log("LocationFormTab: No initialLocation, setting defaults");
      // Set defaults for a new location
      setValue('location.quantity', 1);
      setValue('location.area_id', null);
      setValue('location.unit_id', null);
      setValue('location.location_subtype', '');
      setValue('location.details', {});
      setValue('location.notes', '');
    }
  }, [initialLocation, setValue, allStorageUnits]);

  // Render location subtype specific fields
  const renderLocationSubtypeFields = () => {
    if (!selectedSubtype) return null;

    switch (selectedSubtype) {
      case LocationSubType.Crate:
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1 text-gray-600">Shelf Level</label>
              <Controller
                name="location.details.level"
                control={control}
                defaultValue=""
                rules={{ required: 'Level is required' }}
                shouldUnregister={false}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter shelf level (e.g., 3 or Top)"
                    className="w-full"
                  />
                )}
              />
              {errors.location?.details?.level && (
                <p className="text-red-500 text-xs mt-1">{errors.location.details.level.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium mb-1 text-gray-600">Crate Code</label>
              <Controller
                name="location.details.crate_code"
                control={control}
                defaultValue=""
                rules={{ required: 'Crate Code is required' }}
                shouldUnregister={false}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter crate code (e.g., A23)"
                    className="w-full"
                  />
                )}
              />
              {errors.location?.details?.crate_code && (
                <p className="text-red-500 text-xs mt-1">{errors.location.details.crate_code.message}</p>
              )}
            </div>
          </div>
        );

      case LocationSubType.Container:
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1 text-gray-600">Shelf Level</label>
              <Controller
                name="location.details.level"
                control={control}
                defaultValue=""
                rules={{ required: 'Level is required' }}
                shouldUnregister={false}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter shelf level (e.g., 3 or Top)"
                    className="w-full"
                  />
                )}
              />
              {errors.location?.details?.level && (
                <p className="text-red-500 text-xs mt-1">{errors.location.details.level.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium mb-1 text-gray-600">Container Code</label>
              <Controller
                name="location.details.container_code"
                control={control}
                defaultValue="C-"
                rules={{
                  required: 'Container Code is required',
                  pattern: { value: /^C-.+/, message: 'Container code must start with C-' }
                }}
                shouldUnregister={false}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter container code (e.g., C-101)"
                    className="w-full"
                  />
                )}
              />
              {errors.location?.details?.container_code && (
                <p className="text-red-500 text-xs mt-1">{errors.location.details.container_code.message}</p>
              )}
              <p className="text-xs text-gray-500 mt-1">Container codes must start with C-</p>
            </div>
          </div>
        );

      case LocationSubType.OpenShelf:
      case LocationSubType.ShelfSection:
        return (
          <div>
            <label className="block text-sm font-medium mb-1 text-gray-600">Shelf Level</label>
            <Controller
              name="location.details.level"
              control={control}
              defaultValue=""
              rules={{ required: 'Level is required' }}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="Enter shelf level (e.g., 3 or Top)"
                  className="w-full"
                />
              )}
            />
            {errors.location?.details?.level && (
              <p className="text-red-500 text-xs mt-1">{errors.location.details.level.message}</p>
            )}
          </div>
        );

      case LocationSubType.CageSection:
        return (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1 text-gray-600">Row</label>
              <Controller
                name="location.details.row"
                control={control}
                defaultValue=""
                rules={{ required: 'Row is required' }}
                shouldUnregister={false}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter row (e.g., A)"
                    className="w-full"
                  />
                )}
              />
              {errors.location?.details?.row && (
                <p className="text-red-500 text-xs mt-1">{errors.location.details.row.message}</p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium mb-1 text-gray-600">Column</label>
              <Controller
                name="location.details.col"
                control={control}
                defaultValue=""
                rules={{ required: 'Column is required' }}
                shouldUnregister={false}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter column (e.g., 5)"
                    className="w-full"
                  />
                )}
              />
              {errors.location?.details?.col && (
                <p className="text-red-500 text-xs mt-1">{errors.location.details.col.message}</p>
              )}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (isLoadingLocation) {
    return <div className="text-center py-6 text-gray-500">Loading location info...</div>;
  }

  return (
    <div className="space-y-6 pt-4">
      <h4 className="font-medium mb-2 text-gray-700">Part Location</h4>

      {/* Show a message if no location exists yet */}
      {!initialLocation && (
        <div className="bg-blue-50 p-3 rounded-md mb-4 text-blue-700 text-sm">
          No location has been set for this part yet. Use the form below to assign a location.
        </div>
      )}

      {/* Hidden field for location_id (if updating existing) */}
      {initialLocation?.location_id && (
        <input type="hidden" {...register('location.location_id')} />
      )}

      {/* Storage Area Selection */}
      <div>
        <label className="block text-sm font-medium mb-1 text-gray-600">Storage Area</label>
        <Controller
          name="location.area_id"
          control={control}
          rules={{ required: 'Storage Area is required' }}
          render={({ field }) => (
            <select
              {...field}
              value={field.value || ''}
              className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white disabled:bg-gray-100"
              onChange={(e) => {
                const value = e.target.value ? parseInt(e.target.value, 10) : '';
                field.onChange(value);
                // Reset unit when area changes
                setValue('location.unit_id', null);
                setValue('location.location_subtype', '');
                setValue('location.details', {});
              }}
              disabled={isLoadingLocation}
            >
              <option value="">Select Storage Area</option>
              {storageAreas.map((area) => (
                <option key={area.area_id} value={area.area_id}>
                  {area.name} ({area.location_type}, {area.level})
                </option>
              ))}
            </select>
          )}
        />
        {errors.location?.area_id && (
          <p className="text-red-500 text-sm mt-1">{errors.location.area_id.message}</p>
        )}
      </div>

      {/* Storage Unit Selection */}
      <div>
        <label className="block text-sm font-medium mb-1 text-gray-600">Storage Unit</label>
        <Controller
          name="location.unit_id"
          control={control}
          rules={{ required: 'Storage Unit is required' }}
          render={({ field }) => (
            <select
              {...field}
              value={field.value || ''}
              className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white disabled:bg-gray-100"
              onChange={(e) => {
                const value = e.target.value ? parseInt(e.target.value, 10) : '';
                field.onChange(value);
                // Reset subtype when unit changes
                setValue('location.location_subtype', '');
                setValue('location.details', {});
              }}
              disabled={!selectedAreaId || isLoadingLocation}
            >
              <option value="">Select Storage Unit</option>
              {filteredUnits.map((unit) => (
                <option key={unit.unit_id} value={unit.unit_id}>
                  {unit.identifier} ({unit.unit_type})
                </option>
              ))}
              {selectedAreaId && filteredUnits.length === 0 && (
                <option value="" disabled>No units found for this area</option>
              )}
            </select>
          )}
        />
        {errors.location?.unit_id && (
          <p className="text-red-500 text-sm mt-1">{errors.location.unit_id.message}</p>
        )}
      </div>

      {/* Location Subtype Selection */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1 text-gray-600">Location Type</label>
          <Controller
            name="location.location_subtype"
            control={control}
            rules={{ required: 'Location Type is required' }}
            render={({ field }) => (
              <select
                {...field}
                className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white disabled:bg-gray-100"
                onChange={(e) => {
                  const newSubtype = e.target.value as LocationSubType | '';
                  field.onChange(newSubtype);

                  // Initialize details object when subtype changes with appropriate defaults
                  if (newSubtype === LocationSubType.Container) {
                    setValue('location.details', { container_code: 'C-', level: '' });
                  } else if (newSubtype === LocationSubType.Crate) {
                    setValue('location.details', { crate_code: '', level: '' });
                  } else if (newSubtype === LocationSubType.OpenShelf || newSubtype === LocationSubType.ShelfSection) {
                    setValue('location.details', { level: '' });
                  } else if (newSubtype === LocationSubType.CageSection) {
                    setValue('location.details', { row: '', col: '' });
                  } else {
                    setValue('location.details', {});
                  }
                }}
                disabled={!selectedUnitId}
              >
                <option value="">Select Location Type</option>
                {Object.values(LocationSubType).map((subtype) => (
                  <option key={subtype} value={subtype}>
                    {subtype.replace(/_/g, ' ')}
                  </option>
                ))}
              </select>
            )}
          />
          {errors.location?.location_subtype && (
            <p className="text-red-500 text-sm mt-1">{errors.location.location_subtype.message}</p>
          )}
        </div>

        {/* Quantity Input */}
        <div>
          <label className="block text-sm font-medium mb-1 text-gray-600">Quantity</label>
          <Controller
            name="location.quantity"
            control={control}
            defaultValue={1}
            rules={{
              required: 'Quantity is required',
              min: { value: 1, message: 'Quantity must be at least 1' }
            }}
            shouldUnregister={false}
            render={({ field }) => (
              <NumberInput
                name="location.quantity"
                control={control}
                label="Quantity"
                rules={{ required: 'Quantity is required', min: { value: 1, message: 'Quantity must be at least 1' } }}
                className="w-full"
              />
            )}
          />
          {errors.location?.quantity && (
            <p className="text-red-500 text-sm mt-1">{errors.location.quantity.message}</p>
          )}
        </div>
      </div>

      {/* Dynamic Detail Fields based on Subtype */}
      {selectedSubtype && (
        <div className="bg-gray-100 p-3 rounded-md border border-gray-200 mt-3">
          <h5 className="text-sm font-medium mb-2 text-gray-700">
            Location Details ({selectedSubtype.replace(/_/g, ' ')})
          </h5>
          {renderLocationSubtypeFields()}
        </div>
      )}

      {/* Notes */}
      <div>
        <label className="block text-sm font-medium mb-1 text-gray-600">Location Notes (Optional)</label>
        <Controller
          name="location.notes"
          control={control}
          defaultValue=""
          shouldUnregister={false}
          render={({ field }) => (
            <textarea
              {...field}
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              placeholder="Any additional notes about this location..."
            />
          )}
        />
      </div>
    </div>
  );
};

export default LocationFormTab;
