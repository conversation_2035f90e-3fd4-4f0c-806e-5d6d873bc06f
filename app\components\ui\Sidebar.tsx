'use client';

import React, { useMemo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { menuItems, MenuItem } from '@/app/config/menu';
import { useUserCookie } from '@/app/hooks/useUserCookie';
import { Loader2 } from 'lucide-react';

export default function Sidebar() {
  const pathname = usePathname();
  const { roleName, isLoading } = useUserCookie();

  // Filter menu items based on user role
  const filteredMenuItems = useMemo(() => {
    if (!roleName) return [];

    return menuItems.filter(item => {
      // If no roles specified, show to everyone
      if (!item.roles) return true;

      // Check if user's role is in the allowed roles for this item
      return item.roles.includes(roleName);
    });
  }, [roleName]);

  // Handle loading state
  if (isLoading) {
    return (
      <div className="fixed left-0 top-0 h-screen w-64 bg-white border-r border-gray-200 hidden md:block flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  return (
    <div className="fixed left-0 top-0 h-screen w-64 bg-white border-r border-gray-200 hidden md:block">
      <div className="flex flex-col h-full">
        <div className="p-4 border-b border-gray-200">
          <h1 className="text-xl font-bold">AutoFlow</h1>
          {roleName && (
            <p className="text-sm text-gray-500 mt-1">{roleName}</p>
          )}
        </div>
        <nav className="flex-1 overflow-y-auto">
          <ul className="space-y-1 p-2">
            {filteredMenuItems.map((item) => (
              <li key={item.id}>
                <Link
                  href={item.href}
                  className={`flex items-center gap-3 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-100 ${
                    pathname === item.href ? 'bg-gray-100' : ''
                  }`}
                >
                  <item.icon className="h-5 w-5" />
                  <span>{item.label}</span>
                </Link>

                {/* Render children if any */}
                {item.children && item.children.length > 0 && (
                  <ul className="ml-6 mt-1 space-y-1">
                    {item.children
                      .filter(child => !child.roles || child.roles.includes(roleName))
                      .map(child => (
                        <li key={child.id}>
                          <Link
                            href={child.href}
                            className={`flex items-center gap-3 rounded-lg px-3 py-2 text-gray-700 transition-all hover:bg-gray-100 ${
                              pathname === child.href ? 'bg-gray-100' : ''
                            }`}
                          >
                            {child.icon && <child.icon className="h-4 w-4" />}
                            <span>{child.label}</span>
                          </Link>
                        </li>
                      ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </div>
  );
}