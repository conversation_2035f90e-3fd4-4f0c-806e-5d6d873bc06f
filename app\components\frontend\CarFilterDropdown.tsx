'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { ChevronDown, Car } from 'lucide-react';
import Cookies from 'js-cookie';

interface CarFilterDropdownProps {
  onFilterApply: (filter: CarFilter | null) => void;
  initialFilter?: CarFilter | null;
}

export interface CarFilter {
  brandId?: number;
  brandName?: string;
  modelId?: number;
  modelName?: string;
  generationId?: number;
  generationName?: string;
  variationId?: number;
  variationName?: string;
  trimId?: number;
  trimName?: string;
}

const CarFilterDropdown: React.FC<CarFilterDropdownProps> = ({ onFilterApply, initialFilter }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [brands, setBrands] = useState<{ brand_id: number; brand_name: string }[]>([]);
  const [models, setModels] = useState<{ id: number; model_name: string }[]>([]);
  const [generations, setGenerations] = useState<{ id: number; name: string; start_production_year: number; end_production_year: number }[]>([]);
  const [variations, setVariations] = useState<{ id: number; variation: string }[]>([]);
  const [trims, setTrims] = useState<{ id: number; trim: string }[]>([]);

  const [selectedBrand, setSelectedBrand] = useState<number | undefined>(initialFilter?.brandId);
  const [selectedModel, setSelectedModel] = useState<number | undefined>(initialFilter?.modelId);
  const [selectedGeneration, setSelectedGeneration] = useState<number | undefined>(initialFilter?.generationId);
  const [selectedVariation, setSelectedVariation] = useState<number | undefined>(initialFilter?.variationId);
  const [selectedTrim, setSelectedTrim] = useState<number | undefined>(initialFilter?.trimId);

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const supabase = createClient();

  // Fetch brands on component mount
  useEffect(() => {
    const fetchBrands = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const { data, error } = await supabase
          .from('car_brands')
          .select('*')
          .order('brand_name');

        if (error) throw error;

        setBrands(data || []);
      } catch (err) {
        console.error('Error fetching car brands:', err);
        setError('Failed to load car brands');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBrands();
  }, []);

  // Fetch models when brand is selected
  useEffect(() => {
    if (!selectedBrand) {
      setModels([]);
      return;
    }

    const fetchModels = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const { data, error } = await supabase
          .from('car_models')
          .select('*')
          .eq('brand_id', selectedBrand)
          .order('model_name');

        if (error) throw error;

        setModels(data || []);
      } catch (err) {
        console.error('Error fetching car models:', err);
        setError('Failed to load car models');
      } finally {
        setIsLoading(false);
      }
    };

    fetchModels();
  }, [selectedBrand]);

  // Fetch generations when model is selected
  useEffect(() => {
    if (!selectedModel) {
      setGenerations([]);
      return;
    }

    const fetchGenerations = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const { data, error } = await supabase
          .from('car_generation')
          .select('*')
          .eq('model_id', selectedModel)
          .order('start_production_year', { ascending: false });

        if (error) throw error;

        setGenerations(data || []);
      } catch (err) {
        console.error('Error fetching car generations:', err);
        setError('Failed to load car generations');
      } finally {
        setIsLoading(false);
      }
    };

    fetchGenerations();
  }, [selectedModel]);

  // Fetch variations when generation is selected
  useEffect(() => {
    if (!selectedGeneration) {
      setVariations([]);
      return;
    }

    const fetchVariations = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const { data, error } = await supabase
          .from('car_variation')
          .select('*')
          .eq('generation_id', selectedGeneration)
          .order('variation');

        if (error) throw error;

        setVariations(data || []);
      } catch (err) {
        console.error('Error fetching car variations:', err);
        setError('Failed to load car variations');
      } finally {
        setIsLoading(false);
      }
    };

    fetchVariations();
  }, [selectedGeneration]);

  // Fetch trims when variation is selected
  useEffect(() => {
    if (!selectedVariation) {
      setTrims([]);
      return;
    }

    const fetchTrims = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const { data, error } = await supabase
          .from('variation_trim')
          .select('*')
          .eq('variation_id', selectedVariation)
          .order('trim');

        if (error) throw error;

        setTrims(data || []);
      } catch (err) {
        console.error('Error fetching car trims:', err);
        setError('Failed to load car trims');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTrims();
  }, [selectedVariation]);

  // Handle brand selection
  const handleBrandChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const brandId = e.target.value ? parseInt(e.target.value) : undefined;
    setSelectedBrand(brandId);
    setSelectedModel(undefined);
    setSelectedGeneration(undefined);
    setSelectedVariation(undefined);
    setSelectedTrim(undefined);
  };

  // Handle model selection
  const handleModelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const modelId = e.target.value ? parseInt(e.target.value) : undefined;
    setSelectedModel(modelId);
    setSelectedGeneration(undefined);
    setSelectedVariation(undefined);
    setSelectedTrim(undefined);
  };

  // Handle generation selection
  const handleGenerationChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const generationId = e.target.value ? parseInt(e.target.value) : undefined;
    setSelectedGeneration(generationId);
    setSelectedVariation(undefined);
    setSelectedTrim(undefined);
  };

  // Handle variation selection
  const handleVariationChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const variationId = e.target.value ? parseInt(e.target.value) : undefined;
    setSelectedVariation(variationId);
    setSelectedTrim(undefined);
  };

  // Handle trim selection
  const handleTrimChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const trimId = e.target.value ? parseInt(e.target.value) : undefined;
    setSelectedTrim(trimId);
  };

  // Apply filter
  const applyFilter = () => {
    if (!selectedBrand) {
      onFilterApply(null);
      // Remove cookie with explicit path to ensure it's removed from all paths
      console.log('Removing carFilter cookie from applyFilter (no brand selected)');
      Cookies.remove('carFilter', { path: '/' });
      return;
    }

    const selectedBrandName = brands.find(b => b.brand_id === selectedBrand)?.brand_name;
    const selectedModelName = models.find(m => m.id === selectedModel)?.model_name;
    const selectedGenerationName = generations.find(g => g.id === selectedGeneration)?.name;
    const selectedVariationName = variations.find(v => v.id === selectedVariation)?.variation;
    const selectedTrimName = trims.find(t => t.id === selectedTrim)?.trim;

    const filter: CarFilter = {
      brandId: selectedBrand,
      brandName: selectedBrandName,
      modelId: selectedModel,
      modelName: selectedModelName,
      generationId: selectedGeneration,
      generationName: selectedGenerationName,
      variationId: selectedVariation,
      variationName: selectedVariationName,
      trimId: selectedTrim,
      trimName: selectedTrimName
    };

    // Save filter to cookie with proper encoding
    try {
      const filterString = JSON.stringify(filter);
      console.log('Setting carFilter cookie:', filterString);
      Cookies.set('carFilter', filterString, { expires: 30 }); // Expires in 30 days
    } catch (error) {
      console.error('Error setting carFilter cookie:', error);
    }

    onFilterApply(filter);
    setIsOpen(false);
  };

  // Clear filter
  const clearFilter = () => {
    setSelectedBrand(undefined);
    setSelectedModel(undefined);
    setSelectedGeneration(undefined);
    setSelectedVariation(undefined);
    setSelectedTrim(undefined);

    // Remove cookie with explicit path to ensure it's removed from all paths
    console.log('Removing carFilter cookie from CarFilterDropdown');
    Cookies.remove('carFilter', { path: '/' });

    // Verify cookie was removed
    setTimeout(() => {
      const cookieValue = Cookies.get('carFilter');
      console.log('After removal in CarFilterDropdown, carFilter cookie value:', cookieValue);
    }, 100);

    onFilterApply(null);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md bg-white text-gray-700"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Car size={18} />
        <span>Filter by Car</span>
        <ChevronDown size={18} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute z-50 mt-2 w-72 md:w-96 bg-white rounded-md shadow-lg border border-gray-200 p-4">
          <h3 className="font-medium text-lg mb-4">Filter Parts by Car</h3>

          {error && <p className="text-red-500 mb-2">{error}</p>}

          <div className="space-y-4">
            {/* Brand Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Brand</label>
              <select
                value={selectedBrand || ''}
                onChange={handleBrandChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                disabled={isLoading || brands.length === 0}
              >
                <option value="">Select Brand</option>
                {brands.map(brand => (
                  <option key={brand.brand_id} value={brand.brand_id}>
                    {brand.brand_name}
                  </option>
                ))}
              </select>
            </div>

            {/* Model Selection */}
            {selectedBrand && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Model</label>
                <select
                  value={selectedModel || ''}
                  onChange={handleModelChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  disabled={isLoading || models.length === 0}
                >
                  <option value="">Select Model</option>
                  {models.map(model => (
                    <option key={model.id} value={model.id}>
                      {model.model_name}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Generation Selection */}
            {selectedModel && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Generation</label>
                <select
                  value={selectedGeneration || ''}
                  onChange={handleGenerationChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  disabled={isLoading || generations.length === 0}
                >
                  <option value="">Select Generation</option>
                  {generations.map(generation => (
                    <option key={generation.id} value={generation.id}>
                      {generation.name} ({generation.start_production_year}-{generation.end_production_year || 'Present'})
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Variation Selection */}
            {selectedGeneration && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Body Style</label>
                <select
                  value={selectedVariation || ''}
                  onChange={handleVariationChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  disabled={isLoading || variations.length === 0}
                >
                  <option value="">Select Body Style</option>
                  {variations.map(variation => (
                    <option key={variation.id} value={variation.id}>
                      {variation.variation}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* Trim Selection */}
            {selectedVariation && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Trim</label>
                <select
                  value={selectedTrim || ''}
                  onChange={handleTrimChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                  disabled={isLoading || trims.length === 0}
                >
                  <option value="">Select Trim</option>
                  {trims.map(trim => (
                    <option key={trim.id} value={trim.id}>
                      {trim.trim}
                    </option>
                  ))}
                </select>
              </div>
            )}

            <div className="flex justify-between pt-2">
              <button
                onClick={clearFilter}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-100"
              >
                Clear
              </button>
              <button
                onClick={applyFilter}
                className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700"
                disabled={isLoading}
              >
                Apply Filter
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CarFilterDropdown;
