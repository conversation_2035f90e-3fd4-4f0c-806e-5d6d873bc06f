'use client';

import React from 'react';
import AnimatedEllipsisLoader from '../AnimatedEllipsisLoader';

interface LoadingLabelProps {
  text: string;
  bgColor?: string;
  textColor?: string;
  className?: string;
}

const LoadingLabel: React.FC<LoadingLabelProps> = ({
  text,
  bgColor = '#f3f4f6',
  textColor = '#4b5563',
  className = ''
}) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span className="text-sm font-medium" style={{ color: textColor }}>
        {text}
      </span>
      <AnimatedEllipsisLoader
        text=""
        bgColor={bgColor}
        textColor={textColor}
      />
    </div>
  );
};

export default LoadingLabel; 