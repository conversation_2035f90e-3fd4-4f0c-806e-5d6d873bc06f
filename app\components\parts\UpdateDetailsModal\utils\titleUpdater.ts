// titleUpdater.ts
import { createClient } from '@/app/libs/supabase/client';

interface CarDetails {
  brandId: string | number;
  modelId: string | number;
  generationId: string | number;
  variationId: string | number;
  trimId: string | number;
}

/**
 * Fetches car details from Supabase
 */
export const fetchCarDetails = async (carData: CarDetails) => {
  const supabase = createClient();

  // Fetch brand name
  const { data: brandData } = await supabase
    .from('car_brands')
    .select('brand_name')
    .eq('brand_id', carData.brandId)
    .single();

  // Fetch model name
  const { data: modelData } = await supabase
    .from('car_models')
    .select('model_name')
    .eq('id', carData.modelId)
    .single();

  // Fetch generation details
  const { data: generationData } = await supabase
    .from('car_generation')
    .select('name, start_production_year, end_production_year')
    .eq('id', carData.generationId)
    .single();

  // Fetch variation
  const { data: variationData } = await supabase
    .from('car_variation')
    .select('variation')
    .eq('id', carData.variationId)
    .single();

  // Fetch trim
  const { data: trimData } = await supabase
    .from('variation_trim')
    .select('trim')
    .eq('id', carData.trimId)
    .single();

  return {
    brand: brandData?.brand_name || '',
    model: modelData?.model_name || '',
    generation: generationData?.name || '',
    generationYears: generationData ?
      (generationData.end_production_year ?
        `(${generationData.start_production_year}-${generationData.end_production_year})` :
        `(${generationData.start_production_year})`) :
      '',
    variation: variationData?.variation || '',
    trim: trimData?.trim || ''
  };
};

/**
 * Extracts the non-car part of the title (the part description)
 */
export const extractPartDescription = (title: string, carDetails: any): string => {
  // If we don't have car details, use a generic approach
  if (!carDetails || !carDetails.brand || !carDetails.model) {
    return extractPartDescriptionGeneric(title);
  }

  try {
    // Escape special regex characters in car details
    const escapeRegex = (str: string) => str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    const brandEscaped = escapeRegex(carDetails.brand);
    const modelEscaped = escapeRegex(carDetails.model);
    const generationEscaped = carDetails.generation ? escapeRegex(carDetails.generation) : '';
    const variationEscaped = carDetails.variation ? escapeRegex(carDetails.variation) : '';
    const trimEscaped = carDetails.trim ? escapeRegex(carDetails.trim) : '';

    // Create a regex pattern to match car details in the title
    const carDetailsPattern = new RegExp(
      `^\\s*${brandEscaped}\\s+${modelEscaped}\\s+` +
      (generationEscaped ? `${generationEscaped}\\s+` : '') +
      `(?:\\(\\d{4}(?:-\\d{4})?\\))?\\s*` +
      (variationEscaped ? `${variationEscaped}\\s+` : '') +
      (trimEscaped ? `${trimEscaped}\\s+` : '')
    );

    // Remove car details from the title to get the part description
    const match = title.match(carDetailsPattern);
    if (match && match[0]) {
      return title.substring(match[0].length).trim();
    }

    // If no exact match, try a more generic approach
    return extractPartDescriptionGeneric(title);
  } catch (error) {
    console.error('Error in extractPartDescription:', error);
    return extractPartDescriptionGeneric(title);
  }
};

/**
 * Generic approach to extract part description when we don't have specific car details
 * or when the specific approach fails
 */
export const extractPartDescriptionGeneric = (title: string): string => {
  // This regex looks for common patterns in car titles
  // Format: Brand Model Generation (Years) Body-Type Trim
  const patterns = [
    // Specific pattern for the example case: "VW Touran Mk1 2003-2015 Van Base New Fog Covers(no fog light) Right"
    /^(VW|Volkswagen)\s+(Touran|Tiguan)\s+(Mk\d+(?:\s+\w+)?)\s+(?:\d{4}-\d{4}|\(\d{4}-\d{4}\))\s+(Van|SUV)\s+(Base)\s+(.+)$/i,

    // Pattern 1: Brand Model Generation (Years) Body-Type Trim
    /^([\w\s]+?)\s+([\w\s]+?)\s+(Mk\d+(?:\s+\w+)?)\s+(?:\(\d{4}(?:-\d{4})?\))?\s+([\w\s]+?)\s+([\w\s]+?)\s+(.+)$/i,

    // Pattern 2: Brand Model Generation Body-Type
    /^([\w\s]+?)\s+([\w\s]+?)\s+(Mk\d+(?:\s+\w+)?)\s+(?:\(\d{4}(?:-\d{4})?\))?\s+([\w\s]+?)\s+(.+)$/i,

    // Pattern 3: Brand Model Body-Type
    /^([\w\s]+?)\s+([\w\s]+?)\s+(?:\(\d{4}(?:-\d{4})?\))?\s+([\w\s]+?)\s+(.+)$/i,

    // Pattern 4: Brand Model
    /^([\w\s]+?)\s+([\w\s]+?)\s+(.+)$/i
  ];

  // Try each pattern
  for (const pattern of patterns) {
    const match = title.match(pattern);
    if (match) {
      // The last capture group should be the part description
      return match[match.length - 1].trim();
    }
  }

  // If all patterns fail, try a simpler approach - look for common body types
  const bodyTypes = ['Van', 'SUV', 'Sedan', 'Coupe', 'Hatchback', 'Wagon', 'Convertible', 'Truck'];
  for (const bodyType of bodyTypes) {
    const index = title.indexOf(bodyType);
    if (index > 0) {
      // Look for the next word after the body type
      const afterBodyType = title.substring(index + bodyType.length).trim();
      const firstWordMatch = afterBodyType.match(/^\s*(\w+)/);
      if (firstWordMatch) {
        // Skip the first word (likely "Base" or trim level) and return the rest
        const firstWordEndIndex = afterBodyType.indexOf(firstWordMatch[1]) + firstWordMatch[1].length;
        return afterBodyType.substring(firstWordEndIndex).trim();
      }
      return afterBodyType;
    }
  }

  // If all else fails, return the last third of the title as a fallback
  const words = title.split(' ');
  if (words.length > 3) {
    return words.slice(Math.floor(words.length * 2/3)).join(' ');
  }

  return title;
};

/**
 * Updates the title with new car details
 */
export const updateTitleWithCarDetails = async (
  currentTitle: string,
  oldCarData: CarDetails | null,
  newCarData: CarDetails
): Promise<string> => {
  // If all car fields are empty, return the original title
  if (!newCarData.brandId && !newCarData.modelId && !newCarData.generationId &&
      !newCarData.variationId && !newCarData.trimId) {
    return currentTitle;
  }

  try {
    // First check if this is a trim-only change
    let isTrimOnlyChange = false;
    if (oldCarData) {
      isTrimOnlyChange = 
        oldCarData.brandId == newCarData.brandId &&
        oldCarData.modelId == newCarData.modelId &&
        oldCarData.generationId == newCarData.generationId &&
        oldCarData.variationId == newCarData.variationId &&
        oldCarData.trimId != newCarData.trimId;
    }
    
    console.log('Is trim-only change:', isTrimOnlyChange);
    
    // Fetch new car details
    const newCarDetails = await fetchCarDetails(newCarData);
    
    // For specifically identifying the non-car part of the Audi Q5 fog covers title
    // This pattern is very specific to the structure we're seeing with Fog Covers
    const fogCoverPattern = /(?:New\s+)?Fog\s+Covers\s*\((?:no\s+|)fog\s+light\)\s*(?:Right|Left)(?:\s+Chrome)?/i;
    const fogMatch = currentTitle.match(fogCoverPattern);
    
    let partDescription = '';
    
    if (fogMatch) {
      // We found a fog cover specific pattern
      partDescription = fogMatch[0];
      console.log('Fog cover pattern matched:', partDescription);
    } else if (oldCarData &&
        oldCarData.brandId && oldCarData.modelId) {
      // If we have old car data, use it to extract the part description more accurately
      const oldCarDetails = await fetchCarDetails(oldCarData);
      
      // For trim changes, we need to extract more cautiously
      if (isTrimOnlyChange) {
        console.log('Processing trim change from', oldCarDetails.trim, 'to', newCarDetails.trim);
        
        // For trim changes, try to directly replace the old trim with nothing to extract the part
        if (oldCarDetails.trim) {
          const trimIndex = currentTitle.indexOf(oldCarDetails.trim);
          if (trimIndex > 0) {
            // Get everything after the trim
            const afterTrim = currentTitle.substring(trimIndex + oldCarDetails.trim.length).trim();
            if (afterTrim) {
              partDescription = afterTrim;
              console.log('Trim-change: extracted part description:', partDescription);
            }
          }
        }
        
        // If we couldn't extract via direct trim replacement, try pattern matching
        if (!partDescription) {
          // Try to remove common trims from the title
          let tempTitle = currentTitle;
          const commonTrims = ['Base', 'Sport', 'Premium', 'Luxury', 'S Line', 'SE', 'LE'];
          for (const trim of commonTrims) {
            // Replace trim and ensure it's a whole word
            const trimRegex = new RegExp(`(^|\\s)${trim}(\\s|$)`, 'g');
            tempTitle = tempTitle.replace(trimRegex, ' ').trim();
          }
          
          // If we successfully removed trim words, look for part-specific patterns
          const commonPartPatterns = [
            /(?:New\s+)?(?:Fog|Head|Tail|Corner)\s+(?:Light|Lamp|Cover)s?(?:\s+\w+)?/i,
            /(?:Front|Rear|Side)\s+(?:Bumper|Grille|Mirror|Door|Window|Panel)(?:\s+\w+)?/i,
            /(?:Engine|Transmission|Exhaust)\s+(?:Cover|Mount|Bracket|System)(?:\s+\w+)?/i
          ];
          
          for (const pattern of commonPartPatterns) {
            const match = tempTitle.match(pattern);
            if (match) {
              partDescription = match[0];
              console.log('Trim-change: found part pattern:', partDescription);
              break;
            }
          }
        }
      }
      
      // If not a trim change or we couldn't extract the description from the trim change
      if (!partDescription) {
        // Build a pattern to match the exact old car information in the title
        const oldCarPrefix = [
          oldCarDetails.brand,
          oldCarDetails.model,
          oldCarDetails.generation,
          oldCarDetails.generationYears,
          oldCarDetails.variation,
          oldCarDetails.trim
        ].filter(Boolean).join(' ');
        
        console.log('Old car prefix:', oldCarPrefix);
        
        // Remove the exact old car prefix from the title
        if (currentTitle.startsWith(oldCarPrefix)) {
          partDescription = currentTitle.substring(oldCarPrefix.length).trim();
          console.log('Part description extracted by exact matching:', partDescription);
        } else {
          // Fall back to generic extraction
          partDescription = extractPartDescription(currentTitle, oldCarDetails);
        }
      }
    } else {
      // Use the generic approach if we don't have old car data
      partDescription = extractPartDescriptionGeneric(currentTitle);
    }

    // Check if the part description still contains car details that should be removed
    // This helps catch cases where the extraction didn't fully remove the car details
    const carTerms = ['SUV Base', 'SUV Sport', 'SUV Premium', 'Van Base', 'Mk1', 'Mk2', 'Mk3', '(2008-2017)', '(2013-2017)'];
    
    // Add the old trim to the list of terms to remove if we have that info
    if (oldCarData && oldCarData.trimId) {
      const oldTrimDetails = await fetchCarDetails({...newCarData, trimId: oldCarData.trimId});
      if (oldTrimDetails.trim) {
        carTerms.push(oldTrimDetails.trim);
      }
    }
    
    for (const term of carTerms) {
      // Only remove if it's a standalone term (surrounded by spaces or at beginning/end)
      const termRegex = new RegExp(`(^|\\s)${term}(\\s|$)`, 'g');
      partDescription = partDescription.replace(termRegex, ' ').trim();
    }
    
    // Handle repeated words that might occur after removing car terms
    partDescription = partDescription.replace(/\b(\w+)(\s+\1)+\b/gi, '$1').trim();
    
    // If we still don't have a good part description, use a fallback
    if (!partDescription || partDescription.length < 5) {
      // Try to extract specific part indicators
      const commonPartPatterns = [
        /(?:New\s+)?(?:Fog|Head|Tail|Corner)\s+(?:Light|Lamp|Cover)s?(?:\s+\w+)?/i,
        /(?:Front|Rear|Side)\s+(?:Bumper|Grille|Mirror|Door|Window|Panel)(?:\s+\w+)?/i,
        /(?:Engine|Transmission|Exhaust)\s+(?:Cover|Mount|Bracket|System)(?:\s+\w+)?/i
      ];
      
      for (const pattern of commonPartPatterns) {
        const match = currentTitle.match(pattern);
        if (match) {
          partDescription = match[0];
          break;
        }
      }
      
      // Last resort fallback
      if (!partDescription || partDescription.length < 5) {
        const words = currentTitle.split(' ');
        if (words.length > 5) {
          partDescription = words.slice(-4).join(' ');
        } else {
          // Use the last two words as a minimum
          partDescription = words.slice(-2).join(' ');
        }
      }
    }

    // Construct the new title
    const carPart = [
      newCarDetails.brand,
      newCarDetails.model,
      newCarDetails.generation,
      newCarDetails.generationYears,
      newCarDetails.variation,
      newCarDetails.trim
    ].filter(Boolean).join(' ');

    // Log for debugging
    console.log('Title update details:', {
      originalTitle: currentTitle,
      extractedPartDescription: partDescription,
      newCarPart: carPart,
      newTitle: `${carPart} ${partDescription}`.trim()
    });

    return `${carPart} ${partDescription}`.trim();
  } catch (error) {
    console.error('Error updating title with car details:', error);
    return currentTitle; // Return original title if there's an error
  }
};
