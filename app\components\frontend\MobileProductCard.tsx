'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Phone, MessageCircle } from 'lucide-react';
import { generateProductSlug } from '@/app/utils/slugify';
import { getAdjustedPrice } from '@/app/utils/priceAdjustment';
import WhatsAppModal from './WhatsAppModal';
import DirectImage from '@/app/components/ui/DirectImage';

interface MobileProductCardProps {
  id: string;
  title: string;
  price: number;
  discountedPrice?: number;
  thumbnailUrl: string;
}

const MobileProductCard: React.FC<MobileProductCardProps> = ({
  id,
  title,
  price,
  discountedPrice,
  thumbnailUrl
}) => {
  const [isWhatsAppModalOpen, setIsWhatsAppModalOpen] = useState(false);

  // Generate product URL
  const productUrl = `/shop/product/${generateProductSlug(title, id)}`;

  // Calculate the display price (use discounted price if available)
  const displayPrice = discountedPrice
    ? getAdjustedPrice(discountedPrice)
    : getAdjustedPrice(price);

  // Handle WhatsApp button click
  const handleWhatsAppClick = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent navigation to product page
    e.stopPropagation();
    setIsWhatsAppModalOpen(true);
  };

  // Handle call button click
  const handleCallClick = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent navigation to product page
    e.stopPropagation();
    // Phone link will be handled by the browser
  };

  return (
    <>
      <div className="bg-white rounded-lg overflow-hidden border border-gray-200">
        {/* Product Image and Title - Clickable area */}
        <Link href={productUrl} className="block">
          {/* Product Image */}
          <div className="relative aspect-square">
            <DirectImage
              src={thumbnailUrl || '/images/placeholder.jpg'}
              alt={title}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 50vw, 33vw"
            />
          </div>

          {/* Product Title */}
          <div className="p-2 pb-0">
            <h3 className="text-sm font-medium text-gray-900 mb-1">
              {title}
            </h3>
          </div>
        </Link>

        {/* Price and Contact Buttons - Non-clickable area */}
        <div className="p-2 pt-0">
          <div className="flex justify-between items-center mt-1">
            {/* Price */}
            <div className="text-left">
              <span className="font-bold text-gray-900">
                Kshs {displayPrice.toLocaleString()}
              </span>
            </div>

            {/* Contact Buttons */}
            <div className="flex space-x-2">
              {/* Call Button */}
              <button
                onClick={(e) => {
                  e.preventDefault();
                  window.location.href = 'tel:+254724288400';
                }}
                className="p-1.5 bg-blue-500 text-white rounded-full hover:bg-blue-600"
                title="Call +254724288400"
              >
                <Phone size={14} />
              </button>

              {/* WhatsApp Button */}
              <button
                className="p-1.5 bg-green-500 text-white rounded-full hover:bg-green-600"
                title="Contact via WhatsApp"
                onClick={handleWhatsAppClick}
              >
                <MessageCircle size={14} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* WhatsApp Modal */}
      {isWhatsAppModalOpen && (
        <WhatsAppModal
          onClose={() => setIsWhatsAppModalOpen(false)}
          partTitle={title}
          partImage={thumbnailUrl}
          partId={id}
        />
      )}
    </>
  );
};

export default MobileProductCard;
