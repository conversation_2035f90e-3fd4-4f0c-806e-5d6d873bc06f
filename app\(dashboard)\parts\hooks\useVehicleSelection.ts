// hooks/useVehicleSelection.ts
import { useState } from 'react';
import { UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { PartFormValues } from '../types';

interface UseVehicleSelectionProps {
  watch: UseFormWatch<PartFormValues>;
  setValue: UseFormSetValue<PartFormValues>;
}

// Fetch functions
const fetchModels = async (brandId: string) => {
  const response = await fetch(`/api/car/models?brandId=${brandId}`);
  if (!response.ok) throw new Error('Failed to fetch models');
  return response.json();
};

const fetchGenerations = async (modelId: string) => {
  const response = await fetch(`/api/car/generations?modelId=${modelId}`);
  if (!response.ok) throw new Error('Failed to fetch generations');
  return response.json();
};

const fetchVariations = async (generationId: string) => {
  const response = await fetch(`/api/car/variations?generationId=${generationId}`);
  if (!response.ok) throw new Error('Failed to fetch variations');
  return response.json();
};

const fetchTrims = async (variationId: string) => {
  const response = await fetch(`/api/car/trims?variationId=${variationId}`);
  if (!response.ok) throw new Error('Failed to fetch trims');
  return response.json();
};

export const useVehicleSelection = ({ watch, setValue }: UseVehicleSelectionProps) => {
  const [loadingStates, setLoadingStates] = useState({
    brands: false,
    models: false,
    generations: false,
    variations: false,
    trims: false
  });

  const [vehicleSelectionError, setVehicleSelectionError] = useState<string | null>(null);

  const validateVehicleSelection = () => {
    const brandId = watch('brandId');
    const modelId = watch('modelId');
    const generationId = watch('generationId');
    const variationId = watch('variationId');
    const trimId = watch('trimId');

    // Only validate if any field has been filled
    if (brandId || modelId || generationId || variationId || trimId) {
      const isValid = !!(brandId && modelId && generationId && variationId && trimId);
      setVehicleSelectionError(isValid ? null : 'Please complete all vehicle selection fields');
      return isValid;
    }
    return true; // If no fields are filled, consider it valid
  };

  const handleBrandModelSelectionChange = async (field: string, value: string, name?: string, years?: string) => {
    console.log('Handling vehicle selection change:', { field, value, name, years });
    setLoadingStates(prev => ({ ...prev, [field]: true }));

    try {
      // Set the ID value with setValue
      console.log(`Setting ${field} to:`, value);
      setValue(field as keyof PartFormValues, value, { shouldValidate: true });

      // Set the name value if provided using setValue
      if (name) {
        const nameField = `${field}Name` as keyof PartFormValues;
        console.log(`Setting ${nameField} to:`, name);
        try {
          setValue(nameField, name, { shouldValidate: false });
        } catch (err) {
          console.error(`Error setting ${nameField}:`, err);
        }
      }

      // Set the years value if provided using setValue
      if (years) {
        const yearsField = `${field}Years` as keyof PartFormValues;
        console.log(`Setting ${yearsField} to:`, years);
        try {
          setValue(yearsField, years, { shouldValidate: false });
        } catch (err) {
          console.error(`Error setting ${yearsField}:`, err);
        }
      }

      // Reset dependent fields
      if (field === 'brandId') {
        setValue('modelId', '');
        setValue('modelName', '');
        setValue('generationId', '');
        setValue('generationName', '');
        setValue('generationYears', '');
        setValue('variationId', '');
        setValue('variationName', '');
        setValue('trimId', '');
        setValue('trimName', '');
      } else if (field === 'modelId') {
        setValue('generationId', '');
        setValue('generationName', '');
        setValue('generationYears', '');
        setValue('variationId', '');
        setValue('variationName', '');
        setValue('trimId', '');
        setValue('trimName', '');
      } else if (field === 'generationId') {
        setValue('variationId', '');
        setValue('variationName', '');
        setValue('trimId', '');
        setValue('trimName', '');
      } else if (field === 'variationId') {
        setValue('trimId', '');
        setValue('trimName', '');
      }

      // Fetch dependent data
      if (field === 'brandId' && value) {
        const models = await fetchModels(value);
        console.log('Fetched models:', models);
      } else if (field === 'modelId' && value) {
        const generations = await fetchGenerations(value);
        console.log('Fetched generations:', generations);
      } else if (field === 'generationId' && value) {
        const variations = await fetchVariations(value);
        console.log('Fetched variations:', variations);
      } else if (field === 'variationId' && value) {
        const trims = await fetchTrims(value);
        console.log('Fetched trims:', trims);
      }
    } catch (error) {
      console.error(`Error handling ${field} selection:`, error);
    } finally {
      setLoadingStates(prev => ({ ...prev, [field]: false }));
    }
  };

  return {
    validateVehicleSelection,
    vehicleSelectionError,
    loadingStates,
    handleBrandModelSelectionChange
  };
};