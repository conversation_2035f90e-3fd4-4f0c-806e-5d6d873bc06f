'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useRBAC } from '@/app/providers/RBACProvider';

interface PermissionResult {
  [key: string]: boolean;
}

interface PermissionGroup {
  name: string;
  permissions: string[];
}

interface UseMultiplePermissionsResult {
  permissions: PermissionResult;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  hasAllPermissions: (permissions: string[]) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasPermissionGroup: (groupName: string) => boolean;
}

// Predefined permission groups
export const PERMISSION_GROUPS: Record<string, PermissionGroup> = {
  parts: {
    name: 'Parts Management',
    permissions: ['parts:view', 'parts:create', 'parts:edit', 'parts:delete'],
  },
  users: {
    name: 'User Management',
    permissions: ['users:view', 'users:create', 'users:edit', 'users:delete'],
  },
  orders: {
    name: 'Order Management',
    permissions: ['orders:view', 'orders:create', 'orders:edit', 'orders:delete'],
  },
  admin: {
    name: 'Admin Access',
    permissions: ['admin:access', 'admin:settings', 'admin:logs'],
  },
};

export function useMultiplePermissions(
  permissionNames: string[],
  options: {
    groups?: string[];
    cacheTime?: number;
    refetchInterval?: number;
  } = {}
): UseMultiplePermissionsResult {
  const { checkPermission, isLoading: rbacLoading } = useRBAC();
  const [permissions, setPermissions] = useState<PermissionResult>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [lastFetch, setLastFetch] = useState<number>(0);

  // Combine individual permissions with group permissions
  const allPermissions = useMemo(() => {
    const groupPermissions = (options.groups || [])
      .flatMap(groupName => PERMISSION_GROUPS[groupName]?.permissions || []);
    
    return Array.from(new Set([...permissionNames, ...groupPermissions]));
  }, [permissionNames, options.groups]);

  const fetchPermissions = useCallback(async () => {
    const now = Date.now();
    const cacheTime = options.cacheTime || 5 * 60 * 1000; // 5 minutes default

    // Check if we need to refetch based on cache time
    if (now - lastFetch < cacheTime && Object.keys(permissions).length > 0) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const results = await Promise.all(
        allPermissions.map(async (permission) => {
          const hasPermission = await checkPermission(permission);
          return [permission, hasPermission];
        })
      );

      setPermissions(Object.fromEntries(results));
      setLastFetch(now);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to check permissions'));
    } finally {
      setIsLoading(false);
    }
  }, [allPermissions, checkPermission, lastFetch, options.cacheTime, permissions]);

  // Set up refetch interval if specified
  useEffect(() => {
    if (options.refetchInterval) {
      const interval = setInterval(fetchPermissions, options.refetchInterval);
      return () => clearInterval(interval);
    }
  }, [fetchPermissions, options.refetchInterval]);

  // Initial fetch
  useEffect(() => {
    fetchPermissions();
  }, [fetchPermissions]);

  // Helper functions
  const hasAllPermissions = useCallback(
    (permissionsToCheck: string[]) => {
      return permissionsToCheck.every(permission => permissions[permission]);
    },
    [permissions]
  );

  const hasAnyPermission = useCallback(
    (permissionsToCheck: string[]) => {
      return permissionsToCheck.some(permission => permissions[permission]);
    },
    [permissions]
  );

  const hasPermissionGroup = useCallback(
    (groupName: string) => {
      const group = PERMISSION_GROUPS[groupName];
      if (!group) return false;
      return hasAllPermissions(group.permissions);
    },
    [hasAllPermissions]
  );

  return {
    permissions,
    isLoading: isLoading || rbacLoading,
    error,
    refetch: fetchPermissions,
    hasAllPermissions,
    hasAnyPermission,
    hasPermissionGroup,
  };
} 