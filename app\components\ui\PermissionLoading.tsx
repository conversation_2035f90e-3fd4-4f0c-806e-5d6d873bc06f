'use client';

import React from 'react';
import Spinner from './Spinner';

interface PermissionLoadingProps {
  message?: string;
}

export function PermissionLoading({ message = 'Checking permissions...' }: PermissionLoadingProps) {
  return (
    <div className="flex min-h-[100px] flex-col items-center justify-center rounded-lg border border-gray-200 bg-gray-50 p-6 text-center">
      <Spinner size="md" />
      <p className="text-sm text-gray-600">{message}</p>
    </div>
  );
} 