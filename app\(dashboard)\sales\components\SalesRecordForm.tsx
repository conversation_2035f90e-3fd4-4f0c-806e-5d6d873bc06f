'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { createClient } from '@/app/libs/supabase/client';
import { toast } from 'react-hot-toast';

interface SalesRecordFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

interface FormValues {
  partId: string;
  quantity: number;
  salePrice: number;
  customerId?: string;
  saleDate: string;
  paymentMethod: string;
  notes?: string;
}

export default function SalesRecordForm({ onSuccess, onCancel }: SalesRecordFormProps) {
  const [parts, setParts] = useState<any[]>([]);
  const [customers, setCustomers] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const supabase = createClient();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<FormValues>({
    defaultValues: {
      quantity: 1,
      saleDate: new Date().toISOString().split('T')[0],
      paymentMethod: 'cash',
    },
  });

  // Watch the selected part to calculate total price
  const selectedPartId = watch('partId');
  const quantity = watch('quantity');
  const salePrice = watch('salePrice');

  // Fetch parts and customers on component mount
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch parts with stock > 0
        const { data: partsData, error: partsError } = await supabase
          .from('parts')
          .select(`
            id, 
            title, 
            parts_condition (
              id,
              condition,
              stock
            )
          `)
          .eq('parts_condition.stock', '>', 0);

        if (partsError) throw partsError;
        setParts(partsData || []);

        // Fetch customers
        const { data: customersData, error: customersError } = await supabase
          .from('profiles')
          .select('id, full_name, email');

        if (customersError) throw customersError;
        setCustomers(customersData || []);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to load data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [supabase]);

  // Update sale price when part changes
  useEffect(() => {
    if (selectedPartId) {
      const selectedPart = parts.find(part => part.id === selectedPartId);
      if (selectedPart) {
        // Set default sale price based on part price (you might need to adjust this logic)
        setValue('salePrice', 0); // Set a default or fetch the price from somewhere
      }
    }
  }, [selectedPartId, parts, setValue]);

  const onSubmit = async (data: FormValues) => {
    setIsLoading(true);
    try {
      // 1. Create a sales record
      const { data: salesRecord, error: salesError } = await supabase
        .from('sales')
        .insert({
          part_id: data.partId,
          quantity: data.quantity,
          sale_price: data.salePrice,
          customer_id: data.customerId || null,
          sale_date: data.saleDate,
          payment_method: data.paymentMethod,
          notes: data.notes || null,
          total_amount: data.quantity * data.salePrice,
        })
        .select()
        .single();

      if (salesError) throw salesError;

      // 2. Update the part stock
      const selectedPart = parts.find(part => part.id === data.partId);
      if (selectedPart && selectedPart.parts_condition && selectedPart.parts_condition.length > 0) {
        const conditionId = selectedPart.parts_condition[0].id;
        const currentStock = selectedPart.parts_condition[0].stock;
        
        if (currentStock < data.quantity) {
          throw new Error('Not enough stock available');
        }

        const { error: stockError } = await supabase
          .from('parts_condition')
          .update({ stock: currentStock - data.quantity })
          .eq('id', conditionId);

        if (stockError) throw stockError;
      }

      toast.success('Sale recorded successfully');
      if (onSuccess) onSuccess();
    } catch (error: any) {
      console.error('Error recording sale:', error);
      toast.error(error.message || 'Failed to record sale');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-6 text-gray-800 dark:text-gray-100">Record New Sale</h2>
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {/* Part Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Part
          </label>
          <select
            {...register('partId', { required: 'Part is required' })}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            disabled={isLoading}
          >
            <option value="">Select a part</option>
            {parts.map(part => (
              <option key={part.id} value={part.id}>
                {part.title} - Stock: {part.parts_condition?.[0]?.stock || 0}
              </option>
            ))}
          </select>
          {errors.partId && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.partId.message}</p>
          )}
        </div>

        {/* Quantity */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Quantity
          </label>
          <input
            type="number"
            {...register('quantity', { 
              required: 'Quantity is required',
              min: { value: 1, message: 'Quantity must be at least 1' },
              valueAsNumber: true
            })}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            disabled={isLoading}
          />
          {errors.quantity && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.quantity.message}</p>
          )}
        </div>

        {/* Sale Price */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Sale Price (per unit)
          </label>
          <input
            type="number"
            step="0.01"
            {...register('salePrice', { 
              required: 'Sale price is required',
              min: { value: 0.01, message: 'Sale price must be greater than 0' },
              valueAsNumber: true
            })}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            disabled={isLoading}
          />
          {errors.salePrice && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.salePrice.message}</p>
          )}
        </div>

        {/* Total Amount (calculated) */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Total Amount
          </label>
          <div className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100">
            ${((quantity || 0) * (salePrice || 0)).toFixed(2)}
          </div>
        </div>

        {/* Customer (Optional) */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Customer (Optional)
          </label>
          <select
            {...register('customerId')}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            disabled={isLoading}
          >
            <option value="">Select a customer</option>
            {customers.map(customer => (
              <option key={customer.id} value={customer.id}>
                {customer.full_name} ({customer.email})
              </option>
            ))}
          </select>
        </div>

        {/* Sale Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Sale Date
          </label>
          <input
            type="date"
            {...register('saleDate', { required: 'Sale date is required' })}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            disabled={isLoading}
          />
          {errors.saleDate && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.saleDate.message}</p>
          )}
        </div>

        {/* Payment Method */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Payment Method
          </label>
          <select
            {...register('paymentMethod', { required: 'Payment method is required' })}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            disabled={isLoading}
          >
            <option value="cash">Cash</option>
            <option value="credit_card">Credit Card</option>
            <option value="debit_card">Debit Card</option>
            <option value="bank_transfer">Bank Transfer</option>
            <option value="mobile_payment">Mobile Payment</option>
            <option value="other">Other</option>
          </select>
          {errors.paymentMethod && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.paymentMethod.message}</p>
          )}
        </div>

        {/* Notes (Optional) */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Notes (Optional)
          </label>
          <textarea
            {...register('notes')}
            rows={3}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            disabled={isLoading}
          ></textarea>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2"
            disabled={isLoading}
          >
            {isLoading ? 'Recording...' : 'Record Sale'}
          </button>
        </div>
      </form>
    </div>
  );
}
