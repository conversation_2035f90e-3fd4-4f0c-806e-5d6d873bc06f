export const dynamic = 'force-dynamic';

import { Suspense } from 'react';
import Spinner from '@/app/components/ui/Spinner';
import PermissionsContent from './components/PermissionsContent';

export default function PermissionsPage() {
    return (
        <Suspense fallback={<div className="flex min-h-[300px] items-center justify-center">
            <Spinner size="lg" />
        </div>}>
            <PermissionsContent />
        </Suspense>
    );
}
