import { FlatCategory } from '../types';
import { NestedSelectItem } from '@/app/components/ui/inputs/NestedSelect';

export const buildCategoryTree = (categories: FlatCategory[] = [], parentId: string | number | null = null): NestedSelectItem[] => {
  if (!Array.isArray(categories)) {
    return []; // Return an empty array if 'categories' is not an array
  }
  
  const filtered = categories.filter(category => {
    const categoryParentId = category.parentId?.toString() || null; // Convert parentId to string
    const compareParentId = parentId?.toString() || null; // Convert parentId to string
    const matches = categoryParentId === compareParentId; // Compare as strings
    
    // For root level (parentId is null), also include categories with undefined parentId
    const isRootLevel = parentId === null && (categoryParentId === null || categoryParentId === undefined);
    
    return matches || isRootLevel;
  });
  
  return filtered.map(category => {
    const item: NestedSelectItem = {
      label: category.name || 'Unnamed Category',  // Provide a fallback
      value: category.id.toString(),  // Convert to string for consistency in NestedSelect
      children: buildCategoryTree(categories, category.id) // Pass the original number ID
    };
    
    return item;
  });
};