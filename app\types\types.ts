// types.ts
import { User as SupabaseUser, Session as SupabaseSession } from '@supabase/supabase-js';

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export interface BaseDatabase {
  public: {
    Tables: {
      car_part_categories: {  // Should now appear here
        Row: {
          id: number;
          label: string;
          href: string;
          parent_category_id: number | null;
          icon: string | null;
          library: string | null;
          // ... other columns
        };
        // ... rest of the table definition
      };
      profiles: {
        Row: {
          full_name: string | null;
          id: string;
          updated_at: string | null;
          username: string | null;
          website: string | null;
          idle_timeout: boolean;
          logout_only: boolean;
          phone: string | null;
        };
        Insert: {
          full_name?: string | null;
          id: string;
          updated_at?: string | null;
          username?: string | null;
          website?: string | null;
          idle_timeout?: boolean;
          logout_only?: boolean;
        };
        Update: {
          full_name?: string | null;
          id?: string;
          updated_at?: string | null;
          username?: string | null;
          website?: string | null;
          idle_timeout?: boolean;
          logout_only?: boolean;
        };
        Relationships: [
          {
            foreignKeyName: "profiles_id_fkey";
            columns: ["id"];
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    part_images: {
      Row: {
        created_at: string | null
        image_id: string
        image_url: string | null
        part_id: string | null
      }
      Insert: {
        created_at?: string | null
        image_id?: string
        image_url?: string | null
        part_id?: string | null
      }
      Update: {
        created_at?: string | null
        image_id?: string
        image_url?: string | null
        part_id?: string | null
      }
      Relationships: [
        {
          foreignKeyName: "part_images_part_id_fkey"
          columns: ["part_id"]
          isOne: false
          referencedRelation: "parts"
          referencedColumns: ["part_id"]
        }
      ]
    }
    parts: {
      Row: {
        created_at: string | null
        part_id: string
        part_name: string | null
        part_number: string | null
        price: number | null
        stock: number | null
      }
      Insert: {
        created_at?: string | null
        part_id?: string
        part_name?: string | null
        part_number?: string | null
        price?: number | null
        stock?: number | null
      }
      Update: {
        created_at?: string | null
        part_id?: string
        part_name?: string | null
        part_number?: string | null
        price?: number | null
        stock?: number | null
      }
      Relationships: []
    }
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}

export type User = SupabaseUser;
export type Session = SupabaseSession;