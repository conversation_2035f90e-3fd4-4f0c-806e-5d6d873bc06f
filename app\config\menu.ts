import { LucideIcon } from 'lucide-react';
import {
  LayoutDashboard,
  Package,
  FolderTree,
  Settings,
  User,
  LogOut,
  List,
  Plus,
  Shield,
  Users,
  FileText,
  Lock,
  DollarSign,
  Building,
  Car,
  Warehouse,
  Share2
} from 'lucide-react';

export interface MenuItem {
  id: string;
  label: string;
  icon: LucideIcon;
  href: string;
  roles?: string[]; // Array of roles that can access this item
  children?: MenuItem[];
}

export const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: LayoutDashboard,
    href: '/dashboard',
    roles: ['Employee', 'Admin', 'Super Admin', 'Supplier'] // All roles can access dashboard
  },
  {
    id: 'parts',
    label: 'Parts',
    icon: Package,
    href: '/parts',
    roles: ['Employee', 'Admin', 'Super Admin'] // Employees, Admins, and Super Admins can access parts
  },
  {
    id: 'categories',
    label: 'Categories',
    icon: FolderTree,
    href: '/categories',
    roles: ['Admin', 'Super Admin'], // Only Admins and Super Admins can access categories
    children: [
      {
        id: 'all-categories',
        label: 'All Categories',
        icon: List,
        href: '/categories',
        roles: ['Admin', 'Super Admin']
      },
      {
        id: 'add-category',
        label: 'Add Category',
        icon: Plus,
        href: '/categories/add',
        roles: ['Admin', 'Super Admin']
      }
    ]
  },
  {
    id: 'cars',
    label: 'Cars',
    icon: Car,
    href: '/cars',
    roles: ['Admin', 'Super Admin'], // Only Admins and Super Admins can access cars
    children: [
      {
        id: 'cars-dashboard',
        label: 'Dashboard',
        icon: LayoutDashboard,
        href: '/cars',
        roles: ['Admin', 'Super Admin']
      },
      {
        id: 'cars-brands',
        label: 'Brands',
        icon: List,
        href: '/cars?tab=brands',
        roles: ['Admin', 'Super Admin']
      },
      {
        id: 'cars-models',
        label: 'Models',
        icon: List,
        href: '/cars?tab=models',
        roles: ['Admin', 'Super Admin']
      }
    ]
  },
  {
    id: 'sales',
    label: 'Sales',
    icon: DollarSign,
    href: '/sales',
    roles: ['Employee', 'Admin', 'Super Admin']
  },
  {
    id: 'clients',
    label: 'Clients',
    icon: Building,
    href: '/clients',
    roles: ['Employee', 'Admin', 'Super Admin'],
    children: [
      {
        id: 'clients-dashboard',
        label: 'Dashboard',
        icon: LayoutDashboard,
        href: '/clients',
        roles: ['Employee', 'Admin', 'Super Admin']
      },
      {
        id: 'clients-list',
        label: 'Client List',
        icon: List,
        href: '/clients/list',
        roles: ['Employee', 'Admin', 'Super Admin']
      },
      {
        id: 'clients-add',
        label: 'Add Client',
        icon: Plus,
        href: '/clients/add',
        roles: ['Employee', 'Admin', 'Super Admin']
      }
    ]
  },
  {
    id: 'storage',
    label: 'Storage',
    icon: Warehouse,
    href: '/storage',
    roles: ['Employee', 'Admin', 'Super Admin'],
  },
  {
    id: 'integrations',
    label: 'Integrations',
    icon: Share2,
    href: '/integrations',
    roles: ['Admin', 'Super Admin'],
    children: [
      {
        id: 'integrations-dashboard',
        label: 'Dashboard',
        icon: LayoutDashboard,
        href: '/integrations',
        roles: ['Admin', 'Super Admin']
      },
      {
        id: 'google-merchant',
        label: 'Google Merchant',
        icon: Package,
        href: '/integrations/google-merchant',
        roles: ['Admin', 'Super Admin']
      },
      {
        id: 'google-sheets',
        label: 'Google Sheets',
        icon: FileText,
        href: '/integrations/google-sheets',
        roles: ['Admin', 'Super Admin']
      }
    ]
  },
  {
    id: 'admin',
    label: 'Admin',
    icon: Shield,
    href: '/admin',
    roles: ['Super Admin']
  },
  {
    id: 'supplier',
    label: 'Supplier Portal',
    icon: Package,
    href: '/supplier',
    roles: ['Supplier'] // Only Suppliers can access supplier portal
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: Settings,
    href: '/settings',
    roles: ['Employee', 'Admin', 'Super Admin', 'Supplier'] // All roles can access settings
  },
  {
    id: 'profile',
    label: 'Profile',
    icon: User,
    href: '/profile',
    roles: ['Employee', 'Admin', 'Super Admin', 'Supplier'] // All roles can access profile
  },
  {
    id: 'logout',
    label: 'Logout',
    icon: LogOut,
    href: '/logout',
    roles: ['Employee', 'Admin', 'Super Admin', 'Supplier'] // All roles can access logout
  }
];