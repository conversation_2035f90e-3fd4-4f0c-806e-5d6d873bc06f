'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/app/libs/supabase/client';
import { Check, X } from 'lucide-react';
import Input from '@/app/components/ui/inputs/Input';
import Button from '@/app/components/ui/inputs/Button';
import Password from '@/app/components/ui/inputs/Password';
import PhoneInput from '@/app/components/ui/inputs/PhoneInput';
import toast, { Toaster } from 'react-hot-toast';

// Add server action import for profile updates
import { completeUserRegistration } from '@/app/profile/actions';

export default function InvitationRegistrationForm() {
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [email, setEmail] = useState('');
  const [userId, setUserId] = useState<string | null>(null); // Store user ID
  const [accessToken, setAccessToken] = useState<string | null>(null); // Keep track of token if needed for session re-establishment

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    phoneNumber: '',
    password: '',
    confirmPassword: '',
  });

  // Password strength indicators
  const [passwordStrength, setPasswordStrength] = useState({
    hasMinLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false,
    hasSpecialChar: false,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  // State for client data
  const [clientId, setClientId] = useState<string | null>(null);
  const [isClientInvitation, setIsClientInvitation] = useState(false);

  // Check for invitation parameters from Supabase Auth
  useEffect(() => {
    const validateInvitation = async () => {
      setIsLoading(true);
      setError(''); // Reset error on validation start
      const supabase = createClient();

      try {
        // Check URL query parameters for client invitation
        const urlParams = new URLSearchParams(window.location.search);
        const isInvitation = urlParams.get('invitation') === 'true';
        const clientIdParam = urlParams.get('clientId');

        if (clientIdParam) {
          setClientId(clientIdParam);
          setIsClientInvitation(true);
        }

        // First check if we have a valid session already
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
            console.error("Error getting session:", sessionError);
        }

        if (session?.user) {
          // We already have a session, use that user's info
          console.log('Existing session found for user:', session.user.id);
          setEmail(session.user.email || '');
          setUserId(session.user.id); // Store user ID from session

          // If this is a client invitation, check for client data
          if (isClientInvitation && clientIdParam && session.user.user_metadata) {
            console.log('Client invitation detected with client ID:', clientIdParam);

            // Check if the client ID in the URL matches the one in user metadata
            const metadataClientId = session.user.user_metadata.client_id;
            if (metadataClientId && metadataClientId !== clientIdParam) {
              console.warn('Client ID mismatch between URL and user metadata');
            }
          }

          setIsLoading(false);
          return;
        }

        // If no session, check URL hash parameters
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const token = hashParams.get('access_token');
        const type = hashParams.get('type');
        const refreshToken = hashParams.get('refresh_token'); // Get refresh token if present

        // Only proceed if type is 'invite' or 'recovery' and a token exists
        if ((type === 'invite' || type === 'recovery') && token) {
          console.log('Found invitation/recovery token in URL hash.');
          setAccessToken(token); // Store the token temporarily

          // Attempt to set the session using the tokens from the URL
          const { error: setSessionError } = await supabase.auth.setSession({
            access_token: token,
            refresh_token: refreshToken || '', // Provide refresh token if available
          });

          if (setSessionError) {
            console.error('Error setting session from URL token:', setSessionError);
            const { data: { user }, error: userError } = await supabase.auth.getUser(token);
            if (userError || !user) {
                console.error('Failed to get user with token after setSession failed:', userError);
                throw new Error(userError?.message || 'Invalid or expired invitation link.');
            }
            console.log('Got user via token after setSession error:', user.id);
            setEmail(user.email || '');
            setUserId(user.id);

            // Check for client metadata
            if (isClientInvitation && user.user_metadata && user.user_metadata.client_id) {
              const metadataClientId = user.user_metadata.client_id;
              if (!clientIdParam) {
                setClientId(metadataClientId);
              } else if (metadataClientId !== clientIdParam) {
                console.warn('Client ID mismatch between URL and user metadata');
              }
            }

            setIsLoading(false);
            return;
          }

          // Session should be set, now get the user details from the established session
          const { data: { user }, error: getUserError } = await supabase.auth.getUser();

          if (getUserError || !user) {
            console.error('Error getting user after setting session:', getUserError);
            throw new Error(getUserError?.message || 'Could not retrieve user details. The invitation might be invalid or expired.');
          }

          // Successfully got user details
          console.log('Successfully set session and got user:', user.id);
          setEmail(user.email || '');
          setUserId(user.id); // Store user ID

          // Check for client metadata
          if (isClientInvitation && user.user_metadata && user.user_metadata.client_id) {
            const metadataClientId = user.user_metadata.client_id;
            if (!clientIdParam) {
              setClientId(metadataClientId);
            } else if (metadataClientId !== clientIdParam) {
              console.warn('Client ID mismatch between URL and user metadata');
            }
          }

          setIsLoading(false);

        } else {
          // No session and no valid token/type in URL
          console.log('No session and no valid invitation parameters in URL.');
          throw new Error('Invalid or missing invitation link. Please check your email for a valid invitation link.');
        }

      } catch (err: any) {
        console.error('Invitation validation error:', err);
        setError(err.message || 'Failed to validate invitation. The link may be invalid or expired.');
        setIsLoading(false);
      }
    };

    validateInvitation();
  }, []); // Run only once on mount

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Check password strength when password field changes
    if (name === 'password') {
      setPasswordStrength({
        hasMinLength: value.length >= 8,
        hasUppercase: /[A-Z]/.test(value),
        hasLowercase: /[a-z]/.test(value),
        hasNumber: /[0-9]/.test(value),
        hasSpecialChar: /[^A-Za-z0-9]/.test(value),
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(''); // Clear previous errors

    // --- Form Validation ---
    if (!formData.firstName.trim() || !formData.lastName.trim()) {
      toast.error('First and last name are required');
      return;
    }
    if (!formData.phoneNumber.trim()) { // Add basic phone validation if needed
      toast.error('Phone number is required');
      return;
    }
    if (!formData.password) {
      toast.error('Password is required');
      return;
    }
    const isStrongPassword = Object.values(passwordStrength).every(Boolean);
    if (!isStrongPassword) {
      toast.error('Please create a stronger password that meets all requirements');
      return;
    }
    if (formData.password !== formData.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }
    // --- End Validation ---

    setIsSubmitting(true);
    const supabase = createClient();

    try {
        // --- Ensure Authentication ---
        if (!userId) {
            console.error('User ID is missing before submission.');
            const { data: { session } } = await supabase.auth.getSession();
            if (session?.user?.id) {
                setUserId(session.user.id);
                console.log('Recovered userId from session before update:', session.user.id);
            } else {
                 if (accessToken) {
                     console.log('Attempting to re-set session with stored token before update...');
                     const { error: reSetSessionError } = await supabase.auth.setSession({ access_token: accessToken, refresh_token: '' });
                     if (!reSetSessionError) {
                         const { data: { user: recoveredUser } } = await supabase.auth.getUser();
                         if (recoveredUser) {
                             setUserId(recoveredUser.id);
                             console.log('Recovered userId via token before update:', recoveredUser.id);
                         } else {
                            throw new Error('Session expired or invalid. Please use the invitation link again.');
                         }
                     } else {
                        throw new Error('Session expired or invalid. Please use the invitation link again.');
                     }
                 } else {
                    throw new Error('Session expired or invalid. Please use the invitation link again.');
                 }
            }
            if (!userId && !session?.user?.id) {
                throw new Error('Session expired or invalid. Please use the invitation link again.');
            }
        }
        const currentUserId = userId || (await supabase.auth.getSession()).data.session?.user?.id;
         if (!currentUserId) {
            throw new Error('Could not confirm user identity. Please use the invitation link again.');
         }
        // --- End Authentication Check ---

      // Update both password and user metadata in Supabase Auth
      const fullName = `${formData.firstName} ${formData.lastName}`;

      // First update the password - this is the most critical part
      const { error: passwordUpdateError } = await supabase.auth.updateUser({
        password: formData.password
      });

      if (passwordUpdateError) {
        console.error('Error updating user password:', passwordUpdateError);
        throw new Error(`Password update failed: ${passwordUpdateError.message}. Try a different password.`);
      }

      // Skip trying to update the auth metadata since that's causing 500 errors
      // Instead, just use the server action to update the profile table
      const registrationResult = await completeUserRegistration(currentUserId, {
        full_name: fullName,
        phone: formData.phoneNumber,
        email: email
      });

      if (!registrationResult.success) {
        console.error('Error completing registration:', registrationResult.error);
        toast('Your password was set, but some profile details could not be saved. You can update them later.', { duration: 4000 });
      }

      // If this is a client invitation, link the client profile
      if (isClientInvitation && clientId) {
        try {
          console.log('Linking client profile for client ID:', clientId);

          // Check if client_profiles entry already exists
          const { data: existingLink, error: checkError } = await supabase
            .from('client_profiles')
            .select('*')
            .eq('client_id', clientId)
            .eq('profile_id', currentUserId);

          if (checkError) {
            console.error('Error checking client profile link:', checkError);
          } else if (!existingLink || existingLink.length === 0) {
            // Create client_profiles entry
            const { error: linkError } = await supabase
              .from('client_profiles')
              .insert({
                client_id: clientId,
                profile_id: currentUserId,
                linked_at: new Date().toISOString()
              });

            if (linkError) {
              console.error('Error linking client profile:', linkError);
              toast('Your account was created, but there was an issue linking your client profile.', { duration: 4000 });
            } else {
              console.log('Successfully linked client profile');
            }
          } else {
            console.log('Client profile already linked');
          }
        } catch (err) {
          console.error('Error in client profile linking process:', err);
        }
      }

      // --- Success ---
      toast.success('Registration successful! Redirecting to login...', {
        duration: 2000,
      });

      // Sign out the user after successful registration
      const { error: signOutError } = await supabase.auth.signOut();
      if (signOutError) {
        console.error('Error signing out after registration:', signOutError);
      }

      // Redirect to login page after a delay
      setTimeout(() => {
        router.push('/login');
      }, 2000);

    } catch (err: any) {
      console.error('Registration submission error:', err);

      let errorMessage = 'An error occurred during registration. Please try again.';
      if (err.message) {
           errorMessage = err.message;
      }
      toast.error(errorMessage);

    } finally {
      setIsSubmitting(false);
    }
  };

  // --- Render Logic ---

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Validating invitation...</h1>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-2">Invitation Error</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Button onClick={() => router.push('/')}>
            Return to Home
          </Button>
        </div>
        <Toaster /> {/* Add Toaster here too for errors during validation */}
      </div>
    );
  }

  // --- Registration Form ---
  return (
    <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow">
      <div>
        <h2 className="text-center text-3xl font-extrabold text-gray-900">
          Complete Your Registration
        </h2>
        {email && (
          <p className="mt-2 text-center text-sm text-gray-600">
            Setting up account for: <strong>{email}</strong>
          </p>
        )}
        {isClientInvitation && (
          <div className="mt-4 p-3 bg-teal-50 border border-teal-200 rounded-md">
            <p className="text-center text-sm text-teal-700">
              You've been invited as a client. Complete your registration to access your client portal.
            </p>
          </div>
        )}
      </div>

      <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
        <div className="space-y-4">
          {/* Name fields */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                First Name <span className="text-red-500">*</span>
              </label>
              <Input
                id="firstName"
                name="firstName"
                type="text"
                required
                placeholder="John"
                value={formData.firstName}
                onChange={handleInputChange}
                disabled={isSubmitting}
              />
            </div>
            <div>
              <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                Last Name <span className="text-red-500">*</span>
              </label>
              <Input
                id="lastName"
                name="lastName"
                type="text"
                required
                placeholder="Doe"
                value={formData.lastName}
                onChange={handleInputChange}
                disabled={isSubmitting}
              />
            </div>
          </div>

          {/* Phone Number field */}
          <div>
            <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
              Phone Number <span className="text-red-500">*</span>
            </label>
            <PhoneInput
              id="phoneNumber"
              value={formData.phoneNumber}
              onChange={(value) => setFormData(prev => ({ ...prev, phoneNumber: value }))}
              disabled={isSubmitting}
              placeholder="Enter phone number"
              errorMessage={formData.phoneNumber && formData.phoneNumber.length < 10 ? 'Please enter a valid phone number' : ''}
              helperText="Your phone number will be used for account recovery and notifications."
            />
          </div>

          {/* Email field (disabled) */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </label>
            <Input
              id="email"
              name="email"
              type="email"
              value={email}
              disabled
              readOnly // Add readOnly for clarity
              className="bg-gray-100 cursor-not-allowed" // Indicate disabled state better
            />
          </div>

          {/* Password field */}
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Create Password <span className="text-red-500">*</span>
            </label>
            <Password
              id="password"
              name="password"
              required
              placeholder="********"
              value={formData.password}
              onChange={handleInputChange}
              disabled={isSubmitting}
            />
             {/* Password strength indicators */}
             <div className="mt-2 space-y-1 text-xs">
                <p className="font-medium text-gray-700">Password must contain:</p>
                {[
                { key: 'hasMinLength', label: 'At least 8 characters' },
                { key: 'hasUppercase', label: 'At least one uppercase letter' },
                { key: 'hasLowercase', label: 'At least one lowercase letter' },
                { key: 'hasNumber', label: 'At least one number' },
                { key: 'hasSpecialChar', label: 'At least one special character' },
                ].map(rule => (
                <div key={rule.key} className="flex items-center">
                    {passwordStrength[rule.key as keyof typeof passwordStrength] ?
                    <Check size={12} className="text-green-500 mr-1 flex-shrink-0" /> :
                    <X size={12} className="text-red-500 mr-1 flex-shrink-0" />}
                    <span className={passwordStrength[rule.key as keyof typeof passwordStrength] ? 'text-green-600' : 'text-gray-500'}>
                    {rule.label}
                    </span>
                </div>
                ))}
            </div>
          </div>

          {/* Confirm Password field */}
          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
              Confirm Password <span className="text-red-500">*</span>
            </label>
            <Password
              id="confirmPassword"
              name="confirmPassword"
              required
              placeholder="********"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              disabled={isSubmitting}
            />
            {/* Show mismatch error only if confirm password has been touched and doesn't match */}
            {formData.confirmPassword && formData.password !== formData.confirmPassword && (
              <p className="mt-1 text-xs text-red-500">Passwords do not match</p>
            )}
          </div>
        </div>

        <div>
          <Button
            type="submit"
            disabled={isSubmitting || !email} // Also disable if email somehow isn't set
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creating account...
              </>
            ) : (
              'Complete Registration'
            )}
          </Button>
        </div>
      </form>
      <Toaster position="top-center" reverseOrder={false} />
    </div>
  );
}