import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { ChevronDown } from 'lucide-react';
import useMediaQuery from '@/app/hooks/useMediaQuery';
import Icon from '@/app/components/ui/Icon';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from '@/app/components/ui/NavigationMenu';

// Types and Interfaces
export interface MenuItem {
  id: string;
  label: string;
  href?: string;
  icon?: string;
  children?: MenuItem[];
}

interface DesktopMenuItemProps {
  item: MenuItem;
  level?: number;
}

interface MobileAccordionItemProps {
  item: MenuItem;
}

interface MegaMenuProps {
  items: MenuItem[];
}

// Desktop Menu Components
const DesktopMenuItem: React.FC<DesktopMenuItemProps> = ({ item, level = 1 }) => {
  const hasChildren = (item.children?.length ?? 0) > 0;
  const [activeChild, setActiveChild] = useState<MenuItem | undefined>(
    item.children?.[0]
  );

  if (!hasChildren) {
    return (
      <NavigationMenuItem>
        <Link href={item.href || '#'} legacyBehavior passHref>
          <NavigationMenuLink className={navigationMenuTriggerStyle()}>
            <span className="flex items-center">
              {item.icon && <Icon name={item.icon} size={16} className="mr-2" />}
              {item.label}
            </span>
          </NavigationMenuLink>
        </Link>
      </NavigationMenuItem>
    );
  }

  if (level === 1) {
    return (
      <NavigationMenuItem>
        <NavigationMenuTrigger>
          <span className="flex items-center">
            {item.icon && <Icon name={item.icon} size={16} className="mr-2" />}
            {item.label}
          </span>
        </NavigationMenuTrigger>
        <NavigationMenuContent>
          <div className="flex w-[800px] gap-4 p-4 bg-white">
            <div className="w-1/3 space-y-1 border-r">
              {item.children?.map((child) => (
                <div
                  key={child.id}
                  onMouseEnter={() => setActiveChild(child)}
                  className={`rounded p-2 cursor-pointer transition-colors ${
                    activeChild?.id === child.id 
                      ? 'bg-accent text-accent-foreground' 
                      : 'hover:bg-accent/50'
                  }`}
                >
                  {child.icon && (
                    <Icon name={child.icon} size={16} className="mr-2 inline" />
                  )}
                  {child.label}
                </div>
              ))}
            </div>
            <div className="w-2/3 grid grid-cols-2 gap-4">
              {activeChild?.children?.map((subItem) => (
                <div key={subItem.id} className="space-y-2">
                  <h3 className="font-semibold flex items-center">
                    {subItem.icon && (
                      <Icon name={subItem.icon} size={16} className="mr-2" />
                    )}
                    {subItem.label}
                  </h3>
                  <div className="space-y-1">
                    {subItem.children?.map((link) => (
                      <Link
                        key={link.id}
                        href={link.href || '#'}
                        className="block text-sm text-muted-foreground hover:text-foreground transition-colors flex items-center"
                      >
                        {link.icon && (
                          <Icon name={link.icon} size={14} className="mr-2" />
                        )}
                        {link.label}
                      </Link>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </NavigationMenuContent>
      </NavigationMenuItem>
    );
  }

  return null;
};

// Mobile Menu Components
const MobileAccordionItem: React.FC<MobileAccordionItemProps> = ({ item }) => {
  const [isOpen, setIsOpen] = useState(false);
  const hasChildren = (item.children?.length ?? 0) > 0;
  const contentRef = useRef<HTMLDivElement>(null);
  const [contentHeight, setContentHeight] = useState(0);

  useEffect(() => {
    if (contentRef.current) {
      setContentHeight(contentRef.current.scrollHeight);
    }
  }, [isOpen]);

  if (!hasChildren) {
    return (
      <Link
        href={item.href || '#'}
        className="flex items-center p-4 hover:bg-accent/50 text-sm transition-colors"
      >
        {item.icon && <Icon name={item.icon} size={16} className="mr-2" />}
        {item.label}
      </Link>
    );
  }

  return (
    <div className="border-b last:border-b-0">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full p-4 text-left hover:bg-accent/50 transition-colors"
      >
        <span className="flex items-center">
          {item.icon && <Icon name={item.icon} size={16} className="mr-2" />}
          {item.label}
        </span>
        <ChevronDown
          className={`w-4 h-4 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>
      <div
        style={{ height: isOpen ? contentHeight : 0 }}
        className="overflow-hidden transition-all duration-200 ease-in-out"
      >
        <div ref={contentRef} className="pl-4">
          {item.children?.map((child) => (
            <MobileAccordionItem key={child.id} item={child} />
          ))}
        </div>
      </div>
    </div>
  );
};

// Main Mega Menu Component
const MegaMenu: React.FC<MegaMenuProps> = ({ items }) => {
  const isMobile = useMediaQuery('(max-width: 768px)');

  if (isMobile) {
    return (
      <div className="w-full border rounded-lg overflow-hidden bg-background">
        {items.map((item) => (
          <MobileAccordionItem key={item.id} item={item} />
        ))}
      </div>
    );
  }

  return (
    <NavigationMenu>
      <NavigationMenuList>
        {items.map((item) => (
          <DesktopMenuItem key={item.id} item={item} />
        ))}
      </NavigationMenuList>
    </NavigationMenu>
  );
};

export default MegaMenu;