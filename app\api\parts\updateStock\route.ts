import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';
import { supabaseAdmin } from '@/app/libs/supabase/admin';

// Helper function for logging
function log(message: string) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`);
  return message;
}

interface ConditionStock {
  conditionId: string;
  stock: number;
}

export async function PUT(request: NextRequest) {
  log('--- Starting Update Stock Request ---');
  try {
    // Parse request body
    const { partId, conditionStocks } = await request.json() as { partId: string, conditionStocks: ConditionStock[] };

    if (!partId) {
      log('❌ Error: Part ID is required');
      return NextResponse.json({ error: 'Part ID is required' }, { status: 400 });
    }

    if (!conditionStocks || !Array.isArray(conditionStocks) || conditionStocks.length === 0) {
      log('❌ Error: Condition stocks data is required');
      return NextResponse.json({ error: 'Condition stocks data is required' }, { status: 400 });
    }

    log(`Received request to update stock for part ID: ${partId}`);
    log(`Condition stocks data: ${JSON.stringify(conditionStocks)}`);

    // Use supabaseAdmin to update the stock values
    const supabase = supabaseAdmin;

    // Check if we need to create any conditions first
    const { data: existingConditions, error: conditionsError } = await supabase
      .from('parts_condition')
      .select('id')
      .eq('part_id', partId);

    if (conditionsError) {
      log(`❌ Error fetching existing conditions: ${conditionsError.message}`);
      return NextResponse.json({ error: `Error fetching existing conditions: ${conditionsError.message}` }, { status: 500 });
    }

    // If no conditions exist, create default ones
    if (!existingConditions || existingConditions.length === 0) {
      log(`No conditions found for part ID: ${partId}. Creating default conditions.`);

      try {
        // Create 'Used' condition by default
        const { error: usedError } = await supabase
          .from('parts_condition')
          .insert({
            part_id: partId,
            condition: 'Used',
            stock: 0
          });

        if (usedError) {
          log(`❌ Error creating Used condition: ${usedError.message}`);
          return NextResponse.json({ error: `Error creating Used condition: ${usedError.message}` }, { status: 500 });
        }

        log('✅ Successfully created Used condition');

        // Fetch the newly created conditions
        const { data: newConditions, error: newConditionsError } = await supabase
          .from('parts_condition')
          .select('id, condition')
          .eq('part_id', partId);

        if (newConditionsError) {
          log(`❌ Error fetching new conditions: ${newConditionsError.message}`);
          return NextResponse.json({ error: `Error fetching new conditions: ${newConditionsError.message}` }, { status: 500 });
        }

        // If no condition stocks were provided, create default ones based on the new conditions
        if (conditionStocks.length === 0 && newConditions && newConditions.length > 0) {
          const defaultConditionStocks: ConditionStock[] = newConditions.map(condition => ({
            conditionId: condition.id,
            stock: 0
          }));

          // Use the default condition stocks
          conditionStocks.push(...defaultConditionStocks);

          log(`Created default condition stocks: ${JSON.stringify(defaultConditionStocks)}`);
        }
      } catch (createError) {
        log(`❌ Error in condition creation process: ${createError instanceof Error ? createError.message : createError}`);
        return NextResponse.json({ error: 'Failed to create conditions' }, { status: 500 });
      }
    }

    // Update stock values for each condition
    const updatePromises = conditionStocks.map(async ({ conditionId, stock }) => {
      if (!conditionId || stock === undefined) {
        log(`❌ Error: Invalid data for condition update: ${JSON.stringify({ conditionId, stock })}`);
        return { success: false, conditionId, error: 'Invalid condition data' };
      }

      // Parse stock value to ensure it's a number
      const stockValue = parseInt(stock.toString(), 10);
      if (isNaN(stockValue) || stockValue < 0) {
        log(`❌ Error: Invalid stock value for condition ${conditionId}: ${stock}`);
        return { success: false, conditionId, error: 'Invalid stock value' };
      }

      log(`Updating stock for condition ID: ${conditionId} to value: ${stockValue}`);

      // Update the parts_condition table
      const { error } = await supabase
        .from('parts_condition')
        .update({ stock: stockValue })
        .eq('id', conditionId);

      if (error) {
        log(`❌ Error updating stock for condition ${conditionId}: ${error.message}`);
        return { success: false, conditionId, error: error.message };
      }

      log(`✅ Successfully updated stock for condition ${conditionId}`);
      return { success: true, conditionId };
    });

    // Wait for all updates to complete
    const results = await Promise.all(updatePromises);

    // Check if any updates failed
    const failedUpdates = results.filter(result => !result.success);
    if (failedUpdates.length > 0) {
      log(`❌ Some stock updates failed: ${JSON.stringify(failedUpdates)}`);
      return NextResponse.json({
        error: 'Some stock updates failed',
        details: failedUpdates
      }, { status: 500 });
    }

    log('--- Update Stock Request Completed Successfully ---');
    return NextResponse.json({
      success: true,
      message: `Stock updated successfully for part ${partId}`,
      updatedConditions: results.length
    });

  } catch (error) {
    log(`❌ Unexpected error during update stock request: ${error instanceof Error ? error.message : error}`);
    return NextResponse.json({
      error: 'An unexpected error occurred during the stock update process',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}