// hooks/usePartSubmission.ts
import { useState } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { GoogleGenerativeAI } from '@google/generative-ai';
import type { PartFormValues } from '../types';

const supabase = createClient();
const genAI = new GoogleGenerativeAI(process.env.NEXT_PUBLIC_GEMINI_API_KEY!);

// Add rate limiting
let lastApiCall = 0;
const MIN_API_CALL_INTERVAL = 30000; // 30 seconds minimum between calls

const waitForRateLimit = async () => {
  const now = Date.now();
  const timeSinceLastCall = now - lastApiCall;
  if (timeSinceLastCall < MIN_API_CALL_INTERVAL) {
    const waitTime = MIN_API_CALL_INTERVAL - timeSinceLastCall;
    console.log(`Rate limiting: waiting ${waitTime}ms before next API call`);
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }
  lastApiCall = Date.now();
};

// Add caching
const engineCache = new Map<string, any>();

interface EngineCompatibility {
  engineCode: string;
  engineCapacity: string;
  fuelType: string;
  engineType: string;
}

interface VehicleCompatibility {
  brand: string;
  model: string;
  generation: string;
  variation: string;
  trim: string;
}

interface CarInfo {
  brand: string;
  model: string;
  generation: string;
  variation: string;
  trim: string;
}

interface EngineInfo {
  code: string;
  capacity: string;
  fuelType: string;
  cars: CarInfo[];
}

interface CompatibilityData {
  partName: string;
  compatiblePartNumbers: string[];
  isEnginePart: boolean;
  engineCompatibility: EngineCompatibility[];
  vehicleCompatibility: VehicleCompatibility[];
}

interface UsePartSubmissionProps {
  partNumberExists: boolean;
  selectedCategory: string;
  validateVehicleSelection: () => boolean;
  getValues: () => PartFormValues;
  onSuccess: (partId: number, partName: string) => void;
  onClose: () => void;
  requirePartNumber: boolean;
  flatCategories: any[];
}

interface CategoryAttribute {
  id: string;
  name?: string;
}

export const usePartSubmission = ({
  partNumberExists,
  selectedCategory,
  validateVehicleSelection,
  getValues,
  onSuccess,
  onClose,
  requirePartNumber,
  flatCategories
}: UsePartSubmissionProps) => {
  const [currentTask, setCurrentTask] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Function to save vehicle compatibility data to parts_car table
  const saveVehicleCompatibility = async (partId: number, vehicleCompatibility: VehicleCompatibility[]) => {
    console.log('Processing vehicle compatibility data for part ID:', partId);
    console.log('Vehicle compatibility data:', vehicleCompatibility);

    // Check if we're receiving cleaned data (no parentheses, no combined variations, no "Various" trims)
    const hasProblematicData = vehicleCompatibility.some(vehicle =>
      vehicle.generation?.includes('(') ||
      vehicle.variation?.includes('/') ||
      vehicle.trim?.includes('Various')
    );

    if (hasProblematicData) {
      console.warn('⚠️ Received problematic vehicle data that should have been cleaned:', vehicleCompatibility);
    } else {
      console.log('✅ Received clean vehicle data for processing');
    }

    for (const vehicle of vehicleCompatibility) {
      try {
        // Validate that all required fields are present
        if (!vehicle.brand || !vehicle.model || !vehicle.generation || !vehicle.variation || !vehicle.trim) {
          console.warn('Skipping incomplete vehicle data:', vehicle);
          continue;
        }

        // Create car string in the format expected by get_variation_trim_id function
        const carString = `${vehicle.brand} ${vehicle.model} ${vehicle.generation} ${vehicle.variation} ${vehicle.trim}`;
        console.log('Processing car string:', carString);

        // Use the database function to get variation_trim_id
        const { data: trimId, error: trimError } = await supabase
          .rpc('get_variation_trim_id', { p_car_string: carString });

        if (trimError) {
          console.error('Error getting trim ID for car string:', carString, trimError);
          continue; // Skip this vehicle if we can't find the trim ID
        }

        if (trimId) {
          console.log('✅ Found trim ID:', trimId, 'for car string:', carString);

          // Check if this car-part relationship already exists
          const { data: existingLink, error: checkError } = await supabase
            .from('parts_car')
            .select('id')
            .eq('part_id', partId)
            .eq('variation_trim_id', trimId)
            .maybeSingle();

          if (checkError) {
            console.error('Error checking existing car-part link:', checkError);
            continue;
          }

          if (!existingLink) {
            // Import and use the createCarLinkRecord function
            const { createCarLinkRecord } = await import('@/app/libs/data');
            await createCarLinkRecord(partId, trimId.toString());
            console.log('✅ Successfully linked part', partId, 'to trim', trimId);
          } else {
            console.log('ℹ️ Car-part link already exists for part', partId, 'and trim', trimId);
          }
        } else {
          console.warn('❌ No trim ID found for car string:', carString);
          console.warn('This usually means the car combination does not exist in the database');
          console.warn('Vehicle data that failed:', vehicle);
        }
      } catch (error) {
        console.error('❌ Error processing vehicle:', vehicle, error);
      }
    }
  };

  const generatePartDescription = async (title: string): Promise<string> => {
    // Wait for rate limit
    await waitForRateLimit();

    try {
      const modelName = process.env.NEXT_PUBLIC_GEMINI_MODEL || 'gemini-2.0-flash';
      const model = genAI.getGenerativeModel({ model: `models/${modelName}` });

      const prompt = `
Generate a concise, informative description for the following car part:
"${title}"

The description should:
1. Be 1-3 sentences long
2. Mention what the part is and its function
3. Include any key details from the title (car model, condition, etc.)
4. Be written in a professional, factual tone
5. Be suitable for an e-commerce product listing

Example format: "Used left non-xenon headlights for VW Passat B8 Sedan (2015-2022, Pro trim)."
`;

      const result = await model.generateContent(prompt);
      const description = result.response.text().trim();
      console.log('Generated description:', description);

      return description;
    } catch (error) {
      console.error('Error generating part description:', error);
      return ''; // Return empty string if generation fails
    }
  };

  const generateEngineInfo = async (engineCodes: string[]): Promise<{ engines: EngineInfo[] }> => {
    // Check cache first
    const cacheKey = engineCodes.sort().join(',');
    if (engineCache.has(cacheKey)) {
      console.log('Using cached engine info');
      return engineCache.get(cacheKey);
    }

    // Wait for rate limit
    await waitForRateLimit();

    const prompt = `Given the following engine codes: ${engineCodes.join(', ')}, provide detailed information in JSON format:

{
  "engines": [
    {
      "code": "string",
      "capacity": "string",
      "fuelType": "string",
      "cars": [
        {
          "brand": "VW or Audi",
          "model": "string",
          "generation": "string",
          "variation": "string",
          "trim": "string"
        }
      ]
    }
  ]
}

Rules for car strings:
- Fuel type must be one of: Petrol, Diesel, Electric, Hybrid
- Brand must be VW or Audi only
- Model must be one of: Polo, Golf, Tiguan, Passat, Touareg, Arteon, Jetta, Beetle, Scirocco, Sharan, Touran, Caddy, Amarok, T-Cross, T-Roc, Taigo, ID.3, ID.4, ID.5, ID. Buzz, A1, A3, A4, A5, A6, A7, A8, Q2, Q3, Q5, Q7, Q8, TT, R8, e-tron GT, Q4 e-tron, Q8 e-tron
- Generation must be one of: Mk4, Mk5, Mk6, Mk7, Mk8, Mk1, Mk2, Mk3, B6, B7, B8, B8.5, B9, Type 9C, Type 16, 8P, 8V, 8Y, C6, C7, C8, D3, D4, D5, 8J, 8S, Type 42, Type 4S, 7L, Mk2 7P, 5N Facelift, MK3 Facelift, 8R, 8R Facelift, 80A, 80A Facelift, 8U, 8U Facelift, F3
- Variation must be one of: Hatchback, Wagon/Variant, SUV, Sedan, Coupe, Convertible, Van, Wagon/Avant
- Trim must be one of: Base, Trendline, Comfortline, Highline, R-Line, GTI, R, S, SE, Life, Style, Pro, Business, Pure, GTX, Sportback, Citycarver, SQ8, RSQ8, S line, Advanced, Black Edition, TTS, TT RS, R8 V10, R8 V10 plus, R8 V8, RS e-tron GT, SQ7, RSQ7, SQ5, RSQ3, RSQ3 Sportback, S8, RS7, S6, RS6, RS5, S5, RS4, S4, S3, RS3, S1, S Line, S-Line, R32, GTE`;

    const modelName = process.env.NEXT_PUBLIC_GEMINI_MODEL || 'gemini-2.0-flash';
    const model = genAI.getGenerativeModel({ model: `models/${modelName}` });
    const result = await model.generateContent(prompt);
    const text = result.response.text().replace(/```json/g, '').replace(/```/g, '').trim();
    const data = JSON.parse(text);

    // Cache the result
    engineCache.set(cacheKey, data);

    return data;
  };

  const generatePartTitle = async (aiData: CompatibilityData, additionalEngineCodes: string[]): Promise<string> => {
    const categoryId = parseInt(selectedCategory);
    const partNumber = getValues().partNumber;
    const values = getValues();
    const formValues = getValues();

    console.log('Form values dump:', JSON.stringify(values, null, 2));
    console.log('All form keys available:', Object.keys(values));

    try {
      // Import the title generator
      const { generatePartTitle } = await import('@/app/utils/titleGenerator');

      // Prepare the part data for title generation
      const partData: any = {
        category_id: categoryId,
        part_number: partNumber,
        condition: formValues.condition || 'Used',
        attributes: {}
      };

      // Add brand, model, generation, variation, and trim IDs if available
      if (formValues.brandId) partData.brand_id = parseInt(formValues.brandId);
      if (formValues.modelId) partData.model_id = parseInt(formValues.modelId);
      if (formValues.generationId) partData.generation_id = parseInt(formValues.generationId);
      if (formValues.variationId) partData.variation_id = parseInt(formValues.variationId);
      if (formValues.trimId) partData.trim_id = parseInt(formValues.trimId);

      // Add car data if available
      if (formValues.brandName || formValues.modelName) {
        partData.cars = [{
          brand_name: formValues.brandName || '',
          model_name: formValues.modelName || '',
          generation_name: formValues.generationName || '',
          generation_years: formValues.generationYears || '',
          variation: formValues.variationName || '',
          trim: formValues.trimName || ''
        }];
      }

      // Process category attributes
      const formCategoryAttrs = formValues.categoryAttributes || [];
      if (formCategoryAttrs.length > 0) {
        // Convert attributes to a key-value object
        partData.attributes = {};

        for (const attr of formCategoryAttrs) {
          if (attr.id && attr.value && attr.value.trim() !== '') {
            // Get the attribute name from flatCategories or use a default
            const attributeDetails = await fetchAttributeDetails(parseInt(attr.id));
            if (attributeDetails && attributeDetails.attribute) {
              const attrKey = attributeDetails.attribute.toLowerCase().replace(/\s+/g, '_');
              partData.attributes[attrKey] = attr.value;
            }
          }
        }

        console.log('Processed attributes for title generation:', partData.attributes);
      }

      // Special handling for engine parts
      if (requirePartNumber && aiData?.isEnginePart) {
        // Get all engine codes
        const allEngineCodes = [
          ...(aiData.engineCompatibility?.map((e: EngineCompatibility) => e.engineCode) || []),
          ...additionalEngineCodes
        ];
        const uniqueEngineCodes = Array.from(new Set(allEngineCodes));

        // Get engine capacity, fuel type, and engine type from the first engine
        const firstEngine = aiData.engineCompatibility?.[0];

        if (firstEngine) {
          partData.attributes.engine_codes = uniqueEngineCodes.join('/');
          partData.attributes.engine_capacity = firstEngine.engineCapacity || '';
          partData.attributes.fuel_type = firstEngine.fuelType || '';
          partData.attributes.engine_type = firstEngine.engineType || '';
        }

        // Format: "Volkswagen(VW) Audi [Category] [PartNumber] [Engines] [Engine Capacity] [Fuel Type] [Engine Type]"
        return `Volkswagen(VW) Audi ${flatCategories.find(cat => cat.id.toString() === selectedCategory)?.name || ''} ${partNumber} ${uniqueEngineCodes.join('/')} ${firstEngine?.engineCapacity || ''} ${firstEngine?.fuelType || ''} ${firstEngine?.engineType || ''}`.trim();
      }

      // Generate the title using our new utility
      const title = await generatePartTitle(partData);
      console.log('Generated title using category-specific rules:', title);
      return title;
    } catch (error) {
      console.error('Error generating part title with new utility:', error);

      // Fallback to the original title generation logic
      console.log('Falling back to original title generation logic');

      if (!requirePartNumber) {
        // Get all form values
        const formValues = getValues();

        // Build basic title components
        const brandName = formValues.brandName || '';
        const modelName = formValues.modelName || '';
        const generationName = formValues.generationName || '';
        const generationYears = formValues.generationYears || '';
        const variationName = formValues.variationName || '';
        const trimName = formValues.trimName || '';
        const condition = formValues.condition || 'Used';
        const category = flatCategories.find(cat => cat.id.toString() === selectedCategory)?.name || '';

        // Get attribute values
        let attributeValues = '';
        const formCategoryAttrs = formValues.categoryAttributes || [];
        if (formCategoryAttrs.length > 0) {
          const attributeStrings = formCategoryAttrs
            .filter(attr => attr.value && attr.value.trim() !== '')
            .map(attr => attr.value);
          attributeValues = attributeStrings.join(' ');
        }

        // Build the title
        const titleParts = [
          brandName, modelName, generationName, generationYears,
          variationName, trimName, condition, category, attributeValues
        ].filter(part => part && part.trim() !== '');

        return titleParts.join(' ');
      } else {
        // Format for engine parts with part number
        if (aiData?.isEnginePart) {
          // Get all engine codes
          const allEngineCodes = [
            ...(aiData.engineCompatibility?.map((e: EngineCompatibility) => e.engineCode) || []),
            ...additionalEngineCodes
          ];
          const uniqueEngineCodes = Array.from(new Set(allEngineCodes));

          // Get engine capacity, fuel type, and engine type from the first engine
          const firstEngine = aiData.engineCompatibility?.[0];
          const engineCapacity = firstEngine?.engineCapacity || '';
          const fuelType = firstEngine?.fuelType || '';
          const engineType = firstEngine?.engineType || '';

          // Format: "Volkswagen(VW) Audi [Category] [PartNumber] [Engines] [Engine Capacity] [Fuel Type] [Engine Type]"
          return `Volkswagen(VW) Audi ${flatCategories.find(cat => cat.id.toString() === selectedCategory)?.name || ''} ${partNumber} ${uniqueEngineCodes.join('/')} ${engineCapacity} ${fuelType} ${engineType}`.trim();
        }
        return `${partNumber} ${flatCategories.find(cat => cat.id.toString() === selectedCategory)?.name || ''}`.trim();
      }
    }
  };

  // Helper function to fetch attribute details
  const fetchAttributeDetails = async (attributeId: number): Promise<any> => {
    try {
      const { data, error } = await supabase
        .from('parts_category_attributes')
        .select('*')
        .eq('id', attributeId)
        .single();

      if (error) {
        console.error('Error fetching attribute details:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in fetchAttributeDetails:', error);
      return null;
    }
  };

  const onSubmit = async (values: PartFormValues) => {
    setIsSubmitting(true);
    try {
      console.log('Starting form submission with values:', values);

      // Validate required data
      if (requirePartNumber && !values.partNumber) {
        console.error('Part number is missing');
        throw new Error('Part number is required');
      }

      // Validate vehicle selection if part number is not required
      if (!requirePartNumber && !validateVehicleSelection()) {
        throw new Error('Vehicle selection is required');
      }

      // Make sure we have a valid category ID
      if (!values.categoryId || values.categoryId === '') {
        console.error('Category ID is missing or empty');
        throw new Error('Category ID is required');
      }

      const categoryId = parseInt(values.categoryId);
      if (isNaN(categoryId)) {
        console.error('Invalid category ID:', values.categoryId);
        throw new Error(`Invalid category ID: ${values.categoryId}`);
      }

      // Get the compatibility data
      const compatibilityData = values.compatibilityData as CompatibilityData || {
        compatiblePartNumbers: [],
        engineCompatibility: [],
        vehicleCompatibility: [],
        isEnginePart: false,
        partName: ''
      };

      console.log('Compatibility data:', compatibilityData);

      // Step 1: Check part number in compatibility groups
      setCurrentTask('Checking part compatibility');
      let compatibilityGroupId: number | null = null;

      try {
        // First check if part number exists in part_compatibility_groups table
        const { data: existingGroup, error: groupError } = await supabase
          .from('part_compatibility_groups')
          .select('id')
          .eq('part_number', values.partNumber)
          .single();

        if (groupError && groupError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
          console.error('Error checking part_compatibility_groups:', groupError);
        }

        if (existingGroup) {
          console.log('Found existing compatibility group:', existingGroup);
          compatibilityGroupId = existingGroup.id;
        } else {
          // If not found in part_compatibility_groups, check part_to_group table
          const { data: partToGroup, error: partToGroupError } = await supabase
            .from('part_to_group')
            .select('group_id')
            .eq('partnumber', values.partNumber)
            .single();

          if (partToGroupError && partToGroupError.code !== 'PGRST116') {
            console.error('Error checking part_to_group:', partToGroupError);
          }

          if (partToGroup) {
            console.log('Found existing part in part_to_group:', partToGroup);
            compatibilityGroupId = partToGroup.group_id;
          } else {
            // If not found in either table, create a new compatibility group
            const { data: newGroup, error: newGroupError } = await supabase
              .from('part_compatibility_groups')
              .insert({
                part_number: values.partNumber
              })
              .select('id')
              .single();

            if (newGroupError) {
              console.error('Error creating compatibility group:', newGroupError);
              throw new Error(`Failed to create compatibility group: ${newGroupError.message}`);
            }

            if (newGroup) {
              console.log('Created new compatibility group:', newGroup);
              compatibilityGroupId = newGroup.id;

              // Add the current part number to part_to_group
              const { error: addPartError } = await supabase
                .from('part_to_group')
                .insert({
                  partnumber: values.partNumber,
                  group_id: compatibilityGroupId
                });

              if (addPartError) {
                console.error('Error adding part to group:', addPartError);
              }

              // Add all compatible part numbers from AI data to part_to_group
              if (compatibilityData?.compatiblePartNumbers?.length > 0) {
                const compatibleParts = compatibilityData.compatiblePartNumbers.map(partNum => ({
                  partnumber: partNum,
                  group_id: compatibilityGroupId
                }));

                const { error: bulkAddError } = await supabase
                  .from('part_to_group')
                  .insert(compatibleParts)
                  .select();

                if (bulkAddError) {
                  console.error('Error adding compatible parts to group:', bulkAddError);
                } else {
                  console.log('Added compatible parts to group:', compatibleParts);
                }
              }
            }
          }
        }
      } catch (compatibilityError) {
        console.error('Error handling compatibility groups:', compatibilityError);
        // Continue with form submission despite compatibility error
      }

      // Step 2: Generate engine info for additional codes
      setCurrentTask('Generating engine information');
      console.log('Generating engine info for codes:', values.additionalEngineCodes);
      const additionalEngineCodes = values.additionalEngineCodes?.filter((code: string) => code?.trim() !== '') || [];
      let engineInfo = null;
      if (additionalEngineCodes.length > 0) {
        engineInfo = await generateEngineInfo(additionalEngineCodes);
        console.log('Generated engine info:', engineInfo);
      }

      // Step 3: Generate and save part title
      setCurrentTask('Generating part title');
      const title = await generatePartTitle(compatibilityData, additionalEngineCodes);
      console.log('Generated title:', title);

      // Step 3.5: Generate part description using Gemini AI
      setCurrentTask('Generating part description');
      const description = await generatePartDescription(title);
      console.log('Generated description:', description);

      // Create part record with proper handling of userId
      const insertData: any = {
        category_id: categoryId,
        title,
        description, // Add the generated description
        partnumber_group: compatibilityGroupId,
        createdBy: values.userId && values.userId !== '' ? parseInt(values.userId) : null
      };

      console.log('Creating part record with:', insertData);

      const { data: partData, error: partError } = await supabase
        .from('parts')
        .insert(insertData)
        .select('id')
        .single();

      if (partError) {
        console.error('Error creating part:', partError);
        throw new Error(`Failed to create part: ${partError.message}`);
      }

      if (!partData) {
        console.error('No part data returned after creation');
        throw new Error('Failed to create part: No data returned');
      }

      const partId = partData.id;
      console.log('Created part with ID:', partId);

      // Step 4: Handle engine codes and trims
      setCurrentTask('Processing engine codes and trims');
      const allEngineCodes = [
        ...(compatibilityData.engineCompatibility?.map((e: EngineCompatibility) => e.engineCode) || []),
        ...additionalEngineCodes
      ];
      const uniqueEngineCodes = Array.from(new Set(allEngineCodes));
      const capacities = Array.from(new Set(compatibilityData.engineCompatibility?.map((e: EngineCompatibility) => e.engineCapacity) || []));
      const fuelTypes = Array.from(new Set(compatibilityData.engineCompatibility?.map((e: EngineCompatibility) => e.fuelType) || []));
      const engineTypes = Array.from(new Set(compatibilityData.engineCompatibility?.map((e: EngineCompatibility) => e.engineType) || []));

      for (const engineCode of uniqueEngineCodes) {
        try {
          // Use fetch directly to check if the engine exists
          const engineResponse = await fetch(
            `${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/engines?select=id&engine_code=eq.${engineCode}`,
            {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
                'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!}`,
                'Accept': 'application/json'
              }
            }
          );

          if (!engineResponse.ok) {
            console.error('Error checking engine:', engineResponse.status, engineResponse.statusText);
            continue; // Skip this engine if we can't check it
          }

          const existingEngines = await engineResponse.json();
          const existingEngine = existingEngines.length > 0 ? existingEngines[0] : null;

          let engineId: number;
          if (!existingEngine) {
            // Get engine info from AI data or generated info
            const engineData = engineInfo?.engines.find((e: EngineInfo) => e.code === engineCode) ||
                             compatibilityData.engineCompatibility.find((e: EngineCompatibility) => e.engineCode === engineCode);

            if (!engineData) continue;

            // Insert new engine - use a transaction to handle potential conflicts
            try {
              // First try to get a new ID that doesn't exist
              const { data: maxIdData } = await supabase
                .from('engines')
                .select('id')
                .order('id', { ascending: false })
                .limit(1)
                .single();

              const newId = maxIdData ? maxIdData.id + 1 : 1;

              // Insert with the new ID
              const { data: newEngine, error: engineError } = await supabase
                .from('engines')
                .insert({
                  id: newId,
                  engine_code: engineCode,
                  capacity: 'capacity' in engineData ? engineData.capacity : engineData.engineCapacity,
                  fuel_type: engineData.fuelType
                })
                .select('id')
                .single();

              if (engineError) {
                console.error('Error inserting engine:', engineError);
                continue;
              }

              engineId = newEngine.id;

              // Process car trims
              if ('cars' in engineData && engineData.cars) {
                for (const car of engineData.cars) {
                  const carString = `${car.brand} ${car.model} ${car.generation} ${car.variation} ${car.trim}`;
                  const { data: trimId } = await supabase.rpc('get_variation_trim_id', { p_car_string: carString });

                  if (trimId) {
                    await supabase
                      .from('trim_engines')
                      .insert({
                        trim_id: trimId,
                        engine_id: engineId
                      });
                  }
                }
              }
            } catch (engineInsertError) {
              console.error('Error in engine insertion process:', engineInsertError);
              continue; // Skip this engine if there's an error
            }
          } else {
            engineId = existingEngine.id;
          }

          // Add to parts_engines
          try {
            await supabase
              .from('parts_engines')
              .insert({
                part_id: partId,
                engine_id: engineId
              });
          } catch (partEngineError) {
            console.error('Error linking part to engine:', partEngineError);
          }
        } catch (engineProcessError) {
          console.error(`Error processing engine ${engineCode}:`, engineProcessError);
        }
      }

      // Step 5: Save image(s) to part_images table
      setCurrentTask('Saving part images');

      try {
        // Handle multiple images if present
        if (values.images && values.images.length > 0) {
          console.log(`Processing ${values.images.length} images for part ID ${partId}`);

          for (let index = 0; index < values.images.length; index++) {
            const imageInfo = values.images[index];
            const { url: imageUrl, isMain } = imageInfo;
            console.log(`Processing image ${index + 1}/${values.images.length} (isMain: ${isMain})`);

            // Skip invalid URLs
            if (!imageUrl || !imageUrl.startsWith('http')) {
              console.warn(`Skipping invalid image URL at index ${index}`);
              continue;
            }

            let finalImageUrl = imageUrl;

            // Insert image record into database
            const { error: imageError } = await supabase
              .from('part_images')
              .insert({
                part_id: partId,
                image_url: finalImageUrl,
                alt_text: title.substring(0, 255),
                is_main_image: isMain
              });

            if (imageError) {
              console.error('Error inserting image record:', imageError);
            } else {
              console.log(`Successfully inserted image record (is_main_image: ${isMain})`);
            }
          }
        } else if (values.imageUrl) {
          // Fallback to using the single imageUrl field for backward compatibility
          console.log('No images array found, using legacy imageUrl field:', values.imageUrl);

          // Insert the single image as main image
          const { error: imageError } = await supabase
            .from('part_images')
            .insert({
              part_id: partId,
              image_url: values.imageUrl,
              alt_text: title.substring(0, 255),
              is_main_image: true // Single image is always main
            });

          if (imageError) {
            console.error('Error inserting single image record:', imageError);
          } else {
            console.log('Successfully inserted single image record as main image');
          }
        } else {
          console.log('No images provided for this part');
        }
      } catch (imageError) {
        console.error('Error in image processing:', imageError);
      }

      // Step 6: Create condition record with stock and prices
      setCurrentTask('Saving part condition, stock, and prices');
      try {
        // Determine which condition(s) to create based on the form values
        if (values.condition === 'Both') {
          // Create 'New' condition with stock
          if (values.newStock !== undefined) {
            // First create the condition record
            const { data: newCondition, error: newConditionError } = await supabase
              .from('parts_condition')
              .insert({
                part_id: partId,
                condition: 'New',
                stock: values.newStock
              })
              .select('id')
              .single();

            if (newConditionError) {
              console.error('Error creating New condition record:', newConditionError);
            } else {
              console.log('Successfully created New condition with stock:', values.newStock);

              // Then create the price record if we have a condition ID
              if (newCondition && newCondition.id) {
                const { error: newPriceError } = await supabase
                  .from('part_price')
                  .insert({
                    condition_id: newCondition.id,
                    price: values.newPrice || 0,
                    discounted_price: values.newDiscountPrice || null
                  });

                if (newPriceError) {
                  console.error('Error creating New price record:', newPriceError);
                } else {
                  console.log('Successfully created New price record:', {
                    price: values.newPrice || 0,
                    discounted_price: values.newDiscountPrice || null
                  });
                }
              }
            }
          }

          // Create 'Used' condition with stock
          if (values.usedStock !== undefined) {
            // First create the condition record
            const { data: usedCondition, error: usedConditionError } = await supabase
              .from('parts_condition')
              .insert({
                part_id: partId,
                condition: 'Used',
                stock: values.usedStock
              })
              .select('id')
              .single();

            if (usedConditionError) {
              console.error('Error creating Used condition record:', usedConditionError);
            } else {
              console.log('Successfully created Used condition with stock:', values.usedStock);

              // Then create the price record if we have a condition ID
              if (usedCondition && usedCondition.id) {
                const { error: usedPriceError } = await supabase
                  .from('part_price')
                  .insert({
                    condition_id: usedCondition.id,
                    price: values.usedPrice || 0,
                    discounted_price: values.usedDiscountPrice || null
                  });

                if (usedPriceError) {
                  console.error('Error creating Used price record:', usedPriceError);
                } else {
                  console.log('Successfully created Used price record:', {
                    price: values.usedPrice || 0,
                    discounted_price: values.usedDiscountPrice || null
                  });
                }
              }
            }
          }
        } else {
          // Create a single condition record with stock
          const condition = values.condition || 'Used';
          const stock = values.stock !== undefined ? values.stock : 0;
          const price = values.price !== undefined ? values.price : 0;
          const discountPrice = values.discountPrice !== undefined ? values.discountPrice : null;

          // First create the condition record
          const { data: conditionData, error: conditionError } = await supabase
            .from('parts_condition')
            .insert({
              part_id: partId,
              condition: condition,
              stock: stock
            })
            .select('id')
            .single();

          if (conditionError) {
            console.error('Error creating condition record:', conditionError);
          } else {
            console.log(`Successfully created ${condition} condition with stock:`, stock);

            // Then create the price record if we have a condition ID
            if (conditionData && conditionData.id) {
              const { error: priceError } = await supabase
                .from('part_price')
                .insert({
                  condition_id: conditionData.id,
                  price: price,
                  discounted_price: discountPrice
                });

              if (priceError) {
                console.error('Error creating price record:', priceError);
              } else {
                console.log(`Successfully created price record for ${condition} condition:`, {
                  price: price,
                  discounted_price: discountPrice
                });
              }
            }
          }
        }
      } catch (conditionError) {
        console.error('Error in condition and price processing:', conditionError);
      }

      // Step 7: Save category attribute values
      setCurrentTask('Saving category attribute values');
      try {
        // Get category attributes from form values
        const categoryAttributes = values.categoryAttributes || [];

        if (categoryAttributes.length > 0) {
          console.log('Processing category attributes:', categoryAttributes);

          // Process each attribute
          for (const attribute of categoryAttributes) {
            if (attribute.id && attribute.value && attribute.value.trim() !== '') {
              // Get the attribute details to determine input type
              const { data: attrDetails, error: attrError } = await supabase
                .from('parts_category_attributes')
                .select('input_type')
                .eq('id', attribute.id)
                .single();

              if (attrError) {
                console.error('Error fetching attribute details:', attrError);
                continue; // Skip this attribute if we can't get its details
              }

              // Determine if this is a select/radio/checkbox input
              const isSelectionType = ['select', 'radio', 'checkbox'].includes(attrDetails?.input_type || '');

              // Insert attribute value
              const { error: valueError } = await supabase
                .from('parts_category_attribute_values')
                .insert({
                  part_id: partId,
                  attribute_id: attribute.id,
                  value: isSelectionType ? null : attribute.value,
                  selection_value: isSelectionType ? attribute.value : null
                });

              if (valueError) {
                console.error(`Error saving attribute value for attribute ${attribute.id}:`, valueError);
              } else {
                console.log(`Successfully saved attribute value for attribute ${attribute.id}:`, {
                  value: isSelectionType ? null : attribute.value,
                  selection_value: isSelectionType ? attribute.value : null
                });
              }
            }
          }
        } else {
          console.log('No category attributes to save');
        }
      } catch (attributeError) {
        console.error('Error in attribute value processing:', attributeError);
      }

      // Step 8: Save car data
      setCurrentTask('Saving car data');
      try {
        // For parts that require part numbers, use vehicle compatibility from AI
        if (requirePartNumber && compatibilityData.vehicleCompatibility && compatibilityData.vehicleCompatibility.length > 0) {
          console.log('Processing vehicle compatibility data from AI for part ID:', partId);
          await saveVehicleCompatibility(partId, compatibilityData.vehicleCompatibility);
          console.log('Successfully saved vehicle compatibility data for part ID:', partId);
        }
        // For parts that don't require part numbers, use manually selected car data
        else if (!requirePartNumber && values.trimId) {
          console.log('Processing manually selected car data for part ID:', partId);

          try {
            // Import the createCarLinkRecord function
            const { createCarLinkRecord } = await import('@/app/libs/data');

            // Create car link record
            await createCarLinkRecord(partId, values.trimId);
            console.log('Successfully saved manually selected car data for part ID:', partId);
          } catch (carError) {
            console.error('Error saving manually selected car data:', carError);
          }
        } else {
          console.log('No car data to save - requirePartNumber:', requirePartNumber, 'vehicleCompatibility:', !!compatibilityData.vehicleCompatibility, 'trimId:', !!values.trimId);
        }
      } catch (carError) {
        console.error('Error in car data processing:', carError);
      }

      // Continue with form submission regardless of attribute processing result
      setCurrentTask('Part submission completed');
      console.log('Part submission completed successfully');

      // Ensure we reset the form state properly
      setIsSubmitting(false);

      // First call onSuccess to show the storage location modal with the part ID and title
      // Make sure we're passing the correct part_id and name
      onSuccess(partId, title || 'New Part');

      // We don't call onClose() here anymore because we want to show the storage location modal first
      // The PartsForm component will handle closing the modal after the storage location form is submitted
    } catch (error) {
      console.error('Error in submission process:', error);
      setCurrentTask('Error submitting part');
      setIsSubmitting(false);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleError = (code: string, message: string) => {
    console.error(`Error ${code}: ${message}`);
  };

  return {
    onSubmit,
    currentTask,
    isSubmitting
  };
};