'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { createClient } from '@/app/libs/supabase/client';
import { Trim, TrimFormData, Variation, Generation, Model, Brand } from '../../types';

interface EditTrimModalProps {
  isOpen: boolean;
  onClose: () => void;
  trim: Trim;
  variations: Variation[];
  generations: Generation[];
  models: Model[];
  brands: Brand[];
  onSuccess: () => void;
}

const EditTrimModal: React.FC<EditTrimModalProps> = ({
  isOpen,
  onClose,
  trim,
  variations,
  generations,
  models,
  brands,
  onSuccess
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedBrandId, setSelectedBrandId] = useState<number | ''>('');
  const [selectedModelId, setSelectedModelId] = useState<number | ''>('');
  const [selectedGenerationId, setSelectedGenerationId] = useState<number | ''>('');
  const [filteredModels, setFilteredModels] = useState<Model[]>([]);
  const [filteredGenerations, setFilteredGenerations] = useState<Generation[]>([]);
  const [filteredVariations, setFilteredVariations] = useState<Variation[]>([]);
  
  const { register, handleSubmit, reset, watch, setValue, formState: { errors } } = useForm<TrimFormData>({
    defaultValues: {
      variation_id: trim.variation_id,
      trim: trim.trim
    }
  });
  
  const supabase = createClient();

  // Set initial brand, model, and generation IDs based on the variation
  useEffect(() => {
    if (isOpen && trim.variation_id) {
      const variation = variations.find(v => v.id === trim.variation_id);
      if (variation) {
        setSelectedGenerationId(variation.generation_id);
        
        const generation = generations.find(g => g.id === variation.generation_id);
        if (generation) {
          setSelectedModelId(generation.model_id);
          
          const model = models.find(m => m.id === generation.model_id);
          if (model) {
            setSelectedBrandId(model.brand_id);
          }
        }
      }
    }
  }, [isOpen, trim, variations, generations, models]);

  // Reset form when trim changes
  useEffect(() => {
    if (isOpen) {
      reset({
        variation_id: trim.variation_id,
        trim: trim.trim
      });
    }
  }, [trim, isOpen, reset]);

  // Filter models based on selected brand
  useEffect(() => {
    if (selectedBrandId) {
      const filtered = models.filter(model => model.brand_id === selectedBrandId);
      setFilteredModels(filtered);
      
      // Reset model selection if the current model doesn't belong to the selected brand
      if (selectedModelId && !filtered.some(model => model.id === selectedModelId)) {
        setSelectedModelId('');
        setSelectedGenerationId('');
        setValue('variation_id', undefined);
      }
    } else {
      setFilteredModels(models);
    }
  }, [selectedBrandId, models, selectedModelId, setValue]);

  // Filter generations based on selected model
  useEffect(() => {
    if (selectedModelId) {
      const filtered = generations.filter(generation => generation.model_id === selectedModelId);
      setFilteredGenerations(filtered);
      
      // Reset generation selection if the current generation doesn't belong to the selected model
      if (selectedGenerationId && !filtered.some(generation => generation.id === selectedGenerationId)) {
        setSelectedGenerationId('');
        setValue('variation_id', undefined);
      }
    } else {
      setFilteredGenerations(generations);
    }
  }, [selectedModelId, generations, selectedGenerationId, setValue]);

  // Filter variations based on selected generation
  useEffect(() => {
    if (selectedGenerationId) {
      const filtered = variations.filter(variation => variation.generation_id === selectedGenerationId);
      setFilteredVariations(filtered);
      
      // Reset variation selection if the current variation doesn't belong to the selected generation
      const variationId = watch('variation_id');
      if (variationId && !filtered.some(variation => variation.id === variationId)) {
        setValue('variation_id', undefined);
      }
    } else {
      setFilteredVariations(variations);
    }
  }, [selectedGenerationId, variations, watch, setValue]);

  const onSubmit = async (data: TrimFormData) => {
    setIsSubmitting(true);
    setError(null);
    
    try {
      // Update the trim
      const { error: updateError } = await supabase
        .from('variation_trim')
        .update({
          variation_id: data.variation_id,
          trim: data.trim
        })
        .eq('id', trim.id);
        
      if (updateError) throw updateError;
      
      onSuccess();
      onClose();
    } catch (err: any) {
      console.error('Error updating trim:', err);
      setError(err.message || 'Failed to update trim. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.2 }}
          className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex justify-between items-center p-6 border-b border-gray-200">
            <h3 className="text-xl font-semibold text-gray-800">Edit Trim</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              disabled={isSubmitting}
            >
              <X size={24} />
            </button>
          </div>
          
          <form onSubmit={handleSubmit(onSubmit)} className="p-6">
            <div className="mb-6">
              <label htmlFor="brand_id" className="block text-sm font-medium text-gray-700 mb-2">
                Brand
              </label>
              <select
                id="brand_id"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                value={selectedBrandId}
                onChange={(e) => {
                  setSelectedBrandId(e.target.value ? Number(e.target.value) : '');
                  setSelectedModelId('');
                  setSelectedGenerationId('');
                }}
              >
                <option value="">Select a brand</option>
                {brands.map((brand) => (
                  <option key={brand.brand_id} value={brand.brand_id}>
                    {brand.brand_name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="mb-6">
              <label htmlFor="model_id" className="block text-sm font-medium text-gray-700 mb-2">
                Model
              </label>
              <select
                id="model_id"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                value={selectedModelId}
                onChange={(e) => {
                  setSelectedModelId(e.target.value ? Number(e.target.value) : '');
                  setSelectedGenerationId('');
                }}
                disabled={!selectedBrandId}
              >
                <option value="">Select a model</option>
                {filteredModels.map((model) => (
                  <option key={model.id} value={model.id}>
                    {model.model_name}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="mb-6">
              <label htmlFor="generation_id" className="block text-sm font-medium text-gray-700 mb-2">
                Generation
              </label>
              <select
                id="generation_id"
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                value={selectedGenerationId}
                onChange={(e) => setSelectedGenerationId(e.target.value ? Number(e.target.value) : '')}
                disabled={!selectedModelId}
              >
                <option value="">Select a generation</option>
                {filteredGenerations.map((generation) => (
                  <option key={generation.id} value={generation.id}>
                    {generation.name} ({generation.start_production_year} - {generation.end_production_year || 'Present'})
                  </option>
                ))}
              </select>
            </div>
            
            <div className="mb-6">
              <label htmlFor="variation_id" className="block text-sm font-medium text-gray-700 mb-2">
                Variation
              </label>
              <select
                id="variation_id"
                className={`w-full px-4 py-2 border ${errors.variation_id ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500`}
                {...register('variation_id', { 
                  required: 'Variation is required',
                  valueAsNumber: true
                })}
                disabled={!selectedGenerationId}
              >
                <option value="">Select a variation</option>
                {filteredVariations.map((variation) => (
                  <option key={variation.id} value={variation.id}>
                    {variation.variation}
                  </option>
                ))}
              </select>
              {errors.variation_id && (
                <p className="mt-1 text-sm text-red-600">{errors.variation_id.message}</p>
              )}
            </div>
            
            <div className="mb-6">
              <label htmlFor="trim" className="block text-sm font-medium text-gray-700 mb-2">
                Trim Name
              </label>
              <input
                id="trim"
                type="text"
                className={`w-full px-4 py-2 border ${errors.trim ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500`}
                placeholder="Enter trim name (e.g., SE, Sport, Limited)"
                {...register('trim', { required: 'Trim name is required' })}
              />
              {errors.trim && (
                <p className="mt-1 text-sm text-red-600">{errors.trim.message}</p>
              )}
            </div>
            
            {error && (
              <div className="bg-red-50 text-red-600 p-4 rounded-md mb-6">
                {error}
              </div>
            )}
            
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 transition-colors"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default EditTrimModal;
