'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Part } from '@/app/components/parts/PartCard';

interface PartsResponse {
  parts: Part[];
  totalParts: number;
  currentPage: number;
  totalPages: number;
}

export const usePartsSearch = (initialPage = 1, limit = 12) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [parts, setParts] = useState<Part[]>([]);
  const [totalParts, setTotalParts] = useState(0);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [totalPages, setTotalPages] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get parameters from URL
  const query = searchParams?.get('query') || '';
  const sort = searchParams?.get('sort') || '';
  const filter = searchParams?.get('filter') || '';

  // Function to fetch parts data with retry logic
  const fetchParts = useCallback(async (page: number, searchQuery: string, retryCount = 0) => {
    setIsLoading(true);
    setError(null);

    try {
      // Always use the search endpoint which now uses advanced_part_search for queries
      let endpoint = `/api/parts/search?page=${page}&limit=${limit}`;

      // Add search query if present
      if (searchQuery) {
        endpoint += `&query=${encodeURIComponent(searchQuery)}`;
      }

      // Add sort parameter if present
      if (sort) {
        endpoint += `&sort=${sort}`;
      }

      // Add filter parameter if present
      if (filter) {
        endpoint += `&filter=${filter}`;
      }

      const response = await fetch(endpoint);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error fetching parts: ${response.statusText}`);
      }

      const data: PartsResponse = await response.json();

      // Check if we have valid data
      if (data && Array.isArray(data.parts)) {
        setParts(data.parts);
        setTotalParts(data.totalParts || 0);
        setCurrentPage(data.currentPage || page);
        setTotalPages(data.totalPages || 1);
      } else {
        // If we don't have valid data, set empty parts
        console.warn('Invalid data format received from API');
        setParts([]);
        setTotalParts(0);
        setCurrentPage(page);
        setTotalPages(1);
      }
    } catch (err) {
      console.error('Error fetching parts:', err);

      // Retry logic for transient errors
      if (retryCount < 3) {
        console.log(`Retrying fetch (attempt ${retryCount + 1})...`);
        setTimeout(() => {
          fetchParts(page, searchQuery, retryCount + 1);
        }, 1000 * (retryCount + 1)); // Exponential backoff
        return;
      }

      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setParts([]);
      setTotalParts(0);
      setCurrentPage(page);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  }, [limit]);

  // Update URL when search query changes
  const updateSearchQuery = useCallback((searchQuery: string) => {
    const params = new URLSearchParams(searchParams?.toString());

    if (searchQuery) {
      params.set('query', searchQuery);
    } else {
      params.delete('query');
    }

    // Reset to page 1 when search query changes
    params.set('page', '1');

    // Update the URL without refreshing the page
    router.push(`/parts?${params.toString()}`, { scroll: false });
  }, [router, searchParams]);

  // Handle page change
  const handlePageChange = useCallback((newPage: number) => {
    const params = new URLSearchParams(searchParams?.toString());
    params.set('page', newPage.toString());
    // Remove { scroll: false } to allow the page to scroll to top on navigation
    router.push(`/parts?${params.toString()}`);
  }, [router, searchParams]);

  // Effect to fetch parts when URL params change
  useEffect(() => {
    const page = parseInt(searchParams?.get('page') || '1', 10);
    setCurrentPage(page);
    fetchParts(page, query);

    // Log the current URL parameters for debugging
    console.log('URL Parameters:', {
      page,
      query,
      sort,
      filter
    });
  }, [fetchParts, searchParams, query, sort, filter]);

  return {
    parts,
    setParts,
    totalParts,
    setTotalParts,
    currentPage,
    totalPages,
    isLoading,
    error,
    updateSearchQuery,
    handlePageChange,
    fetchParts
  };
};

export default usePartsSearch;
