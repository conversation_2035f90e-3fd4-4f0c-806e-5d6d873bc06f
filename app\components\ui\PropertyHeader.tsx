import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Search, Menu, User, Bell } from 'lucide-react';
import { motion } from 'framer-motion';

interface PropertyHeaderProps {
  onMenuClick?: () => void;
  userName?: string;
  userImage?: string;
}

const PropertyHeader: React.FC<PropertyHeaderProps> = ({
  onMenuClick,
  userName = 'Heater Clark',
  userImage = '/images/avatar-placeholder.jpg',
}) => {
  return (
    <motion.header
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="bg-white py-4 px-4 shadow-sm sticky top-0 z-50"
    >
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        {/* Logo and Navigation */}
        <div className="flex items-center">
          <Link href="/" className="mr-8">
            <h1 className="text-xl font-bold">
              house<sup>2</sup>
            </h1>
          </Link>
          
          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-6">
            <Link href="/new-buildings" className="text-gray-600 hover:text-gray-900">
              New buildings
            </Link>
            <Link href="/secondary" className="text-gray-600 hover:text-gray-900">
              Secondary
            </Link>
            <Link href="/commercial" className="text-gray-600 hover:text-gray-900">
              Commercial
            </Link>
            <Link href="/searches" className="text-gray-600 hover:text-gray-900">
              Searches
            </Link>
            <Link href="/mortgage" className="text-gray-600 hover:text-gray-900">
              Mortgage
            </Link>
          </nav>
        </div>
        
        {/* Mobile Menu Button */}
        <button 
          className="md:hidden p-2 rounded-full hover:bg-gray-100"
          onClick={onMenuClick}
          aria-label="Open menu"
        >
          <Menu size={24} />
        </button>
        
        {/* User Profile */}
        <div className="hidden md:flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="relative w-8 h-8 rounded-full overflow-hidden">
              <Image 
                src={userImage} 
                alt={userName}
                fill
                className="object-cover"
              />
            </div>
            <span className="text-sm font-medium">{userName}</span>
          </div>
        </div>
      </div>
    </motion.header>
  );
};

export default PropertyHeader;
