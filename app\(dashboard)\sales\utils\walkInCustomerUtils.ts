'use client';

import { createClient } from '@/app/libs/supabase/client';

export interface WalkInCustomer {
  id: string;
  name: string;
  phone_number: string;
  client_type: 'cash';
  category_id: string;
}

export interface ExistingCustomerCheck {
  exists: boolean;
  customer?: WalkInCustomer;
}

/**
 * Check if a customer with the given phone number already exists
 */
export async function checkExistingCustomer(phoneNumber: string): Promise<ExistingCustomerCheck> {
  try {
    const supabase = createClient();
    
    // Clean and format the phone number for consistent searching
    const cleanPhone = phoneNumber.replace(/\s+/g, '').replace(/[^\d+]/g, '');
    
    // Search for existing customer by phone number
    const { data, error } = await supabase
      .from('clients')
      .select(`
        id,
        name,
        phone_number,
        client_type,
        category_id,
        client_categories(name)
      `)
      .eq('phone_number', cleanPhone)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking existing customer:', error);
      throw new Error('Failed to check existing customer');
    }

    if (data) {
      return {
        exists: true,
        customer: {
          id: data.id,
          name: data.name,
          phone_number: data.phone_number,
          client_type: data.client_type as 'cash',
          category_id: data.category_id
        }
      };
    }

    return { exists: false };
  } catch (error) {
    console.error('Error in checkExistingCustomer:', error);
    throw error;
  }
}

/**
 * Get the default cash client category ID
 */
async function getDefaultCashClientCategory(): Promise<string> {
  try {
    const supabase = createClient();
    
    // Look for a "Walk-in Customers" or "Cash Clients" category
    let { data, error } = await supabase
      .from('client_categories')
      .select('id, name')
      .ilike('name', '%walk%in%')
      .single();

    if (error || !data) {
      // Try "Cash Clients"
      const { data: cashData, error: cashError } = await supabase
        .from('client_categories')
        .select('id, name')
        .ilike('name', '%cash%')
        .single();

      if (cashError || !cashData) {
        // Get the first available category as fallback
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('client_categories')
          .select('id, name')
          .limit(1)
          .single();

        if (fallbackError || !fallbackData) {
          throw new Error('No client categories found. Please create a client category first.');
        }

        return fallbackData.id;
      }

      return cashData.id;
    }

    return data.id;
  } catch (error) {
    console.error('Error getting default cash client category:', error);
    throw error;
  }
}

/**
 * Register a new walk-in customer
 */
export async function registerWalkInCustomer(
  name: string, 
  phoneNumber: string
): Promise<WalkInCustomer> {
  try {
    const supabase = createClient();
    
    // Clean and format the phone number
    const cleanPhone = phoneNumber.replace(/\s+/g, '').replace(/[^\d+]/g, '');
    
    // Validate inputs
    if (!name.trim()) {
      throw new Error('Customer name is required');
    }
    
    if (!cleanPhone) {
      throw new Error('Customer phone number is required');
    }

    // Get the default category for cash clients
    const categoryId = await getDefaultCashClientCategory();

    // Create the new customer
    const { data, error } = await supabase
      .from('clients')
      .insert({
        name: name.trim(),
        phone_number: cleanPhone,
        client_type: 'cash',
        category_id: categoryId,
        can_receive_credit: false
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating walk-in customer:', error);
      
      // Check if it's a duplicate phone number error
      if (error.code === '23505' && error.message.includes('phone_number')) {
        throw new Error('A customer with this phone number already exists');
      }
      
      throw new Error('Failed to register customer');
    }

    return {
      id: data.id,
      name: data.name,
      phone_number: data.phone_number,
      client_type: 'cash',
      category_id: data.category_id
    };
  } catch (error) {
    console.error('Error in registerWalkInCustomer:', error);
    throw error;
  }
}

/**
 * Search for customers by phone number with partial matching
 */
export async function searchCustomersByPhone(phoneNumber: string): Promise<WalkInCustomer[]> {
  try {
    const supabase = createClient();
    
    // Clean the phone number for searching
    const cleanPhone = phoneNumber.replace(/\s+/g, '').replace(/[^\d+]/g, '');
    
    if (cleanPhone.length < 3) {
      return [];
    }

    // Search for customers with phone numbers containing the search term
    const { data, error } = await supabase
      .from('clients')
      .select(`
        id,
        name,
        phone_number,
        client_type,
        category_id
      `)
      .ilike('phone_number', `%${cleanPhone}%`)
      .eq('client_type', 'cash')
      .limit(10);

    if (error) {
      console.error('Error searching customers by phone:', error);
      throw new Error('Failed to search customers');
    }

    return data.map(customer => ({
      id: customer.id,
      name: customer.name,
      phone_number: customer.phone_number,
      client_type: 'cash' as const,
      category_id: customer.category_id
    }));
  } catch (error) {
    console.error('Error in searchCustomersByPhone:', error);
    throw error;
  }
}
