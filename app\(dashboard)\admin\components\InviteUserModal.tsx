'use client';

import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { createClient } from '@/app/libs/supabase/client';

interface Role {
  id: string;
  name: string;
  description?: string;
}

interface InviteUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInvite: (email: string, roleId: string) => Promise<void>;
}

export default function InviteUserModal({ isOpen, onClose, onInvite }: InviteUserModalProps) {
  const [email, setEmail] = useState('');
  const [selectedRoleId, setSelectedRoleId] = useState('');
  const [roles, setRoles] = useState<Role[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Fetch available roles when the modal opens
  useEffect(() => {
    if (isOpen) {
      fetchRoles();
    }
  }, [isOpen]);

  const fetchRoles = async () => {
    setIsLoading(true);
    try {
      console.log('Fetching roles...');
      const supabase = createClient();
      
      // Use the get_all_roles database function which has security definer privileges
      const { data, error } = await supabase
        .rpc('get_all_roles');
      
      if (error) {
        console.error('Error fetching roles:', error);
        throw error;
      }

      console.log('Roles fetched successfully:', data);
      if (data && data.length > 0) {
        setRoles(data);
        // Set default selected role to the first one
        if (!selectedRoleId && data.length > 0) {
          setSelectedRoleId(data[0].id);
        }
      } else {
        console.log('No roles returned from database');
      }
    } catch (err: any) {
      console.error('Failed to load roles:', err);
      setError('Failed to load roles: ' + (err.message || 'Unknown error'));
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    // Basic email validation
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setError('Please enter a valid email address');
      return;
    }

    // Validate role selection
    if (!selectedRoleId) {
      setError('Please select a role for the user');
      return;
    }

    try {
      setIsSubmitting(true);
      await onInvite(email, selectedRoleId);
      setSuccess(`Invitation sent to ${email}`);
      setEmail('');
      setSelectedRoleId(roles.length > 0 ? roles[0].id : '');
      setTimeout(() => {
        onClose();
        setSuccess('');
      }, 2000);
    } catch (err: any) {
      setError(err.message || 'Failed to send invitation');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-lg font-semibold">Invite User</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4">
          <div className="mb-4">
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500"
              disabled={isSubmitting || isLoading}
            />
            <p className="mt-1 text-sm text-gray-500">
              An invitation link will be sent to this email address.
            </p>
          </div>

          <div className="mb-4">
            <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
              User Role
            </label>
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 border-t-2 border-indigo-500 rounded-full animate-spin"></div>
                <span className="text-sm text-gray-500">Loading roles...</span>
              </div>
            ) : (
              <select
                id="role"
                value={selectedRoleId}
                onChange={(e) => setSelectedRoleId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500"
                disabled={isSubmitting || isLoading || roles.length === 0}
              >
                {roles.length === 0 ? (
                  <option value="">No roles available</option>
                ) : (
                  roles.map((role) => (
                    <option key={role.id} value={role.id}>
                      {role.name}{role.description ? ` - ${role.description}` : ''}
                    </option>
                  ))
                )}
              </select>
            )}
            <p className="mt-1 text-sm text-gray-500">
              Select the role to assign to this user.
            </p>
          </div>

          {error && (
            <div className="mb-4 p-2 bg-red-50 text-red-700 rounded-md text-sm">
              {error}
            </div>
          )}

          {success && (
            <div className="mb-4 p-2 bg-green-50 text-green-700 rounded-md text-sm">
              {success}
            </div>
          )}

          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Sending...' : 'Send Invitation'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
