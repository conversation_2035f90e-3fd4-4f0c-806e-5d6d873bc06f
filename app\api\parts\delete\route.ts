import { NextRequest, NextResponse } from 'next/server';
// Import the dedicated admin client instead of the browser client
import { supabaseAdmin } from '@/app/libs/supabase/admin'; // Adjust path if needed

// Helper function to ensure logs are properly displayed in serverless environments
function log(message: string) {
  const timestamp = new Date().toISOString(); // Use ISO format for better sorting/parsing
  const logMessage = `[${timestamp}] ${message}`;
  // Use console.log for standard logging in Next.js API routes
  console.log(logMessage);
  // Returning the message might be useful if you aggregate logs elsewhere
  return logMessage;
}

export async function DELETE(request: NextRequest) {
  log('--- Starting DELETE Request ---');
  try {
    // Destructure request body - remove unused variables if confirmed not needed
    const { partId /*, userId */ } = await request.json(); // Keep userId commented if potentially needed later for auth
    
    if (!partId) {
      log('❌ Error: Part ID is required');
      return NextResponse.json({ error: 'Part ID is required' }, { status: 400 });
    }
    log(`Received request to delete part with ID: ${partId}`);

    // Use the admin client initialized with the Service Role Key
    const supabase = supabaseAdmin;
    const bucketName = 'car-part-images'; // Define bucket name clearly

    // --- 1. Fetch Part Data (Optional, for context or pre-checks) ---
    // This step isn't strictly required for deletion but can be useful.
    const { data: partData, error: partError } = await supabase
      .from('parts')
      .select('id') // Select only necessary fields, e.g., 'id'
      .eq('id', partId)
      .single();
      
    if (partError || !partData) {
      // Log specific error type (e.g., not found vs. other DB error)
      const errorMessage = partError ? `Database error: ${partError.message}` : 'Part not found';
      log(`❌ Error fetching part data for ID ${partId}: ${errorMessage}`);
      // Return appropriate status code (e.g., 404 if not found, 500 otherwise)
      const status = partError && partError.code === 'PGRST116' ? 404 : 500;
      return NextResponse.json({
        error: 'Failed to fetch part data or part not found',
        details: partError?.message || 'Part not found',
      }, { status });
    }
    log(`✅ Found part data for ID: ${partId}`);


    // --- 2. Fetch Associated Image Records ---
    log(`Fetching image records associated with part ID: ${partId}`);
    const { data: imageData, error: imageError } = await supabase
      .from('part_images')
      .select('id, image_url') // Select only needed fields
      .eq('part_id', partId);
      
    if (imageError) {
      log(`❌ Error fetching part images from database: ${imageError.message}`);
      // Decide if you should proceed to delete the part record even if images can't be fetched/deleted
      // Returning an error here prevents deleting the part if image fetching fails.
      return NextResponse.json({
        error: 'Failed to fetch associated image data',
        details: imageError.message,
      }, { status: 500 });
    }

    log(`Found ${imageData?.length || 0} image records associated with part ${partId}.`);
    let allImagesDeletedSuccessfully = true; // Track overall success

    // --- 3. Delete Images from Storage ---
      if (imageData && imageData.length > 0) {
      log('--- Starting Image Deletion Loop ---');
        for (const img of imageData) {
        log(`Processing image record ID: ${img.id}, URL: ${img.image_url}`);

        if (!img.image_url) {
          log(`🟡 Skipping image record ID: ${img.id} - No image URL found.`);
          continue; // Skip to the next image record
        }

        let correctStoragePath = '';
        try {
          // --- 3a. Extract Correct Storage Path ---
          const urlString = img.image_url;
          // Check if it's a full Supabase public URL for the target bucket
          const expectedPrefix = `/storage/v1/object/public/${bucketName}/`;
          if (urlString.includes(expectedPrefix)) {
             const urlObject = new URL(urlString);
             const pathParts = urlObject.pathname.split('/');
             const bucketIndex = pathParts.indexOf(bucketName);
             if (bucketIndex !== -1 && bucketIndex + 1 < pathParts.length) {
               // Join parts *after* the bucket name
               correctStoragePath = pathParts.slice(bucketIndex + 1).join('/');
               // Sanitize: Remove leading/trailing slashes just in case
               correctStoragePath = correctStoragePath.replace(/^\/|\/$/g, '');
             }
          } else if (!urlString.includes('/') && urlString.includes('.')) {
             // Fallback: Assume it might be just the filename (if legacy data exists)
             correctStoragePath = urlString;
             log(`🟡 Assuming filename-only storage path for URL: ${urlString}`);
          }

          if (!correctStoragePath || !correctStoragePath.includes('.')) {
            log(`⚠️ Could not determine a valid file path from URL: "${urlString}". Path extracted: "${correctStoragePath}". Skipping storage deletion for this image.`);
            allImagesDeletedSuccessfully = false; // Mark failure for this image
            continue; // Skip to next image
          }

          log(`Extracted storage path: "${correctStoragePath}"`);

          // --- 3b. Attempt Storage Deletion ---
          // Now using the admin client with service_role permissions
          log(`Attempting deletion from bucket "${bucketName}" with path: "${correctStoragePath}"`);
          const { data: removeData, error: deleteError } = await supabase.storage
            .from(bucketName)
            .remove([correctStoragePath]); // Pass the single correct path in an array

          if (deleteError) {
            // If this still errors, double-check the bucket policy syntax and service_role key itself
            log(`❌ Failed to delete image from storage. Path: "${correctStoragePath}". Error: ${deleteError.message}`);
            allImagesDeletedSuccessfully = false; // Mark failure
            // Continue to the next image instead of stopping the whole process
          } else {
            log(`✅ Successfully initiated delete for path: "${correctStoragePath}"`);
            log(`Remove operation response: ${JSON.stringify(removeData)}`); // Log success data

            // --- 3c. Verification (Optional but Recommended) ---
            const folderPath = correctStoragePath.includes('/') ? correctStoragePath.split('/').slice(0, -1).join('/') : '';
            const filename = correctStoragePath.split('/').pop() || '';

            log(`Verifying deletion via list: Folder: "${folderPath}", File: "${filename}"`);
            const { data: filesAfterDelete, error: verifyError } = await supabase.storage
              .from(bucketName)
              .list(folderPath, { search: filename }); // Use search for efficiency

            if (verifyError) {
                log(`🟡 Warning: Error verifying deletion via list: ${verifyError.message}. Continuing cautiously.`);
            } else if (filesAfterDelete && filesAfterDelete.length > 0) {
                // Check if the specific file name still exists
                const stillExists = filesAfterDelete.some(f => f.name === filename);
                if (stillExists) {
                    log(`❌ Verification Failed: List API shows file "${filename}" still exists in "${folderPath}".`);
                    allImagesDeletedSuccessfully = false; // Mark failure if verification fails
                } else {
                     log(`✅ Verification Succeeded: List API confirms file "${filename}" is gone.`);
              }
            } else {
                log(`✅ Verification Succeeded: List API returned no results for "${filename}" in "${folderPath}".`);
            }
            // --- End Verification ---
          }
        } catch (processingError) {
          log(`❌ Unexpected error processing image URL "${img.image_url}": ${processingError instanceof Error ? processingError.message : processingError}. Skipping storage deletion.`);
          allImagesDeletedSuccessfully = false; // Mark failure
          continue; // Skip to next image
        }
      } // End of image loop
      log('--- Finished Image Deletion Loop ---');
    } else {
      log('No image records found in database to delete from storage.');
    }

    // --- 4. Delete Image Records from Database ---
    if (imageData && imageData.length > 0) {
        const imageIdsToDelete = imageData.map(img => img.id);
        log(`Attempting to delete ${imageIdsToDelete.length} image records from 'part_images' table...`);
        const { error: deleteImageRecordsError } = await supabase
        .from('part_images')
        .delete()
            .in('id', imageIdsToDelete);
        
        if (deleteImageRecordsError) {
            log(`❌ Error deleting image records from database: ${deleteImageRecordsError.message}`);
            // Consider if this should prevent the part deletion. For now, we log and continue.
      } else {
            log(`✅ Successfully deleted image records from database.`);
      }
    }
    
    // --- 5. Delete the Part Record from Database ---
    // The database trigger will automatically handle all related data cleanup
    log(`Attempting to delete part record ID: ${partId} from 'parts' table (trigger will handle cleanup)...`);
    const { error: deletePartError } = await supabase
      .from('parts')
      .delete()
      .eq('id', partId);
      
    if (deletePartError) {
      log(`❌ Error deleting part record from database: ${deletePartError.message}`);
      return NextResponse.json({
        error: 'Failed to delete part record from database after attempting image deletion',
        details: deletePartError.message,
        imageDeletionStatus: allImagesDeletedSuccessfully ? 'All attempted images deleted' : 'Some image deletions may have failed',
      }, { status: 500 });
    }

    log(`✅ Successfully deleted part record ID: ${partId} from database.`);

    // --- 6. Return Final Response ---
    log('--- DELETE Request Completed Successfully ---');
    return NextResponse.json({
      success: true,
      message: `Part ${partId} and associated data deleted.`,
      imageDeletionStatus: allImagesDeletedSuccessfully ? 'All attempted images deleted successfully' : 'Note: Some image deletions may have failed or could not be verified.',
    });
    
  } catch (error) {
    // Catch unexpected errors during request processing (e.g., JSON parsing)
    log(`❌ Unexpected error during DELETE request: ${error instanceof Error ? error.message : error}`);
    // Also catch errors from admin client creation if env vars are missing
    if (error instanceof Error && error.message.includes('Supabase URL or Service Role Key')) {
        return NextResponse.json({ error: error.message }, { status: 500 });
    }
    return NextResponse.json({ 
      error: 'An unexpected error occurred during the deletion process.',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}