import { Metadata } from 'next';
import { createClient } from '@/app/libs/supabase/client';

// Generate metadata for the part page
export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  const partId = params.id;

  try {
    const supabase = createClient();

    // Get the basic part information
    const { data, error } = await supabase
      .from('parts')
      .select(`
        id,
        title,
        partnumber_group,
        category_id,
        description
      `)
      .eq('id', partId)
      .single();

    if (error || !data) {
      return {
        title: 'Part Not Found | Autoflow',
        description: 'The requested auto part could not be found.'
      };
    }

    // Description is already fetched from the parts table

    // Get images from part_images
    const { data: imagesData } = await supabase
      .from('part_images')
      .select('image_url')
      .eq('part_id', data.id)
      .limit(1);

    // Get price data
    const { data: conditionData } = await supabase
      .from('parts_condition')
      .select('id')
      .eq('part_id', data.id)
      .single();

    let price = 0;
    if (conditionData?.id) {
      const { data: priceData } = await supabase
        .from('part_price')
        .select('price')
        .eq('condition_id', conditionData.id)
        .single();

      price = priceData?.price || 848000; // Default price if not found
    }

    // Get part number
    const { data: partNumberData } = await supabase
      .from('part_compatibility_groups')
      .select('part_number')
      .eq('id', data.partnumber_group)
      .single();

    // Get category name
    const { data: categoryData } = await supabase
      .from('categories')
      .select('name')
      .eq('id', data.category_id)
      .single();

    const title = data.title || 'Auto Part';
    const partNumber = partNumberData?.part_number || '';
    const category = categoryData?.name || 'Auto Part';
    const description = data.description ||
      `${title} (Part #: ${partNumber}) - View details, specifications, and compatibility information for this ${category}. Available at Autoflow Kenya.`;
    const imageUrl = imagesData?.[0]?.image_url || '';

    // Generate structured data for the product
    const structuredData = {
      '@context': 'https://schema.org',
      '@type': 'Product',
      'name': title,
      'description': description,
      'image': imageUrl,
      'sku': partNumber,
      'brand': {
        '@type': 'Brand',
        'name': 'Autoflow'
      },
      'category': category,
      'offers': {
        '@type': 'Offer',
        'price': price,
        'priceCurrency': 'KES',
        'availability': 'https://schema.org/InStock'
      }
    };

    return {
      title: `${title} | Autoflow Kenya`,
      description,
      openGraph: {
        title: `${title} | Autoflow Kenya`,
        description,
        images: imageUrl ? [imageUrl] : [],
        type: 'website',
      },
      twitter: {
        card: 'summary_large_image',
        title: `${title} | Autoflow Kenya`,
        description,
        images: imageUrl ? [imageUrl] : [],
      },
      other: {
        'structured-data': JSON.stringify(structuredData),
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Auto Parts | Autoflow Kenya',
      description: 'Browse our extensive collection of quality auto parts for all major car brands in Kenya.',
    };
  }
}
