'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';

interface DashboardHeaderProps {
  heading: string;
  text?: string;
  icon?: React.ReactNode;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({ 
  heading, 
  text, 
  icon 
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white p-6 rounded-lg border border-gray-200 mb-6 shadow-sm"
    >
      <div className="flex items-center">
        {icon && (
          <div className="mr-4 text-primary">
            {icon}
          </div>
        )}
        <div>
          <h1 className="text-2xl font-bold text-gray-800">{heading}</h1>
          {text && <p className="text-gray-600">{text}</p>}
        </div>
      </div>
    </motion.div>
  );
};

export default DashboardHeader;
