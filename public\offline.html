<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - AutoFlow</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background-color: #f9fafb;
            color: #1f2937;
        }

        .container {
            text-align: center;
            padding: 2rem;
            max-width: 600px;
        }

        .icon {
            width: 64px;
            height: 64px;
            margin-bottom: 1.5rem;
            color: #059669;
        }

        h1 {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        p {
            font-size: 1.125rem;
            line-height: 1.75;
            color: #4b5563;
            margin-bottom: 2rem;
        }

        .button {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background-color: #059669;
            color: white;
            text-decoration: none;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .button:hover {
            background-color: #047857;
        }

        .cached-content {
            margin-top: 2rem;
            padding: 1rem;
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .cached-content h2 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .cached-content ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .cached-content li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e5e7eb;
        }

        .cached-content li:last-child {
            border-bottom: none;
        }

        .cached-content a {
            color: #059669;
            text-decoration: none;
        }

        .cached-content a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <svg class="icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        <h1>You're Offline</h1>
        <p>It seems you've lost your internet connection. Don't worry, you can still access your cached content while offline.</p>
        <a href="/" class="button">Try Again</a>

        <div class="cached-content">
            <h2>Available Offline</h2>
            <ul id="cached-pages">
                <li><a href="/">Home</a></li>
                <li><a href="/shop">Shop</a></li>
                <li><a href="/cart">Cart</a></li>
                <li><a href="/account">Account</a></li>
            </ul>
        </div>
    </div>

    <script>
        // Check if we're back online
        window.addEventListener('online', () => {
            window.location.reload();
        });

        // Update cached pages list
        async function updateCachedPages() {
            const cache = await caches.open('dynamic-cache-v1');
            const requests = await cache.keys();
            const pages = requests
                .filter(request => request.url.endsWith('.html'))
                .map(request => {
                    const url = new URL(request.url);
                    return {
                        path: url.pathname,
                        title: url.pathname.split('/').pop() || 'Home'
                    };
                });

            const cachedPagesList = document.getElementById('cached-pages');
            if (cachedPagesList) {
                cachedPagesList.innerHTML = pages
                    .map(page => `<li><a href="${page.path}">${page.title}</a></li>`)
                    .join('');
            }
        }

        updateCachedPages();
    </script>
</body>
</html> 