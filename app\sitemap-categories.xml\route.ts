import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/client';

export async function GET(request: NextRequest) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://autoflow.parts';

    console.log('Generating categories sitemap...');

    const categoryPages: any[] = [];

    try {
      const supabase = createClient();

      // Fetch all active categories
      try {
        const { data: categories, error: categoriesError } = await supabase
          .from('categories')
          .select('id, name, updated_at, created_at')
          .eq('is_active', true)
          .order('name');

        if (categoriesError) {
          console.error('Error fetching categories:', categoriesError);
        } else if (categories && categories.length > 0) {
          categories.forEach(category => {
            const slug = category.name
              .toLowerCase()
              .replace(/[^a-z0-9\s-]/g, '')
              .replace(/\s+/g, '-')
              .replace(/-+/g, '-')
              .replace(/^-+|-+$/g, '');

            categoryPages.push({
              url: `${baseUrl}/categories/${slug}`,
              lastModified: category.updated_at || category.created_at || new Date().toISOString(),
              changeFrequency: 'weekly',
              priority: '0.7'
            });
          });
        }
      } catch (categoryError) {
        console.error('Error processing categories:', categoryError);
      }

      // Fetch all active brands
      try {
        const { data: brands, error: brandsError } = await supabase
          .from('car_brands')
          .select('brand_id, brand_name, updated_at, created_at')
          .order('brand_name');

        if (brandsError) {
          console.error('Error fetching brands:', brandsError);
        } else if (brands && brands.length > 0) {
          brands.forEach(brand => {
            const slug = brand.brand_name
              .toLowerCase()
              .replace(/[^a-z0-9\s-]/g, '')
              .replace(/\s+/g, '-')
              .replace(/-+/g, '-')
              .replace(/^-+|-+$/g, '');

            categoryPages.push({
              url: `${baseUrl}/brands/${slug}`,
              lastModified: brand.updated_at || brand.created_at || new Date().toISOString(),
              changeFrequency: 'weekly',
              priority: '0.6'
            });
          });
        }
      } catch (brandError) {
        console.error('Error processing brands:', brandError);
      }

      // Fetch all active models
      try {
        const { data: models, error: modelsError } = await supabase
          .from('car_models')
          .select('id, model_name, updated_at, created_at, car_brands!brand_id(brand_name)')
          .order('model_name');

        if (modelsError) {
          console.error('Error fetching models:', modelsError);
        } else if (models && models.length > 0) {
          models.forEach(model => {
            const brandName = (model.car_brands as any)?.brand_name || 'unknown';
            const brandSlug = brandName
              .toLowerCase()
              .replace(/[^a-z0-9\s-]/g, '')
              .replace(/\s+/g, '-')
              .replace(/-+/g, '-')
              .replace(/^-+|-+$/g, '');

            const modelSlug = model.model_name
              .toLowerCase()
              .replace(/[^a-z0-9\s-]/g, '')
              .replace(/\s+/g, '-')
              .replace(/-+/g, '-')
              .replace(/^-+|-+$/g, '');

            categoryPages.push({
              url: `${baseUrl}/brands/${brandSlug}/${modelSlug}`,
              lastModified: model.updated_at || model.created_at || new Date().toISOString(),
              changeFrequency: 'weekly',
              priority: '0.6'
            });
          });
        }
      } catch (modelError) {
        console.error('Error processing models:', modelError);
      }

    } catch (dbError) {
      console.error('Database connection error for categories sitemap:', dbError);
      // Continue with empty categoryPages array
    }

    // Generate XML sitemap
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${categoryPages.map(page => `  <url>
    <loc>${page.url}</loc>
    <lastmod>${page.lastModified}</lastmod>
    <changefreq>${page.changeFrequency}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('\n')}
</urlset>`;

    console.log(`Generated categories sitemap with ${categoryPages.length} pages`);

    return new NextResponse(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=7200, s-maxage=7200', // Cache for 2 hours
      },
    });

  } catch (error) {
    console.error('Error generating categories sitemap:', error);
    
    // Return empty sitemap on error
    const emptySitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
</urlset>`;

    return new NextResponse(emptySitemap, {
      headers: {
        'Content-Type': 'application/xml',
      },
    });
  }
}
