-- Add created_by column to sales table
-- This column will store the user ID of the staff member who created the sale

-- First, check if the column doesn't already exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'sales' 
        AND column_name = 'created_by'
        AND table_schema = 'public'
    ) THEN
        -- Add the created_by column
        ALTER TABLE public.sales 
        ADD COLUMN created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL;
        
        -- Add an index for better performance
        CREATE INDEX IF NOT EXISTS idx_sales_created_by ON public.sales(created_by);
        
        -- Add a comment to document the column
        COMMENT ON COLUMN public.sales.created_by IS 'User ID of the staff member who created this sale';
        
        RAISE NOTICE 'Successfully added created_by column to sales table';
    ELSE
        RAISE NOTICE 'created_by column already exists in sales table';
    END IF;
END $$;
