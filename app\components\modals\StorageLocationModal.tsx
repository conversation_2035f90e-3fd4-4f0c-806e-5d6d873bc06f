'use client';

import React from 'react';
import Modal from '@/app/components/ui/Modal';
import StorageLocationForm from '@/app/components/forms/StorageLocationForm';

interface StorageLocationModalProps {
  isOpen: boolean;
  onClose: () => void;
  partId: number;
  partName: string;
}

const StorageLocationModal: React.FC<StorageLocationModalProps> = ({
  isOpen,
  onClose,
  partId,
  partName
}) => {
  const handleSuccess = () => {
    // Wait a moment to show success message before closing
    setTimeout(() => {
      onClose();
    }, 1500);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      animationType="slide-in-bottom"
      width="w-full md:max-w-2xl"
    >
      <StorageLocationForm
        partId={partId}
        partName={partName}
        onSuccess={handleSuccess}
        onCancel={onClose}
      />
    </Modal>
  );
};

export default StorageLocationModal;
