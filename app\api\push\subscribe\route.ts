import { NextResponse } from 'next/server';
import { createClient } from '@/app/libs/supabase/server';

export async function POST(request: Request) {
  try {
    const subscription = await request.json();
    const supabase = createClient();

    // Store subscription in database
    const { error } = await supabase
      .from('push_subscriptions')
      .upsert({
        endpoint: subscription.endpoint,
        auth: subscription.keys.auth,
        p256dh: subscription.keys.p256dh,
        user_id: (await supabase.auth.getUser()).data.user?.id,
        created_at: new Date().toISOString(),
      });

    if (error) throw error;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error storing push subscription:', error);
    return NextResponse.json(
      { error: 'Failed to store subscription' },
      { status: 500 }
    );
  }
} 