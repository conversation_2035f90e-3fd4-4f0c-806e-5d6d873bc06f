export interface Category extends FlatCategory {
  children?: Category[];
}

export interface BrandModelSelectionProps {
  control: any; // Replace 'any' with the actual type
  watch: (field: string) => string | undefined;
  loadingStates: {
    brands: boolean;
    models: boolean;
    generations: boolean;
    variations: boolean;
    trims: boolean;
  };
  onSelectionChange: (field: string, value: string) => void;
  error?: string;
}

export interface PartFormValues {
  partNumber: string;
  stock: number;
  price: number;
  discountPrice?: number;
  condition: 'New' | 'Used' | 'Both';
  imageUrl: string;
  imageType: string;
  categoryId: string;
  trimId: string;
  generationId: string;
  variationId: string;
  brandId: string;
  modelId: string;
  attributes: Record<string, any>;
  categoryAttributes: Array<{
    id: number;
    value: string;
  }>;
  selectedCategory: string;
  isCheckingPartNumber: boolean;
  showVehicleSelection: boolean;
  requirePartNumber: boolean;
  additionalEngineCodes: string[];
  newStock?: number;
  newPrice?: number;
  newDiscountPrice?: number;
  usedStock?: number;
  usedPrice?: number;
  usedDiscountPrice?: number;
}

export interface CategoryAttribute {
  id: number;
  category_id?: number;
  attribute: string;
  input_type: 'text' | 'radio' | 'dropdown' | 'checkbox' | 'number' | 'date';
  value?: string;
}

export interface AttributeInputOption {
  id?: number;
  attribute_id?: number; //  Use the correct names from the database!
  option_value: string;
}

export interface Attribute {
  id: number;
  attribute: string;
  input_type: 'text' | 'radio' | 'dropdown' | 'checkbox' | 'number' | 'date';
  options?: {
    id: number;
    option_value: string;
  }[];
}

export interface FlatCategory {
  id: number;
  name: string;
  parentId: number | null;
  parentName: string | null;
  attributes: Attribute[];
  requirePartNumber?: boolean;
}

export interface Brand {
  id: number;
  name: string;
}

export interface Model {
  id: number;
  brandId: number;  // Foreign key to Brand
  name: string;
}

export interface Generation {
  id: number;
  model_id: number;
  name: string;
  start_production_year: number | null;
  end_production_year: number | null;
}

export interface Variation {
  id: number;
  generation_id: number;
  variation: string;
}

export interface Trim {
  id: number;
  variation_id: number;
  trim: string;
}

export interface CreatePartData {
  partnumber: string | null; // Allow null
  category_id: number | undefined; // Number, allow undefined
  title: string;
  createdBy: number;
  updatedBy: number;
  brand?: string | null; // Optional, allow null
  model?: string | null; // Optional, allow null
  stock?: number | null;  //optional and can be null
  price?: number | null; //optional and can be null
  condition?: 'New' | 'Used' | null; //optional and can be null
}

export interface CreateImageData {
    part_id: number;
    image_url: string;
    is_main_image: boolean;
    alt_text: string;
}

// Database Types
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      part_images: {
        Row: {
          alt_text: string | null
          id: number
          image_url: string
          is_main_image: boolean | null
          part_id: number
        }
        Insert: {
          alt_text?: string | null
          id?: number
          image_url: string
          is_main_image?: boolean | null
          part_id: number
        }
        Update: {
          alt_text?: string | null
          id?: number
          image_url?: string
          is_main_image?: boolean | null
          part_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "part_images_part_id_fkey"
            columns: ["part_id"]
            isOneToOne: false
            referencedRelation: "parts"
            referencedColumns: ["id"]
          }
        ]
      }
      parts: {
        Row: {
          category_id: number | null
          createdAt: string | null
          createdBy: number | null
          id: number
          partnumber: string | null
          title: string | null
          updatedAt: string | null
          updatedBy: number | null
          brand: string | null
          model: string | null
          stock: number | null
          price: number | null
          condition: string | null
        }
        Insert: {
          category_id?: number | null
          createdAt?: string | null
          createdBy?: number | null
          id?: number
          partnumber?: string | null
          title?: string | null
          updatedAt?: string | null
          updatedBy?: number | null
          brand?: string | null
          model?: string | null
          stock?: number | null
          price?: number | null
          condition?: string | null
        }
        Update: {
          category_id?: number | null
          createdAt?: string | null
          createdBy?: number | null
          id?: number
          partnumber?: string | null
          title?: string | null
          updatedAt?: string | null
          updatedBy?: number | null
          brand?: string | null
          model?: string | null
          stock?: number | null
          price?: number | null
          condition?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_parts_category_id"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "car_part_categories"
            referencedColumns: ["id"]
          }
        ]
      }
      car_part_categories: {
        Row: {
          id: number
          name: string
        }
        Insert: {
          id?: number
          name: string
        }
        Update: {
          id?: number
          name?: string
        }
        Relationships: []
      }
      car_generation: {
        Row: {
          id: number
          model_id: number
          name: string
          start_production_year: number
          end_production_year: number
        }
        Insert: {
          id: number
          model_id?: number
          name?: string
          start_production_year?: number
          end_production_year?: number
        }
        Update: {
          id?: number
          model_id?: number
          name?: string
          start_production_year?: number
          end_production_year?: number
        }
        Relationships: [
          {
            foreignKeyName: "car_generation_model_id_fkey"
            columns: ["model_id"]
            isOneToOne: false
            referencedRelation: "models"
            referencedColumns: ["id"]
          }
        ]
      }
      car_variation: {
        Row: {
          id: number
          generation_id: number
          variation: string
        }
        Insert: {
          id: number
          generation_id?: number
          variation?: string
        }
        Update: {
          id?: number
          generation_id?: number
          variation?: string
        }
        Relationships: [
          {
            foreignKeyName: "car_variation_generation_id_fkey"
            columns: ["generation_id"]
            isOneToOne: false
            referencedRelation: "car_generation"
            referencedColumns: ["id"]
          }
        ]
      }
      variation_trim: {
        Row: {
          id: number
          variation_id: number
          trim: string
        }
        Insert: {
          id?: number
          variation_id?: number
          trim?: string
        }
        Update: {
          id?: number
          variation_id?: number
          trim?: string
        }
        Relationships: [
          {
            foreignKeyName: "variation_trim_variation_id_fkey"
            columns: ["variation_id"]
            isOneToOne: false
            referencedRelation: "car_variation"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}