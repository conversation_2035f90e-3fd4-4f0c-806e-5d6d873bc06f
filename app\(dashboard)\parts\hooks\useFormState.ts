// hooks/useFormState.ts
import { useState } from 'react';
import { UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { PartFormValues } from '../types';

interface UseFormStateProps {
  setValue: UseFormSetValue<PartFormValues>;
  watch: UseFormWatch<PartFormValues>;
}

export const useFormState = ({ setValue, watch }: UseFormStateProps) => {
  const [croppedImageUrl, setCroppedImageUrl] = useState<string | null>(null);
  const [showCategorySelection, setShowCategorySelection] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [partNumberExists, setPartNumberExists] = useState(false);
  const [existingTrims, setExistingTrims] = useState<any[]>([]);
  const [showVehicleSelection, setShowVehicleSelection] = useState(false);
  const [compatibilityData, setCompatibilityData] = useState<any>(null);
  const [currentTask, setCurrentTask] = useState('');

  const handleImageUploadSuccess = async (url: string | null) => {
    setCroppedImageUrl(url || null);
    setShowCategorySelection(!!url);
    return url;
  };

  // const handleCategoryChange = (value: string) => {
  //   // Update both local state and form state
  //   setSelectedCategory(value);
  //   setValue('categoryId', value, { shouldValidate: true });
  //   console.log("Category selected:", value);
  // };

  return {
    croppedImageUrl,
    showCategorySelection,
    selectedCategory,
    partNumberExists,
    existingTrims,
    showVehicleSelection,
    compatibilityData,
    currentTask,
    handleImageUploadSuccess,
    //handleCategoryChange,  // Now expects direct string value
    setShowVehicleSelection,
    setSelectedCategory,
    setPartNumberExists,
    setExistingTrims,
    setCompatibilityData,
    setCurrentTask,
  };
};