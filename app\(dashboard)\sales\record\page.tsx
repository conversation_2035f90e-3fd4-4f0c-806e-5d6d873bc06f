'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import SalesRecordForm from '../components/SalesRecordForm';

export default function RecordSalePage() {
  const router = useRouter();

  const handleSuccess = () => {
    router.push('/sales');
  };

  const handleCancel = () => {
    router.push('/sales');
  };

  return (
    <div className="p-4 md:p-6 lg:p-8 bg-gray-100 dark:bg-gray-900 min-h-screen">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6">Record New Sale</h1>
        <SalesRecordForm onSuccess={handleSuccess} onCancel={handleCancel} />
      </div>
    </div>
  );
}
