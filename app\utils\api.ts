'use client';

import { toast } from 'react-hot-toast';

interface RequestOptions extends RequestInit {
  showToast?: boolean;
  cache?: RequestCache;
  retry?: number;
  retryDelay?: number;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
}

class ApiCache {
  private static instance: ApiCache;
  private cache: Map<string, CacheEntry<any>>;
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

  private constructor() {
    this.cache = new Map();
  }

  static getInstance(): ApiCache {
    if (!ApiCache.instance) {
      ApiCache.instance = new ApiCache();
    }
    return ApiCache.instance;
  }

  set<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now() + ttl,
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.timestamp) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  clear(): void {
    this.cache.clear();
  }
}

const apiCache = ApiCache.getInstance();

async function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export async function fetchApi<T>(
  endpoint: string,
  options: RequestOptions = {}
): Promise<T> {
  const {
    showToast = true,
    cache = 'default',
    retry = 3,
    retryDelay = 1000,
    ...fetchOptions
  } = options;

  const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
  const cacheKey = `${endpoint}-${JSON.stringify(fetchOptions)}`;

  // Check cache for GET requests
  if (fetchOptions.method === 'GET' || !fetchOptions.method) {
    const cachedData = apiCache.get<T>(cacheKey);
    if (cachedData) {
      return cachedData;
    }
  }

  let lastError: Error | null = null;
  let attempts = 0;

  while (attempts < retry) {
    try {
      const response = await fetch(`/api${endpoint}`, {
        ...fetchOptions,
        cache,
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
          ...fetchOptions.headers,
        },
      });

      if (!response.ok) {
        const error = await response.json().catch(() => ({
          message: 'An error occurred',
        }));
        throw new Error(error.message || 'Request failed');
      }

      const data = await response.json();

      // Cache successful GET responses
      if (fetchOptions.method === 'GET' || !fetchOptions.method) {
        apiCache.set(cacheKey, data);
      }

      return data as T;
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      attempts++;

      if (attempts < retry) {
        await delay(retryDelay * attempts); // Exponential backoff
        continue;
      }

      if (showToast) {
        toast.error(lastError.message);
      }
      throw lastError;
    }
  }

  throw lastError;
}

// Helper functions for common operations
export const api = {
  get: <T>(endpoint: string, options: RequestOptions = {}) =>
    fetchApi<T>(endpoint, { ...options, method: 'GET' }),

  post: <T>(endpoint: string, data: any, options: RequestOptions = {}) =>
    fetchApi<T>(endpoint, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data),
    }),

  put: <T>(endpoint: string, data: any, options: RequestOptions = {}) =>
    fetchApi<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  delete: <T>(endpoint: string, options: RequestOptions = {}) =>
    fetchApi<T>(endpoint, { ...options, method: 'DELETE' }),

  clearCache: () => apiCache.clear(),
};

// Example usage:
// const data = await fetchApi<Part[]>('/parts', { method: 'GET' });
// const result = await fetchApi<{ success: boolean }>('/cart/add', {
//   method: 'POST',
//   body: JSON.stringify({ partId: '123', quantity: 1 }),
// }); 