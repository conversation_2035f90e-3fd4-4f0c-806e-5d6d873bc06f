'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Plus, FileText } from 'lucide-react';
import DashboardHeader from './components/DashboardHeader';
import SalesStats from './components/SalesStats';
import SalesList from './components/SalesList';
import AddSaleModal from './components/modals/AddSaleModal';
import DraftsModal from './components/modals/DraftsModal';
import { SalesDraft } from './hooks/useSalesDrafts';

export default function SalesPage() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isDraftsModalOpen, setIsDraftsModalOpen] = useState(false);
  const [selectedDraft, setSelectedDraft] = useState<SalesDraft | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleLoadDraft = (draft: SalesDraft) => {
    setSelectedDraft(draft);
    setIsDraftsModalOpen(false);
    setIsAddModalOpen(true);
  };

  const handleCloseAddModal = () => {
    setIsAddModalOpen(false);
    setSelectedDraft(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader title="Sales Management" />

      <div className="container mx-auto px-4 py-8">
        {/* Action Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="flex flex-col md:flex-row justify-between items-center mb-6"
        >
          <div className="mb-4 md:mb-0">
            <h2 className="text-xl font-semibold text-gray-800">Sales Overview</h2>
            <p className="text-gray-600">Manage and track your sales transactions</p>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={() => setIsDraftsModalOpen(true)}
              className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              <FileText className="h-5 w-5 mr-2" />
              <span>Drafts</span>
            </button>
            <button
              onClick={() => setIsAddModalOpen(true)}
              className="flex items-center px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 transition-colors"
            >
              <Plus className="h-5 w-5 mr-2" />
              <span>Add Sale</span>
            </button>
          </div>

        </motion.div>

        {/* Sales Stats */}
        <SalesStats refreshTrigger={refreshTrigger} />

        {/* Sales List */}
        <SalesList
          refreshTrigger={refreshTrigger}
          onEdit={handleRefresh}
          onDelete={handleRefresh}
        />

        {/* Add Sale Modal */}
        <AddSaleModal
          isOpen={isAddModalOpen}
          onClose={handleCloseAddModal}
          onSaleAdded={handleRefresh}
          initialDraft={selectedDraft}
        />

        {/* Drafts Modal */}
        <DraftsModal
          isOpen={isDraftsModalOpen}
          onClose={() => setIsDraftsModalOpen(false)}
          onLoadDraft={handleLoadDraft}
        />
      </div>
    </div>
  );
}
