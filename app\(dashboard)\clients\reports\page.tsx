import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Download, Filter } from 'lucide-react';
import Link from 'next/link';
import ClientReports from '../components/ClientReports';

export default function ClientReportsPage() {
  return (
    <div className="p-4 md:p-6 lg:p-8 bg-gray-100 min-h-screen">
      {/* Back button */}
      <Link href="/clients" className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-6">
        <ArrowLeft className="w-4 h-4 mr-2" /> Back to Clients
      </Link>

      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col md:flex-row md:items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 mb-1">Client Reports</h1>
              <p className="text-gray-600">Analyze client data and generate reports</p>
            </div>
            <div className="mt-4 md:mt-0 flex space-x-2">
              <button className="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 flex items-center">
                <Filter className="w-4 h-4 mr-2" /> Filter
              </button>
              <button className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 flex items-center">
                <Download className="w-4 h-4 mr-2" /> Export
              </button>
            </div>
          </div>
        </div>

        <ClientReports />
      </div>
    </div>
  );
}
