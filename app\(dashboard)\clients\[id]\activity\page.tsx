import React from 'react';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import ClientActivityFeed from '../../components/ClientActivityFeed';

export default function ClientActivityPage({ params }: { params: { id: string } }) {
  const clientId = params.id;
  
  return (
    <div className="p-4 md:p-6 lg:p-8 bg-gray-100 min-h-screen">
      {/* Back button */}
      <Link href={`/clients/${clientId}`} className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-6">
        <ArrowLeft className="w-4 h-4 mr-2" /> Back to Client
      </Link>

      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h1 className="text-2xl font-semibold text-gray-900 mb-1">Client Activity History</h1>
          <p className="text-gray-600">View all activities and interactions for this client</p>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <ClientActivityFeed clientId={clientId} limit={20} showPagination={true} />
        </div>
      </div>
    </div>
  );
}
