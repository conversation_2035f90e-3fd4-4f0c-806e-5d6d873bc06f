// components/DataTable.tsx
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { 
  ChevronFirst, ChevronLast, ChevronLeft, ChevronRight, 
  Edit, Trash2, Plus, Settings, Check, X, ArrowUpDown 
} from 'lucide-react';
import Modal from '../Modal';
import { useRouter } from 'next/navigation';
import AnimatedEllipsisLoader from '../AnimatedEllipsisLoader';

interface DataTableProps {
  title?: string;
  createUrl?: string;
  readUrl: string;
  updateUrl?: string;
  deleteUrl?: string;
  columns: {
    id: string;
    header: string;
    accessor: string | ((row: any) => any);
    sortable?: boolean;
    hideable?: boolean;
  }[];
  initialVisibleColumns?: string[];
  initialData?: any[];
}

interface Pagination {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
}

const DataTable: React.FC<DataTableProps> = ({
  title,
  createUrl,
  readUrl,
  updateUrl,
  deleteUrl,
  columns,
  initialVisibleColumns,
  initialData = [],
}) => {
  const router = useRouter();
  const [data, setData] = useState<any[]>(initialData);
  const [isLoading, setIsLoading] = useState(false);
  const [pagination, setPagination] = useState<Pagination>({
    currentPage: 1,
    totalPages: 1,
    pageSize: 10,
    totalItems: 0,
  });
  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null);
  const [visibleColumns, setVisibleColumns] = useState<string[]>(
    initialVisibleColumns || columns.map(col => col.id)
  );
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalUrl, setModalUrl] = useState('');

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    const queryParams = new URLSearchParams({
      page: pagination.currentPage.toString(),
      limit: pagination.pageSize.toString(),
      search: searchQuery,
      sortBy: sortConfig?.key || '',
      order: sortConfig?.direction || '',
    });

    try {
      const response = await fetch(`${readUrl}?${queryParams}`);
      const result = await response.json();
      setData(result.data || []);
      setPagination(prev => ({
        ...prev,
        totalPages: result.totalPages || 1,
        totalItems: result.totalItems || 0,
      }));
    } catch (error) {
      console.error('Error fetching data:', error);
      setData([]);
    } finally {
      setIsLoading(false);
    }
  }, [readUrl, pagination.currentPage, pagination.pageSize, searchQuery, sortConfig]);

  useEffect(() => {
    if (readUrl) {
      fetchData();
    }
  }, [fetchData, readUrl]);

  const getCellValue = (row: any, accessor: string | ((row: any) => any)): any => {
    if (typeof accessor === 'function') return accessor(row);
    
    // Handle concatenated accessors
    if (typeof accessor === 'string' && accessor.includes(',')) {
      return accessor.split(',')
        .map(path => {
          const trimmedPath = path.trim();
          return trimmedPath.split('.').reduce((obj, key) => obj?.[key], row);
        })
        .join(' ');
    }
    
    return accessor.split('.').reduce((obj, key) => obj?.[key], row) || '';
  };

  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig?.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const handlePageChange = (page: number) => {
    if (page < 1 || page > pagination.totalPages) return;
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  const toggleColumn = (columnId: string) => {
    setVisibleColumns(prev =>
      prev.includes(columnId)
        ? prev.filter(id => id !== columnId)
        : [...prev, columnId]
    );
  };

  const toggleSelectAll = () => {
    if (selectedRows.length === data.length) {
      setSelectedRows([]);
    } else {
      setSelectedRows(data.map(item => item.id));
    }
  };

  const handleDelete = async (ids: string[]) => {
    if (!deleteUrl) return;
    try {
      await fetch(deleteUrl, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids }),
      });
      await fetchData();
      setSelectedRows([]);
    } catch (error) {
      console.error('Error deleting items:', error);
    }
  };

  const generatePageNumbers = () => {
    const pages = [];
    const start = Math.max(1, pagination.currentPage - 3);
    const end = Math.min(pagination.totalPages, pagination.currentPage + 3);
    
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    return pages;
  };

  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      {/* Header */}
      <div className="flex flex-wrap items-center justify-between mb-4 gap-4">
        <h2 className="text-xl font-semibold">{title}</h2>
        <div className="flex items-center gap-2">
          <input
            type="text"
            placeholder="Search..."
            className="px-4 py-2 border rounded-md"
            value={searchQuery}
            onChange={handleSearch}
          />
          <div className="relative group">
            <button className="p-2 hover:bg-gray-100 rounded">
              <Settings className="w-5 h-5" />
            </button>
            <div className="hidden group-hover:block absolute right-0 mt-2 bg-white border rounded-lg shadow-lg p-2 min-w-[150px]">
              {columns.filter(col => col.hideable).map(col => (
                <label key={col.id} className="flex items-center gap-2 p-2 hover:bg-gray-50">
                  <input
                    type="checkbox"
                    checked={visibleColumns.includes(col.id)}
                    onChange={() => toggleColumn(col.id)}
                  />
                  {col.header}
                </label>
              ))}
            </div>
          </div>
          {createUrl && (
            <button
              className="flex items-center gap-2 px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600"
              onClick={() => {
                setModalUrl(createUrl);
                setIsModalOpen(true);
              }}
            >
              <Plus className="w-5 h-5" /> Add
            </button>
          )}
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="bg-gray-50">
              <th className="w-12 p-2">
                <input
                  type="checkbox"
                  checked={selectedRows.length === data.length && data.length > 0}
                  onChange={toggleSelectAll}
                />
              </th>
              {columns.map(col => visibleColumns.includes(col.id) && (
                <th key={col.id} className="p-2 text-left">
                  <div className="flex items-center gap-1">
                    {col.header}
                    {col.sortable && (
                      <button onClick={() => typeof col.accessor === 'string' && handleSort(col.accessor)}>
                        <ArrowUpDown className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </th>
              ))}
              <th className="p-2 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td colSpan={visibleColumns.length + 2} className="text-center py-4">
                  <div className="flex justify-center items-center h-24">
                    <AnimatedEllipsisLoader text="Loading data" />
                  </div>
                </td>
              </tr>
            ) : data.length === 0 ? (
              <tr>
                <td colSpan={visibleColumns.length + 2} className="text-center py-4">
                  No data to display
                </td>
              </tr>
            ) : (
              data.map(item => (
                <tr key={item.id} className="border-t hover:bg-gray-50">
                  <td className="p-2">
                    <input
                      type="checkbox"
                      checked={selectedRows.includes(item.id)}
                      onChange={() => setSelectedRows(prev =>
                        prev.includes(item.id)
                          ? prev.filter(id => id !== item.id)
                          : [...prev, item.id]
                      )}
                    />
                  </td>
                  {columns.map(col => visibleColumns.includes(col.id) && (
                    <td key={col.id} className="p-2">
                      {getCellValue(item, col.accessor)}
                    </td>
                  ))}
                  <td className="p-2 flex gap-2">
                    {updateUrl && (
                      <button
                        className="text-blue-500 hover:text-blue-600"
                        onClick={() => {
                          setModalUrl(updateUrl.replace(':id', item.id));
                          setIsModalOpen(true);
                        }}
                      >
                        <Edit className="w-5 h-5" />
                      </button>
                    )}
                    {deleteUrl && (
                      <button
                        className="text-red-500 hover:text-red-600"
                        onClick={() => handleDelete([item.id])}
                      >
                        <Trash2 className="w-5 h-5" />
                      </button>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="flex flex-wrap items-center justify-between mt-4 gap-4">
        <div className="flex items-center gap-2">
          <span>Items per page:</span>
          <select
            className="px-2 py-1 border rounded"
            value={pagination.pageSize}
            onChange={e => setPagination(prev => ({
              ...prev,
              pageSize: Number(e.target.value),
              currentPage: 1,
            }))}
          >
            {[5, 10, 20, 50].map(size => (
              <option key={size} value={size}>{size}</option>
            ))}
          </select>
        </div>

        <div className="flex items-center gap-2">
          <button
            className="p-2 disabled:opacity-50"
            onClick={() => handlePageChange(1)}
            disabled={pagination.currentPage === 1}
          >
            <ChevronFirst className="w-5 h-5" />
          </button>
          <button
            className="p-2 disabled:opacity-50"
            onClick={() => handlePageChange(pagination.currentPage - 1)}
            disabled={pagination.currentPage === 1}
          >
            <ChevronLeft className="w-5 h-5" />
          </button>

          {generatePageNumbers().map(page => (
            <button
              key={page}
              className={`px-3 py-1 rounded ${pagination.currentPage === page ? 'bg-blue-500 text-white' : 'hover:bg-gray-100'}`}
              onClick={() => handlePageChange(page)}
            >
              {page}
            </button>
          ))}

          <button
            className="p-2 disabled:opacity-50"
            onClick={() => handlePageChange(pagination.currentPage + 1)}
            disabled={pagination.currentPage === pagination.totalPages}
          >
            <ChevronRight className="w-5 h-5" />
          </button>
          <button
            className="p-2 disabled:opacity-50"
            onClick={() => handlePageChange(pagination.totalPages)}
            disabled={pagination.currentPage === pagination.totalPages}
          >
            <ChevronLast className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedRows.length > 0 && (
        <div className="mt-4 p-2 bg-gray-100 rounded flex items-center gap-4">
          <span>{selectedRows.length} selected</span>
          <button
            className="flex items-center gap-2 px-4 py-2 text-white bg-red-500 rounded hover:bg-red-600"
            onClick={() => handleDelete(selectedRows)}
          >
            <Trash2 className="w-5 h-5" /> Delete Selected
          </button>
        </div>
      )}

      {/* Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          fetchData();
          router.refresh();
        }}
        url={modalUrl}
        width="w-full md:w-3/4 lg:w-2/3"
      />
    </div>
  );
};

export default DataTable;