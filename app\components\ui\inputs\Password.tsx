import React, { useState, FocusEvent, ChangeEvent, MouseEvent } from 'react';

interface PasswordProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  errorMessage?: string;
  helperText?: string;
}

const Password: React.FC<PasswordProps> = ({
  label,
  errorMessage,
  helperText,
  value,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [internalValue, setInternalValue] = useState(value || '');
  const [showPassword, setShowPassword] = useState(false);

  const handleFocus = (event: FocusEvent<HTMLInputElement>) => {
    setIsFocused(true);
    props.onFocus?.(event);
  };

  const handleBlur = (event: FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    props.onBlur?.(event);
  };

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    setInternalValue(event.target.value);
    props.onChange?.(event);
  };

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };
  
  const handleMouseDown = (event: MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
  }

  const hasError = !!errorMessage;
  const hasContent = !!internalValue;

  return (
    <div className="w-full relative">
      <div className="relative">
        {label && (
          <label
            htmlFor={props.id}
            className={`absolute transition-all duration-300 ease-in-out ${
              hasContent || isFocused
                ? 'text-xs -top-0 left-3 px-1 pt-1 pb-1'
                : 'text-sm top-1/2 -translate-y-1/2 left-4'
            } ${
              hasError
                ? 'text-red-700 dark:text-red-500'
                : 'text-gray-500 dark:text-gray-400'
            } pointer-events-none`}
          >
            {label}
          </label>
        )}
        <input
          {...props}
          type={showPassword ? 'text' : 'password'}
          value={internalValue}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          className={`
            block w-full h-12 px-4 text-gray-900 border rounded-lg 
            focus:outline-none 
            ${hasContent || isFocused ? 'pt-6 pb-2' : 'py-3'}
            ${
              hasError
                ? 'border-red-500 focus:ring-red-500 focus:border-red-500 dark:bg-red-100 dark:border-red-400'
                : isFocused
                ? 'border-blue-500 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-800 dark:border-blue-500'
                : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white'
            }
            ${props.disabled ? 'bg-gray-100 dark:bg-gray-600' : ''}
          `}
        />
        <button
          type="button"
          onClick={toggleShowPassword}
          onMouseDown={handleMouseDown}
          className="absolute inset-y-0 right-0 flex items-center px-4 text-gray-600"
        >
          {showPassword ? (
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
              />
            </svg>
          ) : (
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
              />
            </svg>
          )}
        </button>
      </div>
      {hasError && (
        <p className="mt-2 text-sm text-red-600 dark:text-red-500">
          {errorMessage}
        </p>
      )}
      {helperText && !hasError && (
        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          {helperText}
        </p>
      )}
    </div>
  );
};

export default Password;