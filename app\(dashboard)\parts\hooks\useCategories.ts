'use client';

import { useState, useEffect } from 'react';
import { UseFormSetValue, UseFormWatch, UseFormGetValues } from 'react-hook-form';
import { PartFormValues } from '../types';
import {
  fetchCategories,
  fetchCategoryAttributes,
  fetchAttributeInputOptions
} from '@/app/libs/data';
import { CategoryAttribute, AttributeInputOption, FlatCategory } from '../types';
import { buildCategoryTree } from '../utils/categoryUtils';

interface UseCategoriesProps {
  setValue: UseFormSetValue<PartFormValues>;
  watch: UseFormWatch<PartFormValues>;
  getValues: UseFormGetValues<PartFormValues>;
}

export const useCategories = ({
  setValue,
  watch,
  getValues
}: UseCategoriesProps) => {
  const [flatCategories, setFlatCategories] = useState<FlatCategory[]>([]);
  const [nestedCategories, setNestedCategories] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;

    const loadCategories = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const categories = await fetchCategories();

        if (!isMounted) return;

        console.log('Loaded categories:', categories.length);

        if (!Array.isArray(categories)) {
          throw new Error('Invalid category data received');
        }

        if (categories.length === 0) {
          throw new Error('No categories found');
        }

        setFlatCategories(categories);

        // Build the nested tree for dropdown
        const processed = buildCategoryTree(categories);
        console.log('Processed tree:', processed.length);
        setNestedCategories(processed);

      } catch (error) {
        if (!isMounted) return;
        console.error('Error loading categories:', error);
        setError(error instanceof Error ? error.message : 'Failed to load categories');
        setFlatCategories([]);
        setNestedCategories([]);
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    loadCategories();

    return () => {
      isMounted = false;
    };
  }, []); // Load categories immediately when hook is initialized

  return {
    flatCategories,
    nestedCategories,
    isLoading,
    error
  };
};