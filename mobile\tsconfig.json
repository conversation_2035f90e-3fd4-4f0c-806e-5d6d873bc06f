{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react-native", "lib": ["es2017"], "moduleResolution": "node", "noEmit": true, "strict": true, "target": "esnext", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/screens/*": ["screens/*"], "@/services/*": ["services/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/store/*": ["store/*"], "@/constants/*": ["constants/*"]}}, "include": ["src/**/*", "index.js"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}