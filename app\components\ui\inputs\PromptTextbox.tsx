import { useState, useEffect, useRef, forwardRef } from 'react';
import { Send } from 'lucide-react';

interface PromptTextboxProps {
  value?: string;
  onChange?: (value: string) => void;
  onSubmit?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  buttonAriaLabel?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const PromptTextbox = forwardRef<HTMLTextAreaElement, PromptTextboxProps>(
  ({
    value = '',
    onChange,
    onSubmit,
    placeholder = 'Type your message...',
    disabled = false,
    buttonAriaLabel = 'Send message',
    size = 'md',
    className = '',
  }, ref) => {
    const [internalValue, setInternalValue] = useState(value);
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    const sizeClasses = {
      sm: 'py-2 px-4 text-sm',
      md: 'py-3 px-5 text-base',
      lg: 'py-4 px-6 text-lg',
    };

    useEffect(() => {
      if (value !== internalValue) {
        setInternalValue(value);
      }
    }, [value]);

    useEffect(() => {
      const textarea = textareaRef.current;
      if (textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = `${textarea.scrollHeight}px`;
      }
    }, [internalValue]);

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value;
      setInternalValue(newValue);
      onChange?.(newValue);
    };

    const handleSubmit = () => {
      if (!disabled && internalValue.trim()) {
        onSubmit?.(internalValue);
        setInternalValue('');
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        handleSubmit();
      }
    };

    return (
      <div className={`relative w-full ${className}`}>
        <textarea
          ref={textareaRef || ref}
          value={internalValue}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          aria-label="Message input"
          disabled={disabled}
          className={`
            w-full resize-none overflow-hidden rounded-xl border
            border-gray-300 bg-white pr-16 font-sans
            placeholder:text-gray-400 focus:border-blue-500 focus:ring-2
            focus:ring-blue-200 focus:ring-offset-2 disabled:cursor-not-allowed
            disabled:opacity-50 ${sizeClasses[size]}
            transition-[height] duration-200 ease-in-out
          `}
          style={{ minHeight: size === 'sm' ? '40px' : '48px' }}
          rows={1}
        />
        
        <button
          type="button"
          onClick={handleSubmit}
          disabled={disabled || !internalValue.trim()}
          aria-label={buttonAriaLabel}
          className={`
            absolute right-2 bottom-2 flex items-center justify-center rounded-full
            bg-blue-600 p-2 text-white transition-all hover:bg-blue-700
            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
            disabled:opacity-50 disabled:hover:bg-blue-600
            ${size === 'sm' ? 'h-8 w-8' : 'h-10 w-10'}
          `}
        >
          <Send
            className={`
              ${size === 'sm' ? 'h-4 w-4' : 'h-5 w-5'}
              transition-transform duration-200 hover:scale-110
            `}
          />
        </button>
      </div>
    );
  }
);

PromptTextbox.displayName = 'PromptTextbox';

export default PromptTextbox;


{/* 
    USAGE
    
    <PromptTextbox
        onSubmit={(value) => console.log(value)}
        size="md"
        placeholder="Type your message..."
        className="max-w-2xl mx-auto"
    /> 

*/}