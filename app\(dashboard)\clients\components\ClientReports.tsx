'use client';

import React, { useState, useEffect } from 'react';
import { createClient } from '@/app/libs/supabase/client';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON>,
  PieChart,
  TrendingUp,
  Users,
  Calendar,
  DollarSign,
  Car,
  Briefcase,
  Store,
  Wrench,
  User
} from 'lucide-react';
import LoadingSpinner from '@/app/components/ui/LoadingSpinner';

// Types
interface ClientStats {
  totalClients: number;
  activeClients: number;
  inactiveClients: number;
  creditClients: number;
  cashClients: number;
  categoryBreakdown: {
    [key: string]: number;
  };
  newClientsThisMonth: number;
  newClientsLastMonth: number;
}

// Stats Card Component
interface StatsCardProps {
  title: string;
  value: string | number;
  change?: number;
  icon: React.ReactNode;
  color: 'teal' | 'orange' | 'gray' | 'blue';
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, change, icon, color }) => {
  const colorClasses = {
    teal: 'bg-teal-50 border-teal-200',
    orange: 'bg-orange-50 border-orange-200',
    gray: 'bg-gray-50 border-gray-200',
    blue: 'bg-blue-50 border-blue-200',
  };

  const iconColorClasses = {
    teal: 'text-teal-600',
    orange: 'text-orange-600',
    gray: 'text-gray-600',
    blue: 'text-blue-600',
  };

  const changeColor = change && change > 0 ? 'text-green-600' : 'text-red-600';

  return (
    <div className={`p-4 md:p-6 rounded-lg shadow-sm border ${colorClasses[color]} flex flex-col justify-between`}>
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-medium text-gray-500">{title}</h3>
        <div className={`p-2 rounded-full bg-white ${iconColorClasses[color]}`}>
          {icon}
        </div>
      </div>
      <p className="text-2xl md:text-3xl font-semibold text-gray-800 mb-1">{value}</p>
      {change !== undefined && (
        <div className={`flex items-center text-xs ${changeColor}`}>
          <TrendingUp className="w-3 h-3 mr-1" />
          <span>{Math.abs(change)}% vs last period</span>
        </div>
      )}
    </div>
  );
};

// Category Distribution Chart
interface CategoryChartProps {
  categories: { name: string; count: number }[];
  isLoading: boolean;
}

const CategoryChart: React.FC<CategoryChartProps> = ({ categories, isLoading }) => {
  // Calculate total for percentages
  const total = categories.reduce((sum, category) => sum + category.count, 0);

  // Get category icon
  const getCategoryIcon = (categoryName: string) => {
    switch(categoryName) {
      case 'Individual':
        return <User className="w-4 h-4" />;
      case 'Garage':
        return <Wrench className="w-4 h-4" />;
      case 'Shop':
        return <Store className="w-4 h-4" />;
      case 'Broker':
        return <Briefcase className="w-4 h-4" />;
      default:
        return <Users className="w-4 h-4" />;
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-800">Client Categories</h2>
        <PieChart className="w-5 h-5 text-gray-500" />
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <LoadingSpinner size={24} />
        </div>
      ) : (
        <div className="space-y-4">
          {categories.map((category) => {
            const percentage = total > 0 ? Math.round((category.count / total) * 100) : 0;

            return (
              <div key={category.name} className="space-y-1">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <div className="mr-2 text-gray-600">
                      {getCategoryIcon(category.name)}
                    </div>
                    <span className="text-sm font-medium">{category.name}</span>
                  </div>
                  <span className="text-sm text-gray-500">{category.count} ({percentage}%)</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-teal-600 h-2 rounded-full"
                    style={{ width: `${percentage}%` }}
                  ></div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

// Client Type Chart
interface ClientTypeChartProps {
  creditCount: number;
  cashCount: number;
  isLoading: boolean;
}

const ClientTypeChart: React.FC<ClientTypeChartProps> = ({ creditCount, cashCount, isLoading }) => {
  const total = creditCount + cashCount;
  const creditPercentage = total > 0 ? Math.round((creditCount / total) * 100) : 0;
  const cashPercentage = total > 0 ? Math.round((cashCount / total) * 100) : 0;

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-800">Client Types</h2>
        <BarChart className="w-5 h-5 text-gray-500" />
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <LoadingSpinner size={24} />
        </div>
      ) : (
        <div className="space-y-4">
          <div className="space-y-1">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                <span className="text-sm font-medium">Credit Clients</span>
              </div>
              <span className="text-sm text-gray-500">{creditCount} ({creditPercentage}%)</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full"
                style={{ width: `${creditPercentage}%` }}
              ></div>
            </div>
          </div>

          <div className="space-y-1">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-orange-500 mr-2"></div>
                <span className="text-sm font-medium">Cash Clients</span>
              </div>
              <span className="text-sm text-gray-500">{cashCount} ({cashPercentage}%)</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-orange-500 h-2 rounded-full"
                style={{ width: `${cashPercentage}%` }}
              ></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Monthly Growth Chart
interface MonthlyGrowthChartProps {
  currentMonth: number;
  previousMonth: number;
  isLoading: boolean;
}

const MonthlyGrowthChart: React.FC<MonthlyGrowthChartProps> = ({ currentMonth, previousMonth, isLoading }) => {
  const growthPercentage = previousMonth > 0
    ? Math.round(((currentMonth - previousMonth) / previousMonth) * 100)
    : currentMonth > 0 ? 100 : 0;

  const isPositiveGrowth = growthPercentage >= 0;

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-800">Monthly Growth</h2>
        <TrendingUp className="w-5 h-5 text-gray-500" />
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <LoadingSpinner size={24} />
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500">Growth Rate</p>
              <p className={`text-2xl font-semibold ${isPositiveGrowth ? 'text-green-600' : 'text-red-600'}`}>
                {isPositiveGrowth ? '+' : ''}{growthPercentage}%
              </p>
            </div>
            <div className={`p-3 rounded-full ${isPositiveGrowth ? 'bg-green-100' : 'bg-red-100'}`}>
              <TrendingUp className={`w-6 h-6 ${isPositiveGrowth ? 'text-green-600' : 'text-red-600'}`} />
            </div>
          </div>

          <div className="space-y-3">
            <div className="space-y-1">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Current Month</span>
                <span className="text-sm text-gray-500">{currentMonth} new clients</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-teal-600 h-2 rounded-full"
                  style={{ width: '100%' }}
                ></div>
              </div>
            </div>

            <div className="space-y-1">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Previous Month</span>
                <span className="text-sm text-gray-500">{previousMonth} new clients</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-gray-400 h-2 rounded-full"
                  style={{ width: currentMonth > 0 ? `${(previousMonth / currentMonth) * 100}%` : '0%' }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Main Component
const ClientReports: React.FC = () => {
  const [stats, setStats] = useState<ClientStats>({
    totalClients: 0,
    activeClients: 0,
    inactiveClients: 0,
    creditClients: 0,
    cashClients: 0,
    categoryBreakdown: {},
    newClientsThisMonth: 0,
    newClientsLastMonth: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch client statistics
  useEffect(() => {
    const fetchClientStats = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const supabase = createClient();

        // Fetch all clients
        const { data: clients, error: clientsError } = await supabase
          .from('clients')
          .select(`
            *,
            client_categories(name)
          `);

        if (clientsError) throw new Error(clientsError.message);

        // Calculate dates for monthly stats
        const now = new Date();
        const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const firstDayOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const firstDayOfNextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);

        // Process client data
        const processedClients = clients.map(client => ({
          ...client,
          category_name: client.client_categories?.name
        }));

        // Calculate category breakdown
        const categoryBreakdown: {[key: string]: number} = {};
        processedClients.forEach(client => {
          const categoryName = client.category_name || 'Unknown';
          categoryBreakdown[categoryName] = (categoryBreakdown[categoryName] || 0) + 1;
        });

        // Calculate statistics
        const statsData: ClientStats = {
          totalClients: processedClients.length,
          activeClients: processedClients.filter(c => c.is_active).length,
          inactiveClients: processedClients.filter(c => !c.is_active).length,
          creditClients: processedClients.filter(c => c.client_type === 'credit').length,
          cashClients: processedClients.filter(c => c.client_type === 'cash').length,
          categoryBreakdown,
          newClientsThisMonth: processedClients.filter(c =>
            new Date(c.created_at) >= firstDayOfMonth &&
            new Date(c.created_at) < firstDayOfNextMonth
          ).length,
          newClientsLastMonth: processedClients.filter(c =>
            new Date(c.created_at) >= firstDayOfLastMonth &&
            new Date(c.created_at) < firstDayOfMonth
          ).length
        };

        setStats(statsData);
      } catch (err) {
        console.error('Error fetching client statistics:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchClientStats();
  }, []);

  // Prepare category data for chart
  const categoryData = Object.entries(stats.categoryBreakdown).map(([name, count]) => ({
    name,
    count
  }));

  // Calculate growth percentage
  const growthPercentage = stats.newClientsLastMonth > 0
    ? Math.round(((stats.newClientsThisMonth - stats.newClientsLastMonth) / stats.newClientsLastMonth) * 100)
    : stats.newClientsThisMonth > 0 ? 100 : 0;

  // Render error state
  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
        <StatsCard
          title="Total Clients"
          value={isLoading ? '-' : stats.totalClients}
          icon={<Users className="w-5 h-5" />}
          color="teal"
        />
        <StatsCard
          title="Active Clients"
          value={isLoading ? '-' : stats.activeClients}
          change={stats.totalClients > 0 ? Math.round((stats.activeClients / stats.totalClients) * 100) : 0}
          icon={<Users className="w-5 h-5" />}
          color="blue"
        />
        <StatsCard
          title="Credit Clients"
          value={isLoading ? '-' : stats.creditClients}
          change={stats.totalClients > 0 ? Math.round((stats.creditClients / stats.totalClients) * 100) : 0}
          icon={<DollarSign className="w-5 h-5" />}
          color="orange"
        />
        <StatsCard
          title="New This Month"
          value={isLoading ? '-' : stats.newClientsThisMonth}
          change={growthPercentage}
          icon={<Calendar className="w-5 h-5" />}
          color="gray"
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
        <CategoryChart
          categories={categoryData}
          isLoading={isLoading}
        />
        <ClientTypeChart
          creditCount={stats.creditClients}
          cashCount={stats.cashClients}
          isLoading={isLoading}
        />
        <MonthlyGrowthChart
          currentMonth={stats.newClientsThisMonth}
          previousMonth={stats.newClientsLastMonth}
          isLoading={isLoading}
        />
      </div>

      {/* Additional Reports */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">Client Reports</h2>
        <p className="text-gray-600 mb-6">
          Select a report type to generate detailed client analytics.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <div className="flex flex-col items-center">
              <Users className="w-8 h-8 text-teal-600 mb-2" />
              <h3 className="font-medium text-gray-900">Client Demographics</h3>
              <p className="text-sm text-gray-500 text-center mt-1">
                Analyze client distribution by type and category
              </p>
            </div>
          </button>

          <button className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <div className="flex flex-col items-center">
              <TrendingUp className="w-8 h-8 text-blue-600 mb-2" />
              <h3 className="font-medium text-gray-900">Growth Analysis</h3>
              <p className="text-sm text-gray-500 text-center mt-1">
                Track client acquisition and retention over time
              </p>
            </div>
          </button>

          <button className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <div className="flex flex-col items-center">
              <Car className="w-8 h-8 text-orange-600 mb-2" />
              <h3 className="font-medium text-gray-900">Car Distribution</h3>
              <p className="text-sm text-gray-500 text-center mt-1">
                Analyze car brands and models across clients
              </p>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ClientReports;
