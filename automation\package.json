{"name": "jiji-automation", "version": "1.0.0", "description": "Automated browser tool for posting automotive parts to Jiji.co.ke marketplace", "main": "jiji-automation.ts", "scripts": {"start": "ts-node run-jiji-automation.ts run", "test": "ts-node run-jiji-automation.ts test", "config": "ts-node run-jiji-automation.ts config", "build": "tsc", "dev": "ts-node run-jiji-automation.ts run --no-headless", "install-browsers": "npx playwright install", "setup": "npm install && npm run install-browsers"}, "keywords": ["automation", "jiji", "marketplace", "automotive", "parts", "playwright", "browser-automation"], "author": "Autoflow Team", "license": "MIT", "dependencies": {"playwright": "^1.40.0", "@playwright/test": "^1.40.0", "@supabase/supabase-js": "^2.38.0", "dotenv": "^16.3.1", "commander": "^11.1.0", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "devDependencies": {"@types/node": "^20.8.0"}, "engines": {"node": ">=16.0.0"}}