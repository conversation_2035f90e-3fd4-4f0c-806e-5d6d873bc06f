-- Fix the delete_part_and_references function to use SECURITY DEFINER
-- This allows the function to access vault secrets without requiring explicit permissions

-- Drop the existing function if it exists
DROP FUNCTION IF EXISTS public.delete_part_and_references;

-- Recreate the function with SECURITY DEFINER
CREATE OR REPLACE FUNCTION public.delete_part_and_references(p_part_id integer)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER -- Add SECURITY DEFINER to use the privileges of the function owner
AS $BODY$
DECLARE
    v_image_id integer;
BEGIN
    -- Get the image_id associated with the part
    SELECT id INTO v_image_id FROM part_images WHERE part_id = p_part_id LIMIT 1;
    
    -- Delete from part_categories
    DELETE FROM part_categories WHERE part_id = p_part_id;
    
    -- Delete from part_images
    DELETE FROM part_images WHERE part_id = p_part_id;
    
    -- Delete from part_suppliers
    DELETE FROM part_suppliers WHERE part_id = p_part_id;
    
    -- Delete from part_vehicles
    DELETE FROM part_vehicles WHERE part_id = p_part_id;
    
    -- Delete from parts
    DELETE FROM parts WHERE id = p_part_id;
    
    -- Return success
    RETURN;
END;
$BODY$;

-- Set ownership to postgres (or the appropriate owner with necessary privileges)
ALTER FUNCTION public.delete_part_and_references(integer) OWNER TO postgres;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.delete_part_and_references(integer) TO authenticated;
GRANT EXECUTE ON FUNCTION public.delete_part_and_references(integer) TO service_role;
