// app/page.tsx
'use client'; // This directive is required for client-side components

import dynamic from 'next/dynamic';
import LandingLayout from './components/landing/LandingLayout';

// Dynamically import the LandingPage component
// This ensures that framer-motion animations work correctly
const LandingPage = dynamic(
  () => import('./components/landing/LandingPage'),
  { ssr: true }
);

export default function Home() {
  return (
    <LandingLayout>
      <LandingPage />
    </LandingLayout>
  );
}