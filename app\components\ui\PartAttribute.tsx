import React from 'react';
import Icon from './Icon';

interface PartAttributeProps {
  icon: string;
  value: string | number;
  label?: string;
  library?: 'mdi' | 'lucide';
  className?: string;
}

const PartAttribute: React.FC<PartAttributeProps> = ({
  icon,
  value,
  label,
  library = 'lucide',
  className = '',
}) => {
  return (
    <div className={`flex items-center ${className}`}>
      <Icon name={icon} size={20} library={library} className="text-gray-500 mr-2" />
      <div className="flex items-center">
        <span className="font-medium text-gray-800">{value}</span>
        {label && <span className="text-gray-500 ml-1">{label}</span>}
      </div>
    </div>
  );
};

export default PartAttribute;
